package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.User;
import com.magnamedia.entity.serializer.IdOnlySerializer;
import java.util.Date;
import javax.persistence.Embeddable;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import lombok.Getter;
import lombok.Setter;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Jun 26, 2021
 * Jirra ACC-3542
 */
@Embeddable
@Setter
@Getter
public class MaintenanceRequestNote{

    public MaintenanceRequestNote() {
    }

    public MaintenanceRequestNote(String notes, User user) {
        this.notes = notes;
        this.userId = user.getId();
        this.userName = user != null ? user.getFullName() : "";
    }
    
    @Lob
    private String notes;
    
    private String userName;
    
    @JsonIgnore
    private Long userId;
    
    private Date creationDate = new Date();
}
