package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.extra.DiscountsWithVatHelper;
import com.magnamedia.module.type.ContractPackageType;
import com.magnamedia.module.type.PaymentTermConfigType;
import org.apache.commons.lang3.BooleanUtils;

import javax.persistence.*;
import java.util.List;

@MappedSuperclass
public abstract class AbstractPaymentTerm extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "NATIONALITY_ID")
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem nationality;

    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "CONTRACT_PROSPECT_TYPE_ID")
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem contractProspectType;

    @Column(nullable = false, name = "TYPE")
    @Enumerated(EnumType.STRING)
    private PaymentTermConfigType type;

    @Column(name = "PACKAGE")
    @Enumerated(EnumType.STRING)
    private ContractPackageType packageType;

    @Column(nullable = false)
    private Boolean isRemote = false;

    @Column(nullable = false)
    private Boolean periodicalAdditionalDiscount;

    @Column
    private Double weeklyAmount;

    @Column
    private Double dailyRateAmount;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem cptFamily;

    @Column
    private Double liveOutAmount;

    public PicklistItem getNationality() {
        return nationality;
    }

    public void setNationality(PicklistItem nationality) {
        this.nationality = nationality;
    }

    public PicklistItem getContractProspectType() {
        return contractProspectType;
    }

    public void setContractProspectType(PicklistItem contractProspectType) {
        this.contractProspectType = contractProspectType;
    }

    public PaymentTermConfigType getType() {
        return type;
    }

    public void setType(PaymentTermConfigType type) {
        this.type = type;
    }

    public double getMonthlyPayment() {
        AbstractPaymentTypeConfig monthlyPaymentTypeConfig = getPaymentTypeConfig(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE);
        return monthlyPaymentTypeConfig != null ? monthlyPaymentTypeConfig.getAmount() : 0;
    }

    public void setMonthlyPayment(Double monthlyPayment) {
        AbstractPaymentTypeConfig monthlyPaymentTermConfig =
                this.getPaymentTypeConfig(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE);
        if (monthlyPaymentTermConfig != null) {
            monthlyPaymentTermConfig.setAmount(monthlyPayment);
        }
    }

    public Double getDiscount() {
        AbstractPaymentTypeConfig monthlyPaymentTypeConfig = getPaymentTypeConfig(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE);
        return monthlyPaymentTypeConfig != null ? monthlyPaymentTypeConfig.getDiscount() : 0;
    }

    public void setDiscount(Double discount) {
        AbstractPaymentTypeConfig monthlyPaymentTermConfig =
                this.getPaymentTypeConfig(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE);
        if (monthlyPaymentTermConfig != null) {
            monthlyPaymentTermConfig.setDiscount(discount);
        }
    }

    //Jirra ACC-3272
    public boolean isAffectedByAdditionalDiscount(String paymentTypeCode) {
        AbstractPaymentTypeConfig paymentTermConfig = this.getPaymentTypeConfig(paymentTypeCode);

        return paymentTermConfig != null && BooleanUtils.toBoolean(paymentTermConfig.getAffectedByAdditionalDiscount());
    }

    public int getDiscountEffectiveAfter() {
        AbstractPaymentTypeConfig monthlyPaymentTypeConfig = getPaymentTypeConfig(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE);
        return monthlyPaymentTypeConfig != null ? monthlyPaymentTypeConfig.getDiscountEffectiveAfter() : 0;
    }

    public void setDiscountEffectiveAfter(Integer discountEffectiveAfter) {
        AbstractPaymentTypeConfig monthlyPaymentTermConfig =
                this.getPaymentTypeConfig(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE);
        if (monthlyPaymentTermConfig != null) {
            monthlyPaymentTermConfig.setDiscountEffectiveAfter(discountEffectiveAfter);
        }
    }

    public double getAgencyFee() {
        AbstractPaymentTypeConfig agencyPaymentTypeConfig = getPaymentTypeConfig(AbstractPaymentTypeConfig.AGENCY_FEE_TYPE_CODE);
        return agencyPaymentTypeConfig != null ? agencyPaymentTypeConfig.getAmount() : 0;
    }

    public void setAgencyFee(Double agencyFee) {
        AbstractPaymentTypeConfig agencyPaymentTypeConfig = getPaymentTypeConfig(AbstractPaymentTypeConfig.AGENCY_FEE_TYPE_CODE);
        if (agencyPaymentTypeConfig != null) {
            agencyPaymentTypeConfig.setAmount(agencyFee);
        }
    }

    public ContractPackageType getPackageType() {
        return packageType;
    }

    public void setPackageType(ContractPackageType packageType) {
        this.packageType = packageType;
    }

    public Boolean getIsRemote() {
        return isRemote;
    }

    public void setIsRemote(Boolean remote) {
        this.isRemote = remote;
    }

    public Boolean getPeriodicalAdditionalDiscount() {
        return periodicalAdditionalDiscount;
    }

    public void setPeriodicalAdditionalDiscount(Boolean periodicalAdditionalDiscount) {
        this.periodicalAdditionalDiscount = periodicalAdditionalDiscount;
    }

    @JsonIgnore
    public double getDiscountedMonthlyPayment() {
        return this.getMonthlyPayment() - this.getDiscount();
    }

    @JsonIgnore
    public double getMonthlyPaymentWithoutVat() {
        return Math.floor(DiscountsWithVatHelper.getAmountWithoutVat(this.getMonthlyPayment()));
    }

    @JsonIgnore
    public double getDiscountedMonthlyPaymentWithoutVat() {
        return DiscountsWithVatHelper.getAmountWithoutVat(this.getDiscountedMonthlyPayment());
    }

    @JsonIgnore
    abstract List<AbstractPaymentTypeConfig> getPaymentTypeConfigs(String paymentTypeCode);

    @JsonIgnore
    public AbstractPaymentTypeConfig getPaymentTypeConfig(String paymentTypeCode) {
        List<AbstractPaymentTypeConfig> paymentConfigTypes = getPaymentTypeConfigs(paymentTypeCode);
        if (paymentConfigTypes == null || paymentConfigTypes.isEmpty()) return null;

        return paymentConfigTypes.get(0);
    }

    public Double getWeeklyAmount() {
        return weeklyAmount == null ? 0.0 : weeklyAmount;
    }

    public Double getDailyRateAmount() {
        return dailyRateAmount == null ? 0.0 : dailyRateAmount;
    }

    public void setWeeklyAmount(Double weeklyAmount) {

        this.weeklyAmount = weeklyAmount != null && weeklyAmount == 0.0 ? null : weeklyAmount;
    }

    public void setDailyRateAmount(Double dailyRateAmount) {

        this.dailyRateAmount = dailyRateAmount != null && dailyRateAmount == 0.0 ? null : dailyRateAmount;
    }

    public PicklistItem getCptFamily() { return cptFamily; }

    public void setCptFamily(PicklistItem cptFamily) { this.cptFamily = cptFamily; }

    public Double getLiveOutAmount() { return liveOutAmount; }

    public void setLiveOutAmount(Double liveOutAmount) {
        this.liveOutAmount = liveOutAmount;
    }
}
