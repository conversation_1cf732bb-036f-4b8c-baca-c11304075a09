//package com.magnamedia.entity.serializer;
//
//import com.fasterxml.jackson.core.JsonGenerator;
//import com.fasterxml.jackson.core.JsonProcessingException;
//import com.fasterxml.jackson.databind.JsonSerializer;
//import com.fasterxml.jackson.databind.SerializerProvider;
//import com.fasterxml.jackson.databind.jsontype.TypeSerializer;
//import com.magnamedia.entity.BaseReportCompany;
//
//import java.io.IOException;
//
///**
// * Created by hp on 7/21/2020.
// */
//public class BasePLCompanySerializer extends JsonSerializer<BaseReportCompany> {
//
//    @Override
//    public void serialize(BaseReportCompany value, JsonGenerator gen, SerializerProvider serializers)
//            throws IOException, JsonProcessingException {
//        if (value == null) {
//            gen.writeNull();
//            return;
//        }
//        gen.writeStartObject();
//        gen.writeNumberField("id",
//                value.getId());
//        gen.writeStringField("label",
//                value.getLabel());
//        gen.writeEndObject();
//    }
//
//    @Override
//    public void serializeWithType(BaseReportCompany value, JsonGenerator gen,
//                                  SerializerProvider serializers, TypeSerializer typeSer)
//            throws IOException, JsonProcessingException {
//        if (value == null) {
//            gen.writeNull();
//            return;
//        }
//        gen.writeStartObject();
//        gen.writeStringField("type", value.getType());
//        gen.writeNumberField("id", value.getId());
//        gen.writeStringField("label",  value.getLabel());
//        gen.writeEndObject();
//    }
//
//}
