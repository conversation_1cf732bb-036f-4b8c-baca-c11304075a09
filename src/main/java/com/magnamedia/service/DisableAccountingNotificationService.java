package com.magnamedia.service;


import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PushNotification;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.master.repository.ModuleRepository;
import com.magnamedia.entity.*;
import com.magnamedia.extra.CcNotificationTemplateCode;
import com.magnamedia.extra.DirectDebitGenerationPlanTemplate;
import com.magnamedia.extra.MvNotificationTemplateCode;
import com.magnamedia.helper.PushNotificationHelper;
import com.magnamedia.module.type.DDMessagingType;
import com.magnamedia.module.type.DirectDebitCategory;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.repository.*;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Service
public class DisableAccountingNotificationService {
    
    protected static final Logger logger = Logger.getLogger(DisableAccountingNotificationService.class.getName());
    
    @Autowired
    private HousemaidAttendanceLogRepository housemaidAttendanceLogRepository;
    @Autowired
    private DisablePushNotificationRepository disablePushNotificationRepository;
    @Autowired
    private MessagingService localPushNotificationService;
    @Autowired
    private HousemaidRepository housemaidRepository;
    @Autowired
    private DirectDebitRepository directDebitRepository;
    @Autowired
    private ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private ContractRepository contractRepository;
    @Autowired
    private NotificationTemplateService disableAccountingNotificationGetTemplateService;
    @Autowired
    private ModuleRepository moduleRepository;
    @Autowired
    private LocalSmsRepository localSmsRepository;
    @Autowired
    private ContractPaymentConfirmationToDoService contractPaymentConfirmationToDoService;

    // ACC-5214
    public void disableNotificationOnBouncedPaymentReceived() {
        logger.log(Level.INFO, "Disable notifications on bounced payment received");
        
        Pageable pageRequest = PageRequest.of(0, 100);
        Page<PushNotification> notifications = disablePushNotificationRepository
            .findActiveNotificationsForBouncedFlow(pageRequest);
        
        while (notifications.hasContent()) {
            List<Long> notificationsId = notifications.getContent().stream().map(PushNotification::getId).collect(Collectors.toList());
            
            logger.log(Level.INFO, "Disable notifications on bounced payment received", notificationsId.size());
            localPushNotificationService.createDisableNotificationBGT(notificationsId, "Disable notifications on bounced payment received");
            
            pageRequest = pageRequest.next();
            notifications = disablePushNotificationRepository.findActiveNotificationsForBouncedFlow(pageRequest);
            
        }
    }
    
    public void disableProofTransferSubmit() {
        logger.log(Level.INFO, "Disable on proof transfer submit");
        List<String> notificationCodeList =
            Arrays.asList(
                CcNotificationTemplateCode.CC_ACCOUNTING_NOT_OWED_MONEY_FROM_CLIENT_8_1_2_NOTIFICATION.toString(),
                CcNotificationTemplateCode.CC_ACCOUNTING_WRONGLY_CHARGED_MONEY_ON_CLIENT_8_1_3_NOTIFICATION.toString(),
                MvNotificationTemplateCode.MV_ACCOUNTING_NOT_OWED_MONEY_FROM_CLIENT_8_1_2_NOTIFICATION.toString(),
                MvNotificationTemplateCode.MV_ACCOUNTING_WRONGLY_CHARGED_MONEY_ON_CLIENT_8_1_3_NOTIFICATION.toString());
        
        Pageable pageRequest = PageRequest.of(0, 100);
        Page<PushNotification> notifications = disablePushNotificationRepository
            .findActiveNotificationsAndProofTransferSubmit(notificationCodeList, pageRequest);
        
        while (notifications.hasContent()) {
            List<Long> notificationsId = notifications.getContent().stream()
                .map(PushNotification::getId).collect(Collectors.toList());
            
            logger.log(Level.INFO, "Notifications : found " + notificationsId.size());
            localPushNotificationService.createDisableNotificationBGT(notificationsId, "Disable on proof transfer submit");
            
            pageRequest = pageRequest.next();
            notifications = disablePushNotificationRepository
                .findActiveNotificationsAndProofTransferSubmit(notificationCodeList, pageRequest);
            
        }
    }
    // ACC-5214
    public void handleContractCancelled(Contract contract) {
        contract = contractRepository.findOne(contract.getId());

        logger.log(Level.INFO, "Disable on contract cancelled contract id: {0}", contract.getId());

        // disable CTA
        List<String> notificationCodeDisableList = Arrays.asList(
            MvNotificationTemplateCode.MV_DIRECT_DEBIT_GENERATION_PLAN_SAME_DAY_RECRUITMENT_FEE_NOTIFICATION.toString(),
            MvNotificationTemplateCode.MV_DIRECT_DEBIT_GENERATION_PLAN_INSURANCE_NOTIFICATION.toString(),
            MvNotificationTemplateCode.MV_DIRECT_DEBIT_GENERATION_PLAN_OTHER_DD_TYPE_NOTIFICATION.toString(),
            CcNotificationTemplateCode.CC_ACCOUNTING_NOT_OWED_MONEY_FROM_CLIENT_8_1_2_NOTIFICATION.toString(),
            CcNotificationTemplateCode.CC_ACCOUNTING_WRONGLY_CHARGED_MONEY_ON_CLIENT_8_1_3_NOTIFICATION.toString(),
            CcNotificationTemplateCode.CC_ACCOUNTING_OWE_MONEY_TO_CLIENT_8_1_1_NOTIFICATION.toString());

        List<PushNotification> notifications = disablePushNotificationRepository
            .findActiveNotificationsByAccountingModuleAndClient(contract.getClient().getId().toString(),
                moduleRepository.findByCode("accounting").getId(), contract.getId());
        
        List<Long> notificationsId = notifications.stream().filter(n -> {
                try {
                    if (n.getContext().contains("payment_credit_card") ||
                        n.getContext().contains("paytab_link_click_here") ||
                        n.getContext().contains("bounced_payment_clicking_here") ||
                        n.getContext().contains("pay_using_different_bank_account_click_here") ||
                        n.getContext().contains("sign_now") ||
                        n.getContext().contains("link_send_dd_details") ||
                        n.getContext().contains("link6") ||
                        n.getContext().contains("link7") ||
                        n.getContext().contains("link8") ||
                        n.getContext().contains("link9") ||
                        n.getContext().contains("accommodation_location") ||
                        notificationCodeDisableList.contains(n.getType().getCode())) {
                        logger.log(Level.INFO, "notification id " + n.getId() + " client id " + n.getRecepientId());
                        
                        return true;
                    }
                } catch (Exception ex) {
                    logger.log(Level.SEVERE, "Error while disabling notification: " + n.getId(), ex);
                }
                return false;
            }).map(PushNotification::getId)
            .collect(Collectors.toList());
        
        logger.log(Level.INFO, "Notifications : found " + notificationsId.size());
        localPushNotificationService.createDisableNotificationBGT(notificationsId, "Disable on contract cancelled");
        
        // move to inbox
        List<String> notificationCodeHideList = new ArrayList<>(Arrays.asList(
            MvNotificationTemplateCode.MV_PAYMENT_EXPIRY_4_1_1_NOTIFICATION.toString(),
            MvNotificationTemplateCode.MV_PAYMENT_EXPIRY_4_1_2_NOTIFICATION.toString(),
            CcNotificationTemplateCode.CC_PAYMENT_EXPIRY_4_1_1_NOTIFICATION.toString(),
            CcNotificationTemplateCode.CC_PAYMENT_EXPIRY_4_1_2_NOTIFICATION.toString(),
            MvNotificationTemplateCode.MV_DIRECT_DEBIT_GENERATION_PLAN_SAME_DAY_RECRUITMENT_FEE_NOTIFICATION.toString(),
            MvNotificationTemplateCode.MV_DIRECT_DEBIT_GENERATION_PLAN_INSURANCE_NOTIFICATION.toString(),
            CcNotificationTemplateCode.CC_CLIENT_REFUND_BANK_TRANSFER_DETAILS_NOTIFICATION.toString(),
            MvNotificationTemplateCode.MV_DIRECT_DEBIT_GENERATION_PLAN_OTHER_DD_TYPE_NOTIFICATION.toString(),
            DirectDebitGenerationPlanTemplate.SAME_DAY_RECRUITMENT_FEE_PAYING_FIA_CREDIT_CARD.toString(),
            DirectDebitGenerationPlanTemplate.INSURANCE.toString(),
            DirectDebitGenerationPlanTemplate.INSURANCE_PAYING_FIA_CREDIT_CARD.toString(),
            DirectDebitGenerationPlanTemplate.OTHER_DD_TYPE.toString(),
            DirectDebitGenerationPlanTemplate.OTHER_DD_TYPE_PAYING_FIA_CREDIT_CARD.toString()));
    
        notificationCodeHideList.addAll(disableAccountingNotificationGetTemplateService
            .invalidAccountRejectionHideOnContractTerminated());

        notificationCodeHideList.addAll(disableAccountingNotificationGetTemplateService
            .getReceivePaymentNotificationCodes());

        notificationCodeHideList.addAll(disableAccountingNotificationGetTemplateService
                .terminationMessageGetTemplates());
        notificationsId = notifications.stream().filter(n -> {
                try {
                    if (notificationCodeHideList.contains(n.getType().getCode())) {
                        logger.log(Level.INFO, "notification id " + n.getId() + " client id " + n.getRecepientId());
                        return true;
                    }
                } catch (Exception ex) {
                    logger.log(Level.SEVERE, "Error while disabling notification: " + n.getId(), ex);
                }
                return false;
            }).map(PushNotification::getId)
            .collect(Collectors.toList());
        
        localPushNotificationService.createMoveToInboxBgt(notificationsId,
            "move to inbox once the contract is terminated");

        moveMaidNotificationsToInbox(contract);
    }
    
    // ACC-5214
    public void disableOnRetractContractTermination(Contract contract) {
        
        contract = contractRepository.findOne(contract.getId());
        logger.log(Level.INFO, "Disable on retract contract termination contract id {0}", contract.getId());
        
        List<PushNotification> notifications = disablePushNotificationRepository
                .findActiveNotificationsByScheduleTermCategory(contract.getClient().getId().toString(), contract.getId());
        
        logger.log(Level.INFO, "Notifications : found " + notifications.size());
        localPushNotificationService.createDisableNotificationBGT(
            notifications.stream().map(BaseEntity::getId).collect(Collectors.toList()),
            "Disable on retract contract termination");

        // ACC-7201
        DDMessagingToDoRepository ddMessagingToDoRepository = Setup.getRepository(DDMessagingToDoRepository.class);
        List<DDMessagingToDo> ddMessagingToDos = ddMessagingToDoRepository
                .findActiveDDMessagingToDoWithTerminateDDMessaging(contract.getUuid(), DDMessagingType.Termination);

        ddMessagingToDos.forEach(ddMessagingToDo -> ddMessagingToDo.setActive(false));

        ddMessagingToDoRepository.save(ddMessagingToDos);

        moveMaidNotificationsToInbox(contract);
    }

    // ACC-7097
    private void moveMaidNotificationsToInbox(Contract contract) {
        if (contract.getHousemaid() == null) return;

        List<PushNotification> maidNotifications = disablePushNotificationRepository
                .findActiveNotificationsByScheduleTermCategoryForHousemaid(
                        contract.getHousemaid().getId().toString(),
                        contract.getId());

        localPushNotificationService.createMoveToInboxBgt(
                maidNotifications.stream().map(BaseEntity::getId).collect(Collectors.toList()),
                "Move to inbox on retract contract termination or contract got cancelled");
    }
    
    public void disableNotificationAfterXDay(int days) {
        logger.log(Level.INFO, "Disable notifications after {0} day", days);
        List<String> notificationCodeList = null;
        switch (days) {
            case 3:
                notificationCodeList = Arrays.asList(
                    CcNotificationTemplateCode.CC_CLIENT_REFUND_BANK_TRANSFER_DETAILS_NOTIFICATION.toString());
                break;
            case 7: // ACC-4715
                notificationCodeList = disableAccountingNotificationGetTemplateService.ddSigningOfferTemplate();
                break;
        }
        
        Pageable pageRequest = PageRequest.of(0, 100);
        Page<PushNotification> notifications = disablePushNotificationRepository
            .findActiveNotifications(notificationCodeList, pageRequest);
        
        while (notifications.hasContent()) {
            List<Long> notificationsId = notifications.getContent().stream()
                .filter(n -> {
                    try {
                        logger.log(Level.INFO, "notification id " + n.getId()
                            + " client id " + Long.valueOf(n.getRecepientId()));
                        if (new DateTime().isAfter(new DateTime(n.getCreationDate()).plusDays(days)))
                            return true;
                    } catch (Exception ex) {
                        logger.log(Level.SEVERE, "Error while disabling notification: " + n.getId(), ex);
                    }
                    return false;
                }).map(PushNotification::getId)
                .collect(Collectors.toList());
    
            logger.log(Level.INFO, "Disable notifications after {0} day size {1}", new Object[]{days, notificationsId.size()});
            localPushNotificationService.createDisableNotificationBGT(notificationsId,
                "Disable notifications after " + days +" days");
            
            pageRequest = pageRequest.next();
            notifications = disablePushNotificationRepository.findActiveNotifications(notificationCodeList, pageRequest);
        }
    }

    // ACC-5214 ACC-5235
    public void incompleteDdStatusChanged() {
        logger.log(Level.INFO, "Disable on incomplete dd status changed");
        List<String> notificationCodeList =
            Arrays.asList(
                MvNotificationTemplateCode.MV_DD_PENDING_INFO_NOTIFICATION.toString(),
                CcNotificationTemplateCode.CC_DD_PENDING_INFO_NOTIFICATION.toString());
        
        Pageable pageRequest = PageRequest.of(0, 100);
        Page<PushNotification> notifications = disablePushNotificationRepository
            .findActiveNotifications(notificationCodeList, pageRequest);
        
        while (notifications.hasContent()) {
            List<Long> notificationsId = notifications.getContent().stream()
                .filter(n -> {
                    try {
                        logger.log(Level.INFO, "notification id " + n.getId() + " client id " + Long.parseLong(n.getRecepientId()));
                        
                        if (!Arrays.asList("Contract", "OnCloseTaxiWorkOrder").contains(n.getOwnerType()))
                            return false;
                        
                        Contract contract = contractRepository.findOne(n.getOwnerId());
                        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
                        
                        SelectQuery<DirectDebit> query = new SelectQuery<>(DirectDebit.class);
                        query.filterBy("contractPaymentTerm.id", "=", cpt.getId());
                        query.filterBy(new SelectFilter("status", "=", DirectDebitStatus.IN_COMPLETE)
                            .or("MStatus", "=", DirectDebitStatus.IN_COMPLETE));
                        query.setLimit(1);
                        
                        return query.execute().isEmpty();
                        
                    } catch (Exception ex) {
                        logger.log(Level.SEVERE, "Error while disabling notification: " + n.getId(), ex);
                    }
                    
                    return false;
                }).map(PushNotification::getId)
                .collect(Collectors.toList());
            
            logger.log(Level.INFO, "Notifications : found " + notificationsId.size());
            localPushNotificationService.createDisableNotificationBGT(notificationsId, "Disable on incomplete dd status changed");
            
            pageRequest = pageRequest.next();
            notifications = disablePushNotificationRepository.findActiveNotifications(notificationCodeList, pageRequest);
        }
    }
    // ACC-5214
    public void disableOnDdRejectedAuthorizationConfirmed() {
        
        logger.log(Level.INFO, "Disable notifications On dd rejected authorization confirmed");
        List<String> notificationCodeList = disableAccountingNotificationGetTemplateService
            .rejectedAuthorization();
        
        Pageable pageRequest = PageRequest.of(0, 100);
        Page<PushNotification> notifications = disablePushNotificationRepository
            .findActiveNotifications(notificationCodeList, pageRequest);
        
        while (notifications.hasContent()) {
            List<Long> notificationsId = notifications.getContent().stream()
                .filter(n -> {
                    try {
                        logger.log(Level.INFO, "notification id " + n.getId() + " client id " + Long.parseLong(n.getRecepientId()));
    
                        if (!n.getOwnerType().equals("DirectDebit")) return false;
    
                        DirectDebit dd = directDebitRepository.findOne(n.getOwnerId());
                        return dd != null && dd.getDirectDebitRejectionToDo() != null
                            && dd.getDirectDebitRejectionToDo().getDirectDebits().stream().anyMatch(d ->
                            (d.getCategory().equals(DirectDebitCategory.B) && d.getStatus().equals(DirectDebitStatus.CONFIRMED))
                            || (d.getCategory().equals(DirectDebitCategory.A) && d.getMStatus().equals(DirectDebitStatus.CONFIRMED)));

                    } catch (Exception ex) {
                        logger.log(Level.SEVERE, "Error while disabling notification: " + n.getId(), ex);
                    }
                    return false;
                }).map(PushNotification::getId)
                .collect(Collectors.toList());
            
            logger.log(Level.INFO, "Disable notifications On dd rejected authorization confirmed size {0}",
                notificationsId.size());
            localPushNotificationService.createDisableNotificationBGT(notificationsId,
                "Disable notifications On dd rejected authorization confirmed");
            
            pageRequest = pageRequest.next();
            notifications = disablePushNotificationRepository.findActiveNotifications(notificationCodeList, pageRequest);
        }
    }

    public void disableDdRejectedAuthorizationOnSwitching(Contract c) {

        logger.log(Level.INFO, "Disable dd Rejected category authorization on Switching");
        List<String> notificationCodeList = disableAccountingNotificationGetTemplateService
            .rejectedAuthorization();

        List<PushNotification> notifications = disablePushNotificationRepository
            .findActiveNotifications(c.getClient().getId().toString(), c.getId(), notificationCodeList);
        
        localPushNotificationService.createDisableNotificationBGT(
            notifications.stream().map(BaseEntity::getId).collect(Collectors.toList()),
            "Disable dd Rejected category authorization on Switching");
    }

    //ACC-4715
    public void disableNotificationReceivedAfterXDay(int days) {

        logger.log(Level.INFO, "Disable notifications received after {0} day", days);
        List<String> notificationCodeList = null;
        switch (days) {
            case 2:
                notificationCodeList =
                        disableAccountingNotificationGetTemplateService.rejectedDdsSubmittedGetTemplate();
                break;
        }

        Pageable pageRequest = PageRequest.of(0, 100);
        Page<PushNotification> notifications = disablePushNotificationRepository
                .findActiveNotifications(notificationCodeList, pageRequest);

        while (notifications.hasContent()) {
            List<Long> notificationsId = notifications.getContent().stream()
                    .filter(n -> {
                        try {
                            logger.log(Level.INFO, "notification id " + n.getId()
                                    + " client id " + Long.valueOf(n.getRecepientId()));

                            HistorySelectQuery<PushNotification> query = new HistorySelectQuery<>(PushNotification.class);
                            query.filterBy("id", "=", n.getId());
                            query.filterBy("received", "=", true);
                            query.filterByChanged("received");
                            query.sortBy("lastModificationDate", false, true);
                            query.setLimit(1);

                            List<PushNotification> notificationList = query.execute();
                            if (notificationList.isEmpty()) return false;

                            return new DateTime().isAfter(new DateTime(
                                    notificationList.get(0).getLastModificationDate()).plusDays(days));
                        } catch (Exception ex) {
                            logger.log(Level.SEVERE, "Error while disabling notification: " + n.getId(), ex);
                        }
                        return false;
                    }).map(PushNotification::getId)
                    .collect(Collectors.toList());

            logger.log(Level.INFO, "Disable notifications after {0} day size {1}", new Object[]{days,
                    notificationsId.size()});
            localPushNotificationService.createDisableNotificationBGT(notificationsId,
                    "Disable notifications received after " + days +" days");

            pageRequest = pageRequest.next();
            notifications = disablePushNotificationRepository.findActiveNotifications(notificationCodeList, pageRequest);
        }
    }
    //ACC-4715
    public void disableNotificationAfterSmsSentXDay(int days) {
        logger.log(Level.INFO, "Disable notifications after sms sent {0} day", days);
        List<String> notificationCodeList = null;
        switch (days) {
            case 2:
                notificationCodeList =
                        disableAccountingNotificationGetTemplateService.rejectedDdsSubmittedGetTemplate();
                break;
        }

        Pageable pageRequest = PageRequest.of(0, 100);
        Page<PushNotification> notifications = disablePushNotificationRepository
                .findActiveNotifications(notificationCodeList, pageRequest);

        while (notifications.hasContent()) {
            List<Long> notificationsId = notifications.getContent().stream()
                    .filter(n -> {
                        try {
                            Long clientId = Long.valueOf(n.getRecepientId());
                            logger.log(Level.INFO, "notification id " + n.getId() + " client id " + clientId);

                            return localSmsRepository.existsSmsByTemplateNameAndCreationDateLessThanEqual(clientId,
                                    n.getType().getCode(), new DateTime().minusDays(days).toDate(), n.getCreationDate());
                        } catch (Exception ex) {
                            logger.log(Level.SEVERE, "Error while disabling notification: " + n.getId(), ex);
                        }
                        return false;
                    }).map(PushNotification::getId)
                    .collect(Collectors.toList());

            logger.log(Level.INFO, "Disable notifications after {0} day size {1}", new Object[]{days,
                    notificationsId.size()});
            localPushNotificationService.createDisableNotificationBGT(notificationsId,
                    "Disable notifications after sms sent " + days +" days");

            pageRequest = pageRequest.next();
            notifications = disablePushNotificationRepository.findActiveNotifications(notificationCodeList, pageRequest);
        }
    }

    // ACC-4591
    public void disableOnClientPaidOnlinePaymentViaCard(Long todoId) {
        logger.log(Level.INFO, "Disable notifications on online card payment status changed todo id: {0}", todoId);

        List<PushNotification> notifications = disablePushNotificationRepository
                .findActiveNotificationsByOwner(todoId, "ContractPaymentConfirmationToDo");

        localPushNotificationService.createDisableNotificationBGT(
                notifications.stream().map(BaseEntity::getId).collect(Collectors.toList()),
                "Disable notifications on online card payment status changed");
    }

    public void disableOnlineCardPaymentForApprovalOnPaymentStatusChanged() {
        // ACC-6168
        Page<ContractPaymentConfirmationToDo> l =
                contractPaymentConfirmationToDoRepository.findOnlineCardTodosAndPaymentStatusChangedToReceived(-1L, PageRequest.of(0, 200));

        while (!l.isEmpty()) {
            // ACC-6596
            disableOnlineCardPaymentForApprovalOnPaymentStatusChanged(l.stream()
                    .filter(t -> contractPaymentConfirmationToDoService.invalidToDo(t))
                    .collect(Collectors.toList()));

            l = contractPaymentConfirmationToDoRepository.findOnlineCardTodosAndPaymentStatusChangedToReceived(l.getContent().get(l.getContent().size() - 1).getId(), PageRequest.of(0, 200));
        }

        /*l = contractPaymentConfirmationToDoRepository.findOnlineCardTodosAndPaymentStatusChanged(-1L, PageRequest.of(0, 200));

        while (!l.isEmpty()) {

            disableOnlineCardPaymentForApprovalOnPaymentStatusChanged(l.getContent());

            l = contractPaymentConfirmationToDoRepository.findOnlineCardTodosAndPaymentStatusChanged(l.getContent().get(l.getContent().size() - 1).getId(), PageRequest.of(0, 200));
        }*/
    }

    public void disableOnlineCardPaymentForApprovalOnPaymentStatusChanged(List<ContractPaymentConfirmationToDo> l) {

        l.forEach(t -> {
            try {
                logger.log(Level.INFO, "todo id: {0}", t.getId());

                //ACC-7421
                if (t.isCreditCardOffer() && t.getRelatedEntityId() != null && t.getRelatedEntityType() != null) {
                    Setup.getApplicationContext().getBean(PushNotificationHelper.class)
                            .getByOwnerTypeAndId(t.getRelatedEntityType(), t.getRelatedEntityId())
                            .forEach(p -> Setup.getApplicationContext().getBean(MessagingService.class)
                                    .disablePushNotificationCtaByName(p, "credit_card_offer_cta"));
                    return;
                }

                switch (t.getSource()) {
                        case ERP:
                        case PAYMENT_REMINDER:
                            Setup.getApplicationContext()
                                    .getBean(UnpaidOnlineCreditCardPaymentService.class)
                                    .stopFlowAfterPaymentStatusChanged(t);
                            break;
                        case CLIENT_PAYING_VIA_Credit_Card:
                            Setup.getApplicationContext()
                                    .getBean(ClientPayingViaCreditCardService.class)
                                    .stopFlowAfterPaymentStatusChanged(t);
                            break;
                }

                t.setDisabled(true);
                contractPaymentConfirmationToDoRepository.silentSave(t);
            } catch (Exception ex) {
                logger.log(Level.SEVERE, "Error while disabling todo: " + t.getId(), ex);
            }
        });
    }

    public void disableOnNewDDAdded() {
        logger.log(Level.INFO, "Disable on new dd added");
        List<String> notificationCodeList =
            Arrays.asList(
                CcNotificationTemplateCode.CC_SIGNING_OFFER_PAYMENT_RECEIVED_THANKS_MESSAGE_NOTIFICATION.toString());

        Pageable pageRequest = PageRequest.of(0, 100);
        Page<PushNotification> notifications = disablePushNotificationRepository
            .findActiveNotifications(notificationCodeList, pageRequest);

        while (notifications.hasContent()) {
            List<Long> notificationsId = notifications.getContent().stream()
                .filter(n -> {
                    try {
                        if (n.getOwnerType() == null || !n.getOwnerType().equals("Contract") || n.getOwnerId() == null)
                            return false;

                        Contract contract = Setup.getRepository(ContractRepository.class).findOne(n.getOwnerId());
                        SelectQuery<DirectDebit> query = new SelectQuery<>(DirectDebit.class);
                        query.filterBy("contractPaymentTerm.contract.id", "=", contract.getId());
                        query.filterBy("creationDate", ">=", n.getCreationDate());
                        query.setLimit(1);
                        return !query.execute().isEmpty();
                    } catch (Exception ex) {
                        logger.log(Level.SEVERE, "Error while disabling notification: " + n.getId(), ex);
                    }
                    logger.log(Level.INFO, "notification id " + Long.valueOf(n.getId()) + " client id " + n.getRecepientId());
                    return false;
                }).map(PushNotification::getId)
                .collect(Collectors.toList());

            logger.log(Level.INFO, "Notifications : found " + notificationsId.size());
            localPushNotificationService.createDisableNotificationBGT(notificationsId, "Disable on new dd added");

            pageRequest = pageRequest.next();
            notifications = disablePushNotificationRepository.findActiveNotifications(notificationCodeList, pageRequest);
        }
    }

    // ACC-6668
    public void disableNotificationOnDdsGotCanceled() {
        logger.log(Level.INFO, "Disable notifications on DDs got canceled");

        Pageable pageRequest = PageRequest.of(0, 100);
        Page<PushNotification> notifications = disablePushNotificationRepository.findActiveNotificationsForRejectionFlow(pageRequest);

        while (notifications.hasContent()) {
            List<Long> notificationsId = notifications.getContent().stream().map(PushNotification::getId)
                    .collect(Collectors.toList());

            logger.info( "Disable notifications on DDs got canceled: " + notificationsId.size());
            localPushNotificationService.createDisableNotificationBGT(notificationsId, "Disable notifications on DDs got canceled");

            pageRequest = pageRequest.next();
            notifications = disablePushNotificationRepository.findActiveNotificationsForRejectionFlow(pageRequest);

        }
    }

    // ACC-6668
    public void disableNotificationClientSignViaPaperMode() {
        logger.log(Level.INFO, "Disable notifications on client signed via paper mode");

        Pageable pageRequest = PageRequest.of(0, 100);
        Page<PushNotification> notifications = disablePushNotificationRepository
                .findActiveNotificationsBySignLinkWithOpenGD(pageRequest);

        while (notifications.hasContent()) {
            List<Long> notificationsId = notifications.getContent().stream()
                    .map(PushNotification::getId)
                    .collect(Collectors.toList());

            logger.info( "Disable notifications on client signed via paper mode: " + notificationsId.size());
            localPushNotificationService.createDisableNotificationBGT(notificationsId, "Disable notifications on client signed via paper mode");

            pageRequest = pageRequest.next();
            notifications = disablePushNotificationRepository.findActiveNotificationsBySignLinkWithOpenGD(pageRequest);

        }
    }

    public void moveReceivePaymentNotificationsToInbox(Contract contract) {
        logger.info("contract id: " + contract.getId());
        List<PushNotification> l = disablePushNotificationRepository.findActiveNotificationsAndLocationHome(
                contract.getClient().getId().toString(),
                contract.getId(),
                disableAccountingNotificationGetTemplateService.getReceivePaymentNotificationCodes());

        localPushNotificationService.createMoveToInboxBgt(l.stream().map(PushNotification::getId).collect(Collectors.toList()),
                "When the notifications becomes disabled (manually or automatically) OR " +
                        "when another notification in the same flow is received OR " +
                        "after 24 hours");
    }
}