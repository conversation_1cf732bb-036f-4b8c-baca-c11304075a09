package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Category;
import com.magnamedia.entity.PurchaseOrder;
import com.magnamedia.entity.PurchasingToDo;
import com.magnamedia.entity.Supplier;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.module.type.PurchaseOrderStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <PERSON> (Feb 04, 2021)
 */
@Repository
public interface PurchaseOrderRepository extends BaseRepository<PurchaseOrder> {
    List<PurchaseOrder> findByPurchasingToDo(PurchasingToDo purchasingToDo);

    List<PurchaseOrder> findByPurchasingToDoAndStatus(PurchasingToDo purchasingToDo, PurchaseOrderStatus pendingPurchasing);

    PurchaseOrder findTop1ByExpenseRequestTodoOrderByCreationDateDesc(ExpenseRequestTodo expenseRequestTodo);

    @Query("SELECT t FROM PurchaseOrder t "
            + " WHERE (?3 IS NULL OR t.creationDate >= ?3) AND (?4 IS NULL OR t.creationDate <= ?4) "
            + " AND (?1 IS NULL OR t.supplier = ?1) " + " AND (?2 IS NULL OR t.purchasingToDo.category = ?2) " +
            " AND (?5 IS NULL OR t.status = ?5)")
    Page<PurchaseOrder> getOrderHistoryWithSearch(Supplier supplier, Category category, Date dayStart,
                                                  Date endDay, String status, Pageable pageable);
}
