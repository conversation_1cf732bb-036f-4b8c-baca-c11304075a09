package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Bucket;
import com.magnamedia.entity.projection.BucketCSVProjection;
import com.magnamedia.entity.projection.BucketProjection;
import com.magnamedia.extra.AccountingPage;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.extra.FilterItem;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.BucketType;
import com.magnamedia.repository.BucketRepository;
import com.magnamedia.scheduledjobs.DataCorrectionandIntegrityScheduledJob;
import com.magnamedia.service.AccountBalanceService;
import com.magnamedia.service.QueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RequestMapping("/buckets")
@RestController
public class BucketsController extends BaseRepositoryController<Bucket> {

    @Autowired
    private BucketRepository bucketRepository;
    @Autowired
    private ProjectionFactory projectionFactory;
    @Autowired
    private AccountBalanceService accountBalanceService;

    private DataCorrectionandIntegrityScheduledJob correctionandIntegrityScheduledJob;

    @Override
    public BaseRepository<Bucket> getRepository() {
        return bucketRepository;
    }

    @PreAuthorize("hasPermission('buckets','wrongIds')")
    @RequestMapping("/wrongIds")
    public ResponseEntity<String> GenerateWrongIds() {
        correctionandIntegrityScheduledJob = new DataCorrectionandIntegrityScheduledJob();
        return new ResponseEntity<>(correctionandIntegrityScheduledJob.getDebugInfo(), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('buckets','IncorrectData')")
    @RequestMapping("/IncorrectData")
    public ResponseEntity<String> getIncorrectData(
            @RequestParam(name = "email", required = false) String emails) {

        if (emails == null) emails = "";
        correctionandIntegrityScheduledJob = new DataCorrectionandIntegrityScheduledJob();

        return new ResponseEntity<>(
                correctionandIntegrityScheduledJob.InvalidDataEmail(false, EmailHelper.getMailRecipients(emails)),
                HttpStatus.OK);
    }

    //  </editor-fold>
    @Override
    public ResponseEntity<?> createEntity(Bucket b) {
        validation(b);
        return super.createEntity(b);
    }

    @NoPermission
    @GetMapping("/getRefillFrom")
    public ResponseEntity<?> getRefillFrom(@RequestParam String type, @RequestParam(required = false) Long currentBucket) {
        if (type == null ||
                type.equals(BucketType.MONEY_TRANSFER.toString()) ||
                type.equals(BucketType.BANK_ACCOUNT.toString())) {
            throw new RuntimeException("not supported type");
        }
        SelectQuery<Bucket> query = new SelectQuery(Bucket.class);
        query.leftJoin("refillerBucket");

        SelectFilter filter = new SelectFilter("bucketType", "=", BucketType.BANK_ACCOUNT);

        if (currentBucket != null) {
            query.filterBy(new SelectFilter("refillerBucket.id", "IS NULL", null)
                    .or("refillerBucket.id", "!=", currentBucket));
        }

        query.filterBy("id", "!=", currentBucket);

        if (type.equals(BucketType.CASH_BOX.toString())) {
            filter = filter.or("bucketType", "=", BucketType.CASH_BOX);
        }
        query.filterBy(filter);
        List<Bucket> result = query.execute();

        return new ResponseEntity(project(result, BucketProjectionIdName.class), HttpStatus.OK);

    }

    private void validation(Bucket b) {
        String code = b.getCode();
        String name = b.getName();

        if (b.getTransGuardService() != null && b.getTransGuardService()) {
            if (b.getBucketType() == null
                    || b.getBucketType() != BucketType.CASH_BOX)
                throw new RuntimeException("Transguard service only available for CashBox");
        }

        if (b.isTransGuard() && (b.getTransGuardCeilingAmount() == null ||
                b.getLevelAfterReplenishment() == null ||
                b.getTransGuardCeilingAmount() <= b.getLevelAfterReplenishment())) {
            throw new BusinessException("Error! Transguard Ceiling Amount should be more than Balance after Replenishment.");
        }

        if ((b.getAutoReplenishment() != null &&
                b.getAutoReplenishment()) &&
                (b.getBucketType() == null ||
                        (b.getBucketType() != BucketType.CREDIT_CARD &&
                                b.getBucketType() != BucketType.CASH_BOX))) {
            throw new RuntimeException("auto replenishment can be applied only on credit and cash buckets");
        }

        List<Bucket> holderOtherCashBuckets = null;
        if (b.getId() != null) {

            if (b.getRefillerBucket() != null && b.getRefillerBucket().getId() != null && b.getId().equals(b.getRefillerBucket().getId())) {
                throw new RuntimeException("can't set re-filler bucket same as original bucket");
            }

            if (code == null || code.isEmpty() || bucketRepository.existsBeforeByCode(b.getId(), code)) {
                throw new RuntimeException("code should be non empty / unique");
            }

            if (name == null || name.isEmpty() || bucketRepository.existsBeforeByName(b.getId(), name)) {
                throw new RuntimeException("name should be non empty / unique");
            }
            if (b.getBucketType() == BucketType.BANK_ACCOUNT && checkBucketTypeExisting(BucketType.BANK_ACCOUNT, b.getId())) {
                throw new RuntimeException("Bucket with type: " + BucketType.BANK_ACCOUNT.getLabel() + " already exist");
            }
            if (b.getBucketType() == BucketType.MONEY_TRANSFER && checkBucketTypeExisting(BucketType.MONEY_TRANSFER, b.getId())) {
                throw new RuntimeException("Bucket with type: " + BucketType.MONEY_TRANSFER.getLabel() + " already exist");
            }

            if (b.getHolder() != null && b.getBucketType().equals(BucketType.CASH_BOX)) {
                holderOtherCashBuckets = bucketRepository.findByHolderAndBucketTypeAndIdNotIn(b.getHolder(), b.getBucketType(), Arrays.asList(b.getId()));
            }
        } else {
            if (code == null || code.isEmpty() || bucketRepository.existsByCode(code)) {
                throw new RuntimeException("code should be non empty / unique");
            }

            if (name == null || name.isEmpty() || bucketRepository.existsByName(name)) {
                throw new RuntimeException("name should be non empty / unique");
            }
            if (b.getBucketType() == BucketType.BANK_ACCOUNT && checkBucketTypeExisting(BucketType.BANK_ACCOUNT, null)) {
                throw new RuntimeException("Bucket with type: " + BucketType.BANK_ACCOUNT.getLabel() + " already exist");
            }
            if (b.getBucketType() == BucketType.MONEY_TRANSFER && checkBucketTypeExisting(BucketType.MONEY_TRANSFER, null)) {
                throw new RuntimeException("Bucket with type: " + BucketType.MONEY_TRANSFER.getLabel() + " already exist");
            }

            if (b.getHolder() != null && b.getBucketType().equals(BucketType.CASH_BOX)) {
                holderOtherCashBuckets = bucketRepository.findByHolderAndBucketTypeAndIdNotIn(b.getHolder(), b.getBucketType(), Arrays.asList(-1L));
            }
        }

        if (holderOtherCashBuckets != null && holderOtherCashBuckets.size() > 0) {
            throw new RuntimeException("User can hold only one Cash Bucket");
        }
    }

    private boolean checkBucketTypeExisting(BucketType type, Long id) {
        SelectQuery<Bucket> query = new SelectQuery<>(Bucket.class);
        query.filterBy("bucketType", "=", type);
        List<Bucket> result = query.execute();
        if (id == null)
            return !result.isEmpty();
        else
            return !(result.isEmpty() ||
                    (result.size() == 1 && result.get(0).getId().longValue() == id.longValue()));
    }

    @Override
    protected ResponseEntity<?> updateEntity(Bucket b) {
        validation(b);
        return super.updateEntity(b);
    }

    @PreAuthorize("hasPermission('buckets','downloadattachment')")
    @GetMapping(path = "/page/searchBuckets/csv")
    public void downloadAttachment(
            @RequestParam(name = "isActive", required = false) Boolean isActive,
            @RequestParam(name = "limit", required = false) Integer limit,
            @RequestParam(name = "search", required = false) String queryString,
            @RequestParam(name = "type", required = false) String type,
            @RequestParam(name = "autoReplenishment", required = false) Boolean autoReplenishment,
            Sort sort,
            HttpServletResponse response) {

        if (limit == null) limit = 1000;

        SelectQuery<Bucket> query = new SelectQuery(Bucket.class);

        if (queryString != null && !queryString.isEmpty()) {
            query.filterBy(
                    new SelectFilter("name", "like", "%" + queryString + "%")
                            .or("code", "like", "%" + queryString + "%"));
        }
        if (type != null && !type.isEmpty()) {
            query.filterBy("bucketType", "=", BucketType.valueOf(type));
        }
        if (autoReplenishment != null) {
            query.filterBy("autoReplenishment", "=", autoReplenishment);
        }

        if (isActive != null) {
            query.filterBy("isActive", "=", isActive);
        }

        //Sorting
        if (sort != null) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("name", true, false);
        }

        InputStream is = getCSVStream(query, null, limit);

        try {
            if (is != null) {
                createDownloadResponse(response, "Buckets.csv", is);
            }
        }finally {
            StreamsUtil.closeStream(is);
        }
    }

    @PreAuthorize("hasPermission('buckets','downloadattachment')")
    @PostMapping(value = "/page/advancesearchbuckets/csv")
    public void downloadAttachmentAdvanceSearch(
            Sort sort,
            @RequestParam(name = "limit", required = false) Integer limit,
            @RequestBody List<FilterItem> filters,
            HttpServletResponse response) {

        if (limit == null) limit = 1000;
        SelectQuery<Bucket> query = new SelectQuery(Bucket.class);
        SelectFilter selectFilter = new SelectFilter();
        FilterItem balanceFilter = null;

        for (FilterItem filter : filters) {
            if (filter.getProperty().equalsIgnoreCase("balance")) {
                balanceFilter = filter;
                continue;
            }

            selectFilter = selectFilter.and(filter.getSelectFilter(Bucket.class));
        }
        query.filterBy(selectFilter);

        //Sorting
        if (sort != null) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("name", true, false);
        }

        InputStream is = getCSVStream(query, balanceFilter, limit);

        try {
            if (is != null) {
                createDownloadResponse(response, "Buckets.csv", is);
            }
        } finally {
            StreamsUtil.closeStream(is);
        }
    }

    private InputStream getCSVStream(
            SelectQuery<Bucket> query,
            FilterItem balanceFilter,
            Integer limit) {

        InputStream is = null;

        try {
            accountBalanceService.calculateCorrectBalanceBasedTransaction();
            //Date toDate = new DateTime().withTimeAtStartOfDay().toDate();

            String[] namesOrdered = {"name", "code", "initialBalance", "balance", "authorizedBalance", "holder", "bucketType", "autoReplenished", "enabled"};

            query.setLimit(limit);
            List<Bucket> buckets = query.execute(), filteredBuckets = new ArrayList();


            for (Bucket bucket : buckets) {
//                Double bucketBalance = accountBalanceService.getBucketBalanceAmount(bucket, toDate, null);
//                bucket.setBalance(bucketBalance);

                if (!validateBalanceAmount(bucket, balanceFilter)) continue;

                filteredBuckets.add(bucket);
            }

            is = generateCsv(filteredBuckets, BucketCSVProjection.class, namesOrdered);

            return is;
        } catch (IOException ex) {
            logger.log(Level.SEVERE, ex.getMessage(), ex);
            throw new RuntimeException(ex.getMessage());
        }
    }

    private boolean validateBalanceAmount(
            Bucket bucket,
            FilterItem amountFilter) {

        if (amountFilter == null) return true;
        Double amount = Double.parseDouble(amountFilter.getValue().toString());

        if (amountFilter.getOperation().equalsIgnoreCase("="))
            return bucket.getBalance().equals(amount);

        if (amountFilter.getOperation().equalsIgnoreCase("!="))
            return !bucket.getBalance().equals(amount);

        if (amountFilter.getOperation().equalsIgnoreCase(">"))
            return bucket.getBalance() > amount;

        if (amountFilter.getOperation().equalsIgnoreCase("<"))
            return bucket.getBalance() < amount;

        return true;
    }

    @PreAuthorize("hasPermission('buckets','searchBuckets')")
    @GetMapping(value = "/page/searchBuckets")
    public ResponseEntity<?> searchBuckets(
            @RequestParam(name = "isActive", required = false) Boolean isActive,
            @RequestParam(name = "search", required = false) String queryString,
            @RequestParam(name = "withStats", required = false, defaultValue = "false") Boolean withStats,
            @RequestParam(name = "type", required = false) String type,
            @RequestParam(name = "autoReplenishment", required = false) Boolean autoReplenishment,
            Pageable pageable) {

        SelectQuery<Bucket> query = new SelectQuery(Bucket.class);
        List<FilterItem> filters = new ArrayList<>();
        if (queryString != null && !queryString.isEmpty()) {
            filters.add(new FilterItem("name" , Collections.singletonList("code"), "like", queryString));
            query.filterBy(new SelectFilter("name", "like", "%" + queryString + "%")
                    .or("code", "like", "%" + queryString + "%"));

        }
        if (type != null && !type.isEmpty()) {
            filters.add(new FilterItem("bucketType", "=", type));
            query.filterBy("bucketType", "=", BucketType.valueOf(type));

        }
        if (autoReplenishment != null) {
            filters.add(new FilterItem("autoReplenishment", "=", autoReplenishment));
            query.filterBy("autoReplenishment", "=", autoReplenishment);
        }
        if (isActive != null) {
            filters.add(new FilterItem("isActive", "=", isActive));
            query.filterBy("isActive", "=", isActive);
        }

        //Sorting
        if (pageable.getSort() != null) {
            for (Sort.Order order : pageable.getSort()) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("name", true, false);
        }

        Page page = query.execute(pageable);

        if (!withStats) {
            List<BucketProjection> projectedList = ((List<Bucket>) page.getContent())
                    .stream()
                    .map(obj -> projectionFactory.createProjection(BucketProjection.class, obj))
                    .collect(Collectors.toList());

            return new ResponseEntity(new PageImpl(projectedList, pageable, page.getTotalElements()), HttpStatus.OK);
        }

        AccountingPage accountingPageResult = getBucketsSearchPage(page, pageable, filters);

        return new ResponseEntity(accountingPageResult, HttpStatus.OK);
    }

    // ACC-3282
    @PreAuthorize("hasPermission('buckets','getBalances')")
    @PostMapping(value = "/getBalances")
    public ResponseEntity getBucketBalance(
            @RequestParam("ids") List<Long> ids,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {

        if (date == null) date = new Date();

        List<Map> result = new ArrayList();
        for (Long bucketId : ids) {
            Bucket bucket = getRepository().findOne(bucketId);

            Map bucketBalance = new HashMap();
            bucketBalance.put("id", bucketId);
            bucketBalance.put("balance", accountBalanceService.getBucketBalanceAmount(
                    bucket, date, null));

            result.add(bucketBalance);
        }

        return ResponseEntity.ok(result);
    }

    @PreAuthorize("hasPermission('buckets','advancesearchbuckets')")
    @PostMapping(value = "/page/advancesearchbuckets")
    public ResponseEntity<?> advanceSearchBuckets(
            Pageable pageable,
            Sort sort,
            @RequestBody List<FilterItem> filters) {

        SelectQuery<Bucket> query = new SelectQuery(Bucket.class);

        SelectFilter selectFilter = new SelectFilter();
        for (FilterItem filter : filters) {
            selectFilter = selectFilter.and(filter.getSelectFilter(Bucket.class));
        }
        query.filterBy(selectFilter);

        //Sorting
        if (sort != null) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("name", true, false);
        }

        Page mainPage = query.execute(pageable);
        return new ResponseEntity(getBucketsSearchPage(mainPage, pageable, filters), HttpStatus.OK);
    }

    private AccountingPage getBucketsSearchPage(Page page, Pageable pageable, List<FilterItem> filters) {

        Double balanceSum = Setup.getApplicationContext()
                .getBean(QueryService.class)
                .getTheSumBalanceForAllBuckets(filters);

        List<BucketProjection> projectedList = ((List<Bucket>) page.getContent())
                .stream()
                .map(obj -> projectionFactory.createProjection(BucketProjection.class, obj))
                .collect(Collectors.toList());

        AccountingPage accountingPageResult = new AccountingPage(projectedList, pageable,
                page.getTotalElements(), balanceSum);
        return accountingPageResult;
    }

    public interface BucketProjectionIdName {
        Long getId();
        String getName();
        BucketType getBucketType();
    }

    @PreAuthorize("hasPermission('buckets','searchBuckets')")
    @RequestMapping(value = "/getLastCalculateBucketAmountJobRun", method = RequestMethod.GET)
    public ResponseEntity<?> getLastCalculateBucketAmountJobRun() {

        String d = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_LAST_CALCULATE_BUCKETS_BALANCE);

        return new ResponseEntity<>(!d.isEmpty() ? ("Latest Balance Calculation was on: "
                + d) : "" , HttpStatus.OK);
    }
}
