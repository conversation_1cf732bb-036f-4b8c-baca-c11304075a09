package com.magnamedia.service;

import com.magnamedia.controller.ContractPaymentTermController;
import com.magnamedia.controller.DirectDebitCancelationToDoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.Sms;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.core.type.SmsReceiverType;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.DirectDebitCancelationToDo;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.extra.DDUtils;
import com.magnamedia.extra.ExceptionUtils;
import com.magnamedia.extra.PaymentHelper;
import com.magnamedia.extra.Utils;
import com.magnamedia.helper.ContractPaymentTermHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import org.apache.commons.lang3.BooleanUtils;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Masod <<EMAIL>>, Abdelrahman.bazrto <<EMAIL>>
 *         Created on Aug 23, 2020
 *         Jirra ACC-2418
 */

@Service
public class SwitchingBankAccountService {
    protected static final Logger logger = Logger.getLogger(SwitchingBankAccountService.class.getName());
    private static final String prefix = "MMM ";

    @Autowired
    private DirectDebitRepository ddRepo;

    @Autowired
    private ContractRepository contractRepo;

    @Autowired
    private DirectDebitCancelationToDoRepository ddCancelationToDoRepo;

    @Autowired
    private ContractPaymentTermController cptCtrl;

    @Autowired
    private ContractPaymentTermServiceNew contractPaymentTermServiceNew;

    @Autowired
    private ContractPaymentTermHelper contractPaymentTermHelper;

    @Autowired
    private AttachementRepository attachmentRepo;

    @Autowired
    private AccountingEntityPropertyRepository accountingEntityPropertyRepository;

    @Autowired
    private Utils utils;

    @Autowired
    private ContractPaymentTermRepository cptRep;

    @Autowired
    private InterModuleConnector moduleConnector;

    @Autowired
    private PaymentRepository paymentRepository;

    @Autowired
    private DirectDebitRejectionToDoRepository directDebitRejectionToDoRepository;

    @Autowired
    private DirectDebitCancellationService directDebitCancellationService;
    
    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;

    @Autowired
    private DirectDebitSignatureService directDebitSignatureService;

    @Autowired
    private PaymentService paymentService;

    public void handleSwitchingBankAccount(DirectDebit dd) {
        ContractPaymentTerm oldCPT = dd.getContractPaymentTerm();

        ContractPaymentTerm newCPT = oldCPT.getContract().getActiveContractPaymentTerm();

        if (ddRepo.existsByContractPaymentTermAndImageForDD(newCPT, dd)) {
            logger.info("dd id " + dd.getId() + " already cloned to the new cpt");
            return;
        }

        generateNewDDsOnSwitching(newCPT, Arrays.asList(dd), null);
        signDDsAfterSwitching(newCPT, oldCPT);
    }
    
    public boolean isClientSwitchingBankAccount(Payment payment) {
        if (payment.getDirectDebit() == null || payment.getDirectDebit().getId() == null)
            return false;

        DirectDebit dd = ddRepo.findOne(payment.getDirectDebit().getId());
        if(dd==null)
            return false;

        ContractPaymentTerm oldCPT = dd.getContractPaymentTerm();

        if (oldCPT.isActive())
            return false;

        return oldCPT.getSwitchedBankAccount() != null && oldCPT.getSwitchedBankAccount();
    }

    public boolean isClientSwitchingBankAccount(DirectDebit directDebit) {
        if (directDebit == null)
            return false;

        ContractPaymentTerm oldCPT = directDebit.getContractPaymentTerm();

        if (oldCPT.isActive())
            return false;

        return oldCPT.getSwitchedBankAccount() != null && oldCPT.getSwitchedBankAccount();
    }

    public boolean isSwitchingBankAccountDDsCoverPayment(Payment payment) {
        return getPaymentDDImage(payment) != null;
    }

    public boolean isSwitchingBankAccountDDsCoverPaymentAndApproved(Payment payment) {
        DirectDebit ddImage = getPaymentDDImage(payment);
        return ddImage != null && (ddImage.getStatus().equals(DirectDebitStatus.CONFIRMED) || ddImage.getMStatus().equals(DirectDebitStatus.CONFIRMED));
    }

    public boolean isSwitchingBankAccountDDAConfirmed(DirectDebit dd, DateTime dateTime) {
        ContractPaymentTerm oldCPT = dd.getContractPaymentTerm();
        ContractPaymentTerm newCPT = oldCPT.getContract().getActiveContractPaymentTerm();

        List<DirectDebit> newDDs = ddRepo.findByContractPaymentTerm(newCPT);

        for (DirectDebit ddImage : newDDs) {
            // if dd covers the payment
            if (ddImage.getCategory().equals(DirectDebitCategory.A) &&
                    DDUtils.isDDConfirmed(ddImage) && DDUtils.doesDDCoverDate(ddImage, dateTime.toDate())) {
                return true;
            }
        }

        return false;
    }

    public void swapPaymentDirectDebit(Payment payment) {

        DirectDebit oldDirectDebit = payment.getDirectDebit();

        List<DirectDebit> newDirectDebits = ddRepo.findByImageForDD(oldDirectDebit);

        if (newDirectDebits == null || newDirectDebits.size() == 0)
            return;

        DirectDebit newDirectDebit = newDirectDebits.get(0);

        List<Payment> newDirectDebitBouncedPayments = paymentRepository.findByDirectDebitIdAndStatus(newDirectDebit.getId(), PaymentStatus.BOUNCED);

        if ((payment.getReplaced() == null || !payment.getReplaced())
                && !payment.getSentToBankByMDD()) {

            Long id = payment.getId();
            Long directDebitId = payment.getDirectDebitId();

            for (Payment newBouncedPayment : newDirectDebitBouncedPayments) {
                if (newBouncedPayment.getDateOfPayment().getMonth() == payment.getDateOfPayment().getMonth()
                        && newBouncedPayment.getAmountOfPayment().equals(payment.getAmountOfPayment())
                        && newBouncedPayment.getMethodOfPayment() == PaymentMethod.DIRECT_DEBIT
                        && payment.getMethodOfPayment() == PaymentMethod.DIRECT_DEBIT) {

                    BeanUtils.copyProperties(newBouncedPayment, payment);
                    payment.setId(id);
                    payment.setDirectDebitId(directDebitId);
                    paymentService.forceUpdatePayment(payment);

                    newBouncedPayment.setStatus(PaymentStatus.DELETED);
                    paymentService.forceUpdatePayment(newBouncedPayment);
                }
            }
        }
    }

    public DirectDebit getPaymentDDImage(Payment payment) {
        DirectDebit paymentDD = ddRepo.findOne(payment.getDirectDebit().getId());
        List<DirectDebit> ddImages = getDDImages(paymentDD);

        for (DirectDebit ddImage : ddImages) {
            // if dd covers the payment
            if (paymentDD.getCategory().equals(DirectDebitCategory.A) && ddImage.getCategory().equals(DirectDebitCategory.A)) {
                return ddImage;
            }

            if (DDUtils.doesDDCoverDate(ddImage, payment.getDateOfPayment()))
                return ddImage;
        }

        return null;
    }

    public Payment getPaymentImage(Payment payment) {
        DirectDebit paymentDD = ddRepo.findOne(payment.getDirectDebit().getId());
        DirectDebit imageDD = getPaymentDDImage(payment);

        if (imageDD == null) return null;

        if (paymentDD.getCategory().equals(DirectDebitCategory.A) && imageDD.getCategory().equals(DirectDebitCategory.A)) {
            return PaymentHelper.getPaymentOfDDA(imageDD.getId());
        }

        SelectQuery<Payment> paymentImagesQuery = new SelectQuery(Payment.class);
        paymentImagesQuery.filterBy("directDebitId", "=", imageDD.getId());
        paymentImagesQuery.filterBy("methodOfPayment", "=", payment.getMethodOfPayment());
        paymentImagesQuery.filterBy("typeOfPayment.id", "=", payment.getTypeOfPayment().getId());
        paymentImagesQuery.filterBy("amountOfPayment", "=", payment.getAmountOfPayment());

        List<Payment> paymentImages = paymentImagesQuery.execute();

        if (paymentImages != null && !paymentImages.isEmpty()) {
            for (Payment paymentImage : paymentImages) {
                if (PaymentHelper.doesPaymentCoverDate(paymentImage, payment.getDateOfPayment()))
                    return paymentImage;
            }
        }

        return null;
    }

    public List<DirectDebit> getDDImages(DirectDebit directDebit) {
        SelectQuery<DirectDebit> ddImagesQuery = new SelectQuery(DirectDebit.class);
        ddImagesQuery.filterBy("imageForDD.id", "=", directDebit.getId());
        ddImagesQuery.sortBy("creationDate", false);

        return ddImagesQuery.execute();
    }

    public void cancelDDOrShowHiddenCancellationToDos(DirectDebit directDebit) {
        logger.log(Level.SEVERE, prefix + "show or Create ToDos.");
        if (directDebit == null || directDebit.getDirectDebitFiles() == null) return;

        List<DirectDebitFile> ddfs = directDebitFileRepository.findAll(directDebit.getDirectDebitFiles()
                .stream().map(d -> d.getId()).collect(Collectors.toList()));
        
        for (DirectDebitFile ddf : ddfs) {
            List<DirectDebitCancelationToDo> alreadyExistsDDCancelationToDos = ddCancelationToDoRepo.findByDirectDebitFileAndCompletedFalseAndStoppedFalse(ddf);
            
            if (alreadyExistsDDCancelationToDos != null && !alreadyExistsDDCancelationToDos.isEmpty()) {
                logger.log(Level.SEVERE, "a cancellation to do for this direct debit file#" + ddf.getId() + " already exists, -> show it");
                showHiddenCancellationToDos(alreadyExistsDDCancelationToDos);
            } else {
                logger.log(Level.SEVERE, "cancellation to do for this direct debit file#" + ddf.getId() + " not found, -> cancel it");
                directDebitCancellationService.cancelDDf(ddf.getId(), DirectDebitCancellationToDoReason.SWITCHING_BANK_ACCOUNT);
            }
        }
    }

    public void showHiddenCancellationToDos(DirectDebitFile directDebitFile) {
        SelectQuery<DirectDebitCancelationToDo> cancelToDosQuery = new SelectQuery(DirectDebitCancelationToDo.class);
        cancelToDosQuery.filterBy("directDebitFile.id", "=", directDebitFile.getId());
        cancelToDosQuery.filterBy("hidden", "=", Boolean.TRUE);
        List<DirectDebitCancelationToDo> cancelToDos = cancelToDosQuery.execute();

        showHiddenCancellationToDos(cancelToDos);
    }

    public void showHiddenCancellationToDos(List<DirectDebitCancelationToDo> cancelToDos) {
        logger.log(Level.SEVERE, prefix + "show hidden ToDos.");
        for (DirectDebitCancelationToDo ddCancellationTodo : cancelToDos) {
            logger.log(Level.SEVERE, prefix + "show ToDo with ID: " + ddCancellationTodo.getId());
            ddCancellationTodo.setHidden(false);
            ddCancelationToDoRepo.save(ddCancellationTodo);
        }
    }

    public DirectDebit addNewOneTimeDD(
            Long contractId, Date ddStartDate, Double amount,
            DirectDebit imageFor, Payment bouncedPayment) {

        Contract contract = contractRepo.findOne(contractId);

        DirectDebit oneTimeDD = contractPaymentTermServiceNew.addNewDD(
                contract, ddStartDate, ddStartDate,
                null, null, null,
                Math.floor(amount), null, DirectDebitType.ONE_TIME,
                Setup.getItem("TypeOfPayment", "monthly_payment"),
                true, bouncedPayment, true, false,
                true, null, true);

        if (imageFor != null) {
            oneTimeDD.setImageForDD(imageFor);
            ddRepo.save(oneTimeDD);
        }
        return oneTimeDD;
    }

    public DirectDebitRejectionToDo cloneRejectionTodo(DirectDebit dd, DirectDebitCategory ddCategory, boolean withSignatures) {
        DirectDebitRejectionToDo directDebitRejectionToDo = dd.getDirectDebitRejectionToDo();

        if (directDebitRejectionToDo == null) return null;
        if (directDebitRejectionToDo.isCompleted() || directDebitRejectionToDo.isStopped()) return null;

        directDebitRejectionToDo.setStopped(true);
        directDebitRejectionToDo.setStopReason("due switching account");
        directDebitRejectionToDoRepository.save(directDebitRejectionToDo);

//        cancelOldDDsNotApproved(directDebitRejectionToDo);

        DirectDebitRejectionToDoType stepName = null;
        if (ddCategory.equals(DirectDebitCategory.A)) {
            stepName = withSignatures ? DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE :
                    DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE;

        } else if (ddCategory.equals(DirectDebitCategory.B)) {
            if (dd.getStatus() == DirectDebitStatus.CONFIRMED || dd.getMStatus() == DirectDebitStatus.CONFIRMED) return null;

            stepName = withSignatures ? DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B_CASE_D :
                    DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_CASE_D;
        }

        if (stepName == null) return null;

        DirectDebitRejectionToDo cloned = dd.getDirectDebitRejectionToDo().clone(stepName, null);
        directDebitRejectionToDoRepository.save(cloned);

        return cloned;
    }

    private void cancelOldDDsNotApproved(DirectDebitRejectionToDo directDebitRejectionToDo) {
        DirectDebitCancelationToDoController ddCancellationController = Setup.getApplicationContext().getBean(DirectDebitCancelationToDoController.class);
        for (DirectDebit directDebit : directDebitRejectionToDo.getDirectDebits()) {
            if (directDebit.getCategory() == DirectDebitCategory.A) {
                directDebitCancellationService.cancelWholeDD(directDebit, DirectDebitCancellationToDoReason.SWITCHING_BANK_ACCOUNT);
            } else if (directDebit.getCategory() == DirectDebitCategory.B) {
                if (directDebit.getStatus() != DirectDebitStatus.CONFIRMED && directDebit.getMStatus() != DirectDebitStatus.CONFIRMED) {
                    directDebitCancellationService.cancelWholeDD(directDebit, DirectDebitCancellationToDoReason.SWITCHING_BANK_ACCOUNT);
                } else if (directDebit.getMStatus() == DirectDebitStatus.CONFIRMED) {
                    List<DirectDebitFile> manuals = directDebit.getDirectDebitFiles()
                            .stream()
                            .filter(ddf -> ddf.getDdMethod() == DirectDebitMethod.MANUAL)
                            .collect(Collectors.toList());

                    manuals.forEach(ddf -> {
                        if (ddf.getDdStatus() != DirectDebitStatus.CONFIRMED)
                            directDebitCancellationService.cancelDDf(ddf, DirectDebitCancellationToDoReason.SWITCHING_BANK_ACCOUNT);
                    });

                } else if (directDebit.getStatus() == DirectDebitStatus.CONFIRMED) {
                    List<DirectDebitFile> autos = directDebit.getDirectDebitFiles()
                            .stream()
                            .filter(ddf -> ddf.getDdMethod() == DirectDebitMethod.AUTOMATIC)
                            .collect(Collectors.toList());

                    autos.forEach(ddf -> {
                        if (ddf.getDdStatus() != DirectDebitStatus.CONFIRMED)
                            directDebitCancellationService.cancelDDf(ddf, DirectDebitCancellationToDoReason.SWITCHING_BANK_ACCOUNT);
                    });
                }
            }
        }
    }

    public void markNewDDFsAsForBouncingPayment(List<DirectDebit> directDebits) {
        if (directDebits == null || directDebits.isEmpty()) {
            logger.log(Level.INFO, "markDDFsAsForBouncingPayment, DDs Null or Empty: " + directDebits.size());
            return;
        }
        logger.log(Level.INFO, "markDDFsAsForBouncingPayment, DDs Count: " + directDebits.size());
        moduleConnector.postJsonAsync("accounting/directDebit/mark-manual-ddfs/for-bouncing", directDebits.stream().map(dd -> dd.getId()).collect(Collectors.toList()));
    }

    public Date getSwitchingBankAccountDDStartDate(DirectDebit oldDD, DirectDebitCategory toGenerateDDCategory, DateTime switchingDate) {
        DateTime switchingFirstNextMonth = switchingDate.plusMonths(1).withDayOfMonth(1).withTimeAtStartOfDay();
        if (oldDD.getStatus().equals(DirectDebitStatus.CONFIRMED) || oldDD.getMStatus().equals(DirectDebitStatus.CONFIRMED)) {
            if (oldDD.getCategory().equals(DirectDebitCategory.A)) {
                return oldDD.getStartDate();
            } else if (oldDD.getCategory().equals(DirectDebitCategory.B)) {
                if (toGenerateDDCategory.equals(DirectDebitCategory.A)) {
                    return switchingFirstNextMonth.toDate();
                } else if (toGenerateDDCategory.equals(DirectDebitCategory.B)) {
                    return oldDD.getStartDate().before(switchingDate.plusMonths(2).withDayOfMonth(1).withTimeAtStartOfDay().toDate()) ? switchingFirstNextMonth.plusMonths(1).toDate() : oldDD.getStartDate();
                }
            }
        }

        return oldDD.getStartDate();
    }

    public Date getSwitchingBankAccountDDEndDate(DirectDebit oldDD, Date newDDStartDate) {
        if (oldDD.getCategory().equals(DirectDebitCategory.A)) {
            int oneTimeDDMonthDuration = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_ONE_TIME_DD_MONTH_DURATION));
            return new DateTime(newDDStartDate).plusMonths(oneTimeDDMonthDuration).toDate();
        }

        return oldDD.getExpiryDate();
    }

    public List<DirectDebit> generateNewDDsOnSwitching(ContractPaymentTerm newCPT, List<DirectDebit> oldDDs, List<Attachment> signatures) {
        int oneTimeDDMonthDuration = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_ONE_TIME_DD_MONTH_DURATION));
        long ddBankInfoGroup = new DateTime().getMillis();

        List<DirectDebit> newDDs = new ArrayList<>();
        for (DirectDebit oldDD : oldDDs) {
            if (ddRepo.existsByContractPaymentTermAndImageForDD(newCPT, oldDD)) {
                logger.info("dd id " + oldDD.getId() + " already cloned to the new cpt");
                continue;
            }

            if (DDUtils.isDDConfirmed(oldDD) && oldDD.getCategory().equals(DirectDebitCategory.B) &&
                    oldDD.getStartDate().before(new DateTime().plusMonths(2).withDayOfMonth(1).withTimeAtStartOfDay().toDate())) {

                Payment switchMonthPayment = PaymentHelper.getPaymentOfDD(oldDD.getId(), new Date());
                if (switchMonthPayment != null &&
                        !switchMonthPayment.getSentToBankByMDD() &&
                        switchMonthPayment.getStatus().equals(PaymentStatus.BOUNCED) &&
                        !switchMonthPayment.getReplaced()) {

                    Date ddStartDate = switchMonthPayment.getDateOfPayment();
                    Date ddExpiryDate = new DateTime(ddStartDate).plusMonths(oneTimeDDMonthDuration).toDate();

                    newDDs.add(generateNewDDOnSwitching(newCPT, oldDD, ddStartDate, ddExpiryDate,
                            DirectDebitType.ONE_TIME, DirectDebitCategory.A, signatures, ddBankInfoGroup));
                }

                logger.info("create new dda for dd id: " + oldDD.getId());
                Date ddStartDate = this.getSwitchingBankAccountDDStartDate(oldDD, DirectDebitCategory.A, DateTime.now());
                Date ddExpiryDate = new DateTime(ddStartDate).plusMonths(oneTimeDDMonthDuration).toDate();

                DirectDebit newDD = this.generateNewDDOnSwitching(newCPT, oldDD, ddStartDate, ddExpiryDate,
                        DirectDebitType.ONE_TIME, DirectDebitCategory.A, signatures, ddBankInfoGroup);

                newDDs.add(newDD);
            }

            Date ddStartDate = this.getSwitchingBankAccountDDStartDate(oldDD, oldDD.getCategory(), DateTime.now());
            Date ddExpiryDate = this.getSwitchingBankAccountDDEndDate(oldDD, ddStartDate);

            logger.info("Old dd id: " + oldDD.getId() + "; category: " + oldDD.getCategory());
            DirectDebit newDD = this.generateNewDDOnSwitching(newCPT, oldDD, ddStartDate, ddExpiryDate,
                    oldDD.getType(), oldDD.getCategory(), signatures, ddBankInfoGroup);
            
            newDDs.add(newDD);
        }

        DDUtils.setDirectDebitsDescription(newCPT, newDDs);

        List<DirectDebit> persistedDDs = new ArrayList();
        for (DirectDebit newDD : newDDs) {
            DirectDebit oldDD = ddRepo.findOne(newDD.getImageForDD().getId());
            logger.info("start move CP from Old dd id: " + oldDD.getId());

            Map<String, List<Payment>> bouncedPayment = !oldDD.getCategory().equals(DirectDebitCategory.A) ? null :
                    paymentRepository.findByDirectDebitIdAndStatus(oldDD.getId(), PaymentStatus.BOUNCED)
                            .stream()
                            .collect(Collectors.groupingBy(p -> p.getTypeOfPayment().getCode()));
            
            List<ContractPayment> contractPayments = new ArrayList();

            for (ContractPayment cp : oldDD.getContractPayments()) {
                ContractPayment newPayment;
                boolean movePayment = false;

                if(bouncedPayment != null && !bouncedPayment.isEmpty()) {
                    if(!bouncedPayment.containsKey(cp.getPaymentType().getCode())) continue;

                    newPayment = new ContractPayment();
                    BeanUtils.copyProperties(cp, newPayment);
                    newPayment.setId(null);
                    newPayment.setDate(newDD.getStartDate());
                    newPayment.setReplaceOf(bouncedPayment.get(cp.getPaymentType().getCode()).get(0));
                } else {
                    newPayment = cp;

                    DateTime ddStartDate = new DateTime(newDD.getStartDate()).dayOfMonth().withMinimumValue().withTimeAtStartOfDay();
                    DateTime ddExpiryDate = new DateTime(newDD.getExpiryDate()).dayOfMonth().withMaximumValue().withTimeAtStartOfDay();
                    DateTime cpDate = new DateTime(cp.getDate());

                    if (newDD.getCategory().equals(DirectDebitCategory.A) && oldDD.getCategory().equals(DirectDebitCategory.A)) {
                        movePayment = true;
                        newPayment.setDate(newDD.getStartDate());
                
                    } else if ((cpDate.isAfter(ddStartDate) || cpDate.equals(ddStartDate)) &&
                            (cpDate.isBefore(ddExpiryDate) || cpDate.equals(ddExpiryDate))) {

                        movePayment = true;
                    }
                }

                if (movePayment || (bouncedPayment != null &&
                        bouncedPayment.containsKey(cp.getPaymentType().getCode()))) {
                    newPayment.setDirectDebit(newDD);
                    newPayment.setContractPaymentTerm(newCPT);
                    newPayment.setOneTime(newDD.getCategory().equals(DirectDebitCategory.A));
                    newPayment.setDescription(Setup.getApplicationContext().getBean(ContractPaymentTermHelper.class)
                            .getPaymentDescriptionForSigningScreen(newPayment));
                    logger.info("Old dd id: " + oldDD.getId() +
                            "; bouncedPayment id: " + (bouncedPayment != null &&
                            bouncedPayment.containsKey(cp.getPaymentType().getCode()) ?
                            bouncedPayment.get(cp.getPaymentType().getCode()).get(0).getId() : "Null") +
                            "; move old CP: " + cp.getId());
                    contractPayments.add(newPayment);
                }
            }

            if (!contractPayments.isEmpty()) {
                logger.info("contractPayments size: " + contractPayments.size());

                newDD.setPayments(contractPayments);
                newDD.setContractPayments(contractPayments);

                persistedDDs.add(ddRepo.saveAndFlush(newDD));
            }
        }

        return persistedDDs;
    }

    public DirectDebit generateNewDDOnSwitching(
            ContractPaymentTerm cpt, DirectDebit oldDD,
            Date ddStartDate, Date ddExpiryDate,
            DirectDebitType type, DirectDebitCategory category,
            List<Attachment> signatures, Long ddBankInfoGroup) {

        DirectDebit newDD = Setup.getApplicationContext().getBean(DirectDebitService.class)
                .createDirectDebit(true, DirectDebitStatus.IN_COMPLETE, DirectDebitStatus.IN_COMPLETE,
                        oldDD.getAmount(), cpt, ddStartDate, ddExpiryDate, type, category,
                        oldDD.getAdditionalDiscount(),false, null, null, true);

        DirectDebitRejectionToDo oldDDRejectionToDo = cloneRejectionTodo(
                oldDD, category, signatures != null && !signatures.isEmpty());
        
        newDD.setDirectDebitRejectionToDo(oldDDRejectionToDo);
        newDD.setRejectCategory(oldDD.getRejectCategory());
        newDD.setBouncingRejectCategory(oldDD.getBouncingRejectCategory());
        newDD.setDdBankInfoGroup(ddBankInfoGroup);
        newDD.setImageForDD(oldDD);
        newDD.setDdcId(oldDD.getDdcId());
        newDD.setHidden(oldDD.isHidden());
        
        // Don't move Bouncing Rejection
        // newDD.setDirectDebitBouncingRejectionToDo(oldDD.getDirectDebitBouncingRejectionToDo());
        
        return newDD;
    }

    public void signDDsAfterSwitching(ContractPaymentTerm newCPT, ContractPaymentTerm oldCPT) {
        Contract contract = newCPT.getContract();

        Map<String, Object> signatureType = directDebitSignatureService.getLastSignatureType(oldCPT, true, false);

        List<Attachment> signatures = directDebitSignatureService
                .getSignatureAttachmentsOnly((List<DirectDebitSignature>) signatureType.get("currentSignatures"));

        if (signatures != null && !signatures.isEmpty()) {
            Attachment eidPhoto = newCPT.getAttachment(ContractPaymentTermController.FILE_TAG_BANK_INFO_EID);
            Attachment ibanPhoto = newCPT.getAttachment(ContractPaymentTermController.FILE_TAG_BANK_INFO_IBAN);
            Attachment accountPhoto = newCPT.getAttachment(ContractPaymentTermController.FILE_TAG_BANK_INFO_ACCOUNT_NAME);
            List<Attachment> pendingOcr = newCPT.getAttachments().stream()
                    .filter(a -> a.getTag().equals(ContractPaymentTermController.FILE_TAG_BANK_INFO_PENDING_OCR))
                    .collect(Collectors.toList());

            Boolean hasAccountRejection  = Setup.getApplicationContext().getBean(DirectDebitService.class)
                    .hasRejectionForAccountName(contract.getClient());

            if ((eidPhoto != null && ibanPhoto != null && (!hasAccountRejection || accountPhoto != null)) ||
                    !pendingOcr.isEmpty()) {

                Attachment a1 = eidPhoto;
                Attachment a2 = ibanPhoto;

                if(a1 == null && pendingOcr.size() > 0) a1 = pendingOcr.get(0);
                if(a2 == null && pendingOcr.size() > 1) a2 = pendingOcr.get(1);

                try {
                    contractPaymentTermServiceNew.signDDByClient(
                            contract.getUuid(), contract,
                            a1, a2, accountPhoto,
                            true, true, true,
                            null, null, null, signatures, false,
                            true, null,
                            null, null, null);
                } catch (Exception e) {
                    logger.severe(ExceptionUtils.getStackTrace(e));
                    throw new RuntimeException(e);
                }
            } else {
                logger.info("Bank Info is not completed");
            }
        } else {
            logger.info("No Available Signatures");
        }
    }

    public void cancelOldDDs(List<DirectDebit> dds) {
        for (DirectDebit dd : dds) {
            if (!DDUtils.isDDConfirmed(dd) && !DDUtils.isDDPendingSent(dd)) {
                directDebitCancellationService.cancelWholeDD(dd, DirectDebitCancellationToDoReason.SWITCHING_BANK_ACCOUNT);
            }
        }
    }

    // being called from BGT
    @Transactional
    public void doSwitchBankInfo(
            String contractUUID, Long bouncedPaymentId,
            Long eidPhotoId, Long ibanPhotoId, Long accountPhotoId,
            List<String> signaturesIDs, Boolean pendingOcr) throws Exception {
        
        if (signaturesIDs == null || signaturesIDs.isEmpty()) throw new RuntimeException("Signatures must be provided");

        Contract contract = contractRepo.findByUuid(contractUUID);

        Payment bouncedPayment = bouncedPaymentId != null ? paymentRepository.findOne(bouncedPaymentId) : null;

        Attachment eidPhoto = eidPhotoId != null ? attachmentRepo.findOne(eidPhotoId) : null;
        Attachment ibanPhoto = ibanPhotoId != null ? attachmentRepo.findOne(ibanPhotoId) : null;
        Attachment accountPhoto = accountPhotoId != null ? attachmentRepo.findOne(accountPhotoId) : null;

        List<Attachment> signatures = signaturesIDs.stream()
                .map(signaturesID -> attachmentRepo.findOne(Long.parseLong(signaturesID)))
                .collect(Collectors.toList());

        if (signatures == null || signatures.isEmpty()) throw new RuntimeException("Couldn't find signatures");

        ContractPaymentTerm currentCPT = null;
        Map termResult = contractPaymentTermHelper.getActiveContractPaymentTermByContract(contract);
        if (termResult != null) currentCPT = (ContractPaymentTerm) termResult.get("contractPaymentTerm");
        if (currentCPT == null) throw new RuntimeException("No Active Contract Payment Term");

        List<DirectDebitStatus> validStatuses = Arrays.asList(
                DirectDebitStatus.PENDING, DirectDebitStatus.PENDING_DATA_ENTRY, 
                DirectDebitStatus.CONFIRMED, DirectDebitStatus.IN_COMPLETE);

        List<DirectDebit> oldDDs = ddRepo.findByContractPaymentTermAndStatusIn(
                currentCPT, validStatuses).stream()
                .filter(dd -> !DDUtils.isDDPendingSent(dd))
                .sorted(Comparator.comparing(DirectDebit::getStartDate)).
                        collect(Collectors.toList());

        //Replace the old CPT
        ContractPaymentTerm newCPT = new ContractPaymentTerm();
        BeanUtils.copyProperties(currentCPT, newCPT);

        currentCPT.setContractPaymentTypes(new ArrayList());
        
        newCPT.setId(null);
        newCPT.setReason(ContractPaymentTermReason.SWITCHING_BANK_ACCOUNT);
        newCPT.setWeeklyAmount(currentCPT.getWeeklyAmount());
        newCPT.setDailyRateAmount(currentCPT.getDailyRateAmount());
        newCPT.setIbanNumber(null);
        newCPT.setEid(null);
        newCPT.setAccountName(null);
        newCPT.setCreditNote(currentCPT.getCreditNote());
        newCPT.setCreditNoteMonths(currentCPT.getCreditNoteMonths());

        //ACC-7321
        newCPT.setDdcId(currentCPT.getDdcId());

        newCPT = cptRep.save(newCPT);

        currentCPT.setActive(false);
        currentCPT.setSwitchedBankAccount(true);
        cptRep.save(currentCPT);

        List<DirectDebit> newDDs = generateNewDDsOnSwitching(newCPT, oldDDs, signatures);

        cancelOldDDs(oldDDs);

        boolean saveBankInfoOnCPT = false;
        if (newDDs != null && !newDDs.isEmpty()) {
            contractPaymentTermServiceNew.signDDByClient(
                    contractUUID, contract,
                    eidPhoto, ibanPhoto, accountPhoto,
                    true, true, true,
                    null, null, null, signatures,  false,
                    true, null,
                    null, null, null, pendingOcr);
        } else {
            saveBankInfoOnCPT = true;
        }

        if (saveBankInfoOnCPT) {
            // ACC-5163
            if (pendingOcr) {
                contractPaymentTermHelper.saveAttachmentsPendingOcr(newCPT, eidPhoto, ibanPhoto, accountPhoto);

            } else {
                contractPaymentTermHelper.extractBankInfoByOCR(newCPT, eidPhoto, ibanPhoto, accountPhoto,
                    true, true, true, null, null, null);
            }

            newCPT = cptRep.save(newCPT);

            directDebitSignatureService.saveNewDirectDebitFileSignatures(signatures, newCPT);
        }

        if (bouncedPayment != null) {
            bouncedPayment.setSwitchingAccountActionTaken(true);
            Setup.getApplicationContext().getBean(PaymentService.class)
                    .forceUpdatePayment(bouncedPayment);
        }

        // delete switching bank info
        List<String> bankInfoAttachmentTags = Arrays.asList(Contract.TEMP_EID_ATTACHMENT_TAG, Contract.TEMP_IBAN_ATTACHMENT_TAG, Contract.TEMP_ACCOUNT_NAME_ATTACHMENT_TAG);
        accountingEntityPropertyRepository.deleteByKeyAndOrigin(Contract.SWITCHING_BANK_ACCOUNT_DATE, contract);
        accountingEntityPropertyRepository.deleteByKeyAndOrigin(Contract.SWITCHING_BANK_ACCOUNT_BOUNCED_PAYMENT_ID, contract);

        for (String bankInfoAttachmentTag : bankInfoAttachmentTags) {
            contract.deleteAttachmentByTag(bankInfoAttachmentTag);
        }

        // ACC-3334
        sendEmailUponSwitching(contract);
        // ACC-5214
        Setup.getApplicationContext().getBean(DisableAccountingNotificationService.class)
            .disableDdRejectedAuthorizationOnSwitching(contract);
        // ACC-7421
        Setup.getApplicationContext().getBean(ContractPaymentTermServiceNew.class)
                .disableAllCcOfferLinkOfCptByBGT(currentCPT);
    }


    public int getLastSwitchingDateInDays(Contract contract) throws ParseException {
        Map switchingInfo = getLastSwitchBankAccountInfo(contract);
        if (switchingInfo != null && switchingInfo.containsKey("date")) {
            return Days.daysBetween(new DateTime(switchingInfo.get("date")), DateTime.now()).getDays();
        }

        return Integer.MAX_VALUE;
    }

    public Map getLastSwitchBankAccountInfo(Contract contract) throws ParseException {
        if (contract == null)
            throw new RuntimeException("Contract not Found");

        Map<String, Object> switchingInfo = new HashMap();
        Date switchingDate = null;
        Attachment eidAttachment = null;
        Attachment ibanAttachment = null;
        Attachment accountNameAttachment = null;
        Boolean clientSigned = null;
        String clientSmsNumber = null;

        AccountingEntityProperty switchingBankAccountDate = accountingEntityPropertyRepository.findByKeyAndOriginAndDeletedFalse(Contract.SWITCHING_BANK_ACCOUNT_DATE, contract);
        if (switchingBankAccountDate != null) {
            clientSigned = false;
            switchingDate = DateUtil.parseDateDashed(switchingBankAccountDate.getValue());
            eidAttachment = contract.getAttachment(Contract.TEMP_EID_ATTACHMENT_TAG);
            ibanAttachment = contract.getAttachment(Contract.TEMP_IBAN_ATTACHMENT_TAG);
            accountNameAttachment = contract.getAttachment(Contract.TEMP_ACCOUNT_NAME_ATTACHMENT_TAG);

            Template smsTemplate  = TemplateUtil.getTemplate("SIGN_DD_MESSAGE");
            if(smsTemplate != null) {
                SelectQuery<Sms> lastSentSMSQuery = new SelectQuery<>(Sms.class);
                lastSentSMSQuery.filterBy("template", "=", smsTemplate)
                        .and("receiverId", "=", contract.getClient().getId())
                        .and("receiverName", "=", contract.getClient().getName())
                        .and("receiverType", "=", SmsReceiverType.Client);
                lastSentSMSQuery.sortBy("id", false);

                List<Sms> sentSmses = lastSentSMSQuery.execute();

                if (!sentSmses.isEmpty()) clientSmsNumber = sentSmses.get(0).getMobileNumber();
            }
        } else {
            SelectQuery<ContractPaymentTerm> cptQuery = new SelectQuery(ContractPaymentTerm.class);
            cptQuery.filterBy("contract", "=", contract);
            cptQuery.sortBy("id", false, true);

            List<ContractPaymentTerm> cptList = cptQuery.execute();

            for (ContractPaymentTerm cpt : cptList) {
                HistorySelectQuery<ContractPaymentTerm> historyQuery = new HistorySelectQuery(ContractPaymentTerm.class);
                historyQuery.filterBy("id", "=", cpt.getId());
                historyQuery.filterBy("reason", "=", ContractPaymentTermReason.SWITCHING_BANK_ACCOUNT);

                List<ContractPaymentTerm> cptRevisions = historyQuery.execute();

                if (cptRevisions != null && !cptRevisions.isEmpty()) {
                    clientSigned = true;
                    switchingDate = cpt.getCreationDate();
                    eidAttachment = cpt.getAttachment(ContractPaymentTermController.FILE_TAG_BANK_INFO_EID);
                    ibanAttachment = cpt.getAttachment(ContractPaymentTermController.FILE_TAG_BANK_INFO_IBAN);
                    accountNameAttachment = cpt.getAttachment(ContractPaymentTermController.FILE_TAG_BANK_INFO_ACCOUNT_NAME);
                    break;
                }
            }
        }

        if (switchingDate != null) {
            switchingInfo.put("date", switchingDate);
        }
        if (eidAttachment != null) {
            switchingInfo.put("eidAttachment", eidAttachment.getUuid());
        }
        if (ibanAttachment != null) {
            switchingInfo.put("ibanAttachment", ibanAttachment.getUuid());
        }
        if (accountNameAttachment != null) {
            switchingInfo.put("accountNameAttachment", accountNameAttachment.getUuid());
        }
        if (clientSigned != null) {
            switchingInfo.put("clientSigned", clientSigned);
        }

        if (clientSmsNumber != null) {
            switchingInfo.put("clientSmsNumber", clientSmsNumber);
        }

        return switchingInfo;
    }

    //Jirra ACC-3334
    private void sendEmailUponSwitching(
            Contract contract) {

        if (contract == null || contract.getClient() == null) return;

        Map<String, String> parameters = new HashMap();
        parameters.put("client_name", contract.getClient().getName());
        parameters.put("Contract_ID", contract.getId().toString());

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff("switching_bank_account_cc_app",
                        parameters, Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_SWITCHING_BANK_ACCOUNT_EMAIL),
                        "Switch Bank Account - CC APP");
    }

    public void handleReceivingPaymentStatus(Payment payment) {
        logger.info("payment id " + payment.getId() +
                "; Date: " + payment.getDateOfPayment() +
                "; Status: " + payment.getStatus() +
                "; Replaced: " + BooleanUtils.toBoolean(payment.getReplaced()) +
                "; Method: " + payment.getMethodOfPayment());
        
        DirectDebit oldDD = null;
        Payment ddPayment = null;

        if (payment.getMethodOfPayment() != null && !payment.getMethodOfPayment().equals(PaymentMethod.DIRECT_DEBIT)) {
            ddPayment = paymentRepository.findFirstByContractAndAmountOfPaymentAndMethodOfPaymentAndTypeOfPayment_CodeAndDateOfPaymentBetweenAndStatusNot
                    (payment.getContract(), payment.getAmountOfPayment(), PaymentMethod.DIRECT_DEBIT, "monthly_payment",
                            new LocalDate(payment.getDateOfPayment()).dayOfMonth().withMinimumValue().toDate(),
                            new LocalDate(payment.getDateOfPayment()).dayOfMonth().withMaximumValue().toDate(), PaymentStatus.DELETED);

            if (ddPayment != null) {
                logger.info("DDPayment id: " + ddPayment.getId());
                oldDD = ddPayment.getDirectDebit() == null ? null : ddRepo.findOne(ddPayment.getDirectDebit().getId());
            }
        } else if (payment.getDirectDebit() != null) {
            oldDD = ddRepo.findOne(payment.getDirectDebit().getId());
            ddPayment = payment;
        }

        if (!isClientSwitchingBankAccount(oldDD)) {
            logger.info("Client is Not Switching Bank Account");
            return;
        }

        if (!payment.getTypeOfPayment().getId()
                .equals(Setup.getItem("TypeOfPayment", "monthly_payment").getId())) {
            handleNonMonthlyPaymentStateChanged(payment);
            return;
        }

        logger.info("Client is Switching Bank Account");

        ContractPaymentTerm oldCPT = oldDD.getContractPaymentTerm();
        ContractPaymentTerm newCPT = oldCPT.getContract().getActiveContractPaymentTerm();

        DateTime switchingDate = new DateTime(newCPT.getCreationDate());
        logger.info("Switching Date: " + switchingDate);
        logger.info("Old DD Category: " + oldDD.getCategory());

        if (PaymentHelper.doesPaymentCoverDate(payment, switchingDate.toDate())) {
            logger.info("Payment covers switching month payment");
            
            if (oldDD.getCategory().equals(DirectDebitCategory.B)) {
                if (payment.getStatus().equals(PaymentStatus.BOUNCED) && !BooleanUtils.toBoolean(payment.getReplaced())) {
                    logger.info("Payment got Bounced and covers switching month payment -> Generate One Time DD + Cancel old ");
                    if (!isSwitchingBankAccountDDsCoverPayment(ddPayment)) {
                        logger.info("Payment isn't Covered in new DDs");
                        addNewOneTimeDD(payment.getContract().getId(), payment.getDateOfPayment(),
                                payment.getAmountOfPayment(), oldDD, payment);
                    }
                } else if(payment.getStatus().equals(PaymentStatus.RECEIVED)) {
                    // switch month payment related to DDB and is received -> cancel old DDB if new bank DDA of next month is confirmed
                    logger.info("Payment RECEIVED and covers switching month payment");
                    
                    if (!isSwitchingBankAccountDDAConfirmed(oldDD, switchingDate.plusMonths(1))) {
                        logger.info("The new DDA is not yet confirmed for next month's of switching");
                        return;
                    }
                    
                    logger.info("DDB Cancel Old DD#" + oldDD.getId());
                    cancelDDOrShowHiddenCancellationToDos(oldDD);
                }
            } else if (oldDD.getCategory().equals(DirectDebitCategory.A)) {

                if(payment.getStatus().equals(PaymentStatus.RECEIVED)) {

                    logger.info("Payment got Received and covers switching month payment -> cancel new DDA");
                    DirectDebit ddImage = getPaymentDDImage(ddPayment);

                    if (ddImage != null && ddImage.getDirectDebitFiles() != null &&
                            ddImage.getDirectDebitFiles().stream().noneMatch(f ->
                                 f.getStatus().equals(DirectDebitFileStatus.SENT))) {

                        logger.info("Cancelling new DDA");
                        directDebitCancellationService.cancelWholeDD(ddImage, DirectDebitCancellationToDoReason.PAYMENT_RECEIVAL_NO_NEED_MONEY);
                    }

                    if (!isSwitchingBankAccountDDAConfirmed(oldDD, switchingDate.plusMonths(1))) {
                        logger.info("The new DDA is not yet confirmed for next month's of switching");
                        return;
                    }
                    
                    logger.info("Payment got Received related to DDA covers switching month payment -> cancel old DDB");
                    DirectDebit oldDDB = DDUtils.getPaymentDD(payment, oldCPT, DirectDebitCategory.B);
                    if (oldDDB != null) cancelDDOrShowHiddenCancellationToDos(oldDDB);
                    
                } else if(payment.getStatus().equals(PaymentStatus.BOUNCED)) {
                    logger.info("Payment got Bounced and covers switching month payment -> cancel old DDA");
                    cancelDDOrShowHiddenCancellationToDos(oldDD);
                }
            }
        } else if (oldDD.getCategory().equals(DirectDebitCategory.A) || PaymentHelper.doesPaymentCoverDate(payment, switchingDate.plusMonths(1).toDate())) {
            if (!isSwitchingBankAccountDDAConfirmed(oldDD, switchingDate.plusMonths(1))) {
                logger.info("The new DDA is not yet confirmed for next month's of switching");
                return;
            }
            if (payment.getStatus().equals(PaymentStatus.RECEIVED) || BooleanUtils.toBoolean(payment.getReplaced())) {
                logger.info("Payment got Received and covers switching next month payment -> cancel new DDA");
                DirectDebit ddImage = getPaymentDDImage(ddPayment);

                if(ddImage != null) {
                    logger.info("Cancelling new DDA");
                    directDebitCancellationService.cancelWholeDD(ddImage, DirectDebitCancellationToDoReason.PAYMENT_RECEIVAL_NO_NEED_MONEY);
                }
                
                logger.info("Cancelling old DDB");
                DirectDebit oldDDB = DDUtils.getPaymentDD(payment, oldCPT, DirectDebitCategory.B);
                if(oldDDB != null) cancelDDOrShowHiddenCancellationToDos(oldDDB);
            }
            else if (payment.getStatus().equals(PaymentStatus.BOUNCED) &&
                    PaymentHelper.doesPaymentCoverDate(payment, switchingDate.plusMonths(1).toDate())) {
                
                logger.info("Payment got Bounced and covers switching month payment + 1 -> cancel old DDA");
                cancelDDOrShowHiddenCancellationToDos(oldDD);
                
                logger.info("Payment status bounced -> Cancelling old DDB");
                DirectDebit oldDDB = DDUtils.getPaymentDD(payment, oldCPT, DirectDebitCategory.B);
                if(oldDDB != null) cancelDDOrShowHiddenCancellationToDos(oldDDB);
            }
        }
    }

    private void handleNonMonthlyPaymentStateChanged(Payment p) {
        if (p.getDirectDebit() == null || p.getDirectDebit().getId() == null) return;

        DirectDebit ddImage = getPaymentDDImage(p);
        if(ddImage == null) {
            logger.info("ddImage is null -> do nothing");
            return;
        }

        if (p.getStatus().equals(PaymentStatus.RECEIVED) || BooleanUtils.toBoolean(p.getReplaced())) {
            logger.info("Payment got Received -> cancel new DDA");
            directDebitCancellationService.cancelWholeDD(ddImage, DirectDebitCancellationToDoReason.PAYMENT_RECEIVAL_NO_NEED_MONEY);

        } else if (p.getStatus().equals(PaymentStatus.BOUNCED)) {
            logger.info("Payment got Bounced -> cancel old DDA");
            cancelDDOrShowHiddenCancellationToDos(ddRepo.findOne(p.getDirectDebit().getId()));
        }
    }

    public void handleConfirmNewDDASwitchingBankAccount(DirectDebit newDD) {
        DirectDebit oldDD = ddRepo.findOne(newDD.getImageForDD().getId());
        DateTime switchingDate = new DateTime(newDD.getContractPaymentTerm().getCreationDate());

        DirectDebit oldDDB = oldDD.getCategory().equals(DirectDebitCategory.B) ?
                oldDD : DDUtils.getDirectDebit(oldDD.getContractPaymentTerm(), DirectDebitCategory.B);

        if(!isClientSwitchingBankAccount(oldDD)) return;

        logger.info("start: newDD " + newDD.getId() + "; old DD -> " + oldDD.getCategory());

        boolean isNonMonthlyDD = !Setup.getRepository(DirectDebitRepository.class).isMonthlyDD(newDD.getId());

        if(oldDD.getCategory().equals(DirectDebitCategory.A) && isNonMonthlyDD) {
            Payment oneTimePayment = PaymentHelper.getPaymentOfDD(oldDD.getId(), null);

            if (newDdaConfirmedAndOldPaymentReceived(oneTimePayment, newDD)) return;

            if(oneTimePayment != null && oneTimePayment.getSentToBankByMDD() &&
                    oneTimePayment.getStatus().equals(PaymentStatus.PDC)) return;

            cancelDDOrShowHiddenCancellationToDos(oldDD);

        } else {
            Payment oldPayment = null;

            if(oldDD.getCategory().equals(DirectDebitCategory.A)) {
                oldPayment = PaymentHelper.getPaymentOfDD(oldDD.getId(), null);

                if (newDdaConfirmedAndOldPaymentReceived(oldPayment, newDD)) return;

                if(oldPayment == null || !oldPayment.getStatus().equals(PaymentStatus.PDC) || !oldPayment.getSentToBankByMDD()) {
                    logger.info("DDA confirmed -> cancelling old DDA");
                    cancelDDOrShowHiddenCancellationToDos(oldDD);
                }
            }

            if(oldDDB == null) {
                logger.info("oldDDB is null; exiting");
                return;
            }

            if(DDUtils.doesDDCoverDate(newDD, switchingDate.toDate())) {
                logger.info("DDA confirmed for switch month");

                if(oldPayment == null) {
                    oldPayment = PaymentHelper.getPaymentOfDD(oldDDB.getId(), switchingDate.toDate());
                }

                if (oldPayment != null) {
                    logger.info("oldPayment id" + oldPayment.getId());
                    List<Payment> payments = paymentRepository.
                            findByDirectDebit_ContractPaymentTerm_IdAndDateOfPaymentOrderByCreationDateDesc
                                    (oldDDB.getContractPaymentTerm().getId(), oldPayment.getDateOfPayment());

                    if (payments.stream().anyMatch(payment -> payment.getStatus().equals(PaymentStatus.RECEIVED))) {
                        logger.info("switch month payment status is received -> cancelling new dd");
                        directDebitCancellationService.cancelWholeDD(newDD,
                                DirectDebitCancellationToDoReason.PAYMENT_RECEIVAL_NO_NEED_MONEY);

                    } else if (payments.stream().anyMatch(payment -> payment.getStatus().equals(PaymentStatus.BOUNCED))) {
                        logger.info("switch month payment status is bounced cancel -> cancelling old DDB");
                        cancelDDOrShowHiddenCancellationToDos(oldDDB);

                        return;

                    } else {
                        logger.info("switch month payments check if any payment status is received");
                        Payment receivedPayment = paymentRepository.
                                findFirstByContractAndTypeOfPayment_CodeAndStatusAndDateOfPaymentBetween(oldPayment.getContract(),
                                        "monthly_payment", PaymentStatus.RECEIVED,
                                        new LocalDate(oldPayment.getDateOfPayment()).dayOfMonth().withMinimumValue().toDate(),
                                        new LocalDate(oldPayment.getDateOfPayment()).dayOfMonth().withMaximumValue().toDate());

                        if (receivedPayment != null) {
                            logger.info("switch month payment status is received -> cancelling new dd");
                            directDebitCancellationService.cancelWholeDD(newDD,
                                    DirectDebitCancellationToDoReason.PAYMENT_RECEIVAL_NO_NEED_MONEY);
                        }
                    }
                }

                if (isSwitchingBankAccountDDAConfirmed(oldDD, switchingDate.plusMonths(1))) {
                    logger.info("DDA confirmed for month following switch month -> cancelling old DDB");
                    cancelDDOrShowHiddenCancellationToDos(oldDDB);
                }
            } else if(DDUtils.doesDDCoverDate(newDD, switchingDate.plusMonths(1).toDate())) {
                logger.info("DDA confirmed for month following switch month");

                if(oldDD.getCategory().equals(DirectDebitCategory.A) && newDD.getCategory().equals(DirectDebitCategory.A)) {
                    logger.info("switch next month payment is one time and not sent for collection -> cancelling old DDA");
                    oldPayment = PaymentHelper.getPaymentOfDD(oldDD.getId(), null);

                    if (oldPayment == null || !oldPayment.getStatus().equals(PaymentStatus.PDC) || !oldPayment.getSentToBankByMDD()) {
                        cancelDDOrShowHiddenCancellationToDos(oldDD);
                    }
                }

                Boolean switchNextMonthPaymentReceived = paymentRepository.findByContractAndStatusOrderByDateOfPaymentDesc(
                                oldDD.getContractPaymentTerm().getContract(), PaymentStatus.RECEIVED)
                        .stream().anyMatch(p -> p.getTypeOfPayment().getCode().equals("monthly_payment") &&
                                new LocalDate(p.getDateOfPayment().getTime()).toString("yyyy-MM")
                                        .equals(new LocalDate(newDD.getContractPaymentTerm().getCreationDate().getTime())
                                                .plusMonths(1).toString("yyyy-MM")));

                if(switchNextMonthPaymentReceived) {
                    logger.info("payment received for switch month plus one -> cancelling old DDB");
                    cancelDDOrShowHiddenCancellationToDos(oldDDB);

                    logger.info("payment received for switch month plus one -> cancelling new DDA");
                    directDebitCancellationService.cancelWholeDD(newDD,
                            DirectDebitCancellationToDoReason.PAYMENT_RECEIVAL_NO_NEED_MONEY);

                    return;
                }

                Boolean switchNextMonthPaymentBounced = paymentRepository.findByContractAndStatusOrderByDateOfPaymentDesc(
                                oldDD.getContractPaymentTerm().getContract(), PaymentStatus.BOUNCED)
                        .stream().anyMatch(p -> p.getTypeOfPayment().getCode().equals("monthly_payment") &&
                                new LocalDate(p.getDateOfPayment().getTime()).toString("yyyy-MM")
                                        .equals(new LocalDate(newDD.getContractPaymentTerm().getCreationDate().getTime())
                                                .plusMonths(1).toString("yyyy-MM")));

                if (switchNextMonthPaymentBounced) {
                    logger.info("payment bounced for switch month plus one -> cancelling old DDB");
                    cancelDDOrShowHiddenCancellationToDos(oldDDB);
                    return;
                }

                Boolean switchPaymentReceived = paymentRepository.findByContractAndStatusOrderByDateOfPaymentDesc(
                                oldDD.getContractPaymentTerm().getContract(), PaymentStatus.RECEIVED)
                        .stream().anyMatch(p -> p.getTypeOfPayment().getCode().equals("monthly_payment") &&
                                new LocalDate(p.getDateOfPayment().getTime()).toString("yyyy-MM")
                                        .equals(new LocalDate(newDD.getContractPaymentTerm().getCreationDate().getTime()).toString("yyyy-MM")));

                if(switchPaymentReceived){
                    logger.info("payment received for switch month -> cancelling old DDB");
                    cancelDDOrShowHiddenCancellationToDos(oldDDB);
                    return;
                }

                if(isSwitchingBankAccountDDAConfirmed(oldDD, switchingDate)) {
                    logger.info("DDA confirmed for switch month -> cancelling old DDB");
                    cancelDDOrShowHiddenCancellationToDos(oldDDB);
                }
            }
        }
    }

    private boolean newDdaConfirmedAndOldPaymentReceived(Payment p, DirectDebit newDd) {
        if (p == null || (!p.getStatus().equals(PaymentStatus.RECEIVED) && !p.isReplaced())) return false;
        logger.info("payment id: " + p.getId() + " status is received -> cancelling new dd id: " + newDd.getId());

        directDebitCancellationService.cancelWholeDD(newDd,
                DirectDebitCancellationToDoReason.PAYMENT_RECEIVAL_NO_NEED_MONEY);
        return true;
    }

    public boolean isSwitchBankAccountFlowRunning(ContractPaymentTerm cpt) {
        if (!ContractPaymentTermReason.SWITCHING_BANK_ACCOUNT.equals(cpt.getReason())) return false;

        logger.info("cpt id: " + cpt.getId());
        ContractPaymentTerm oldCPT = Setup.getRepository(ContractPaymentTermRepository.class)
                .findFirstByContractAndCreationDateLessThanOrderByCreationDateDesc(cpt.getContract(), cpt.getCreationDate());

        if (oldCPT == null) return false;

        return ddRepo.hasActiveDDByCpt(oldCPT, DirectDebitService.notAllowedStatuses);
    }
}