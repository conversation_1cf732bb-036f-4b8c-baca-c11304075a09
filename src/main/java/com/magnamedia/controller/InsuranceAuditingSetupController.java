package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.InsuranceAuditingSetup;
import com.magnamedia.repository.InsuranceAuditingSetupRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2/11/2021
 */
@RequestMapping("/InsuranceAuditingSetup")
@RestController
public class InsuranceAuditingSetupController extends BaseRepositoryController<InsuranceAuditingSetup> {

    @Autowired
    private InsuranceAuditingSetupRepository insuranceAuditingSetupRepository;


    @Override
    public BaseRepository<InsuranceAuditingSetup> getRepository() {
        return insuranceAuditingSetupRepository;
    }


    @Override
    public ResponseEntity<?> createEntity(InsuranceAuditingSetup entity) {
        validation(entity);
        return super.createEntity(entity);
    }

    @Override
    protected ResponseEntity<?> updateEntity(InsuranceAuditingSetup entity) {
        InsuranceAuditingSetup insuranceAuditingSetup = getRepository().findAll().get(0);
        if(insuranceAuditingSetup==null)
            throw new RuntimeException("no exist setup to update");
        entity.setId(insuranceAuditingSetup.getId());
        return  super.updateEntity(entity);
    }

    private void validation(InsuranceAuditingSetup entity) {
        List<InsuranceAuditingSetup> all = insuranceAuditingSetupRepository.findAll();
        if(!all.isEmpty())
            throw new RuntimeException("We can have only one entity");
    }
}
