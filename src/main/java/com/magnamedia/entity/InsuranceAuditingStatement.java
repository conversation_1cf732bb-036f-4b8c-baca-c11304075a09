package com.magnamedia.entity;

import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.helper.NumberFormatter;
import com.magnamedia.module.type.InsuranceAuditingStatementStatus;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2/11/2021
 */
@Entity
public class InsuranceAuditingStatement extends BaseEntity {

    @Column
    private Date fromDate;
    @Column
    private Date toDate;
    @Column
    private Double amount;
    @Column
    private Double balance;

    @OneToMany(fetch = FetchType.LAZY,  mappedBy = "statement", cascade = CascadeType.REMOVE)
    private List<InsuranceAuditingRecord> records=new ArrayList<>();

    //Audit summary

    @Column
    private Double erpTotalAmount=0D;
    @Column
    private Double invoiceTotalAmount=0D;
    @Column
    private Double dismissedAmount=0D;
    @Transient
    private Double difference;

    // Jira ACC-4655
    @Enumerated(EnumType.STRING)
    private InsuranceAuditingStatementStatus status = InsuranceAuditingStatementStatus.PENDING;

    public InsuranceAuditingStatementStatus getStatus() {
        return status;
    }

    public void setStatus(InsuranceAuditingStatementStatus status) {
        this.status = status;
    }

    public Double getErpTotalAmount() {
        return erpTotalAmount;
    }

    public void setErpTotalAmount(Double erpTotalAmount) {
        this.erpTotalAmount = erpTotalAmount;
    }

    public Double getInvoiceTotalAmount() {
        return invoiceTotalAmount;
    }

    public void setInvoiceTotalAmount(Double invoiceTotalAmount) {
        this.invoiceTotalAmount = invoiceTotalAmount;
    }

    public Double getDismissedAmount() {
        return dismissedAmount;
    }

    public void setDismissedAmount(Double dismissedAmount) {
        this.dismissedAmount = dismissedAmount;
    }

    public Double getDifference() {
        return NumberFormatter.twoDecimalPoints(invoiceTotalAmount - dismissedAmount - erpTotalAmount);
    }

    public void setDifference(Double difference) {
        this.difference = difference;
    }

    public Date getFromDate() {
        return fromDate;
    }

    public void setFromDate(Date fromDate) {
        this.fromDate = fromDate;
    }

    public Date getToDate() {
        return toDate;
    }

    public void setToDate(Date toDate) {
        this.toDate = toDate;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getBalance() {
        return balance;
    }

    public void setBalance(Double balance) {
        this.balance = balance;
    }

    public List<InsuranceAuditingRecord> getRecords() {
        return records;
    }

    public void setRecords(List<InsuranceAuditingRecord> records) {
        this.records = records;
    }
}