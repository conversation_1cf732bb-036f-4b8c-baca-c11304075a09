package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Parameter;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.repository.ParameterRepository;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DDFExportingConfig;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.entity.Payment;
import com.magnamedia.extra.ManualDDFBean;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.DDFExportingConfigRepository;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.service.JobExecutionService;
import com.magnamedia.service.MessagingService;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.io.File;
import java.io.FileInputStream;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on Apr 13, 2020
 *         Jirra ACC-1598
 */

public class SendingApprovedManualDDToBankScheduledJob implements MagnamediaJob {
    private static final Logger LOGGER =
            Logger.getLogger(SendingApprovedManualDDToBankScheduledJob.class.getName());

    String[] headers = new String[]{"Record number", "Client DD reference number", "Amount", "Client IBAN Number"};

    String[] columns = new String[]{"rowIndex", "applicationId", "amount", "iban"};

    private DDFExportingConfigRepository ddfExportingConfigRepository;
    private ParameterRepository parameterRepository;
    private JobExecutionService jobExecutionService;
    private final PaymentRepository paymentRepository;

    private List<Long> filteredDDFs;

    public SendingApprovedManualDDToBankScheduledJob() {
        ddfExportingConfigRepository = Setup.getRepository(DDFExportingConfigRepository.class);
        parameterRepository = Setup.getRepository(ParameterRepository.class);
        jobExecutionService = Setup.getApplicationContext().getBean(JobExecutionService.class);
        paymentRepository = Setup.getApplicationContext().getBean(PaymentRepository.class);
    }

    @Override
    public void run(Map<String, ?> map) {
        this.runJob();
    }

    private void runJob() {
        filteredDDFs = new ArrayList<>();
        DDFExportingConfig ddfAutomaticExportingConfig = ddfExportingConfigRepository.findFirstByName("Automatic");
        DDFExportingConfig ddfManualExportingConfig = ddfExportingConfigRepository.findFirstByName("Manual");

        List<DDFPayment> directDebitFiles = getPaymentsForSendingManualDDToBank
                (PaymentStatus.PDC, ddfAutomaticExportingConfig.getOic(), ddfManualExportingConfig.getOic());
        directDebitFiles.addAll(getPaymentsForSendingManualDDToBank(
                PaymentStatus.BOUNCED, ddfAutomaticExportingConfig.getOic(), ddfManualExportingConfig.getOic()));

        logger.log(Level.SEVERE, "directDebitFiles size: " + directDebitFiles.size());

        Map<String, List<ManualDDFBean>> map = new HashMap();
        Long batchMaxSize = Long.parseLong(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_MANUAL_DD_BATCH_MAX));
        String fileIndex = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_MANUAL_DD_BATCH_FILE_INDEX);
        Parameter manualDDBatchInitialIncrement = parameterRepository.findByModuleAndCode(Setup.getCurrentModule(), AccountingModule.PARAMETER_MANUAL_DD_BATCH_INITIAL_INCREMENT);
        Long initialValue = Long.parseLong(manualDDBatchInitialIncrement.getValue());
        Date now = new Date();
        String dateFormat = DateUtil.formatDayNumber(now)
                + DateUtil.formatMonthNumber(now) + DateUtil.formatYearNumber(now);
        
        Long index = 1L;
        logger.log(Level.SEVERE, "directDebitFiles size: " + directDebitFiles.size());
        logger.log(Level.SEVERE, "batchMaxSize: " + batchMaxSize);

        Collections.sort(directDebitFiles);
        
        for (DDFPayment ddfPayment : directDebitFiles) {
            try {
                String currentKey = fileIndex + dateFormat + ddfPayment.ddfManualOIC + String.format("%06d", initialValue);

                if(!map.containsKey(currentKey)) {
                    map.put(currentKey, new ArrayList());
                } else if (map.get(currentKey).size() >= batchMaxSize) {
                    currentKey = fileIndex + dateFormat + ddfPayment.ddfManualOIC + String.format("%06d", ++initialValue);
                    map.put(currentKey, new ArrayList());
                }
                
                logger.log(Level.SEVERE, "currentKey : " + currentKey);

                map.get(currentKey).add(new ManualDDFBean(
                        ddfPayment.directDebitFile, ddfPayment.payment, index++));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Exception while running on Payment#" + ddfPayment.payment.getId() + " DDF#" + ddfPayment.directDebitFile.getId());
                logger.log(Level.SEVERE, "Exception :" + ExceptionUtils.getStackTrace(e));
            }
        }

        logger.log(Level.SEVERE, "Map size: " + map.size());
        MessagingService messagingService = Setup.getApplicationContext()
                .getBean(MessagingService.class);
        //persisting new entity for each batch file
        for (String key : map.keySet()) {
            List<ManualDDFBean> manualDDFBeanList = new ArrayList();
            try {
                manualDDFBeanList = filterDDFs(map.get(key));
                File csvFile = jobExecutionService.runSendingApprovedManualDDToBankScheduledJob(
                        map, key, manualDDFBeanList, columns);

                if (csvFile == null) continue;

                // send email with csv as an attachment
                messagingService.sendEmailToOfficeStaffWithAttachments("manual_dd_batch_file",
                                new HashMap<>(), Setup.getParameter(Setup.getCurrentModule(),
                                        AccountingModule.PARAMETER_APPROVED_MANUAL_DD_EMAIL),
                                Collections.singletonList(Storage.storeTemporary(csvFile.getName(),
                                        new FileInputStream(csvFile), null,false)),
                                "Manual DD Batch File for " + DateUtil.formatClientFullDate(now));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "ERROR with DDFS#" + manualDDFBeanList.stream().map(manualDDFBean -> manualDDFBean.getId()).collect(Collectors.toList()));
                logger.log(Level.SEVERE, e.getMessage());
            }
        }

        // update parameter value
        if (index > 1) {
            manualDDBatchInitialIncrement.setValue((++initialValue).toString());
            parameterRepository.save(manualDDBatchInitialIncrement);
        }
    }

    private List<DDFPayment> getPaymentsForSendingManualDDToBank(PaymentStatus paymentStatus, String autOIC, String manualOIC) {
        Long lastId = -1L;
        Page<Payment> page;
        List<DDFPayment> directDebitFiles = new ArrayList<>();

        //get due payments to send related manual ddf to the bank
        do {
            page = paymentStatus.equals(PaymentStatus.PDC) ?
                    paymentRepository.findForSendingApprovedManualDDToBankScheduledJob_PDC(lastId, new Date(), autOIC, PageRequest.of(0, 100)) :
                    paymentRepository.findForSendingApprovedManualDDToBankScheduledJob_BOUNCED(lastId, autOIC, PageRequest.of(0, 100));

            logger.info( "page Size: " + page.getSize());
            for (Payment payment : page.getContent()) {
                try {
                    logger.info( "payment id: " + payment.getId() + "; payment status: " + payment.getStatus());
                    Contract contract = payment.getContract();

                    logger.info("is Contract Cancelled within First X Days: " + contract.isCancelledWithinFirstXDays());
                    boolean isCCAndCancelledWithinFirstXDays = contract.isCancelledWithinFirstXDays();
                    if (isCCAndCancelledWithinFirstXDays) continue;

                    if (payment.getStatus().equals(PaymentStatus.PDC)) {
                        String ddfManualOIC = payment.getDirectDebitFile().getDdaRefNo() != null ?
                                payment.getDirectDebitFile().getDdaRefNo().substring(0, 9) : manualOIC;
                        directDebitFiles.add(new DDFPayment(payment.getDirectDebitFile(), payment, ddfManualOIC));
                    } else {
                        String ddfManualOIC = payment.getDirectDebit().getManualDdfFile().getDdaRefNo() != null ?
                                payment.getDirectDebit().getManualDdfFile().getDdaRefNo().substring(0, 9) : manualOIC;
                        directDebitFiles.add(new DDFPayment(payment.getDirectDebit().getManualDdfFile(),
                                payment, ddfManualOIC));
                    }

                } catch (Exception e) {
                    logger.log(Level.SEVERE, "Exception while running on Payment#" + payment.getId());
                    logger.log(Level.SEVERE, "Exception :" + ExceptionUtils.getStackTrace(e));
                }
            }
            if (!page.isEmpty()) {
                lastId = page.getContent().get(page.getContent().size() - 1).getId();
            }
        } while (!page.isEmpty());

        return directDebitFiles;
    }

    private List<ManualDDFBean> filterDDFs(List<ManualDDFBean> ddfBeans) {
        List<ManualDDFBean> temp = new ArrayList();
        logger.log(Level.SEVERE, "ddfBeans size: " + ddfBeans.size());
        for (ManualDDFBean ddfBean : ddfBeans) {
            if (!filteredDDFs.contains(ddfBean.getId())) {
                temp.add(ddfBean);
                filteredDDFs.add(ddfBean.getId());
            }
        }
        logger.log(Level.SEVERE, "filteredDDFs size: " + filteredDDFs.size());
        return temp;
    }

    class DDFPayment implements Comparable {
        private DirectDebitFile directDebitFile;
        private Payment payment;
        private String ddfManualOIC;

        public DDFPayment() {
        }

        public DDFPayment(DirectDebitFile directDebitFile, Payment payment, String ddfManualOIC) {
            this.directDebitFile = directDebitFile;
            this.payment = payment;
            this.ddfManualOIC = ddfManualOIC;
        }

        @Override
        public int compareTo(Object o) {
            return this.ddfManualOIC.compareTo(((DDFPayment)o).ddfManualOIC);
        }
    }
}