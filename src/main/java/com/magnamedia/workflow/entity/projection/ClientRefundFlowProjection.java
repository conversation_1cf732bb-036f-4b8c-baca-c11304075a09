package com.magnamedia.workflow.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.serialize.IdJsonSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.workflow.Task;
import com.magnamedia.entity.*;
import com.magnamedia.entity.serializer.ClientRefundToDoSerializer;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.workflow.type.ClientRefundPaymentMethod;
import com.magnamedia.workflow.type.ClientRefundRequestType;
import com.magnamedia.workflow.type.ClientRefundStatus;
import com.magnamedia.workflow.type.ClientRefundTodoManagerAction;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.rest.core.config.Projection;

import java.sql.Date;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 *         Created on Dec 10, 2020
 *         ACC-2882
 */
@Projection(name = "clientRefundFlowProjection", types = BaseEntity.class)
public interface ClientRefundFlowProjection extends Task {

    Long getId();

    @Value("#{(target.getId())}")
    String getDetail();

    String getTaskName();

    @JsonSerialize(using = IdJsonSerializer.class)
    Contract getContract();

    @JsonSerialize(using = IdLabelSerializer.class)
    Client getClient();

    @Value("#{(target.getPurpose()!=null)?"
            + "{id:target.getPurpose().getId(),"
            + "name:target.getPurpose().getName(),"
            + "label:target.getPurpose().getName(),"
            + "category:target.getPurpose().getCategoryStr()}"
            + ":null}")
    Map<?, ?> getPurpose();

    Double getAmount();

    Integer getNumberOfMonthlyPayments();

    @JsonSerialize(using = IdLabelSerializer.class)
    Complaint getComplaint();

    @JsonSerialize(using = IdLabelSerializer.class)
    ComplaintType getComplaintType();

    ClientRefundPaymentMethod getMethodOfPayment();

    ClientRefundStatus getStatus();
    // ACC-5818
    boolean isLenient();

    @Value("#{target.getLeniencyType() != null ? target.getLeniencyType().getName() : ''}")
    String getLeniencyType();

    @Value("#{target.getContract() != null ? target.getContract().isMaidCc ? 'CC' : 'MV' : ''}")
    String getContractType();

    String getAccountName();

    String getIban();

    String getEid();

    String getDescription();

    String getNotes();

    String getManagerNotes();

    String getAllNotes();

    @Value("#{(target.isAutomaticRefund() ? \"ERP\" : target.getRequesterUserName())}")
    String getRequesterUserName();

    @JsonSerialize(using = IdLabelSerializer.class)
    User getRequesterUser();

    Date getStatusChangeDate();

    java.util.Date getCreationDate();

    ClientRefundTodoManagerAction getManagerAction();

    ClientRefundTodoManagerAction getCeoAction();

    ClientRefundRequestType getRequestType();

    @JsonSerialize(using = IdLabelSerializer.class)
    PicklistItem getPartialRefundForCancellationPaymentMethod();

    Integer getNumberOfUsedDays();

    Integer getNumberOfUnusedDays();

    boolean isProofUploaded();

    boolean getCanDoManagerAction();

    boolean getCanDoCeoAction();

    @JsonSerialize(using = ClientRefundToDoSerializer.class)
    ClientRefundToDo getParent();

    List<Attachment> getAttachments();

    boolean getNeedsCooApproval();

    @JsonSerialize(using = IdLabelSerializer.class)
    User getApprover();

    String getEntityType();

    List<CooQuestion> getCooQuestions();

    boolean isOneQuestionAnswered();
    boolean isNoneQuestionAnswered();
    boolean isAllQuestionsAnswered();
    boolean isConditionalRefund();
    
    String getDisplayId();

    @JsonSerialize(using = IdLabelSerializer.class)
    Transaction getTransaction();

    java.util.Date getLastModificationDate();

    String getTransferReference();
}