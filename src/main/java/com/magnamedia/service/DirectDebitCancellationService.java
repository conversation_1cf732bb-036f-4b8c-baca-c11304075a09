package com.magnamedia.service;

import com.magnamedia.controller.BankDirectDebitCancelationFileController;
import com.magnamedia.controller.DirectDebitCancelationToDoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.DirectDebitCancelationToDo;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.extra.PaymentHelper;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import org.apache.commons.lang3.BooleanUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;


@Service
public class DirectDebitCancellationService {

    protected static final Logger logger = Logger.getLogger(DirectDebitCancellationService.class.getName());

    @Autowired
    private DirectDebitRepository directDebitRepository;
    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;
    @Autowired
    private DirectDebitCancelationToDoController directDebitCancelationToDoController;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private DDFBatchForRPARepository ddfBatchForRPARepository;
    @Autowired
    private DirectDebitRejectionToDoRepository directDebitRejectionToDoRepository;

    public void cancelAllMonthlyDdsByCpt(
        ContractPaymentTerm cpt,
        DirectDebitCancellationToDoReason reason) {

        logger.log(Level.INFO, "cpt id: {0}", cpt.getId());

        List<DirectDebitStatus> ignoredStatuses = Arrays.asList(
            DirectDebitStatus.CANCELED, DirectDebitStatus.EXPIRED,
            DirectDebitStatus.PENDING_FOR_CANCELLATION);

        List<DirectDebit> dds = directDebitRepository.findByContractPaymentTermAndStatusNotInAndMStatusNotIn(
                cpt, ignoredStatuses, ignoredStatuses);

        dds.stream()
                .filter(dd -> dd.getPaymentType() != null &&
                        dd.getPaymentType().getCode().equals("monthly_payment"))
                .forEach(dd -> cancelWholeDD(dd, reason));
    }

    @Transactional
    public void cancelWholeDD(
            DirectDebit directDebit,
            DirectDebitCancellationToDoReason reason) {
        cancelWholeDD(directDebit, false, reason, false);
    }

    //Jirra ACC-1587
    @Transactional
    public void cancelWholeDD(
        DirectDebit directDebit,
        boolean hidden,
        DirectDebitCancellationToDoReason reason) {
        cancelWholeDD(directDebit, hidden, reason, false);
    }

    @Transactional
    public void cancelWholeDD(
        DirectDebit directDebit,
        boolean hidden,
        DirectDebitCancellationToDoReason reason,
        boolean fromExpiryJob) {
        if (directDebit == null) {
            return;
        }

        logger.info("Cancelling DD: " + directDebit.getId());

        if (directDebit.getDirectDebitFiles() == null || directDebit.getDirectDebitFiles().isEmpty()) {
            logger.info("cancelWholeDD if directDebit.getDirectDebitFiles() == null " + directDebit.getId());
            if (directDebit.getStatus().equals(DirectDebitStatus.CANCELED) &&
                    directDebit.getMStatus().equals(DirectDebitStatus.CANCELED)) {
                logger.info("Master DD already canceled -> existing");
                return;
            }
            directDebit.setStatus(DirectDebitStatus.CANCELED);
            directDebit.setMStatus(DirectDebitStatus.CANCELED);

            directDebitRepository.save(directDebit);
            return;
        }

        // this is to avoid Concurrent Modification Exception
        List<Long> ddfsIDs = directDebit.getDirectDebitFiles().stream().map(ddf -> ddf.getId()).collect(Collectors.toList());

        for (Long ddFileId : ddfsIDs) {
            logger.info("in if there are files " + directDebit.getId() + " ddFileId "+ddFileId);
            DirectDebitFile ddFile = directDebitFileRepository.findOne(ddFileId);
            ddFile.setDoNotCancelExpiredDirectly(fromExpiryJob);

            DirectDebitCancelationToDo directDebitCancelationToDo = new DirectDebitCancelationToDo();
            directDebitCancelationToDo.setDirectDebitFile(ddFile);
            directDebitCancelationToDo.setHidden(hidden);
            directDebitCancelationToDo.setReason(reason);

            directDebitCancelationToDo.setIgnoreDDRejectionFlow(true);

            //ACC-2858 stop hiding todos
//            //ACC-2080
//            if (DateUtil.isInLastCoupleDaysOfMonth(new Date())) {
//                directDebitCancelationToDo.setHidden(true);
//            }

            directDebitCancelationToDoController.createEntity(directDebitCancelationToDo);
            logger.info("cancelWholeDD after createEntity " + directDebitCancelationToDo.getId());
        }
    }

    // ACC-1587
    @Transactional
    public void cancelWholeDDList(List<String> ids, String reason) {

        for (String id : ids) {
            cancelWholeDD(directDebitRepository.findOne(Long.valueOf(id)),
                    DirectDebitCancellationToDoReason.valueOf(reason));
        }
    }

    public void cancelDDf(Long ddfID, DirectDebitCancellationToDoReason reason) {
        if (ddfID == null) return;
        DirectDebitFile directDebitFile = directDebitFileRepository.findOne(ddfID);

        cancelDDf(directDebitFile, reason);
    }

    public void cancelDDf(DirectDebitFile ddFile, DirectDebitCancellationToDoReason reason) {

        DirectDebitCancelationToDo directDebitCancelationToDo = new DirectDebitCancelationToDo();
        directDebitCancelationToDo.setDirectDebitFile(ddFile);
        directDebitCancelationToDo.setIgnoreDDRejectionFlow(ddFile.getIgnoreDDRejectionFlow());
        directDebitCancelationToDo.setReason(reason);

        //ACC-2858 stop hiding todos
        //ACC-2080
//        if (DateUtil.isInLastCoupleDaysOfMonth(new Date())) {
//            directDebitCancelationToDo.setHidden(true);
//        }

        directDebitCancelationToDoController.createEntity(directDebitCancelationToDo);
    }

    public void cancelOtherAutoApprovedDDs(DirectDebit directDebit) {

        logger.log(Level.SEVERE, "cancel runs for master dd : " + directDebit.getId());
        logger.log(Level.SEVERE, "auto ddf that's got accepted : " + directDebit.getAutoDdfFile().getId());

        List<DirectDebitFile> autoApprovedDDs = directDebit.getDirectDebitFiles().stream()
            .filter(ddf -> ddf.getDdMethod() == DirectDebitMethod.AUTOMATIC
                && (ddf.getDdStatus() == DirectDebitStatus.CONFIRMED || ddf.getDdStatus() == DirectDebitStatus.PENDING))
            .collect(Collectors.toList());

        for (DirectDebitFile directDebitFile : autoApprovedDDs) {
            logger.log(Level.SEVERE, "auto ddf of master : " + directDebitFile.getId());
            if (!directDebitFile.getId().equals(directDebit.getAutoDdfFile().getId())) {
                logger.log(Level.SEVERE, "auto ddf to cancel : " + directDebitFile.getId());
                cancelDDf(directDebitFile, DirectDebitCancellationToDoReason.CANCEL_OTHER_AUTO_APPROVED_DDS);
            }
        }

        DirectDebit ddInstance = directDebit;
        while (ddInstance != null && ddInstance.getImageForDD() != null) {
            cancelDDsOfImageForDD(ddInstance);
            ddInstance = ddInstance.getImageForDD();
        }

    }

    public void cancelOtherManualApprovedDDs(DirectDebit directDebit) {

        logger.log(Level.SEVERE, "cancel runs for master dd : " + directDebit.getId());
        logger.log(Level.SEVERE, "manual ddf that's got accepted : " + directDebit.getManualDdfFile().getId());

        List<DirectDebitFile> manualApprovedDDs = directDebit.getDirectDebitFiles().stream()
            .filter(ddf -> ddf.getDdMethod() == DirectDebitMethod.MANUAL
                && (ddf.getDdStatus() == DirectDebitStatus.CONFIRMED || ddf.getDdStatus() == DirectDebitStatus.PENDING))
            .collect(Collectors.toList());

        for (DirectDebitFile directDebitFile : manualApprovedDDs) {
            logger.log(Level.SEVERE, "manual ddf of master : " + directDebitFile.getId());
            if (!directDebitFile.getId().equals(directDebit.getManualDdfFile().getId())) {
                logger.log(Level.SEVERE, "manual ddf to cancel : " + directDebitFile.getId());
                cancelDDf(directDebitFile, DirectDebitCancellationToDoReason.CANCEL_OTHER_MANUAL_APPROVED_DDS);
            }
        }

        DirectDebit ddInstance = directDebit;
        while (ddInstance != null && ddInstance.getImageForDD() != null) {
            cancelDDsOfImageForDD(ddInstance);
            ddInstance = ddInstance.getImageForDD();
        }
    }


    private void cancelDDsOfImageForDD(DirectDebit directDebit) {

        DirectDebit imageForDD = directDebit.getImageForDD();
        if (imageForDD == null) return;

        DateTime switchingDate = new DateTime(directDebit.getContractPaymentTerm().getCreationDate());
        DateTime now = DateTime.now();

        if (directDebit.getCategory().equals(DirectDebitCategory.A)) {
            logger.info("DDA#" + directDebit.getId() + ", M_Status: " + directDebit.getMStatus());
            if (imageForDD.getCategory().equals(DirectDebitCategory.A)) {
                logger.info("Image DDA#" + imageForDD.getId() + ", M_Status: " + imageForDD.getMStatus());
                if (imageForDD.getMStatus().equals(DirectDebitStatus.EXPIRED)) {
                    cancelWholeDD(directDebit, DirectDebitCancellationToDoReason.SWITCHING_BANK_ACCOUNT);
                }
                if (imageForDD.getMStatus().equals(DirectDebitStatus.CONFIRMED)) {
                    handleSwitchingBankAccountRequiredPayment(directDebit);
                }
            } else if (imageForDD.getCategory().equals(DirectDebitCategory.B)) {
                logger.info("Image DDB#" + imageForDD.getId() + ", Status: " + imageForDD.getStatus());
                if (now.isBefore(switchingDate.plusMonths(1).withDayOfMonth(1).withTimeAtStartOfDay())) {
                    logger.info("Today's date < 1st of switching next month ");
                    Payment switchingMonthPayment = PaymentHelper.getDDPaymentOfCPTByDate(imageForDD.getContractPaymentTerm(), switchingDate.toDate(), Setup.getItem("TypeOfPayment", "monthly_payment"));
                    if (switchingMonthPayment != null && (switchingMonthPayment.getStatus().equals(PaymentStatus.RECEIVED) || BooleanUtils.toBoolean(switchingMonthPayment.getReplaced()))) {
                        logger.info("switching moth payment is RECEIVED or Replaced -> cancel old DD");
                        cancelWholeDD(imageForDD, DirectDebitCancellationToDoReason.SWITCHING_BANK_ACCOUNT);
                    } else {
                        logger.info("switching moth payment is neither RECEIVED nor Replaced -> do nothing");
                    }
                } else {
                    handleSwitchingBankAccountRequiredPayment(directDebit);
                }
            }
        } else if (directDebit.getCategory().equals(DirectDebitCategory.B) && (!(new DateTime(imageForDD.getStartDate()).isBefore(switchingDate.plusMonths(2).withDayOfMonth(1).withTimeAtStartOfDay())))) {
            logger.info("DDB#" + directDebit.getId() + ", Status: " + directDebit.getStatus());
            cancelWholeDD(imageForDD, DirectDebitCancellationToDoReason.SWITCHING_BANK_ACCOUNT);
        }
    }

    private void handleSwitchingBankAccountRequiredPayment(DirectDebit directDebit) {
        DirectDebit imageForDD = directDebit.getImageForDD();

        logger.info("Today's date >= 1st of switching next month -> Don't send it to bank for collection");
        Payment newDdPayment = PaymentHelper.getPaymentOfDDA(directDebit.getId());
        if (newDdPayment == null) {
            logger.info("No Payments for new DD#" + directDebit.getId() + " -> do nothing");
            return;
        }

        logger.info("Payment#" + newDdPayment.getId() + ", Status: " + newDdPayment.getStatus() + ", Replaced: " + newDdPayment.getReplaced() +
            ", Sent To Bank: " + newDdPayment.getSentToBankByMDD());

        Payment oldDdPayment;
        if (imageForDD.getCategory().equals(DirectDebitCategory.B)) {
            oldDdPayment = PaymentHelper.getPaymentOfDDB(imageForDD.getId(), newDdPayment.getDateOfPayment());
        } else {
            oldDdPayment = PaymentHelper.getPaymentOfDDA(imageForDD.getId());
        }

        if (oldDdPayment == null) {
            logger.info("No Payments for old DD#" + imageForDD.getId() + " -> do nothing");
            return;
        }

        logger.info("Payment#" + oldDdPayment.getId() + ", Status: " + oldDdPayment.getStatus() + ", Replaced: " + oldDdPayment.getReplaced() +
            ", Sent To Bank: " + oldDdPayment.getSentToBankByMDD());

        if (oldDdPayment.getStatus().equals(PaymentStatus.RECEIVED) ||
            (oldDdPayment.getStatus().equals(PaymentStatus.BOUNCED) && BooleanUtils.toBoolean(oldDdPayment.getReplaced()))) {
            cancelWholeDD(directDebit, DirectDebitCancellationToDoReason.SWITCHING_BANK_ACCOUNT);
            paymentService.setPaymentSentToBank(newDdPayment.getId());

            logger.info("cancel old DDB#" + imageForDD.getId());
            if (imageForDD.getCategory().equals(DirectDebitCategory.B)) {
                cancelWholeDD(imageForDD, DirectDebitCancellationToDoReason.SWITCHING_BANK_ACCOUNT);
            }
        } else if (oldDdPayment.getStatus().equals(PaymentStatus.BOUNCED)) {
            if (BooleanUtils.toBoolean(oldDdPayment.getSentToBankByMDD())) {
                paymentService.setPaymentSentToBank(newDdPayment.getId());
            } else {
                logger.info("cancel old DDB#" + imageForDD.getId());
                cancelWholeDD(imageForDD, DirectDebitCancellationToDoReason.SWITCHING_BANK_ACCOUNT);
                newDdPayment.setTrials(oldDdPayment.getTrials());
                newDdPayment.setReminder(oldDdPayment.getReminder());
                newDdPayment.setSentToBankByMDD(oldDdPayment.getSentToBankByMDD());
                paymentService.forceUpdatePayment(newDdPayment);

                paymentService.setPaymentSentToBank(oldDdPayment.getId());
            }
        } else {
            if (imageForDD.getCategory().equals(DirectDebitCategory.A)) {
                logger.info("check if old payment sent to bank for collection");
                if(!oldDdPayment.getSentToBankByMDD()) {
                    cancelWholeDD(imageForDD, DirectDebitCancellationToDoReason.SWITCHING_BANK_ACCOUNT);
                }
            } else {
                paymentService.setPaymentSentToBank(newDdPayment.getId());
            }
        }
    }

    public boolean validateDirectDebitCancellation(DirectDebitFile ddf) {
         return !ddfBatchForRPARepository.existsByIdsContainsAndStatus(
                 ddf.getId().toString(), DDFBatchStatus.UNDER_PROCESS);
    }

    public Page<BankDirectDebitCancelationRecord> getBankCancellationRecords(
            BankDirectDebitCancelationFile file,
            BankDirectDebitCancelationFileController.RecordMatched matched,
            Pageable pageable) {

        SelectQuery<BankDirectDebitCancelationRecord> query = new SelectQuery<>(BankDirectDebitCancelationRecord.class);
        query.leftJoin("directDebitFile");
        query.filterBy("bankDirectDebitCancelationFile", "=", file);

        if (CurrentRequest.getSearchFilter() != null)
            query.filterBy(CurrentRequest.getSearchFilter());

        if (matched == null)
            matched = BankDirectDebitCancelationFileController.RecordMatched.MATCHED;

        switch (matched) {
            case MATCHED:
                query.filterBy("directDebitFile", "IS NOT NULL", null);
                query.filterBy("directDebitFile.ddStatus", "=", DirectDebitStatus.PENDING_FOR_CANCELLATION);
                query.filterBy("status", "=", BankDirectDebitCancelationRecord.status.CONFIRMED);
                break;
            case NOT_MATCHED: // ACC-2622
                query.filterBy(
                        new SelectFilter()
                                .or("directDebitFile", "IS NULL", null)
                                .or("directDebitFile.ddStatus", "NOT IN", Arrays.asList(DirectDebitStatus.PENDING_FOR_CANCELLATION, DirectDebitStatus.CANCELED)));
                break;
            case PREV_MATCHED:
                query.filterBy("directDebitFile", "IS NOT NULL", null);
                query.filterBy(new SelectFilter("directDebitFile.ddStatus", "=", DirectDebitStatus.CANCELED)
                        .or(new SelectFilter("confirmed", "=", true)
                                .and("status", "=", BankDirectDebitCancelationRecord.status.REJECTED)));
                break;
            case MATCHED_AND_REJECTED:
                query.filterBy("directDebitFile", "IS NOT NULL", null);
                query.filterBy("directDebitFile.ddStatus", "=", DirectDebitStatus.PENDING_FOR_CANCELLATION);
                query.filterBy("status", "=", BankDirectDebitCancelationRecord.status.REJECTED);
                query.filterBy("confirmed", "=", false);
                break;
        }

        if (pageable == null) {
            return new PageImpl(query.execute());
        } else {
            return query.execute(pageable);
        }
    }

    public void cancelDdsAndStopRejectionFlow(
            ContractPaymentTerm contractPaymentTerm,
            DirectDebitCancellationToDoReason reason,
            List<DirectDebitStatus> ignoredStatuses, List<ContractPayment> contractPayments) {

        SelectQuery<DirectDebit> query = new SelectQuery<>(DirectDebit.class);
        query.filterBy("contractPaymentTerm.id", "=", contractPaymentTerm.getId());
        query.filterBy("status", "not in", ignoredStatuses);
        query.filterBy("MStatus", "not in", ignoredStatuses);

        List<DirectDebit> dds = query.execute();
        logger.info("dds size: " + dds.size());

        cancelDdsAndStopRejectionFlow(reason, dds);

        // ACC-9222 When switch from Paying via DD to CC -> create All Future Payments covered by DD as Plans
        Setup.getApplicationContext()
                .getBean(ClientPayingViaCreditCardService.class)
                .handleFuturePaymentsAlreadyCreated(contractPaymentTerm, dds, contractPayments);
    }

    public void cancelDdsAndStopRejectionFlow(
            DirectDebitCancellationToDoReason reason,
            List<DirectDebit> dds) {

        if (dds.isEmpty()) return;

        dds.forEach(dd -> {

            if (dd.getDirectDebitRejectionToDo() != null) {
                DirectDebitRejectionToDo toDo = directDebitRejectionToDoRepository.findOne(
                        dd.getDirectDebitRejectionToDo().getId());
                if (!toDo.isStopped() && !toDo.isCompleted()) {
                    logger.log(Level.INFO, "DirectDebtRejectionToDo id : {0}", toDo.getId());
                    toDo.setStopped(true);
                    toDo.setDontSendDdMessage(true);
                    directDebitRejectionToDoRepository.save(toDo);
                }
            }

            logger.log(Level.INFO, "cancel dd  id : {0}", dd.getId());
            cancelWholeDD(dd, reason);
        });
    }

    public void maidVisaCancelDds(Contract entity) {
        DirectDebitRepository directDebitRepository = Setup.getRepository(DirectDebitRepository.class);
        BouncingFlowService bouncingFlowService = Setup.getApplicationContext().getBean(BouncingFlowService.class);

        if (bouncingFlowService.doWeWantMoneyFromClient(entity, true, null)) return;

        logger.info("no required future payments -> cancel DDs");
        List<DirectDebit> dds = directDebitRepository.findByContractPaymentTerm_Contract(entity);
        //ACC-3844
        DirectDebitService ddService = Setup.getApplicationContext().getBean(DirectDebitService.class);

        dds = dds.stream().filter(d -> !ddService.ddHasAllPastNonMonthlyPayment(d))
                .collect(Collectors.toList());
        if (dds.isEmpty()) return;

        Setup.getApplicationContext()
                .getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "cancelWholeDDList_" + entity.getId(),
                        "accounting",
                        "directDebitCancellationService",
                        "cancelWholeDDList")
                        .withRelatedEntity("Contract", entity.getId())
                        .withParameters(
                                new Class[] {List.class, String.class},
                                new Object[] {dds.stream().map(d -> String.valueOf(d.getId())).collect(Collectors.toList()),
                                        DirectDebitCancellationToDoReason.CONTRACT_CANCELLATION.toString()})
                        .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                        .withDelay(120L * 1000L)
                        .build());
    }
}