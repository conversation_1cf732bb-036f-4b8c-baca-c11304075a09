package com.magnamedia.service;

import com.google.api.client.util.ArrayMap;
import com.magnamedia.controller.ClientRefundTodoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.entity.workflow.PaymentRequestPurpose;
import com.magnamedia.extra.CcNotificationTemplateCode;
import com.magnamedia.extra.MvNotificationTemplateCode;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.*;
import com.magnamedia.workflow.type.ClientRefundPaymentMethod;
import com.magnamedia.workflow.type.ClientRefundRequestType;
import org.joda.time.DateTime;
import org.joda.time.Hours;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on Jan 07, 2020
 *          ACC-1135
 */

@Service
public class ClientMessagingAndRefundService {

    private static final Logger logger = Logger.getLogger(ClientMessagingAndRefundService.class.getName());

    @Autowired
    private MessagingService notificationService;
    @Autowired
    private ClientRefundTodoRepository clientRefundTodoRepo;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private ClientRefundService clientRefundService;

    public Map<String, Boolean> partialRefundProcess(
        Contract contract, Client client, Double amountToRefund, Boolean clientInformedAboutRefund, Payment p) {
        Double roundedAmount = Math.ceil(amountToRefund);
        logger.log(Level.SEVERE, "partial refund process");
        Map<String, Boolean> map = new HashMap();
        map.put("refundSentToExpensify", false);
        map.put("clientInformedAboutRefund", clientInformedAboutRefund);

        if (roundedAmount == 0D) return map;

        // ACC-1522
        if (contract.getStatus().equals(ContractStatus.EXPIRED)) return map;

        // Contract is cancelled -> send request to expensify then inform client about refund
        if (contract.getStatus().equals(ContractStatus.CANCELLED)) {
            ClientRefundToDo clientRefundToDo = addClientRefund(contract, client, roundedAmount, ClientRefundRequestType.CONTRACT_CANCELLATION_REFUND, 
                    AccountingModule.PAYMENT_REQUEST_PURPOSE_PARTIAL_REFUND, p, "Cancellation refund flow");
            
            if (clientRefundToDo == null)
                return map;

            // request to expensify sent
            map.put("refundSentToExpensify", true);

            // Send 8.1.1
            // ACC-2275
            if ((contract.getReasonOfTerminationList() == null || !contract.getReasonOfTerminationList().getCode().equalsIgnoreCase(AccountingModule.PICKLIST_ITEM_CONTRACT_TERMINATION_REASON_FROZEN_CONTRACT_CODE))
                    && !clientInformedAboutRefund) {
                map.put("clientInformedAboutRefund", scheduleSendingSms811ToClient(clientRefundToDo));
            }
        } else {
            // Contract is not cancelled & not special case & not client is't informed and termination reason <> '' -> inform client about refund
            // ACC-2275
            if ((contract.getReasonOfTerminationList() == null || !contract.getReasonOfTerminationList().getCode().equalsIgnoreCase(AccountingModule.PICKLIST_ITEM_CONTRACT_TERMINATION_REASON_FROZEN_CONTRACT_CODE))
                    && !contract.getSpecialCase() && !clientInformedAboutRefund) {
                map.put("clientInformedAboutRefund", sendSms813ToClient(roundedAmount, contract));
            }
        }

        logger.info(map.entrySet() + "");
        logger.info(map.values() + "");
        return map;
    }

    public Map<String, Object> fullRefundProcess(
            Contract contract, Client client, Double amountToRefund,
            Boolean clientInformedAboutRefund, Payment p) {

        return fullRefundProcess(contract, client, amountToRefund, clientInformedAboutRefund, false, p);
    }

    public Map<String, Object> fullRefundProcess(
            Contract contract, Client client, Double amountToRefund,
            Boolean clientInformedAboutRefund, boolean groupInOneMessage, Payment p) {
        Map<String, Object> map = new HashMap();
        map.put("refundSentToExpensify", false);
        map.put("clientInformedAboutRefund", clientInformedAboutRefund);

        if (amountToRefund == null || amountToRefund == 0D) return map;

        // ACC-1522
        if (contract.getStatus().equals(ContractStatus.EXPIRED)) return map;

        // if Contract is cancelled
        if (contract.getStatus().equals(ContractStatus.CANCELLED)) {

            ClientRefundToDo clientRefundToDo = addClientRefund(contract, client, amountToRefund, ClientRefundRequestType.CONTRACT_CANCELLATION_REFUND,
                    AccountingModule.PAYMENT_REQUEST_PURPOSE_FULL_REFUND, p,
                contract.isMaidVisa() ? "Maidvisa sub-accounting flow" : "Maids.cc Contract Cancellation flow");
            
            if (clientRefundToDo == null)
                return map;

            // request to expensify sent
            map.put("refundSentToExpensify", true);

            // Send 8.1.2

            // ACC-2275
            if ((contract.getReasonOfTerminationList() == null || !contract.getReasonOfTerminationList().getCode().equalsIgnoreCase(AccountingModule.PICKLIST_ITEM_CONTRACT_TERMINATION_REASON_FROZEN_CONTRACT_CODE))
                    && !clientInformedAboutRefund) {
                if (contract.isMaidVisa())
                    map.put("clientInformedAboutRefund", scheduleSendingSms811ToClient(clientRefundToDo));
                else {
                    map.put("clientInformedAboutRefund", sendSms812ToClient(amountToRefund, contract, groupInOneMessage));
                    if (groupInOneMessage) map.put("amountToRefund", amountToRefund);
                }
            }
        }
        // if Contract is not cancelled
        else {
            // ACC-2275
            if ((contract.getReasonOfTerminationList() == null || !contract.getReasonOfTerminationList().getCode().equalsIgnoreCase(AccountingModule.PICKLIST_ITEM_CONTRACT_TERMINATION_REASON_FROZEN_CONTRACT_CODE))
                    && !contract.getSpecialCase() && !clientInformedAboutRefund) {
                map.put("clientInformedAboutRefund", sendSms813ToClient(amountToRefund, contract));
            }
        }

        logger.info(map.entrySet() + "");
        logger.info(map.values() + "");
        return map;
    }

    @Transactional
    public ClientRefundToDo addClientRefund(
            Contract contract, Client client, Double paymentAmount, ClientRefundRequestType requestType,
            String paymentRequestPurposeParameter, Payment relatedPayment, String flowTriggered) {

        return addClientRefund(contract, client, paymentAmount, requestType,
                paymentRequestPurposeParameter, relatedPayment, flowTriggered, null);
    }

    @Transactional
    public ClientRefundToDo addClientRefund(
        Contract contract, Client client, Double paymentAmount, ClientRefundRequestType requestType,
        String paymentRequestPurposeParameter, Payment relatedPayment, String flowTriggered,
        Map<String, Object> m) {
    
        logger.info("refunding related payment: " + (relatedPayment == null ? null : relatedPayment.getId()));

        if (relatedPayment != null && clientRefundTodoRepo.existsByRelatedPaymentId(relatedPayment.getId())) {
            logger.info("already refunded payment id: " + relatedPayment.getId() + " -> exiting");
            return null;
        }

        // post payment request to expensify
        String paymentRequestPurposeName = Setup.getParameter(Setup.getCurrentModule(), paymentRequestPurposeParameter);
        
        List<PaymentRequestPurpose> paymentRequestPurposeList =
                Setup.getRepository(PaymentRequestPurposeRepository.class).findByForClientAndNameEquals(true, paymentRequestPurposeName);

        if (paymentRequestPurposeList == null || paymentRequestPurposeList.isEmpty()) {
            logger.log(Level.SEVERE, "No Payment Request Purpose with this name: " + paymentRequestPurposeName, new IndexOutOfBoundsException());
            return null;
        }

        client.setContractPaymentTermInfo(contract);
        
        PaymentRequestPurpose paymentRequestPurpose = paymentRequestPurposeList.get(0);

        ClientRefundToDo clientRefundToDo = new ClientRefundToDo();
        clientRefundToDo.setAutomaticRefund(true);
        clientRefundToDo.setFlowTriggered(flowTriggered);
        clientRefundToDo.setPurpose(paymentRequestPurpose);
        clientRefundToDo.setRequestType(requestType);
        clientRefundToDo.setContract(contract);
        clientRefundToDo.setClient(client);
        clientRefundToDo.setMethodOfPayment(ClientRefundPaymentMethod.BANK_TRANSFER);

        if (relatedPayment == null) {
            if (paymentRequestPurpose.getTypeOfPayment() != null && paymentRequestPurpose.getTypeOfPayment().hasTag("refund_for")) {
                List<Payment> payments = paymentRepository.findLastPaymentPaidByContractAndPaymentType(
                        contract.getId(), paymentRequestPurpose.getTypeOfPayment().getTagValue("refund_for").getValue());
                if (!payments.isEmpty()) {
                    relatedPayment = payments.get(0);
                }
            }
        }

        if (relatedPayment != null && relatedPayment.getMethodOfPayment().equals(PaymentMethod.CARD)) {
            clientRefundToDo.setMethodOfPayment(ClientRefundPaymentMethod.CREDIT_CARD);
            List<ContractPaymentConfirmationToDo> toDos = Setup.getRepository(ContractPaymentConfirmationToDoRepository.class)
                    .findTodosByPayment(relatedPayment.getId());
            if (toDos.size() == 1) {
                clientRefundToDo.setTransferReference(toDos.get(0).getTransferReference());
            }
        }

        clientRefundToDo.setAmount(paymentAmount);
        clientRefundToDo.setIban(client.getClientIBAN());
        clientRefundToDo.setAccountName(client.getAccountName());
        clientRefundToDo.setIgnoreRequestedByConstraint(true);
        clientRefundToDo.setRelatedPaymentId(relatedPayment == null ? null : relatedPayment.getId());

        // ACC-7120
        if (m != null) {
            if (m.containsKey("howMuchWeRefundOption")) {
                clientRefundToDo.setHowMuchWeRefundOption((PicklistItem) m.get("howMuchWeRefundOption"));
            }

            if (m.containsKey("pendingSalaryRecord")) {
                clientRefundToDo.setPendingSalaryRecord((java.sql.Date) m.get("pendingSalaryRecord"));
            }

            if (m.containsKey("housemaidPayrollLogId")) {
                clientRefundToDo.setHousemaidPayrollLogId((Long) m.get("housemaidPayrollLogId"));
            }

            if (m.containsKey("requiredPayments")) {
                clientRefundToDo.setRequiredPayments((List<Payment>) m.get("requiredPayments"));
            }

            if (m.containsKey("conditionalRefund")) {
                clientRefundToDo.setConditionalRefund((Boolean) m.getOrDefault("conditionalRefund", false));
            }
        }

        Setup.getApplicationContext()
                .getBean(ClientRefundTodoController.class)
                .createEntity(clientRefundToDo);

        return clientRefundToDo;
    }

    @Transactional
    public void addConditionalClientRefund(
            Contract contract, Client client, Double paymentAmount, ClientRefundRequestType requestType,
            String paymentRequestPurposeParameter, Payment p, String flowTriggered) {

        addClientRefund(contract, client, paymentAmount, requestType,
                paymentRequestPurposeParameter, p, flowTriggered,
                new HashMap<String, Object>() {{
                    put("conditionalRefund", true);
                    put("requiredPayments", new ArrayList<>(Collections.singletonList(p)));
                }});
    }


    private Boolean scheduleSendingSms811ToClient(ClientRefundToDo clientRefundToDo) {
        logger.log(Level.INFO, "schedule sendOnClientRefundApproval");
        clientRefundToDo.addSmsTemplateToSendOnConfirmation(
            CcNotificationTemplateCode.CC_ACCOUNTING_OWE_MONEY_TO_CLIENT_8_1_1_NOTIFICATION.toString());
        clientRefundTodoRepo.save(clientRefundToDo);
        logger.log(Level.INFO, "end scheduling sendOnClientRefundApproval");
        return true;
    }

    public void sendOnClientRefundApproval(ClientRefundToDo clientRefundToDo) {
        logger.log(Level.INFO, "start sendOnClientRefundApproval");

        for (String template : clientRefundToDo.getSmsTemplatesToSendOnConfirmationAsList()) {
            Map<String, String> paramValues = new HashMap<>();
            paramValues.put("remaining_balance", clientRefundToDo.getAmount() != null ?
                    String.valueOf(clientRefundToDo.getAmount().intValue()) : "");

            clientRefundService.sendNotification(clientRefundToDo.getContract(), template, paramValues, true);

        }

        logger.log(Level.INFO, "end sendOnClientRefundApproval");
    }

    public Boolean sendSms812ToClient(Double amountOfPayment, Contract contract, boolean groupInOneMessage) {
        logger.log(Level.INFO, "start sendSms812ToClient");

        if (!groupInOneMessage) {
            Map<String, String> paramValues = new HashMap<>();
            paramValues.put("amount", amountOfPayment != null ? String.valueOf(amountOfPayment.intValue()) : "");

            String contentTemplate = contract.getContractProspectType().getCode().equals(
                    PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE)) ?
                    CcNotificationTemplateCode.CC_ACCOUNTING_NOT_OWED_MONEY_FROM_CLIENT_8_1_2_NOTIFICATION.toString() :
                    MvNotificationTemplateCode.MV_ACCOUNTING_NOT_OWED_MONEY_FROM_CLIENT_8_1_2_NOTIFICATION.toString();

            clientRefundService.sendNotification(contract, contentTemplate, paramValues, false);
        }

        logger.log(Level.INFO, "end sendSms812ToClient");
        return true;
    }

    private Boolean sendSms813ToClient(
            Double amountOfPayment,
            Contract contract) {

        logger.log(Level.INFO, "start sendSms813ToClient");

        Map<String, String> paramValues = new HashMap<>();
        paramValues.put("scheduled_termination_date", contract.getScheduledDateOfTermination() != null ?
                DateUtil.formatFullDate(contract.getScheduledDateOfTermination()) : "");
        paramValues.put("amount", amountOfPayment != null ? String.valueOf(amountOfPayment.intValue()) : "");

        String contentTemplate = contract.getContractProspectType().getCode().equals(
                PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE)) ?
                CcNotificationTemplateCode.CC_ACCOUNTING_WRONGLY_CHARGED_MONEY_ON_CLIENT_8_1_3_NOTIFICATION.toString() :
                MvNotificationTemplateCode.MV_ACCOUNTING_WRONGLY_CHARGED_MONEY_ON_CLIENT_8_1_3_NOTIFICATION.toString();

        clientRefundService.sendNotification(contract, contentTemplate, paramValues, false);

        logger.log(Level.INFO, "end sendSms813ToClient");
        return true;
    }

    // ACC-1689
    public void refundAfterReplacement(
        Contract contract, Double amount, String refundPurpose, String flowTriggered) {

        addClientRefund(contract, contract.getClient(), amount, ClientRefundRequestType.ERP,
            refundPurpose,null, flowTriggered);
    }

    //ACC-3843
    public Map<String, Object> checkProratedContractConditions(
            Contract contract,
            Date dateOfTermination) {

        Map<String, Object> map = new ArrayMap<>();
        boolean returnedMaidPreviousMonth = false;
        boolean clientCancelledWithinXDays = false;

        logger.info("Contract id: " + contract.getId());

        if (contract.getIsProRated()) {
            if (contract.getHousemaid() == null && !contract.getReplacements().isEmpty() &&
                    dateOfTermination != null) {

                Replacement lastReplacement = contract.getReplacements().get(0);
                DateTime lastReplacementDate = new DateTime(lastReplacement.getCreationDate());
                DateTime terminationDatePreviousMonth = new DateTime(dateOfTermination).minusMonths(1);

                if (lastReplacement.getNewHousemaid() == null &&
                        lastReplacementDate.getYear() == terminationDatePreviousMonth.getYear() &&
                        lastReplacementDate.getMonthOfYear() == terminationDatePreviousMonth.getMonthOfYear()) {

                    logger.info("Client returned the maid in the previous month");
                    returnedMaidPreviousMonth = true;
                }
            }

            if (dateOfTermination != null) {
                int passedHours = Hours.hoursBetween(new DateTime(dateOfTermination).withDayOfMonth(1).withTimeAtStartOfDay(),
                        new DateTime(dateOfTermination)).getHours();

                logger.info("Passed Hours: " + passedHours);

                if (passedHours < 24 * Integer.parseInt(Setup.getParameter(
                        Setup.getCurrentModule(), AccountingModule.PARAMETER_CC_CONTRACT_PRORATED_CHARGE_FREE_DAYS))) {

                    logger.info("Client cancelled within the first X days of the month");
                    clientCancelledWithinXDays = true;
                }
            }
        }

        logger.info("returnedMaidPreviousMonth: " + returnedMaidPreviousMonth);
        logger.info("clientCancelledWithinXDays: " + clientCancelledWithinXDays);

        map.put("returnedMaidPreviousMonth", returnedMaidPreviousMonth);
        map.put("clientCancelledWithinXDays", clientCancelledWithinXDays);

        return map;
    }
    
    //ACC-3843
    public void sendProratedContractSMSToClient(
            Contract contract, boolean clientCancelledWithinXDays,
            boolean returnedMaidPreviousMonth, boolean oweRefundToClient) {
        
        logger.info("sendProratedContractSMSToClient contract id " + contract.getId());
        List<Payment> currentMonthPayments = paymentRepository
                .findMonthlyPaymentByContractAndDateOfPaymentBetween(contract,
                        new DateTime().dayOfMonth().withMinimumValue().withTimeAtStartOfDay().toDate(),
                        new DateTime().dayOfMonth().withMaximumValue().toDate());
        
        String templateName = "";
        if (!currentMonthPayments.isEmpty()) {
            Payment currentMonthPayment = currentMonthPayments.get(0);
            logger.info("payment id: " + currentMonthPayment.getId());
            logger.info("payment status: " + currentMonthPayment.getStatus());
            
            boolean isPaymentUnderProcessing = currentMonthPayment.getSentToBankByMDD();
            logger.info("isPaymentUnderProcessing: " + isPaymentUnderProcessing);
            if (clientCancelledWithinXDays) {
                if (isPaymentUnderProcessing && oweRefundToClient &&
                        !currentMonthPayment.getStatus().equals(PaymentStatus.BOUNCED) &&
                        !currentMonthPayment.getStatus().equals(PaymentStatus.RECEIVED)) {

                    templateName = CcNotificationTemplateCode.CC_PAYMENT_UNDER_PROCESSING_8_1_6_NOTIFICATION.toString();
                } else if (currentMonthPayment.getStatus().equals(PaymentStatus.RECEIVED) && oweRefundToClient) {

                    templateName = CcNotificationTemplateCode.CC_PAYMENT_RECEIVED_8_1_6_NOTIFICATION.toString();
                } else {
                    templateName =
                        CcNotificationTemplateCode.CC_PAYMENT_NOT_RECEIVED_OR_BOUNCED_8_1_6_NOTIFICATION.toString();
                }

            } else if (returnedMaidPreviousMonth) {
                if (isPaymentUnderProcessing && oweRefundToClient &&
                        !currentMonthPayment.getStatus().equals(PaymentStatus.BOUNCED) &&
                        !currentMonthPayment.getStatus().equals(PaymentStatus.RECEIVED)) {

                    templateName = CcNotificationTemplateCode.CC_PAYMENT_UNDER_PROCESSING_8_1_5_NOTIFICATION.toString();
                } else if (currentMonthPayment.getStatus().equals(PaymentStatus.RECEIVED) && oweRefundToClient) {

                    templateName = CcNotificationTemplateCode.CC_PAYMENT_RECEIVED_8_1_5_NOTIFICATION.toString();
                } else {
                    templateName =
                        CcNotificationTemplateCode.CC_PAYMENT_NOT_RECEIVED_OR_BOUNCED_8_1_5_NOTIFICATION.toString();
                }
            }

            logger.log(Level.INFO,"TemplateName {0}", templateName);

            notificationService.sendMessageToClient(contract,
                    new ArrayMap<>(),
                    new ArrayMap<>(),
                    contract.getId(),
                    contract.getEntityType(),
                    TemplateUtil.getTemplate(templateName));
        }
    }
}