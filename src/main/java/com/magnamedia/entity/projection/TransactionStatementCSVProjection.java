package com.magnamedia.entity.projection;

import com.magnamedia.module.type.VatType;
import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on June 30, 2020
 * Jirra ACC-2154
 */
public interface TransactionStatementCSVProjection {

    Double getAmount();

    @Value("#{(target.getRevenue()!=null)? target.getRevenue().getCode(): ''}")
    String getRevenueCode();

    Double getVatAmount();

    VatType getVatType();

    String getDescription();

    @Value("#{(target.getId()!=null) ? 'trans-' + target.getRevenue().getCode(): ''}")
    String getTransaction();

}
