package com.magnamedia.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.utils.PdfMerger;
import com.itextpdf.layout.Document;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.JwtSecured;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.*;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.mail.Recipient;
import com.magnamedia.core.mail.TextEmail;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.PushNotificationRepository;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.core.type.NotificationLocation;
import com.magnamedia.entity.*;
import com.magnamedia.entity.ccapp.CcAppTracking.CcAppAction;
import com.magnamedia.entity.projection.DirectDebitSalesProjection;
import com.magnamedia.entity.projection.PaymentSalesProjection;
import com.magnamedia.entity.workflow.*;
import com.magnamedia.extra.*;
import com.magnamedia.extra.DTOs.AddMultipleDirectDebitsDTO;
import com.magnamedia.extra.DTOs.DirectDebitDTO;
import com.magnamedia.extra.annotations.UsedBy;
import com.magnamedia.helper.ContractPaymentTermHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PdfHelper;
import com.magnamedia.helper.UaePhoneNormlizer;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.*;
import com.magnamedia.workflow.type.ClientRefundRequestType;
import com.magnamedia.workflow.type.ClientRefundStatus;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.joda.time.Months;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.magnamedia.controller.DirectDebitFileController.DD_FORM_DOWNLOAD_POSITION;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on Feb 29, 2020
 *          ACC-1435
 *
 */

@RestController
@RequestMapping("/contractpaymentterm")
public class ContractPaymentTermController extends BaseRepositoryController<ContractPaymentTerm> {

    public static final String FILE_TAG_PAYMENTS_RECEIPT = "contract_payments_receipt";
    public static final String WORD_TEMPLATE_CODE_DD_CACELLATION = "contract_direct_debit_cancellation";
    public static final int DEFAILT_CONTRACT_DURATION = 48; // in months
    public static final String PAYMENT_DESCRIPTION_AGENCY_FEE = "Agency Fee";

    // bank photos tags
    public static final String FILE_TAG_BANK_INFO_EID = "bank_info_eid";
    public static final String FILE_TAG_BANK_INFO_IBAN = "bank_info_iban";
    public static final String FILE_TAG_BANK_INFO_ACCOUNT_NAME = "bank_info_account_name";
    public static final String FILE_TAG_BANK_INFO_PENDING_OCR = "pending_ocr";

    // ACC-2860
    public static final String FILE_TAG_PAPER_MODE_BANK_INFO_EID = "paper_mode_bank_info_eid";
    public static final String FILE_TAG_PAPER_MODE_BANK_INFO_IBAN = "paper_mode_bank_info_iban";
    public static final String FILE_TAG_PAPER_MODE_BANK_INFO_ACCOUNT_NAME = "paper_mode_bank_info_account_name";
    public static final String FILE_TAG_PAPER_MODE_BANK_INFO_PENDING_OCR = "paper_mode_bank_info_pending_ocr";

    public static final String FILE_TAG_POSTPONED_SIGNATURE = "postponed_signature_";

    public static final String OCR_EID_PATTERN = "[7][8][4][-][0-9]{4}[-][0-9]{7}[-][0-9]";
    public static final String OCR_EID_NAME_PATTERN = "Name: ";
    public static final String OCR_EID_NATIONALITY_PATTERN = "Nationality: ";
    public static final String OCR_EID_SEX_PATTERN = "Sex: ";
    public static final String OCR_IBAN_PATTERN = "AE[\\d\\s]{19,}"; // SD-41936

    public static final String ACC_DELETE_CREDIT_CARD_TOKEN_POSITION = "delete_credit_card_token_position";

    @Autowired
    private ContractPaymentTermRepository contractPaymentTermRep;
    @Autowired
    private ContractPaymentRepository contractPaymentRep;
    @Autowired
    private ContractRepository contractRep;
    @Autowired
    private HousemaidRepository housemaidRep;
    @Autowired
    private DirectDebitRepository directDebitRepository;
    @Autowired
    private ClientRepository clientRepository;
    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;
    @Autowired
    private ReplacementRepository replacementRep;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private AccountingEntityPropertyRepository accountingEntityPropertyRepository;
    @Autowired
    private AttachementRepository attachementRepository;
    @Autowired
    private PicklistItemRepository picklistItemRepository;
    @Autowired
    private DirectDebitSignatureApprovalLinkRepository directDebitSignatureApprovalLinkRepository;
    @Autowired
    private BackgroundTaskService backgroundTaskService;
    @Autowired
    private DirectDebitController directDebitController;
    @Autowired
    private Utils utils;
    @Autowired
    private SwitchingBankAccountService switchingBankAccountService;
    @Autowired
    private InterModuleConnector moduleConnector;
    @Autowired
    private SwitchingNationalityService switchingNationalityService;
    @Autowired
    private DirectDebitSignatureService directDebitSignatureService;
    @Autowired
    private OecAmendDDsService oecAmendDDsService;
    @Autowired
    private DirectDebitService directDebitService;
    @Autowired
    private ContractPaymentTermServiceNew contractPaymentTermServiceNew;
    @Autowired
    private ContractPaymentTermHelper contractPaymentTermHelper;
    @Autowired
    private QueryService queryService;
    @Autowired
    private OneMonthAgreementFlowService oneMonthAgreementFlowService;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private FlowProcessorService flowProcessorService;
    @Autowired
    private CCAppContentService ccAppContentService;
    @Autowired
    private ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository;
    @Autowired
    private ContractPaymentWrapperRepository contractPaymentWrapperRepository;
    @Autowired
    private MessagingService messagingService;
    @Autowired
    private DisablePushNotificationRepository disablePushNotificationRepository;
    @Autowired
    private DirectDebitGenerationPlanRepository directDebitGenerationPlanRepository;
    @Autowired
    private ContractPaymentTypeRepository contractPaymentTypeRepository;
    @Autowired
    private DirectDebitCancellationService directDebitCancellationService;
    @Autowired
    private ContractPaymentConfirmationToDoService contractPaymentConfirmationToDoService;
    @Autowired
    private CalculateDiscountsWithVatService calculateDiscountsWithVatService;
    @Autowired
    private AccountingAPILogService accountingAPILogService;
    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public BaseRepository<ContractPaymentTerm> getRepository() {
        return contractPaymentTermRep;
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','getContractPaymentTermByContractWithCashPayments')")
    @RequestMapping(value = "/getcontractpaymenttermbycontractwithcashpayments/{id}", method = RequestMethod.GET)
    public ResponseEntity<?> getContractPaymentTermByContractWithCashPayments(
            @PathVariable("id") Contract contract) {

        Map result = contractPaymentTermHelper.getActiveContractPaymentTermByContract(contract);
        if (result == null) return new ResponseEntity("", HttpStatus.OK);

        ContractPaymentTerm contractPaymentTerm = (ContractPaymentTerm) result.get("contractPaymentTerm");
        List<ContractPayment> initialPayments = contractPaymentRep.findCashByContractPaymentTermAndPaymentType(
                contractPaymentTerm, null);
        boolean hasStoredPayments = contractPaymentRep.existsByContractPaymentTerm(contractPaymentTerm);

        if (initialPayments.isEmpty() && !hasStoredPayments)
            initialPayments = (List<ContractPayment>)contractPaymentTermServiceNew
                    .getDefaultInitialPayments(contractPaymentTerm, new HashMap<>()).get("payments");

        result.put("payments", initialPayments);
        result.put("cashPayments", initialPayments.stream()
                .filter(cp -> DateUtil.isSameDate(cp.getDate(), contract.getStartOfContract()))
                .collect(Collectors.groupingBy(ContractPayment::getPaymentType)));
        result.put("hasStoredPayments", hasStoredPayments);

        return new ResponseEntity(contractPaymentTermHelper.projectResultMap(result), HttpStatus.OK);
    }


    @PreAuthorize("hasPermission('ContractPaymentTerm','getContractPaymentTermByContractWithDirectDebitPayments')")
    @GetMapping(value = "/getcontractpaymenttermbycontractwithdirectdebitpayments/{id}")
    public Map<?, ?> getContractPaymentTermByContractWithDirectDebitPaymentsApi(
            @PathVariable("id") Contract contract) {

        Map result = contractPaymentTermHelper.getActiveContractPaymentTermByContract(contract);
        if (result == null) return null;

        ContractPaymentTerm cpt = (ContractPaymentTerm) result.get("contractPaymentTerm");
        result.putAll(contractPaymentTermServiceNew
                .getContractPaymentTermByContractWithDirectDebitPayments(cpt));
        result.put("isOneMonthAgreement", cpt.getContract().isOneMonthAgreement());
        return contractPaymentTermHelper.projectResultMap(result);
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','getContractPaymentTermInfo')")
    @RequestMapping(value = "/getcontractpaymentterminfo/{id}", method = RequestMethod.GET)
    public ResponseEntity<?> getContractPaymentTermInfo(@PathVariable("id") Contract contract) {
        Map result = contractPaymentTermHelper.getActiveContractPaymentTermByContract(contract);
        if (result == null)
            return new ResponseEntity("", HttpStatus.OK);

        ContractPaymentTerm contractPaymentTerms = (ContractPaymentTerm) result.get("contractPaymentTerm");
        List<ContractPayment> payments = contractPaymentRep.findByContractPaymentTerm(contractPaymentTerms);
        List<DirectDebit> directDebits = directDebitRepository.findByContractPaymentTerm(contractPaymentTerms);
        result.put("payments", payments);
        result.put("directDebits", directDebits);

        return new ResponseEntity(contractPaymentTermHelper.projectResultMap(result), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','getNewDDInfo')")
    @RequestMapping(value = "/getnewddInfo", method = RequestMethod.GET)
    public Map getNewDDInfo(
            @RequestParam Long contractId,
            @RequestParam(name = "startDate", required = false) String startDateStr) {

        //ACC-4827
        Date startDate;
        if (startDateStr != null)
            startDate = new DateTime(startDateStr).withTimeAtStartOfDay().toDate();
        else
            startDate = new DateTime().plusMonths(1).withDayOfMonth(1).withTimeAtStartOfDay().toDate();

        Map<String, Object> result = new HashMap<>();
        Contract contract = contractRep.findOne(contractId);
        Map contractInfo = contractPaymentTermHelper.getActiveContractPaymentTermByContract(contract);
        if (contractInfo == null)
            throw new RuntimeException("There is no active Contract Payment Term");

        ContractPaymentTerm contractPaymentTerm = (ContractPaymentTerm) contractInfo.get("contractPaymentTerm");
        double suggestedDiscountedAmount;
        if (calculateDiscountsWithVatService.getMonthlyPayment(contractPaymentTerm, startDate) == 0 ||
                contractPaymentTerm.getDiscount() > calculateDiscountsWithVatService.getMonthlyPayment(contractPaymentTerm, startDate))
            suggestedDiscountedAmount = 0;
        else
            suggestedDiscountedAmount = calculateDiscountsWithVatService.getDiscountedMonthlyPayment(contractPaymentTerm, startDate);

        result.put("startDate", startDate);
        result.put("suggestedAmount", calculateDiscountsWithVatService.getAmountOfMonthlyPaymentAtTimeWithDiscounts(contractPaymentTerm, new LocalDate(startDate)).get("amount"));
        result.put("discountStartDate", new DateTime(calculateDiscountsWithVatService
                .getDiscountStartDateInMillis(contractPaymentTerm))
                .withDayOfMonth(1).toDate());
        result.put("suggestedDiscountedAmount", suggestedDiscountedAmount);
        result.put("discountedAmount", contractPaymentTerm.getDiscount());
        result.put("discountEffectiveAfter", contractPaymentTerm.getDiscountEffectiveAfter());
        result.put("contract", contract);
        return contractPaymentTermHelper.projectResultMap(result);
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','addNewDD')")
    @RequestMapping(value = "/addnewdd", method = RequestMethod.GET)
    @Transactional
    public ResponseEntity<?> addNewDDAPI(
            @RequestParam("contractId") Contract contract,
            @RequestParam("fromDate") @org.springframework.format.annotation.DateTimeFormat(pattern = "yyyy-MM-dd") Date fromDate,
            @RequestParam("toDate") @org.springframework.format.annotation.DateTimeFormat(pattern = "yyyy-MM-dd") Date toDate,
            @RequestParam(value = "additionalDiscount", required = false) Double additionalDiscount,
            @RequestParam(value = "notes", required = false) String notes,
            @RequestParam(value = "attachmentId", required = false) Long attachmentId,
            @RequestParam(value = "amount", required = false) Double amount,
            @RequestParam(value = "suggestedAmount", required = false) Double suggestedAmount,
            @RequestParam(value = "directDebitType", required = false, defaultValue = "MONTHLY") DirectDebitType directDebitType,
            @RequestParam(value = "oneTimePaymentType", required = false) PicklistItem oneTimePaymentType,
            @RequestParam(value = "useOldSignatures", required = false, defaultValue = "false") boolean useOldSignatures,
            @RequestParam(value = "replaceOfPaymentId", required = false) Payment replaceOfPayment,
            @RequestParam(value = "isGeneratingPaymentFromOldCCReplacement", required = false, defaultValue = "false") boolean isGeneratingPaymentFromOldCCReplacement

    ) throws Exception {
        return ResponseEntity.ok(contractPaymentTermServiceNew.addNewDD(contract, fromDate, toDate, additionalDiscount, notes,
                attachmentId, amount, suggestedAmount, directDebitType, oneTimePaymentType,
                useOldSignatures, replaceOfPayment, isGeneratingPaymentFromOldCCReplacement, true, true, null, true));
    }

    // for VPM-1963 do not edit it.
    @Transactional
    public void addNewDD(
            Long contractID, Date fromDate, Date toDate, Double additionalDiscount, String notes,
            Long attachmentId, Double amount, Double suggestedAmount, String directDebitType,
            Long oneTimePaymentTypeId, boolean useOldSignatures, Long replaceOfPaymentID,
            boolean isGeneratingPaymentFromOldCCReplacement) throws Exception {

        addNewDD(contractID, fromDate, toDate, additionalDiscount, notes,
                attachmentId, amount, suggestedAmount, directDebitType,
                oneTimePaymentTypeId, useOldSignatures, replaceOfPaymentID,
                isGeneratingPaymentFromOldCCReplacement, null, null);
    }

    @Transactional
    public void addNewDD(
            Long contractID, Date fromDate, Date toDate, Double additionalDiscount, String notes,
            Long attachmentId, Double amount, Double suggestedAmount, String directDebitType,
            Long oneTimePaymentTypeId, boolean useOldSignatures, Long replaceOfPaymentID,
            boolean isGeneratingPaymentFromOldCCReplacement,
            Long relatedEntityId, String relatedEntityType) throws Exception {

        Contract contract = contractRep.findOne(contractID);
        PicklistItem oneTimePaymentType = picklistItemRepository.findOne(oneTimePaymentTypeId);

        String s = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_PAYMENT_TYPES_TO_BE_COLLECTED_BY_CREDIT_CARD);

        Map<String, Map<String, String>> codes = new HashMap<>();
        try {
            codes = objectMapper.readValue(s, Map.class);
        } catch (Exception e) {
            e.printStackTrace();
        }

        // ACC-6629
        if (flowProcessorService.isPayingViaCreditCard(contract) &&
                (s.isEmpty() || !codes.containsKey(oneTimePaymentType.getCode()))) {
            HashMap<String, String> para = new HashMap<>();
            para.put("client_id", contract.getClient().getId().toString());
            para.put("contract_id", contract.getId().toString());
            para.put("date", new LocalDate(fromDate).toString("yyyy-MM-dd"));
            para.put("type", oneTimePaymentType.getName());
            para.put("typeCode", oneTimePaymentType.getCode().toString());
            para.put("amount", String.valueOf(amount.intValue()));

            Setup.getApplicationContext()
                    .getBean(MessagingService.class)
                    .sendEmailToOfficeStaff("dd_requested_and_client_paying_via_credit_card",
                            para, Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_REQUESTED_AND_CLIENT_PAYING_VIA_CREDIT_CARD_RECIPIENTS),
                            "Payment Must be Collected Manually - " + contract.getClient().getId());

            AccountingEntityProperty p = new AccountingEntityProperty();
            p.setOrigin(contract);
            p.setKey(Payment.PAYMENT_MUST_COLLECTED_MANUALLY);
            p.setValue(new ObjectMapper().writeValueAsString(para));
            accountingEntityPropertyRepository.save(p);
            return;
        }

        Map<String, Object> m = new HashMap<>();
        if (relatedEntityId != null && relatedEntityType != null) {
            m.put("relatedEntityId", relatedEntityId);
            m.put("relatedEntityType", relatedEntityType);
        }
        contractPaymentTermServiceNew.addNewDD(contract, fromDate, toDate, additionalDiscount, notes,
                attachmentId, amount, suggestedAmount, DirectDebitType.valueOf(directDebitType), oneTimePaymentType,
                useOldSignatures, replaceOfPaymentID != null ? paymentRepository.findOne(replaceOfPaymentID) : null,
                isGeneratingPaymentFromOldCCReplacement, false, true, null, true, m);
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','add-multiple-dds')")
    @RequestMapping(value = "/add-multiple-dds/{contractId}", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> addNewManualDD_API(
            @PathVariable("contractId") Contract contract,
            @RequestBody AddMultipleDirectDebitsDTO addMultipleDirectDebitsDTO) throws Exception {

        if (addMultipleDirectDebitsDTO == null || addMultipleDirectDebitsDTO.getDirectDebitList() == null ||
                addMultipleDirectDebitsDTO.getDirectDebitList().isEmpty())
            throw new RuntimeException("You must add at list one DD");

        // ACC-5410
        int ddExpiryDateLimitation = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_LIMITATION_DD_DURATION));

        if (addMultipleDirectDebitsDTO.getDirectDebitList().stream().anyMatch(dd ->
            dd.getToDate().after(new LocalDate(dd.getFromDate()).plusYears(ddExpiryDateLimitation).toDate())))
            throw new RuntimeException("Direct Debit expiry date can’t be more than " + ddExpiryDateLimitation + " years into the future.");

        // ACC-8662
        Setup.getApplicationContext()
                .getBean(ContractService.class)
                .stopPreventCreateOtherDdsIfHas(contract);

        addMultipleDirectDebitsDTO.getDirectDebitList() // ACC-5025 the flag is altering the amount value of the DD by applying discount
                .stream().forEach(o -> o.setGeneratingPaymentFromOldCCReplacement(true));

        //ACC-4133 if monthly payment set the contract as Vatted
        DirectDebitDTO dto = addMultipleDirectDebitsDTO.getDirectDebitList().get(0);
        PicklistItem monthlyPaymentType = getItem("TypeOfPayment", "monthly_payment");
        contract = contractRep.findOne(contract.getId());
        if (!contract.getClientPaidVat() &&
                (DirectDebitType.MONTHLY.equals(dto.getType())
                        || (dto.getOneTimePaymentType() != null &&
                        monthlyPaymentType.getId().equals(dto.getOneTimePaymentType().getId())))) {

            contract.setClientPaidVat(true);
            contract = contractRep.save(contract);
        }
        Map<String, Object> m = new HashMap<>();
        m.put("ignoreMissingBakKInfoFlow", true);
        m.put("addedManuallyFromClientProfile", true);

        List<Long> pIds = addNewManualDD(contract, contract.getClientPaidVat(), false,
                addMultipleDirectDebitsDTO.getDirectDebitList(), false , 0, m);

        List<DirectDebit> newDds = contractPaymentRep.getDirectDebitsOfPayments(pIds);

        newDds.stream().forEach(dd -> dd.setAddedManuallyFromClientProfile(true));
        directDebitRepository.save(newDds);

        List<String> paymentIDs = pIds.stream()
                .map(paymentId -> paymentId.toString())
                .collect(Collectors.toList());

        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
        // ACC-5156
        if (addMultipleDirectDebitsDTO.isUseApprovedSignature()) {
            List<DirectDebitSignature> signatures = (List<DirectDebitSignature>) directDebitSignatureService
                    .getLastSignatureTypeConsiderDisabled(cpt, true, false).get("currentSignatures");
            if (signatures == null) signatures = new ArrayList<>();

            signatures.addAll(directDebitSignatureService.getRejectedSignatureApprovedOnce(cpt));
            addMultipleDirectDebitsDTO.setSignatures(signatures.stream()
                    .map(DirectDebitSignature::getSignatureAttachment)
                    .collect(Collectors.toList()));
        }

        // ACC-6542
        if (contract.isPayingViaCreditCard() &&
                (DirectDebitType.MONTHLY.equals(dto.getType()) ||
                        (dto.getOneTimePaymentType() != null &&
                                monthlyPaymentType.getId().equals(dto.getOneTimePaymentType().getId())))) {

            logger.info("contract id: " + contract.getId() + " remove payingViaCreditCard flag");
            if (contract.isOneMonthAgreement()) {
                oneMonthAgreementFlowService.flowStoppedResetFlag(contract);
            } else {
                Setup.getApplicationContext()
                        .getBean(ContractService.class)
                        .updatePayingViaCreditCardFlag(contract, false);
            }

            Setup.getApplicationContext()
                    .getBean(ContractPaymentConfirmationToDoService.class)
                    .disablePayTabLinksBySource(contract, Arrays.asList(
                            ContractPaymentConfirmationToDo.Source.CLIENT_PAYING_VIA_Credit_Card,
                            ContractPaymentConfirmationToDo.Source.ONE_MONTH_AGREEMENT));

            flowProcessorService.retractContractTermination(cpt, null);

            // ACC-5183
            Setup.getApplicationContext()
                    .getBean(DirectDebitGenerationPlanService.class)
                    .generateDdPassedInClientPayingViaCreditCard(contract);

            // SD-47961
            FlowProcessorEntity f = flowProcessorService.getFirstRunningFlow(
                    contract, FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card);
            if (f != null) {
                f.setStopped(true);
                Setup.getRepository(FlowProcessorEntityRepository.class)
                        .silentSave(f);
            }
        }

        createBGTToUpdateContractPaymentTermWithPayments(addMultipleDirectDebitsDTO.getContractPaymentTerm(), true,
                false, paymentIDs, addMultipleDirectDebitsDTO.getSignatures(),
                addMultipleDirectDebitsDTO.isInformClient(), addMultipleDirectDebitsDTO.isIgnoreRejectionDDs());

        return okResponse();
    }

    // ACC-3597
    // Don’t reactivate this flow without fixing the bug reported in ACC-6092
    // Don’t reactivate this flow without fixing the bug reported in ACC-6629
    @PreAuthorize("hasPermission('ContractPaymentTerm','amend-oec-dds')")
    @RequestMapping(value = "/amend-oec-dds/{contractId}", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> amend_OEC_DDS(
            @PathVariable("contractId") Contract contract,
            @RequestParam(value = "workerSalary", required = true) Double workerSalary,
            @RequestParam(value = "oldWorkerSalary", required = false) Double oldWorkerSalary) throws Exception {

        logger.info("begining of amend_OEC_DDS API contract ID is: "+contract);
        // ACC-5056
        DateTime ddBaseMonth = new DateTime().dayOfMonth().withMinimumValue().withTimeAtStartOfDay();
        DateTime lastReceivedPaymentDate = paymentService.getLastReceivedMonthlyPaymentDate(contract);

        if (lastReceivedPaymentDate != null && ddBaseMonth.isBefore(lastReceivedPaymentDate))
            ddBaseMonth = lastReceivedPaymentDate;

        ContractPaymentTerm currentCPT = contract.getActiveContractPaymentTerm();
        if (oldWorkerSalary == null) { oldWorkerSalary = contract.getWorkerSalaryNew(); }

        logger.log(Level.INFO, "ddBaseMonth : {0}", ddBaseMonth);
        if (accountingEntityPropertyRepository.findByKeyAndOriginAndDeletedFalse(Contract.OEC_AMEND_DDS, contract) == null) {
            AccountingEntityProperty aep = new AccountingEntityProperty();
            aep.setOrigin(contract);
            aep.setKey(Contract.OEC_AMEND_DDS);
            aep.setValue(DateUtil.formatDateDashed(ddBaseMonth.toDate()));
            accountingEntityPropertyRepository.save(aep);
        } else
            throw new RuntimeException("OEC already run on this Contract!");

        logger.info("currentCPT is: "+ currentCPT.getId());

        boolean nextMonthCoverd = directDebitRepository.existsDdConfirmedAndCoverDate(
                currentCPT.getId(), ddBaseMonth.plusMonths(1).toDate());
        Date ddbStartDate = ddBaseMonth.plusMonths(2).dayOfMonth().withMinimumValue().toDate();

        List<DirectDebit> dds = directDebitRepository.findByContractPaymentTermAndExpiryDateIsGreaterThanAndCategoryAndStatusInOrderByStartDate(
                currentCPT, ddbStartDate, DirectDebitCategory.B,
                Arrays.asList(DirectDebitStatus.IN_COMPLETE, DirectDebitStatus.PENDING_DATA_ENTRY,
                        DirectDebitStatus.PENDING, DirectDebitStatus.CONFIRMED));
        logger.info("dds size is: "+ dds.size());

        // ACC-5056
        Double oldMonthlyAmount = null;
        Date expiryDate = null;
        if (!dds.isEmpty()) {
            oldMonthlyAmount = dds.get(0).getAmount();
            expiryDate = dds.get(0).getExpiryDate();
        } else {
            DirectDebitGenerationPlan monthlyPlan = Setup.getRepository(DirectDebitGenerationPlanRepository.class)
                    .findFirstByContractAndOneTimeFalseAndContractPaymentType_Type_CodeAndDdExpiryDateIsGreaterThan(
                            currentCPT.getContract(), "monthly_payment",
                            new java.sql.Date(ddbStartDate.getTime()));

            if(monthlyPlan != null) {
                oldMonthlyAmount = monthlyPlan.getAmount();
                expiryDate = monthlyPlan.getDDExpiryDate();
            }
        }

        if(oldMonthlyAmount == null) throw new RuntimeException("There is no active DDB.");

        // Replace the old CPT
        currentCPT.setSwitchedBankAccount(false);
        currentCPT.setAmendedDate(new DateTime().plusMonths(2).dayOfMonth().withMinimumValue().toDate());
        contractPaymentTermRep.save(currentCPT);

        Double newMonthlyAmount = oldMonthlyAmount + workerSalary - oldWorkerSalary;
        List<Attachment> signaturesAsAttachmentList = new ArrayList();
        List<DirectDebitDTO> ddList = new ArrayList<>();
        ddList.add(new DirectDebitDTO(
            newMonthlyAmount,
            ddBaseMonth.plusMonths(2).dayOfMonth().withMinimumValue().toDate(),
            expiryDate,
            DirectDebitType.MONTHLY,
            null,
            null));
        //ACC-5056
        if (!nextMonthCoverd) {
            int ontTimeDDMonthDuration = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_ONE_TIME_DD_MONTH_DURATION));
            DateTime ddaStartDate = ddBaseMonth.plusMonths(1).dayOfMonth().withMinimumValue();
            ddList.add(new DirectDebitDTO(
                newMonthlyAmount,
                ddaStartDate.toDate(),
                ddaStartDate.plusMonths(ontTimeDDMonthDuration).toDate(),
                DirectDebitType.ONE_TIME,
                getItem("TypeOfPayment", "monthly_payment"),
                null));
        }
        logger.info("ddList: "+ ddList.size());

        List<String> paymentIDs = addNewManualDD(contract, contract.getClientPaidVat(), false,
                ddList, true , workerSalary, new HashMap<>())
                .stream()
                .map(paymentId -> paymentId.toString())
                .collect(Collectors.toList());
        logger.info("paymentIDs: "+ paymentIDs);

        // ACC-3597
        createBGTToUpdateContractPaymentTermWithPayments(currentCPT, true, true, paymentIDs, signaturesAsAttachmentList, true, true);
        contract = contractRep.findOne(contract.getId());
        contract.setTerminateContractDueRejection(false);
        contract.setTempWorkerSalary(workerSalary);
        contractRep.save(contract);

        return okResponse();
    }

    @Transactional
    public List<Long> addNewManualDD(
            Contract contract,
            Boolean clientPaidVat, boolean useOldSignatures,
            List<DirectDebitDTO> ddList,
            boolean addedByOecFlow, double workerSalary, Map<String, Object> m) throws Exception {

        List<Long> paymentIDs = new ArrayList();
        if (ddList == null || ddList.isEmpty()) return paymentIDs;

        // ACC-1841
        if (clientPaidVat == null) clientPaidVat = false;
        contract.setClientPaidVat(clientPaidVat);
        contractRep.save(contract);

        DateTime generateDate = new DateTime();

        for (DirectDebitDTO directDebitDTO : ddList) {
            Payment replaceOfPayment = directDebitDTO.getReplacedBouncedPaymentId() != null ? paymentRepository.findOne(directDebitDTO.getReplacedBouncedPaymentId()) : null;

            if (m !=  null) {
                m.putAll(new HashMap<String, Object>() {{
                    put("additionalDiscountAmount", directDebitDTO.getAdditionalDiscountAmount());
                    put("moreAdditionalDiscount", directDebitDTO.getMoreAdditionalDiscount());
                    put("additionAmount", directDebitDTO.getAdditionAmount());
                    put("subType", directDebitDTO.getSubType());
                    put("isProRated", directDebitDTO.isProrated());
                }});
            }

            contractPaymentTermServiceNew.addNewDD(contract, directDebitDTO.getFromDate(), directDebitDTO.getToDate(), null, null, null,
                    directDebitDTO.getAmount(), null, directDebitDTO.getType(),
                    directDebitDTO.getOneTimePaymentType(), useOldSignatures, replaceOfPayment,
                    directDebitDTO.getGeneratingPaymentFromOldCCReplacement(),
                    true, true, null, true, m);
        }

        ContractPaymentTerm activeCPT = contract.getActiveContractPaymentTerm();
        List<DirectDebit> generatedDDs = directDebitRepository.findByContractPaymentTermAndCreationDateGreaterThanEqual(
                activeCPT, generateDate.minusSeconds(1).withMillisOfSecond(0).toDate());
        generatedDDs.forEach(directDebit -> {
            paymentIDs.addAll(directDebit.getPayments().stream()
                    .map(cp -> cp.getId())
                    .collect(Collectors.toList()));

            directDebit.setAddedByOecFlow(addedByOecFlow);
            directDebit.setOecSalary(workerSalary); // ACC-3974
            directDebitRepository.save(directDebit);

            // ACC-2679, if MV and in Discount Period and (DD is Monthly OR (One Time AND Payment Type is Monthly Payment)) -> includeWorkerSalary = true
            boolean includeWorkerSalary;
            long discountStartDateInMillis = calculateDiscountsWithVatService.getDiscountStartDateInMillis(activeCPT);
            PicklistItem paymentType = directDebit.getPaymentType();
            if (paymentType == null)
                paymentType = directDebit.getPayments() != null && !directDebit.getPayments().isEmpty() ?
                        directDebit.getPayments().get(0).getPaymentType() : null;

            logger.log(Level.SEVERE, "discountStartDateInMillis: " + discountStartDateInMillis);
            logger.log(Level.SEVERE, "discountStartDateInMillis date: " + new Date(discountStartDateInMillis));
            logger.log(Level.SEVERE, "getContractProspectType is: " + contract.getContractProspectType().getCode());
            logger.log(Level.SEVERE, "directDebit.getStartDate().getTime() is : " + directDebit.getStartDate().getTime());
            logger.log(Level.SEVERE, "directDebit.getType() is : " + directDebit.getType());
            logger.log(Level.SEVERE, "directDebit paymentType() is : " + paymentType);

            includeWorkerSalary = contract.getContractProspectType().getCode().equals(PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE)) &&
                    directDebit.getStartDate().getTime() >= discountStartDateInMillis &&
                    (directDebit.getType().equals(DirectDebitType.MONTHLY) ||
                            (directDebit.getType().equals(DirectDebitType.ONE_TIME) && paymentType != null &&
                                    paymentType.getCode().equals(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE)));

            logger.log(Level.SEVERE, "includeWorkerSalary: " + includeWorkerSalary);
            List<ContractPayment> newDdPayments = contractPaymentRep.findByDirectDebit(directDebit);

            if (newDdPayments != null) {
                logger.log(Level.SEVERE, "newDdPayments.size: " + newDdPayments.size());
                for (ContractPayment contractPayment : newDdPayments) {
                    contractPayment.setIncludeWorkerSalary(includeWorkerSalary);
                    contractPaymentRep.save(contractPayment);
                }
            }
        });

        return paymentIDs;
    }

    // Deprecated after ACC-6936
    @PreAuthorize("hasPermission('ContractPaymentTerm','extendContract')")
    @RequestMapping(value = "/extendcontract/{id}", method = RequestMethod.GET)
    @Transactional
    public ResponseEntity<?> extendContract(
            @PathVariable("id") Contract contract,
            @RequestParam(value = "fromDate", required = false) @org.springframework.format.annotation.DateTimeFormat(pattern = "yyyy-MM-dd") Date fromDate,
            @RequestParam(value = "useOldSignatures", required = false, defaultValue = "false") boolean useOldSignatures)
            throws Exception {


        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();

        // ACC-5446
        if (!directDebitRepository.existsByContractPaymentTerm(cpt))
            return ResponseEntity.ok("There are no direct debits");
        // ACC-6863
        if (Setup.getRepository(DirectDebitRejectionToDoRepository.class)
                .existsActiveTodoOnDdb(contract))
            return ResponseEntity.ok("There is a rejection flow running on a DDB");

        DateTime ddPaymentStartDate;
        PicklistItem monthlyPayment = getItem("TypeOfPayment", "monthly_payment");
        Date lastPaymentDate = paymentRepository.findFirstDateOfPaymentByContractAndTypeOfPaymentAndStatusOrderByDateOfPaymentDesc
                (contract.getId(), monthlyPayment.getId(), PaymentStatus.PDC.getValue());

        if (lastPaymentDate != null) {
            ddPaymentStartDate = new DateTime(lastPaymentDate);
        } else {
            ddPaymentStartDate = DateTime.now();
        }
        DateTime eomDate = ddPaymentStartDate.dayOfMonth().withMaximumValue();
        int daysToEOM = Days.daysBetween(ddPaymentStartDate, eomDate).getDays() + 1;
        boolean isEOM = daysToEOM <= Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_PAYMENT_EXPIRY_FLOW_EOM_NUM_OF_DAYS));


        if (!contract.getClientPaidVat()) {
            double discountedValue = cpt.getMonthlyPayment() - cpt.getDiscount();
            Double workerSalary = contract.getContractProspectType().getCode()
                    .equalsIgnoreCase(PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE)) ?
                    contract.getWorkerSalaryNew() : 0D;

            double vat = DiscountsWithVatHelper.getVatPercent();
            double discountedValueWithVat = Math.ceil(DiscountsWithVatHelper.getAmountPlusVat(discountedValue, vat, workerSalary, contract.isWorkerSalaryVatted()));
            double fullValueWithVat = Math.ceil(DiscountsWithVatHelper.getAmountPlusVat(cpt.getMonthlyPayment(), vat));

            cpt.setMonthlyPayment(fullValueWithVat);
            cpt.setDiscount(fullValueWithVat - discountedValueWithVat);
            cpt.setReason(ContractPaymentTermReason.AUTO_EXTENSION_WITH_VAT);
            contractPaymentTermRep.save(cpt);
        }

        DateTime generateDate = new DateTime();
        DateTime ddbStartDate;

        // ACC-3211
        if (isEOM) {
            DateTime ddaStartDate = ddPaymentStartDate.plusMonths(1).withTimeAtStartOfDay().withDayOfMonth(1);

            contractPaymentTermServiceNew.addNewDD(contract, ddaStartDate.toDate(), ddaStartDate.toDate(), null, null, null,
                    calculateDiscountsWithVatService.getCPTAmountAtTime(cpt, ddaStartDate.toDate()), null,
                    DirectDebitType.ONE_TIME, monthlyPayment, useOldSignatures, null,
                    true, true, true, null, true);

            ddbStartDate = ddaStartDate.plusMonths(1);
        } else {
            ddbStartDate = ddPaymentStartDate.plusMonths(1).withTimeAtStartOfDay().withDayOfMonth(1);
        }

        DateTime ddbEndDate = ddbStartDate.plusMonths(switchingNationalityService.getDefaultPaymentsDuration(contract) - 1);

        DateTime discountDate = new DateTime(calculateDiscountsWithVatService.getDiscountStartDateInMillis(cpt));
        if (Months.monthsBetween(ddbStartDate, discountDate).getMonths() > 0) {
            contractPaymentTermServiceNew.addNewDD(contract, ddbStartDate.toDate(), discountDate.minusMonths(1).toDate(), null, null, null, null, null,
                    DirectDebitType.MONTHLY, null, useOldSignatures, null, false, true, true, null, true);
        }

        discountDate = discountDate.isBefore(ddbStartDate) ? ddbStartDate : discountDate;
        if (Months.monthsBetween(discountDate, ddbEndDate).getMonths() > 0) {
            contractPaymentTermServiceNew.addNewDD(contract, discountDate.toDate(), ddbEndDate.toDate(), null, null, null, null, null,
                    DirectDebitType.MONTHLY, null, useOldSignatures, null, false, true, true, null, true);
        }

        List<DirectDebit> generatedDD = directDebitRepository.findByContractPaymentTermAndCreationDateGreaterThanEqual(
                cpt, generateDate.minusSeconds(1).withMillisOfSecond(0).toDate());
        for (DirectDebit directDebit : generatedDD) {
            directDebit.setSource(DirectDebitSource.PAYMENT_EXPIRY_AUTO_EXTENSION);
            directDebitRepository.save(directDebit);
        }

        //ACC-3887
        PaymentExpiryService paymentExpiryService = Setup.getApplicationContext().getBean(PaymentExpiryService.class);
        Map<String, Object> lastSignatureType = directDebitSignatureService.getLastSignatureType(cpt, false, false);

        if (useOldSignatures && ((Boolean) lastSignatureType.get("useApprovedSignature")
                || (Boolean) lastSignatureType.get("useNonRejectedSignature"))) {
            paymentExpiryService.SendFirstMessage(cpt);
        } else {
            paymentExpiryService.SendSecondSMS(contract, cpt.getLastConfirmedDD().getAmount(),
                    ddPaymentStartDate.toString("MM/yyyy"));
        }
        return okResponse();
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','updateContractPaymentTermWithPayments')")
    @PostMapping(value = "/updatecontractpaymenttermwithpayments")
    @Transactional
    public ResponseEntity<?> updateContractPaymentTermWithPaymentsAPI(
            @RequestPart("contractPaymentTerm") JsonNode contractPaymentTerm,
            @RequestPart("generateDD") Boolean generateDD,
            @RequestPart(name = "useApprovedSignature", required = false) Boolean useOldSignatures,
            @RequestPart(name = "signatures", required = false) List<MultipartFile> signatures,
            @RequestPart(name = "informClient", required = false) Boolean informClient,
            @RequestPart(name = "ignoreRejectionDDs", required = false) Boolean ignoreRejectionDDs) throws Exception {

        List<Attachment> signaturesAsAttachmentList = new ArrayList();

        logger.info(signatures == null ? "signatures is null" : "signatures.size(): " + signatures.size());

        if (signatures != null) {
            for (MultipartFile signature : signatures) {
                signaturesAsAttachmentList.add(Storage.storeTemporary(signature, "temp_signature", true, true));
            }
        }

        createBGTToUpdateContractPaymentTermWithPayments(contractPaymentTerm, generateDD,
                useOldSignatures, new ArrayList<>(), signaturesAsAttachmentList, informClient,
                ignoreRejectionDDs);
        return okResponse();
    }

    @Transactional
    public void createBGTToUpdateContractPaymentTermWithPayments(
            JsonNode contractPaymentTerm,
            Boolean generateDD, Boolean useOldSignatures,
            List<String> paymentIDs, List<Attachment> signatures,
            Boolean informClient, Boolean ignoreRejectionDDs) throws Exception {

        ContractPaymentTerm oldCpt = contractPaymentTermRep.findOne(contractPaymentTerm.get("id").asLong());

        ContractPaymentTerm updatedCpt = getObjectMapper().readerForUpdating(oldCpt)
                .readValue(contractPaymentTerm);
        updatedCpt = contractPaymentTermRep.save(updatedCpt);

        createBGTToUpdateContractPaymentTermWithPayments(
                updatedCpt,
                generateDD, useOldSignatures,
                paymentIDs,
                signatures,
                informClient, ignoreRejectionDDs);
    }

    @Transactional
    public void createBGTToUpdateContractPaymentTermWithPayments(
            ContractPaymentTerm contractPaymentTerm,
            Boolean generateDD, Boolean useOldSignatures,
            List<String> paymentIDs, List<Attachment> signatures,
            Boolean informClient, Boolean ignoreRejectionDDs) throws Exception {

        if (ignoreRejectionDDs == null) ignoreRejectionDDs = false;
        if (useOldSignatures == null) useOldSignatures = false;
        if (informClient == null) informClient = false;

        List<String> signaturesIDs = signatures.stream()
                .map(signature -> signature.getId().toString())
                .collect(Collectors.toList());

        Long time = new Date().getTime();
        backgroundTaskService.addDirectCallBackgroundTaskForEntity(
                "Accounting_updateContractPaymentTermWithPaymentsCPT#" + contractPaymentTerm.getId() + "_" + time,
                "contractPaymentTermController",
                "accounting",
                "updateContractPaymentTermWithPayments",
                "ContractPaymentTerm",
                contractPaymentTerm.getId(),
                true,
                false,
                new Class<?>[]{Long.class, Boolean.class, Boolean.class, List.class,
                        List.class, Boolean.class, Boolean.class, Boolean.class},
                new Object[]{contractPaymentTerm.getId(), generateDD, useOldSignatures, paymentIDs,
                        signaturesIDs, informClient, true, ignoreRejectionDDs});
    }

    @Transactional
    public ResponseEntity<?> updateContractPaymentTermWithPayments(
            Long cptID,
            Boolean generateDD, Boolean useOldSignatures,
            List<String> paymentIDs, List<String> signaturesIDs,
            Boolean informClient, Boolean ignoreRejectedSignatures, Boolean ignoreRejectionDDs) throws Exception {

        ContractPaymentTerm cpt = getRepository().findOne(cptID);

        List<Attachment> signatures = signaturesIDs.stream()
                .map(signatureID -> attachementRepository.findOne(Long.parseLong(signatureID)))
                .collect(Collectors.toList());
        List<ContractPayment> payments;

        if(!paymentIDs.isEmpty()) {
            payments = paymentIDs.stream()
                    .map(paymentID -> contractPaymentRep.findOne(Long.parseLong(paymentID)))
                    .collect(Collectors.toList());
        } else {
            Map termResult = contractPaymentTermServiceNew
                    .getContractPaymentTermByContractWithDirectDebitPayments(cpt);
            payments = (List<ContractPayment>) termResult.get("payments");
        }

        return contractPaymentTermServiceNew.updateContractPaymentTermWithPayments(
                cpt, generateDD, useOldSignatures,
                payments, signatures, informClient, signatures.isEmpty(),
                true, ignoreRejectedSignatures, ignoreRejectionDDs,
                true, false);
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','getDDFiles')")
    @RequestMapping(value = "/getddfiles/{CONTRACT_ID}", method = RequestMethod.GET)
    public Map getDirectDebitFiles(@PathVariable("CONTRACT_ID") Contract contract,
                                   @RequestParam(value = "includingInactive", required = false, defaultValue = "false") boolean includingInactive) {
        return contractPaymentTermServiceNew.getDirectDebitFiles(contract, includingInactive);
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','getDirectDebitFilesOfContractPaymentTerm')")
    @RequestMapping(value = "/getddfilesofcpt/{CPT_ID}", method = RequestMethod.GET)
    public Map getDirectDebitFilesOfContractPaymentTerm(@PathVariable("CPT_ID") ContractPaymentTerm contractPaymentTerm) {
        return contractPaymentTermServiceNew.getDirectDebitFilesOfContractPaymentTerm(contractPaymentTerm);
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','getPdfOfImages')")
    @RequestMapping(value = "/getpdfofimages/{attachmentId}/{type}", method = RequestMethod.GET, produces = "application/MediaType.APPLICATION_OCTET_STREAM_VALUE")
    public ResponseEntity<?> getPdfOfImages(@PathVariable("attachmentId") Attachment attachment,
                                            @PathVariable("type") String type) throws IOException {
        User user = CurrentRequest.getUser();
        if (user == null || user.getPositions() == null || user.getPositions().isEmpty() || !user.getPositions().stream().anyMatch(position -> position.getCode().equalsIgnoreCase(DD_FORM_DOWNLOAD_POSITION))) {
            return new ResponseEntity("you don't have the required permission", HttpStatus.UNAUTHORIZED);
        }

        if (attachment == null) {
            return new ResponseEntity<>("Attachment not exist", HttpStatus.INTERNAL_SERVER_ERROR);
        }
        if (!attachment.getExtension().toLowerCase().equals("pdf")) {
            return new ResponseEntity<>("Attachment type is not pdf", HttpStatus.INTERNAL_SERVER_ERROR);
        }

        byte[] contents = PdfHelper.getPdfPages(attachment, type);

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/pdf");
        headers.add("Content-Disposition", "attachment; filename=" + attachment.getName());
        headers.setCacheControl("must-revalidate, post-check=0, pre-check=0");
        ResponseEntity<byte[]> response = new ResponseEntity<>(contents, headers, HttpStatus.OK);
        return response;

    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','getMergeDDdfiles')")
    @RequestMapping(value = "/getmergedddfiles/{CONTRACT_ID}", method = RequestMethod.GET, produces = "application/MediaType.APPLICATION_OCTET_STREAM_VALUE")
    public ResponseEntity<?> getMergeDDdfiles(@PathVariable("CONTRACT_ID") Contract contract,
                                              @RequestParam(required = false, defaultValue = "false") Boolean pendingOnly) throws IOException {
        List<Attachment> attachments = new ArrayList();

        Map result = contractPaymentTermServiceNew.getDirectDebitFiles(contract, false);
        // add DD files
        ((List<DirectDebitSalesProjection>) result.get("directDebits")).forEach(dd -> {
            List<DirectDebitFile> ddfList = directDebitFileRepository.findByDirectDebit_Id(dd.getId());
            ddfList.forEach(ddf -> {
                if ((pendingOnly && ddf.getDdStatus().equals(DirectDebitStatus.PENDING)) || !pendingOnly)
                    attachments.addAll(ddf.getAttachments().stream().filter(
                            attachment -> attachment.getTag().startsWith(DirectDebitFile.FILE_TAG_DD_ACTIVATION))
                            .collect(Collectors.toList()));
            });

        });
//		 // add DD cancellation files
//		attachments.addAll((List<Attachment>) result.get("canellationAttachments"));

        // add payments receipt
        if (result.get("paymentsReceiptAttachment") != null)
            attachments.add((Attachment) result.get("paymentsReceiptAttachment"));

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        PdfDocument pdfDoc = new PdfDocument(new PdfWriter(byteArrayOutputStream));
        if (!attachments.isEmpty()) {
            PdfMerger merger = new PdfMerger(pdfDoc);
            for (Attachment attachement : attachments) {
                PdfDocument srcDoc = new PdfDocument(new PdfReader(Storage.getStream(attachement)));
                merger.merge(srcDoc, 1, srcDoc.getNumberOfPages());
                srcDoc.close();
            }
        } else { // create an empty pdf file
            pdfDoc.addNewPage();
            Document doc = new Document(pdfDoc);
            doc.close();
        }

        pdfDoc.close();
        byte[] contents = byteArrayOutputStream.toByteArray();

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/pdf");
        headers.add("Content-Disposition", "attachment; filename=All Direct Debits.pdf");
        headers.setCacheControl("must-revalidate, post-check=0, pre-check=0");
        ResponseEntity<byte[]> response = new ResponseEntity<>(contents, headers, HttpStatus.OK);
        return response;
    }

    //REC-612
    //@PreAuthorize("hasPermission('ContractPaymentTerm','CheckIBAN')")
    @NoPermission
    @RequestMapping(value = "/checkiban/{IBAN_NUM}", method = RequestMethod.GET)
    public ResponseEntity<?> checkIBAN(@PathVariable("IBAN_NUM") String IBANNumber)
            throws IOException {

        return ResponseEntity.ok(ContractPaymentTermHelper.checkIBAN(IBANNumber));
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','sendDDFilestoClient')")
    @RequestMapping(value = "/sendddfilestoclient/{CONTRACT_ID}", method = RequestMethod.GET)
    @Transactional
    public ResponseEntity<?> sendDDFilesToClientAPI(@PathVariable("CONTRACT_ID") Contract contract,
                                                    @RequestParam(required = false) Client client,
                                                    @RequestParam(required = false, defaultValue = "false") boolean sendToSpouse) throws IOException {

        return contractPaymentTermServiceNew.sendDDFilesToClient(contract, client, sendToSpouse);
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','hasApprovedDDFile')")
    @RequestMapping(path = "/hasapprovedddfile_forcontracts", method = RequestMethod.POST)
    public ResponseEntity hasApprovedDDFile(@RequestBody List<Long> contracts_ids) {

        if (contracts_ids == null || contracts_ids.isEmpty())
            return null;
        Map<Long, Object> result = new HashMap<>();
        List<Contract> contracts = contractRep.findAll(contracts_ids);
        for (Contract c : contracts) {
            Map contractMap = contractPaymentTermHelper.getActiveContractPaymentTermByContract(c);
            if (contractMap == null)
                result.put(c.getId(), "");
            else {
                ContractPaymentTerm cpt = (ContractPaymentTerm) contractMap.get("contractPaymentTerm");
                result.put(c.getId(),
                        directDebitSignatureService.getLastSignatureType(cpt, false, false));
            }
        }
        return new ResponseEntity(result, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','hasApprovedDDFile')")
    @RequestMapping(path = "/hasapprovedddfile/{contract}", method = RequestMethod.GET)
    public ResponseEntity<?> hasApprovedDDFile(@PathVariable Contract contract, @RequestParam String eid) {

        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
        Map<String, Object> lastSignatureType = directDebitSignatureService
                .getLastSignatureTypeConsiderDisabled(cpt,false, false);

        // ACC-5196
        boolean useApprovedSignature = (Boolean) lastSignatureType.get("useApprovedSignature") ||
                !directDebitSignatureService.getRejectedSignatureApprovedOnce(cpt).isEmpty();
        lastSignatureType.put("useApprovedSignature", useApprovedSignature);

        return new ResponseEntity<>(lastSignatureType, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','hasApprovedDDFile')")
    @RequestMapping(path = "/generateAndSavePaymentsReceipt/{contract}", method = RequestMethod.GET)
    public ResponseEntity<?> generateAndSavePaymentsReceipt(
            @PathVariable Contract contract) {

        contractPaymentTermServiceNew.generatePaymentsReceipt(contract, null, true, false);

        return new ResponseEntity<>("Done.", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','hasApprovedDDFile')")
    @RequestMapping(path = "/generatePaymentsReceipt/{contract}", method = RequestMethod.GET)
    public void generatePaymentsReceipt(
            @PathVariable Contract contract,
            WordTemplate wordTemplate,
            @RequestParam(value = "replaceCurrent", required = false, defaultValue = "false") boolean replaceCurrent,
            HttpServletResponse response){

        Attachment a = contractPaymentTermServiceNew.generatePaymentsReceipt(contract, wordTemplate, replaceCurrent, true);
        createDownloadResponse(response,
                "Payment Terms Form.pdf",
                a.getExtension(),
                Storage.getStream(a));
    }

    public void createBackgroundTaskToSign(
            String contractUUID, Contract contract,
            Object eidPhoto, Object ibanPhoto, Object accountPhoto,
            Boolean eidPhotoChanged, Boolean ibanPhotoChanged, Boolean accountNamePhotoChanged,
            String eid, String iban, String account,
            List<Attachment> signatures, boolean useOldSignature, boolean needToSign,List<LinkedHashMap> cashPayments,
            Long ibanRejectionReasonId, Long accountNameRejectionReasonId, Long eidRejectionReasonId,
            boolean pendingOcr) {

        Map payload = getPayloadAsMapForBGT(contractUUID, contract,
            eidPhoto, ibanPhoto, accountPhoto,
            signatures);

        createBackgroundTaskToSign(contractUUID, contract.getId(),
                eidPhotoChanged, ibanPhotoChanged, accountNamePhotoChanged,
                eid, iban, account,
                useOldSignature, needToSign, cashPayments,
                ibanRejectionReasonId, accountNameRejectionReasonId, eidRejectionReasonId,
                pendingOcr, payload);
    }

    public Map getPayloadAsMapForBGT(
            String contractUUID, Contract contract,
            Object eidPhoto, Object ibanPhoto, Object accountPhoto,
            List<Attachment> signatures) {

        Map payload = new HashMap();

        if (contract != null) {
            payload.put("id", contract.getId());
        } else if (!Utils.isEmpty(contractUUID)) {
            contract = contractRep.findByUuid(contractUUID);
        }

        if (contract == null) throw new RuntimeException("Contract is NULL or Not Found");

        List<Map<String, Object>> attachments = new ArrayList();

        if (eidPhoto != null) {
            Map<String, Object> eidMap = new HashMap();
            eidMap.put("id", utils.getAttachmentFromObject(eidPhoto, "temp_eid").getId());
            attachments.add(eidMap);
        }

        if (ibanPhoto != null) {
            Map<String, Object> ibanMap = new HashMap();
            ibanMap.put("id", utils.getAttachmentFromObject(ibanPhoto, "temp_iban").getId());
            attachments.add(ibanMap);
        }

        if (accountPhoto != null) {
            Map<String, Object> accountNameMap = new HashMap();
            accountNameMap.put("id", utils.getAttachmentFromObject(accountPhoto, "temp_account").getId());
            attachments.add(accountNameMap);
        }

        if (signatures != null) {
            for (Object signature : signatures) {
                Map<String, Object> signatureMap = new HashMap();
                signatureMap.put("id", utils.getAttachmentFromObject(signature, "temp_signature").getId());
                attachments.add(signatureMap);
            }
        }

        payload.put("attachments", attachments);
        return payload;
    }

    public void createBackgroundTaskToSign(
        String contractUUID, Long contractId,
        Boolean eidPhotoChanged, Boolean ibanPhotoChanged, Boolean accountNamePhotoChanged,
        String eid, String iban, String account,
        boolean useOldSignature, boolean needToSign,List<LinkedHashMap> cashPayments,
        Long ibanRejectionReasonId, Long accountNameRejectionReasonId, Long eidRejectionReasonId,
        boolean pendingOcr, Map payload) {


        backgroundTaskService.create(new BackgroundTask.builder(
                "Accounting_Sign_DD_Contract#" + contractId + "_" + new Date().getTime(),
                "accounting",
                "contractPaymentTermServiceNew",
                "signDDByClientAsyncBGT")
                .withRelatedEntity("Contract", contractId)
                .withParameters(
                    new Class<?>[]{Map.class, String.class, String.class, String.class, String.class,
                            Boolean.class, Boolean.class, Boolean.class,
                            Boolean.class, Boolean.class,
                            List.class,
                            Long.class, Long.class, Long.class,
                            Boolean.class},
                    new Object[]{payload, contractUUID, eid, iban, account,
                            eidPhotoChanged, ibanPhotoChanged, accountNamePhotoChanged,
                            useOldSignature, needToSign,
                            cashPayments,
                            ibanRejectionReasonId, accountNameRejectionReasonId, eidRejectionReasonId,
                            pendingOcr})
                .build());
    }

    //ACC-3990 Existing Client flow
    @PreAuthorize("hasPermission('ContractPaymentTerm','getDdfDetailsForClient')")
    @GetMapping(value = "/getDdfDetailsForClient/{clientId}")
    public ResponseEntity<?> getDdfDetailsForClient(@PathVariable(name = "clientId") Client client) {

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("hasRejectionForAccountName",
                directDebitService.hasRejectionForAccountName(client));

        BigInteger ddfId = directDebitFileRepository.getLastApprovedSignatureByClient(client);
        if (ddfId == null) return ResponseEntity.ok(resultMap);

        logger.info("Client: " + client.getId() + ", ddfId = " + ddfId);
        DirectDebitFile ddf = directDebitFileRepository.findOne(ddfId.longValue());
        if (ddf == null) return ResponseEntity.ok(resultMap);

        Long eidPhotoId = ddf.getAttachment(FILE_TAG_BANK_INFO_EID) != null ?
                ddf.getAttachment(FILE_TAG_BANK_INFO_EID).getId() :
                ddf.getDirectDebit().getAttachment(FILE_TAG_BANK_INFO_EID) != null ?
                        ddf.getDirectDebit().getAttachment(FILE_TAG_BANK_INFO_EID).getId() : null;

        Long ibanPhotoId = ddf.getAttachment(FILE_TAG_BANK_INFO_IBAN) != null ?
                ddf.getAttachment(FILE_TAG_BANK_INFO_IBAN).getId() :
                ddf.getDirectDebit().getAttachment(FILE_TAG_BANK_INFO_IBAN) != null ?
                        ddf.getDirectDebit().getAttachment(FILE_TAG_BANK_INFO_IBAN).getId() : null;

        Long accountPhotoId = ddf.getAttachment(FILE_TAG_BANK_INFO_ACCOUNT_NAME) != null ?
                ddf.getAttachment(FILE_TAG_BANK_INFO_ACCOUNT_NAME).getId() :
                ddf.getDirectDebit().getAttachment(FILE_TAG_BANK_INFO_ACCOUNT_NAME) != null ?
                        ddf.getDirectDebit().getAttachment(FILE_TAG_BANK_INFO_ACCOUNT_NAME).getId() : null;

        //checking needed attachments
        if ((eidPhotoId == null && ddf.getEid() == null) ||
                (ibanPhotoId == null && ddf.getIbanNumber() == null) ||
                // ACC-5433 #1
                ((boolean) resultMap.get("hasRejectionForAccountName") &&
                        accountPhotoId == null &&
                        ddf.getAccountName() == null)) {

            logger.info("missing bank info");
            return ResponseEntity.ok(resultMap);
        }

        // ACC-6817
        if (eidPhotoId != null) {
            resultMap.put("eidPhotoId", eidPhotoId);
        }

        if (ibanPhotoId != null) {
            resultMap.put("ibanPhotoId", ibanPhotoId);
        }

        if (accountPhotoId != null) {
            resultMap.put("accountPhotoId", accountPhotoId);
        }

        resultMap.put("eid", ddf.getEid());
        resultMap.put("ibanNumber", ddf.getIbanNumber());
        resultMap.put("accountName", ddf.getAccountName());
        resultMap.put("bankId", ddf.getDirectDebit().getBank() != null ? ddf.getDirectDebit().getBank().getId() : null);
        resultMap.put("bankName", ddf.getBankName());
        resultMap.put("signaturePhotoId", ddf.getSignatureAttachment().getId());

        return ResponseEntity.ok(resultMap);
    }

    //ACC-3990 Existing Client flow
    @PreAuthorize("hasPermission('ContractPaymentTerm','signddbyclientasyncforexistingclient')")
    @RequestMapping(value = "/signddbyclientasyncforexistingclient", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> signDDByClientAsyncForExistingClientAPI(
            @RequestParam(name = "contractId") Long contractId,
            @RequestParam(name = "eid") String eid,
            @RequestParam(name = "ibanNumber") String ibanNumber,
            @RequestParam(name = "accountName") String  accountName,
            @RequestParam(name = "bankId") Long bankId,
            @RequestParam(name = "bankName") String bankName,
            @RequestParam(name = "eidPhotoId", required = false) Long eidPhotoId,
            @RequestParam(name = "ibanPhotoId", required = false) Long ibanPhotoId,
            @RequestParam(name = "accountPhotoId", required = false) Long accountPhotoId,
            @RequestParam(name = "ddcId", required = false) Long ddcId) throws Exception {

        Attachment ibanPhoto = ibanPhotoId != null ? attachementRepository.getById(ibanPhotoId) : null;
        Attachment eidPhoto = eidPhotoId != null ?  attachementRepository.getById(eidPhotoId) : null;
        Attachment accountPhoto = accountPhotoId != null ? attachementRepository.getById(accountPhotoId) : null;

        return contractPaymentTermServiceNew.signDDByClientForExistingClient(
                contractId, eid, ibanNumber, accountName, ibanPhoto, eidPhoto, accountPhoto,
                new HashMap<String, Object>() {{
                    if (ddcId != null) {
                        put("ddcId", ddcId);
                    }
                }});
    }

    // ACC-6385
    @PreAuthorize("hasPermission('ContractPaymentTerm','signDDByClientFromClientToDo')")
    @PostMapping(value = "/signDDByClientFromClientToDo")
    @Transactional
    public ResponseEntity<?> signDDByClientFromClientToDo(@RequestBody Map<String, Object> payload) throws Exception {

        Contract contract = contractPaymentTermServiceNew.getContractFromPayload(payload, null);
        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();


        Map<String, Object> bankInfo = contractPaymentTermServiceNew.fillAttatchemnts(payload);
        Attachment eidPhoto = (Attachment) bankInfo.get("eidPhoto");
        Attachment ibanPhoto = (Attachment) bankInfo.get("ibanPhoto");
        Attachment accountPhoto = (Attachment) bankInfo.get("accountPhoto");
        List<Attachment> signatureList = (List<Attachment>) bankInfo.get("signatureList");


        String eid = cpt.getEid();
        if (eidPhoto != null) {
            eid = contractPaymentTermHelper.extractBankInfoEid(
                    cpt, null, true, eidPhoto);
        }

        contractPaymentTermServiceNew.signDDByClient(null, contract, eidPhoto, ibanPhoto, accountPhoto,
                eidPhoto != null , ibanPhoto != null, accountPhoto != null,
                null, null, null, signatureList,
                directDebitSignatureService.hasSignature(cpt, eid),
                false, null,
                null, null,
                null, false);

        return new ResponseEntity("signing DD is being processed", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','signddbyclientasync')")
    @RequestMapping(value = "/signddbyclientasync", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> signDDByClientAsyncAPI(
            @RequestBody Map<String, Object> payload,
            @RequestParam(name = "id", required = false) String contractUUID,
            @RequestParam(name = "eid", required = false) String eid,
            @RequestParam(name = "iban", required = false) String iban,
            @RequestParam(name = "accountName", required = false) String account,
            @RequestParam(name = "eidPhotoChanged", required = false, defaultValue = "true") Boolean eidPhotoChanged,
            @RequestParam(name = "ibanPhotoChanged", required = false, defaultValue = "true") Boolean ibanPhotoChanged,
            @RequestParam(name = "accountNamePhotoChanged", required = false, defaultValue = "true") Boolean accountNamePhotoChanged,
            @RequestParam(name = "useOldSignature", required = false, defaultValue = "false") Boolean useOldSignature,
            @RequestParam(name = "needToSign", required = false, defaultValue = "true") Boolean needToSign,
            @RequestParam(name = "ibanRejectionReason", required = false) Long ibanRejectionReasonId,
            @RequestParam(name = "accountHolderRejectionReason", required = false) Long accountNameRejectionReasonId,
            @RequestParam(name = "eidRejectionReason", required = false) Long eidRejectionReasonId,
            @RequestParam(name = "pendingOcr", required = false, defaultValue = "false") Boolean pendingOcr,
            @RequestParam(name = "ddcId", required = false) Long ddcId,
            @RequestPart(name = "cashPayments", required = false) List<LinkedHashMap> cashPayments) throws Exception {

        return contractPaymentTermServiceNew.signDDByClientAsyncBGT(payload,
                contractUUID,
                eid,
                iban,
                account,
                eidPhotoChanged,
                ibanPhotoChanged,
                accountNamePhotoChanged,
                useOldSignature,
                needToSign,
                cashPayments,
                ibanRejectionReasonId,
                accountNameRejectionReasonId,
                eidRejectionReasonId,
                pendingOcr,
                new HashMap<String, Object>() {{
                    if (ddcId != null) {
                        put("ddcId", ddcId);
                    }
        }});
    }

    @UsedBy(modules = UsedBy.Modules.Sales)
    @PreAuthorize("hasPermission('ContractPaymentTerm','addCashPaymentsAPI')")
    @RequestMapping(value = "/addCashPaymentsAPI", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> addCashPaymentsAPI(
            @RequestParam(name = "contract", required = false) Contract contract,
            @RequestParam(name = "authorizationCode", required = false) String authorizationCode,
            @RequestParam(name = "transferReferenceNumber", required = false) String transferReference,
            @RequestParam(name = "oldContracts", required = false) String oldContracts,
            @RequestParam(name = "addWaivedPayment", required = false , defaultValue = "false") boolean addWaivedPayment,
            @RequestParam(name = "ignoreSendNotification", required = false, defaultValue = "false") boolean ignoreSendNotification,
            @RequestBody List<LinkedHashMap> cashPayments) throws Exception {

        if (contract == null) {
            throw new RuntimeException("Contract is NULL or Not Found");
        }

        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();

        // ACC-8662
        // Check and Start Prevent Create Other Dds
        Setup.getApplicationContext()
                .getBean(ContractService.class)
                .validateAndStartPreventCreateOtherDds(cpt);

        if (addWaivedPayment && contract.isMaidVisa() && contract.getWaivedMonths() > 0) {
            paymentService.addWaivedPayment(contract);
        }

        contractPaymentTermServiceNew.addCashPayments(
                cpt, cashPayments, authorizationCode, transferReference, oldContracts, ignoreSendNotification);
        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','create-dds-for-online-payments-without_cash-payments')")
    @RequestMapping(value = "/create-dds-for-online-payments-without_cash-payments", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> createDDsforOnlinePaymentsWithoutCashPayments(
            @RequestParam(name = "contract") Contract contract) throws Exception {

        Map payload = new HashMap();
        payload.put("id", contract.getId());
        payload.put("attachments", new ArrayList());

        return contractPaymentTermServiceNew.signDDByClientAsyncBGT(payload,
                null,
                null,
                null,
                null,
                true,
                true,
                true,
                false,
                true,
                new ArrayList<>(), // because we don't need to create Cash Payments (already created before)
                null,
                null,
                null,
                false);
    }

    // APIs for sign by Client
    @NoPermission
    @RequestMapping(value = "/saveClientSignatures", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> saveClientSignatures(
            @RequestParam(name = "id", required = false) String contractUUID,
            @RequestPart(name = "signatures", required = false) List<MultipartFile> signatures,
            @RequestParam(name = "signatureApprovalLinkUuid", required = false, defaultValue = "") String signatureApprovalLinkUuid) throws Exception {

        Contract contract = contractRep.findByUuid(contractUUID);
        // ACC-6207
        if (signatureApprovalLinkUuid != null && !signatureApprovalLinkUuid.isEmpty()) {
            DirectDebitSignatureApprovalLink link = directDebitSignatureApprovalLinkRepository.findByUuid(signatureApprovalLinkUuid);
            link.setExpired(true);
            link.setClientProvidedSignatures(signatures != null && !signatures.isEmpty());
            directDebitSignatureApprovalLinkRepository.save(link);
        }

        if (directDebitRepository.existsByActiveContractPaymentTermAndStatus(
                contract.getActiveContractPaymentTerm(), DirectDebitStatus.IN_COMPLETE) &&
                directDebitService.isRequiredBankInfoExist(contract.getActiveContractPaymentTerm())) {
            signDDByClientApi(contractUUID, null, null, null, null,
                    false, false, false, null, null, null, signatures,
                    false, false, null, false, false, false, null);
            return new ResponseEntity<>("signing DD is being processed", HttpStatus.OK);
        }

        List<Attachment> signaturesAsAttachments = null;
        if (signatures != null) {
            signaturesAsAttachments = new ArrayList();
            for (MultipartFile signature : signatures) {
                signaturesAsAttachments.add(Storage.storeTemporary("Temp Signature.png",
                        signature.getInputStream(), "temp_signature",
                        Boolean.FALSE, true));
            }
        }

        if (signaturesAsAttachments != null)
            directDebitSignatureService.saveNewDirectDebitFileSignatures(
                    signaturesAsAttachments, contract.getActiveContractPaymentTerm());

        return new ResponseEntity<>("signing DD is being processed", HttpStatus.OK);
    }

    // APIs for sign DDs by Client
    @NoPermission
    @UsedBy(others = UsedBy.Others.CC_App)
    @RequestMapping(value = "/signddbyclient", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> signDDByClientApi(
            @RequestParam(name = "id", required = false) String contractUUID,
            @RequestParam(name = "contract", required = false) Contract contract,
            @RequestParam(name = "eidPhoto", required = false) MultipartFile eidPhoto,
            @RequestParam(name = "ibanPhoto", required = false) MultipartFile ibanPhoto,
            @RequestParam(name = "accountNamePhoto", required = false) MultipartFile accountPhoto,
            @RequestParam(name = "eidPhotoChanged", required = false, defaultValue = "true") boolean eidPhotoChanged,
            @RequestParam(name = "ibanPhotoChanged", required = false, defaultValue = "true") boolean ibanPhotoChanged,
            @RequestParam(name = "accountNamePhotoChanged", required = false, defaultValue = "true") boolean accountNamePhotoChanged,
            @RequestParam(name = "eid", required = false) String eid,
            @RequestParam(name = "iban", required = false) String iban,
            @RequestParam(name = "accountName", required = false) String account,
            @RequestPart(name = "signatures", required = false) List<MultipartFile> signatures,
            @RequestParam(name = "useOldSignature", required = false, defaultValue = "false") boolean useOldSignature,
            @RequestParam(name = "needToSign", required = false, defaultValue = "true") boolean needToSign,
            @RequestPart(name = "cashPayments", required = false) List<LinkedHashMap> cashPayments,
            @RequestParam(name = "signingPaperMode", required = false, defaultValue = "false") boolean signingPaperMode,
            @RequestParam(name = "fromCcApp", required = false, defaultValue = "false") boolean fromCcApp,
            @RequestParam(name = "pendingOcr", required = false, defaultValue = "false") boolean pendingOcr,
            @RequestParam(name = "requestedUrl", required = false) String requestedUrl) throws Exception {

        return signDDByClientApiWithLogging(
                contractUUID,
                contract,
                eidPhoto,
                ibanPhoto,
                accountPhoto,
                eidPhotoChanged,
                ibanPhotoChanged,
                accountNamePhotoChanged,
                eid,
                iban,
                account,
                signatures,
                useOldSignature,
                needToSign,
                cashPayments,
                signingPaperMode,
                fromCcApp,
                pendingOcr,
                requestedUrl);
    }

    public ResponseEntity<?> signDDByClientApiWithLogging(
            String contractUUID,
            Contract contract,
            MultipartFile eidPhoto,
            MultipartFile ibanPhoto,
            MultipartFile accountPhoto,
            boolean eidPhotoChanged,
            boolean ibanPhotoChanged,
            boolean accountNamePhotoChanged,
            String eid,
            String iban,
            String account,
            List<MultipartFile> signatures,
            boolean useOldSignature,
            boolean needToSign,
            List<LinkedHashMap> cashPayments,
            boolean signingPaperMode,
            boolean fromCcApp,
            boolean pendingOcr,
            String requestedUrl) throws Exception {

        boolean clientMode = !Utils.isEmpty(contractUUID);
        if(contract == null && clientMode) contract = contractRep.findByUuid(contractUUID);
        if (contract == null) throw new RuntimeException("Contract not found");

        accountingAPILogService.loggingSignDdByClientApi(
                contractUUID,
                contract,
                eidPhoto,
                ibanPhoto,
                accountPhoto,
                eidPhotoChanged,
                ibanPhotoChanged,
                accountNamePhotoChanged,
                eid,
                iban,
                account,
                signatures,
                useOldSignature,
                needToSign,
                cashPayments,
                signingPaperMode,
                fromCcApp,
                pendingOcr,
                requestedUrl,
                contract.getId());

        List<Attachment> signaturesAsAttachments = null;
        if (signatures != null) {
            signaturesAsAttachments = new ArrayList();
            for (MultipartFile signature : signatures) {
                signaturesAsAttachments.add(Storage.storeTemporary("Temp Signature.png",
                        signature.getInputStream(), "temp_signature",
                        Boolean.FALSE, true));
            }
        }

        logger.info("contract Id: " + contract.getId());
        logger.info("signingPaperMode: " + signingPaperMode);
        logger.info("useOldSignature: " + useOldSignature);
        // ACC-5549
        if (fromCcApp && contract.isSigningPaperMode()) {
            contract.setSigningPaperMode(false);
            contractRep.save(contract);
            contract = contractRep.findOne(contract.getId());
        }

        if (contract.isSigningPaperMode() && !useOldSignature && signatures != null && !signatures.isEmpty()) {
            if (!signingPaperMode && signatures != null) {
                throw new RuntimeException("you must use Paper Mode Signing");
            }

            ContractPaymentTerm contractPaymentTerm = contract.getActiveContractPaymentTerm();

            if (!pendingOcr && ((eidPhotoChanged && eidPhoto == null) ||
                    (ibanPhotoChanged && ibanPhoto == null) ||
                    (accountNamePhotoChanged && accountPhoto == null))) {

                throw new RuntimeException("Please enter all required bank info");
            }

            //ACC-4657
            Attachment eidFile = utils.getAttachmentFromObject(eidPhoto,
                    pendingOcr ? FILE_TAG_PAPER_MODE_BANK_INFO_PENDING_OCR :
                            FILE_TAG_PAPER_MODE_BANK_INFO_EID);
            if(eidFile != null) contractPaymentTerm.addAttachment(eidFile);

            Attachment ibanFile = utils.getAttachmentFromObject(ibanPhoto,
                    pendingOcr ? FILE_TAG_PAPER_MODE_BANK_INFO_PENDING_OCR :
                            FILE_TAG_PAPER_MODE_BANK_INFO_IBAN);
            if(ibanFile != null) contractPaymentTerm.addAttachment(ibanFile);

            Attachment accountFile = utils.getAttachmentFromObject(accountPhoto,
                    pendingOcr ? FILE_TAG_PAPER_MODE_BANK_INFO_PENDING_OCR :
                            FILE_TAG_PAPER_MODE_BANK_INFO_ACCOUNT_NAME);
            if(accountFile != null) contractPaymentTerm.addAttachment(accountFile);

            contractPaymentTerm = contractPaymentTermRep.save(contractPaymentTerm);

            // ACC-7478 Extract Signatures by OCR
            try {
                Attachment firstSignature = null;
                if(signatures != null && !signatures.isEmpty()) {
                    // Extract first signature
                    firstSignature = directDebitSignatureService.extractSignature(signatures.get(0).getInputStream(), "Temp1 Signature.png", "temp_signature");

                    if(firstSignature == null) {
                        throw new RuntimeException("can't extract first signature by OCR");
                    }

                    Map payload = getPayloadAsMapForBGT(contractUUID, contract,
                            eidPhoto, ibanPhoto, accountPhoto,
                            null);

                    if (!contractPaymentTermServiceNew.validateExtractedSignatureFromOcrUsingGPT(firstSignature)) {
                        throw new RuntimeException("signature is not valid from gpt");
                    }

                    // Extract the remaining signatures
                    backgroundTaskService.create(new BackgroundTask.builder(
                            "extractSignaturesByOCR_" + contract.getId(),
                            "accounting",
                            "contractPaymentTermServiceNew",
                            "extractSignaturesByOCR")
                            .withParameters(
                                    new Class[] { Long.class, List.class, Long.class,
                                                  Map.class, String.class, Long.class, String.class, String.class, String.class,
                                                  Boolean.class, Boolean.class, Boolean.class,
                                                  Boolean.class, Boolean.class,
                                                  List.class, Boolean.class},
                                    new Object[] { contractPaymentTerm.getId(), signaturesAsAttachments
                                            .stream()
                                            .skip(1)
                                            .map(x -> x.getId().toString())
                                            .collect(Collectors.toList()), firstSignature.getId(),
                                            payload, contractUUID, contract.getId(), eid, iban, account,
                                            eidPhotoChanged, ibanPhotoChanged, accountNamePhotoChanged,
                                            useOldSignature, needToSign,
                                            cashPayments, pendingOcr})
                            .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                            .build());

                    return ResponseEntity.ok(firstSignature);
                }
            } catch (Exception e) {
                e.printStackTrace();

                // run Graphic Designer To Do when error in ocr
                contractPaymentTermServiceNew.createGraphicDesignerToDoFromVisaAsync(
                                contract, signaturesAsAttachments
                                            .stream()
                                            .map(signature -> Collections.singletonMap("id", signature.getId()))
                                            .collect(Collectors.toList()));
            }
        } else {
            if (signingPaperMode && !contract.isSigningPaperMode()) {
                throw new RuntimeException("you must use Digital Mode Signing");
            }

            createBackgroundTaskToSign(contractUUID, contract,
                    eidPhoto, ibanPhoto, accountPhoto,
                    eidPhotoChanged, ibanPhotoChanged, accountNamePhotoChanged,
                    eid, iban, account,
                    signaturesAsAttachments,
                    useOldSignature, needToSign, cashPayments,
                    null, null, null, pendingOcr);
        }

        return new ResponseEntity("signing DD is being processed", HttpStatus.OK);
    }

    // USED IN Interview Center IN PAGE accounting/sign-dd/<contract_ID>
    // WHERE THE CLIENT SIGNS DD AFTER NEW CONTRACT CREATION
    @NoPermission
    @GetMapping(value = "/getddinfoforclient")
    public ResponseEntity<?> getDDInfoForClient(
            @RequestParam(name = "id", required = false) Optional<String> contractUUID,
            @RequestParam(name = "contract", required = false) Contract contract,
            @RequestParam(name = "contractextend", required = false, defaultValue = "false") boolean contractExtend,
            @RequestParam(name = "extendduration", required = false, defaultValue = "0") int extendDuration,
            @RequestParam(name = "isCash", required = false, defaultValue = "false") boolean isCash) throws IOException, Exception {

        boolean clientMode = contractUUID.isPresent();
        contract = contract != null ? contract :
                (contractUUID.isPresent() ? contractRep.findByUuid(contractUUID.get()) : null);

        if (contract == null) throw new RuntimeException("Contract not found");

        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
        List<Map> directDebits = new ArrayList();
        List<ContractPayment> oneTimePayments = new ArrayList();

        // ACC-3860
        boolean clientHasPendingGraphicDesignerToDos = contract.isSigningPaperMode() &&
                Setup.getApplicationContext().getBean(DirectDebitRejectionFlowService.class)
                        .doesClientHavePendingDesignerToDo(contract);
        boolean existsIncomplete = directDebitRepository.existsIncompleteDDsByCpt(cpt);
        boolean existsDDs = directDebitRepository.existsByContractPaymentTerm_Contract(contract);
        boolean isNewContract = !existsDDs &&
                (contract.isOneMonthAgreement() || !paymentService.hasOneMonthlyPaymentReceived(contract));

        // ACC-7421
        boolean allDdCanceledPerCpt = existsDDs && directDebitRepository.allDdCanceledByCpt(cpt);

        // REFACTORED IN ACC-5078
        if (!existsDDs || existsIncomplete || allDdCanceledPerCpt) {
            Map m = contractPaymentTermServiceNew.getContractInfo(
                    cpt, contractExtend, extendDuration, isCash);
            directDebits = (List) m.get("directDebits");
            oneTimePayments = (List) m.get("oneTimePayments");
        }

        String paymentTermsForNewContract = "";

        // ACC-1622
        if (isNewContract) {
            List<ContractPaymentTerm> lastActiveCpt =
                    contractPaymentTermRep.getLastOldContractPaymentTermActive(
                            cpt.getContract().getClient(), cpt.getId());

            if (!lastActiveCpt.isEmpty()) {
                logger.log(Level.SEVERE, "lastActiveCpt id: " + lastActiveCpt.get(0).getId());
                cpt.setAccountName(lastActiveCpt.get(0).getAccountName());
                cpt.setIbanNumber(lastActiveCpt.get(0).getIbanNumber());
                cpt.setEid(lastActiveCpt.get(0).getEid());
                cpt.setBank(lastActiveCpt.get(0).getBank());
                cpt.setBankName(lastActiveCpt.get(0).getBankName());

                if (lastActiveCpt.get(0).getAttachment(FILE_TAG_BANK_INFO_ACCOUNT_NAME) != null)
                    cpt.addAttachment(lastActiveCpt.get(0).getAttachment(FILE_TAG_BANK_INFO_ACCOUNT_NAME));

                if (lastActiveCpt.get(0).getAttachment(FILE_TAG_BANK_INFO_IBAN) != null)
                    cpt.addAttachment(lastActiveCpt.get(0).getAttachment(FILE_TAG_BANK_INFO_IBAN));

                if (lastActiveCpt.get(0).getAttachment(FILE_TAG_BANK_INFO_EID) != null)
                    cpt.addAttachment(lastActiveCpt.get(0).getAttachment(FILE_TAG_BANK_INFO_EID));
            }
            // ACC-7151
//            paymentTermsForNewContract = contract.isOneMonthAgreement() ?
//                    DDUtils.getPaymentTermForOneMonthAgreementContract(cpt) :
//                    DDUtils.getPaymentTermsForNewContract(cpt);
        }

        logger.log(Level.INFO, "accountNamePhoto: " + (cpt.getAttachment(FILE_TAG_BANK_INFO_ACCOUNT_NAME) != null ? "TRUE" : "FALSE"));
        logger.log(Level.INFO, "ibanPhoto: " + (cpt.getAttachment(FILE_TAG_BANK_INFO_IBAN) != null ? "TRUE" : "FALSE"));
        logger.log(Level.INFO, "eidPhoto: " + (cpt.getAttachment(FILE_TAG_BANK_INFO_EID) != null ? "TRUE" : "FALSE"));

        boolean postponedClosed = false;

        if (contract.getStatus().equals(ContractStatus.POSTPONED)) {
            for (int i = 0; i < cpt.getAttachments().size(); i++) {
                Attachment att = cpt.getAttachments().get(i);
                if (att.getTag().contains(FILE_TAG_POSTPONED_SIGNATURE)) {
                    postponedClosed = true;
                    break;
                }
            }
        }

        Map result = new HashMap();
        result.put("clientHasFrontEIDDocument", contractPaymentTermHelper.hasClientFrontSideDoc(contract.getClient()));
        boolean allowSigning = (existsIncomplete || !existsDDs || allDdCanceledPerCpt) && !clientHasPendingGraphicDesignerToDos;
        result.put("closed",!allowSigning);
        result.put("isNewContract", false);
        result.put("postPonedClosed", postponedClosed);
        result.put("paymentDuration", contract.getPaymentsDuration());
        result.put("accountName", cpt.getAccountName());
        result.put("accountNamePhoto", cpt.getAttachments().stream().filter(attachment ->
                attachment.getTag().equalsIgnoreCase(FILE_TAG_BANK_INFO_ACCOUNT_NAME)).findFirst().orElse(null));
        result.put("iban", cpt.getIbanNumber());
        result.put("ibanPhoto", cpt.getAttachments().stream().filter(attachment ->
                attachment.getTag().equalsIgnoreCase(FILE_TAG_BANK_INFO_IBAN)).findFirst().orElse(null));
        result.put("bankName", cpt.getBankName());
        result.put("eid", cpt.getEid());
        result.put("eidPhoto", cpt.getAttachments().stream().filter(attachment ->
                attachment.getTag().equalsIgnoreCase(FILE_TAG_BANK_INFO_EID)).findFirst().orElse(null));
        result.put("oneTimePayments", oneTimePayments);
        result.put("directDebits", directDebits);
        result.put("paymentTermsForNewContract", paymentTermsForNewContract);
        result.put("needSignatures", (oneTimePayments != null && !oneTimePayments.isEmpty()) ||
                (directDebits != null && !directDebits.isEmpty()));
        result.put("number_of_required_signatures", Integer.parseInt(
                Setup.getParameter(Setup.getCurrentModule(), clientMode ?
                        AccountingModule.PARAMETER_NUMBER_OF_DIRECT_DEBIT_SIGNATURES_FOR_CLIENT :
                        AccountingModule.PARAMETER_NUMBER_OF_DIRECT_DEBIT_SIGNATURES)));

        List<DirectDebitSignature> lastSignaturesAttachments = (List<DirectDebitSignature>) directDebitSignatureService
                .getLastSignatureType(cpt, true, false)
                .get("currentSignatures");

        if (!clientMode) {
            if (!isNewContract && lastSignaturesAttachments != null)
                result.put("signAttachment", lastSignaturesAttachments.get(0).getSignatureAttachment());
            result.put("remote", contract.getInterview() != null && contract.getInterview().isRemoteSale());
            result.put("contract", contract);
        } else {
            Map clientMap = new HashMap();
            clientMap.put("name", contract.getClient().getName());
            result.put("client", clientMap);

            Map contractMap = new HashMap();
            contractMap.put("status", contract.getStatus());
            contractMap.put("workerCurrentSituation", contract.getWorkerCurrentSituation());
            contractMap.put("workerSalary", contract.getWorkerSalaryWithoutVat());
            contractMap.put("isWorkerSalaryVatted", contract.isWorkerSalaryVatted());
            Date firstDiscountedMonth = new Date(calculateDiscountsWithVatService.getDiscountStartDateInMillis(cpt));
            contractMap.put("firstDiscountedMonth", DateUtil.formatMonth(firstDiscountedMonth) + " 1st, " + DateUtil.formatYear(firstDiscountedMonth));
            contractMap.put("contractProspectType", contract.getContractProspectType());
            contractMap.put("isProRated", contract.getIsProRated());
            contractMap.put("signingPaperMode", contract.isSigningPaperMode());
            result.put("contract", contractMap);

            Map contractPaymentTermMap = new HashMap();
            contractPaymentTermMap.put("isEidRejected", cpt.getIsEidRejected());
            contractPaymentTermMap.put("isIBANRejected", cpt.getIsIBANRejected());
            contractPaymentTermMap.put("isAccountHolderRejected", cpt.getIsAccountHolderRejected());
            contractPaymentTermMap.put("eidRejectionReason", cpt.getEidRejectionReason());
            contractPaymentTermMap.put("ibanRejectionReason", cpt.getIbanRejectionReason());
            contractPaymentTermMap.put("accountNameRejectionReason", cpt.getAccountNameRejectionReason());
            contractPaymentTermMap.put("bankInfoTextBased", cpt.getBankInfoTextBased());
            contractPaymentTermMap.put("discountEffectiveAfter", cpt.getDiscountEffectiveAfter());
            result.put("contractPaymentTerm", contractPaymentTermMap);

            if (!isNewContract && (boolean) result.get("needSignatures") &&
                    lastSignaturesAttachments != null && !lastSignaturesAttachments.isEmpty()) {

                result.put("signAttachment", lastSignaturesAttachments.get(0));
            }
        }

        return new ResponseEntity(contractPaymentTermHelper.projectResultMap(result), HttpStatus.OK);
    }

    /* Start Switch maid functions*/
    @PreAuthorize("hasPermission('ContractPaymentTerm','validateSwitchMaid')")
    @RequestMapping(value = "/validateswitchmaid/{contract_id}/{new_housemaid_id}", method = RequestMethod.POST)
    public ResponseEntity validateSwitchMaid(
            @PathVariable("contract_id") Contract contract,
            @PathVariable("new_housemaid_id") Housemaid newHousemaid) {

        switchingNationalityService.validateSwitchMaid(contract, newHousemaid);
        return okResponse();
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','switchmaid')")
    @Transactional
    @RequestMapping(value = "/switchmaid", method = RequestMethod.GET)
    // this is required to rollback for Business rules
    public ResponseEntity<?> switchMaidAPI(long contractId, long newHousemaidId, boolean saveAndRollback, Boolean keepCurrentDDs, boolean addVatDDs)
            throws Exception {

        return ResponseEntity.ok(switchingNationalityService.switchMaid(
                contractId, newHousemaidId, new DateTime(), saveAndRollback,
                keepCurrentDDs, false, addVatDDs));
    }

    @Autowired
    ProjectionFactory projectionFactory;

    @PreAuthorize("hasPermission('ContractPaymentTerm','getSwitchMaidInfo')")
    @RequestMapping(value = "/getswitchmaidinfo", method = RequestMethod.GET)
    public ResponseEntity<?> getSwitchMaidInfo(@RequestParam("contractId") long contractId,
                                               @RequestParam("newMaidId") long newHousemaidId)
            throws Exception {

        Map result = new HashMap();
        try {
            Contract contract = contractRep.findOne(contractId);
            Date upgradingNationalityDate = contract.getUpgradingNationalityDate();

            DateTime replacementDate;
            if (upgradingNationalityDate != null) {
                replacementDate = new DateTime(upgradingNationalityDate);
            } else {
                replacementDate = DateTime.now();
            }

            switchingNationalityService.switchMaid(contractId, newHousemaidId, replacementDate, true, false, false, true);
        } catch (SaveAndRollbackException ex) {
            result = (Map) ex.getData();
            List<Payment> payments = paymentRepository.getContractRemainingPayment(contractRep.findOne(contractId),
                    Arrays.asList(PaymentStatus.PDC, PaymentStatus.RECEIVED),
                    Arrays.asList("monthly_payment"), null);

            List<PaymentSalesProjection> projectedPayments = new ArrayList();
            if (payments != null) {
                projectedPayments = payments.stream().map(payment ->
                        projectionFactory.createProjection(PaymentSalesProjection.class, payment)).collect(Collectors.toList());
            }

            result.put("payments", projectedPayments);
        } catch (Exception e) {
            throw new TechnicalException(e);
        }
        return new ResponseEntity<>(contractPaymentTermHelper.projectResultMap(result), HttpStatus.OK);
    }

    // CMA-1192
    @JwtSecured
    @PreAuthorize("hasPermission('ContractPaymentTerm','getSwitchMaidInfoForCCAPP_API_V1')")
    @RequestMapping(value = "/ccApp/switchmaid/getswitchmaidinfo", method = RequestMethod.GET)
    public ResponseEntity getSwitchMaidInfoForCCAPP_API_V1(
            @RequestParam("contractId") Contract contract,
            @RequestParam(value = "newMaidId", required = false) Housemaid newHousemaid,
            @RequestParam(value = "nationalityGroup", required = false) String nationalityGroup,
            @RequestParam(value = "nationalityId", required = false) PicklistItem newNationality,
            @RequestParam(value = "liveOut", required = false) Boolean newLiveOut) {

        Map<String, Object> m = switchingNationalityService
                .getSwitchMaidInfoForCCAPP_V2(contract,
                        newHousemaid != null ? newHousemaid.getNationality() : newNationality ,
                        newHousemaid != null ? newHousemaid.getLiveOut() :
                                newLiveOut != null ? newLiveOut : contract.getLiveOut() ,
                        nationalityGroup);
        m.put("canHireMaid", m.get("canHireMaid"));

        return ResponseEntity.ok(m);
    }

    // ACC-3368
    @JwtSecured
    @PreAuthorize("hasPermission('ContractPaymentTerm','getSwitchMaidInfoForCCAPP_API_V2')")
    @RequestMapping(value = "/ccApp/switchmaid/getswitchmaidinfo_v2", method = RequestMethod.GET)
    public ResponseEntity getSwitchMaidInfoForCCAPP_API_V2(
            @RequestParam("contractId") Contract contract,
            @RequestParam(value = "newMaidId", required = false) Housemaid newHousemaid,
            @RequestParam(value = "nationalityGroup", required = false) String nationalityGroup,
            @RequestParam(value = "nationalityId", required = false) PicklistItem newNationality,
            @RequestParam(value = "liveOut", required = false) Boolean newLiveOut) {

        Map<String, Object> m = switchingNationalityService
                .getSwitchMaidInfoForCCAPP_V2(contract,
                        newHousemaid != null ? newHousemaid.getNationality() : newNationality ,
                        newHousemaid != null ? newHousemaid.getLiveOut() :
                                newLiveOut != null ? newLiveOut : contract.getLiveOut() ,
                        nationalityGroup);
        m.put("canHireMaid", m.get("canHireMaid"));

        return ResponseEntity.ok(m);
    }

    // ACC-3368
    @JwtSecured
    @UsedBy(others = UsedBy.Others.CC_App)
    @PreAuthorize("hasPermission('ContractPaymentTerm','getSwitchMaidInfoForCCAPP_API_V2')")
    @RequestMapping(value = "/ccApp/switchmaid/getswitchmaidinfoCma3750", method = RequestMethod.GET)
    public ResponseEntity getswitchmaidinfoCma3750(
            @RequestParam("contractId") Contract contract,
            @RequestParam(value = "newMaidId", required = false) Housemaid newHousemaid,
            @RequestParam(value = "nationalityGroup", required = false) String nationalityGroup,
            @RequestParam(value = "nationalityId", required = false) PicklistItem newNationality,
            @RequestParam(value = "liveOut", required = false) Boolean newLiveOut) {

        Map<String, Object> m = switchingNationalityService
                .getSwitchMaidInfoForCCAPP_V2(contract,
                        newHousemaid != null ? newHousemaid.getNationality() : newNationality ,
                        newHousemaid != null ? newHousemaid.getLiveOut() :
                                newLiveOut != null ? newLiveOut : contract.getLiveOut() ,
                        nationalityGroup);
        m.put("canHireMaid", m.get("canHireMaid"));

        return ResponseEntity.ok(m);
    }

    // CMA-3592
    @JwtSecured
    @PreAuthorize("hasPermission('ContractPaymentTerm','canHireMaid')")
    @RequestMapping(value = "/ccApp/switchmaid/canHireMaid", method = RequestMethod.GET)
    public ResponseEntity canHireMaid(
            @RequestParam("contractId") Contract contract,
            @RequestParam("newMaidId") Housemaid newHousemaid) {
        // ACC-5629
        Housemaid oldHousemaid = Setup.getApplicationContext().getBean(ContractService.class)
                .getLastHousemaid(contract);

        SwitchingNationalityService.SwitchingNationalityType switchingNationalityType
                = switchingNationalityService.getSwitchingNationalityType(oldHousemaid, newHousemaid);

        return ResponseEntity.ok(new HashMap<String, Object>(){{
            put("canHireMaid", true);
        }});
    }

    @JwtSecured
    @UsedBy(others = UsedBy.Others.CC_App)
    @PreAuthorize("hasPermission('ContractPaymentTerm','canHireMaid')")
    @RequestMapping(value = "/ccApp/switchmaid/canHireMaidCma3750", method = RequestMethod.GET)
    public ResponseEntity canHireMaidCma3750(
            @RequestParam("contractId") Contract contract,
            @RequestParam("newMaidId") Housemaid newHousemaid) {
        //ACC-5629
        Housemaid oldHousemaid = Setup.getApplicationContext().getBean(ContractService.class)
                .getLastHousemaid(contract);

        SwitchingNationalityService.SwitchingNationalityType switchingNationalityType =
                switchingNationalityService.getSwitchingNationalityType(oldHousemaid, newHousemaid);

        return ResponseEntity.ok(new HashMap<String, Object>(){{
            put("canHireMaid", true);
        }});
    }

    // ACC-2938
    @JwtSecured
    @PreAuthorize("hasPermission('ContractPaymentTerm','getSwitchMaidInfoForCCAPP_JsonResponse_API')")
    @GetMapping(value = "/getswitchmaidinfo-ccApp/jsonResponse")
    public ResponseEntity getSwitchMaidInfoForCCAPP_JsonResponse_API(
            @RequestParam("contractId") Long contractId,
            @RequestParam("newMaidId") Long newHousemaidId) throws Exception {

        return new ResponseEntity(
                contractPaymentTermServiceNew.getSwitchMaidPricingDifference(contractId, newHousemaidId, new DateTime(), true),
                HttpStatus.OK);
    }

    //used from CMM do not change it
    public Map getSwitchMaidInfoForCCAPP_JsonResponse(Long contractId, Long newHousemaidId) throws Exception {
        return contractPaymentTermServiceNew.getSwitchMaidPricingDifference(contractId, newHousemaidId, new DateTime(), true);
    }

    @JwtSecured
    @UsedBy(others = UsedBy.Others.CC_App)
    @PreAuthorize("hasPermission('ContractPaymentTerm','switchNationality_amendingDirectDebitForms_confirm')")
    @RequestMapping(value = "/switchmaid/amend-dd-forms/confirm", method = RequestMethod.POST)
    public ResponseEntity switchNationality_amendingDirectDebitForms_confirm(@RequestBody Map requestBody) {
        return okResponse();
    }

    public boolean sendSwitchNationalityAmendingDirectDebitFormsEmail(Long contractId, Long housemaidId) throws Exception {
        //ACC-5497 removed condition on fromCcApp

        Contract contract = contractRep.findOne(contractId);
        String clientName = contract.getClient().getName();

        Housemaid housemaid = housemaidId != null ? housemaidRep.findOne(housemaidId) : contract.getHousemaid();

        if (housemaid == null) {
            logger.severe("Housemaid is null -> do nothing");
            return false;
        }

        Map switchingResultMap = contractPaymentTermServiceNew.switchNationality_amendingDirectDebitForms_getTerms(contract, null, housemaid);

        String emailContent = null;

        if (switchingResultMap.containsKey("paymentTerms") && switchingResultMap.get("paymentTerms") != null) {
            emailContent = switchingResultMap.get("paymentTerms").toString();
            if (StringUtils.isEmpty(emailContent) && switchingResultMap.containsKey("relatedCMS") && switchingResultMap.get("relatedCMS") != null) {
                emailContent = switchingResultMap.get("relatedCMS").toString();
            }
        }

        if (StringUtils.isEmpty(emailContent)) {
            return false;
        }

        Map<String, String> parameters = new HashMap();
        parameters.put("client_name", clientName);
        parameters.put("details", emailContent);

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff("switching_nationality_dd_forms_amending_confirmation",
                        parameters, Setup.getParameter(Setup.getCurrentModule(),
                                AccountingModule.PARAMETER_SWITCHING_NATIONALITY_DD_FORMS_AMENDING_CONFIRMATION_MAIL),
                        "Get WhatsApp approval: " + clientName + " to amend his Bank Direct Debit");

        return true;
    }

    @JwtSecured
    @PreAuthorize("hasPermission('ContractPaymentTerm','switchNationality_amendingDirectDebitForms_getTermsAPI')")
    @RequestMapping(value = "/switchmaid/amend-dd-forms/getTerms", method = RequestMethod.GET)
    public ResponseEntity switchNationality_amendingDirectDebitForms_getTermsAPI(@RequestParam("contractId") Contract contract,
                                                                                 @RequestParam("newMaidId") Housemaid newHousemaid) throws Exception {

        if (contract == null || newHousemaid == null) return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();

        Map response = contractPaymentTermServiceNew.switchNationality_amendingDirectDebitForms_getTerms(contract, contract.getHousemaid(), newHousemaid);
        return ResponseEntity.ok(response);
    }

    @JwtSecured
    @UsedBy(others = UsedBy.Others.CC_App)
    @PreAuthorize("hasPermission('ContractPaymentTerm','switchNationality_amendingDirectDebitForms_getTermsAPI')")
    @GetMapping(value = "/switchmaid/amend-dd-forms/getTermsCma3750")
    public ResponseEntity switchNationality_amendingDirectDebitForms_getTermsAPICma3750(
            @RequestParam("contractId") Contract contract,
            @RequestParam("newMaidId") Housemaid newHousemaid,
            @RequestParam(value = "replacementDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date replacementDate) throws Exception {

        if (contract == null || newHousemaid == null) return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();

        return ResponseEntity.ok(contractPaymentTermServiceNew
                .switchNationality_amendingDirectDebitForms_getTermsCma3750(
                        contract, contract.getHousemaid(), newHousemaid,
                        replacementDate == null ? DateTime.now() : new DateTime(replacementDate)));
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','getDowngradeNationalityRefundAmount')")
    @GetMapping(value = "/getDowngradeNationalityRefundAmount")
    public Integer getDowngradeNationalityRefundAmount(
            @RequestParam("contractId") Contract contract,
            @RequestParam("newMaidId") Housemaid newHousemaid,
            @RequestParam(value = "replacementDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date replacementDate) throws Exception {

        if (contract == null || newHousemaid == null) return null;
        if (!contract.isPayingViaCreditCard() && !contract.isOneMonthAgreement()) return null;

        ContractPaymentTerm currentCpt = contract.getActiveContractPaymentTerm();
        if (!switchingNationalityService.getSwitchingNationalityType(
                currentCpt.getHousemaid(), newHousemaid).equals(
                        SwitchingNationalityService.SwitchingNationalityType.DOWNGRADING)) return null;

        int amount = contractPaymentTermServiceNew.getDowngradeNationalityRefundAmount(
                        contract, currentCpt, newHousemaid,
                        replacementDate == null ? DateTime.now() : new DateTime(replacementDate));

        logger.info("Contract id: " + contract.getId() +
                "; newHousemaid id: " + newHousemaid.getId() +
                "; amount: " + amount);

        return amount > 0 ? amount : null;
    }

    // ACC-639
    @JwtSecured
    @PreAuthorize("hasPermission('ContractPaymentTerm','getswitchmaidinfo-ccApp')")
    @RequestMapping(value = "/getswitchmaidinfo-ccApp", method = RequestMethod.GET)
    public ResponseEntity getSwitchMaidInfoForCCAPP(@RequestParam("contractId") long contractId,
                                                    @RequestParam("newMaidId") long newHousemaidId,
                                                    @RequestParam(value = "nationalityGroup", required = false) String nationalityGroup) throws Exception {

        String response = "";
        boolean clientPaidVAT = false;
        SwitchingNationalityService.SwitchingNationalityType switchingNationalityType = null;
        Housemaid newHousemaid = housemaidRep.findOne(newHousemaidId);
        String newNationalityName = nationalityGroup == null || nationalityGroup.isEmpty() ? newHousemaid.getNationality().getName() : nationalityGroup;
        try {
            DateTime replacementDate = DateTime.now();
            Contract contract = contractRep.findOne(contractId);
            switchingNationalityService.validateSwitchMaid(contract, newHousemaid);
            ContractPaymentTerm currentCPT = (ContractPaymentTerm) contractPaymentTermHelper.getActiveContractPaymentTermByContract(contract)
                    .get("contractPaymentTerm");

            clientPaidVAT = contract.getClientPaidVat() != null && contract.getClientPaidVat();

            Housemaid currentHousemaid = currentCPT.getHousemaid();
            switchingNationalityType = switchingNationalityService.getSwitchingNationalityType(currentHousemaid, newHousemaid);

            if (switchingNationalityType.equals(SwitchingNationalityService.SwitchingNationalityType.SAME_GRADE)) {

                response = "You can replace your maid with " + StringUtils.getHousemaidNationality(newNationalityName) + " maid without any changes to your monthly payments";
            } else {
                switchingNationalityService.switchMaid(contractId, newHousemaidId, replacementDate, true, false, true, false);
            }
        } catch (SaveAndRollbackException ex) {
            Map result = (Map) ex.getData();
            List<DirectDebitStatus> acceptedStatuses = Arrays.asList(DirectDebitStatus.IN_COMPLETE, DirectDebitStatus.PENDING_DATA_ENTRY, DirectDebitStatus.PENDING);
            ContractPaymentTerm oldCPT = (ContractPaymentTerm) result.get("currentContractPaymentTerm");
            ContractPaymentTerm newCPT = (ContractPaymentTerm) result.get("newContractPaymentTerm");
            List<DirectDebit> dds = ((List<DirectDebit>) (result.get("wholeDirectDebits") != null ? result.get("wholeDirectDebits") : new ArrayList<DirectDebit>()));
            boolean isDowngradingNationality = switchingNationalityType != null && switchingNationalityType.equals(SwitchingNationalityService.SwitchingNationalityType.DOWNGRADING);
            List<DirectDebit> filteredDirectDebits = dds.stream().filter(dd -> acceptedStatuses.contains(dd.getStatus()) || acceptedStatuses.contains(dd.getMStatus()))
                    .filter(dd -> !isDowngradingNationality || (isDowngradingNationality && !dd.getType().equals(DirectDebitType.ONE_TIME)))
                    .sorted(Comparator.comparing(DirectDebit::getStartDate)).
                            collect(Collectors.toList());
            response = DDUtils.getDirectDebitsDescriptionForCCAPP_SwitchMaid(newCPT, oldCPT, filteredDirectDebits, newNationalityName);
        }

        return new ResponseEntity(response, HttpStatus.OK);
    }
    /* end Switch maid functions*/


    // USED IN Interview Center IN PAGE accounting/sign-dd/<contract_ID>
    // WHERE THE CLIENT SIGNS DD AFTER NEW CONTRACT CREATION
    @PreAuthorize("hasPermission('ContractPaymentTerm','checkIfContractNeedSign')")
    @GetMapping("/checkifcontractneedsign/{contractId}")
    public boolean checkIfContractNeedSign(
            @PathVariable("contractId") Contract contract,
            @RequestParam(value = "checkSpouse", required = false) Boolean checkSpouse) {

        if (checkSpouse != null && checkSpouse &&
                (contract.getClient().getNormalizedSpouseMobileNumber() == null ||
                        contract.getClient().getNormalizedSpouseMobileNumber().isEmpty()))
            throw new RuntimeException("There is no mobile number for spouse");

        List<ContractPaymentTerm> cpt = contractPaymentTermRep.findByContractAndIsActive(
                contract, true);
        if (cpt.isEmpty()) return true;

        if (contract.getStatus().equals(ContractStatus.POSTPONED)) {
            return cpt.get(0).getAttachments().stream()
                    .noneMatch(a -> a.getTag().contains(FILE_TAG_POSTPONED_SIGNATURE));
        }

        // REFACTORED IN ACC-5078
        boolean noDDs = !directDebitRepository.existsByContractPaymentTerm_Contract(contract);
        boolean existsIncomplete = directDebitRepository.existsIncompleteDDsByCpt(cpt.get(0));

        return existsIncomplete || noDDs;
    }

   /* @PreAuthorize("hasPermission('ContractPaymentTerm','testocr')")
    @GetMapping(value = "/testocr")
    public ContractPaymentTerm testOcr(
            @RequestParam("eid") MultipartFile eid,
            @RequestParam("iban") MultipartFile iban,
            @RequestParam("acc") MultipartFile acc) throws Exception {

        return contractPaymentTermHelper.extractBankInfoByOCR(new ContractPaymentTerm(),
                eid.getInputStream(), iban.getInputStream(), acc.getInputStream(),
                true, true, true,
                null, null, null);
    }*/

    @PreAuthorize("hasPermission('ContractPaymentTerm','testocriban')")
    @RequestMapping(value = "/testocriban", method = RequestMethod.GET)
    public ResponseEntity<?> testocriban(
            @RequestParam("iban") MultipartFile iban) throws Exception {

        Attachment attachment = Storage.storeTemporary("iban.png", iban.getInputStream(), FILE_TAG_BANK_INFO_IBAN, true);
        String ibanText = contractPaymentTermHelper.getOcrText(attachment);
        String result = null;
        if (ibanText != null && !ibanText.isEmpty()) {
            result = contractPaymentTermHelper.getMatcherByPattern(ibanText, OCR_IBAN_PATTERN);
        }
        return new ResponseEntity(result, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','sendDDMessageToClient')")
    @GetMapping(value = "/sendddmessagetoclient/{contractId}")
    @Transactional
    public ResponseEntity<?> sendDDMessageToClient(
            @PathVariable("contractId") Contract contract,
            @RequestParam(name = "spouseWillSignDD", required = false, defaultValue = "false") boolean spouseWillSignDD,
            @RequestParam(name = "ignoreErrorMsgs", required = false, defaultValue = "false") boolean ignoreErrorMsgs) {

        return contractPaymentTermServiceNew.sendDDInfoMessages(
                contract, spouseWillSignDD, ignoreErrorMsgs);
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','getDdSignLink')")
    @RequestMapping(value = "/getddsignlink/{contractId}", method = RequestMethod.GET)
    public String getDdSignLink(@PathVariable("contractId") Contract contract) {
        Map<String, Object> map = new HashMap<>();
        map.put("cpt", contract.getActiveContractPaymentTerm());
        map.put("additionalInfo", new HashMap<String, Object>() {{
            put("sentFrom", "/accounting/ContractPaymentTerm/getddsignlink");
        }});
        return utils.getSingDDLink(map);
    }

    @NoPermission
    @PostMapping(value = "/getnamefromeidphoto")
    public String getNameFromEidPhoto(@RequestParam("photo") MultipartFile photo) throws IOException {
        return contractPaymentTermHelper.getNameFromEidPhoto(photo);
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','getAmountAtTime')")
    @RequestMapping(value = "/getAmountAtTime/{contractId}", method = RequestMethod.GET)
    public ResponseEntity getAmountAtTime(
            @PathVariable("contractId") Contract contract,
            @RequestParam(value = "purpose", required = false) PaymentRequestPurpose purpose,
            @RequestParam(value = "partialRefund", required = false) Boolean partialRefund,
            @RequestParam(value = "date", required = false) Date date) throws IOException {

        if (contract == null) throw new RuntimeException("Contract not Found");
        if (date == null) date = new Date();
        Date targetDate = new DateTime(date).dayOfMonth().withMinimumValue()
                .withTimeAtStartOfDay().toDate();
        partialRefund = partialRefund != null && partialRefund;

        return ResponseEntity.ok(Setup.getApplicationContext()
                .getBean(ClientRefundService.class)
                .getRefundData(contract, targetDate, purpose, partialRefund));
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','remove-signature-from-templates')")
    @RequestMapping(value = "/remove-signature-from-templates", method = RequestMethod.POST)
    public ResponseEntity removeSignatureFromTemplateAPI(@RequestParam("fromDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date fromDate,
                                                         @RequestParam("toDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date toDate) throws IOException {
        List<Long> passedCPTs = new ArrayList();

        List<ContractPaymentTerm> cptList = contractPaymentTermRep.findByCreationDateBetween(fromDate, toDate);
        for (ContractPaymentTerm cpt : cptList) {
            if (removeSignatureFromReceipt(cpt.getId())) {
                passedCPTs.add(cpt.getId());
            }
        }

        return ResponseEntity.ok(passedCPTs);
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','test/remove-signature-from-templates')")
    @RequestMapping(value = "/test/remove-signature-from-templates", method = RequestMethod.POST)
    public void removeSignatureFromTemplate_Test_API(
            HttpServletResponse httpServletResponse,
            @RequestParam(name = "template") MultipartFile template,
            @RequestParam(name = "imageIdx", required = false) Integer imageIdx) throws IOException {

        InputStream is = null;
        try {
            if (imageIdx != null) {
                is = PdfHelper.removeImageAtIndex(template.getInputStream(), 0, imageIdx);
            } else {
                is = PdfHelper.removeImageAtLastIndexBeforeText(template.getInputStream(), 0, "Client Signature");
            }

            createDownloadResponse(httpServletResponse,
                    template.getName() + "_without_signature.pdf",
                    "pdf",
                    is);
        }  finally {
            StreamsUtil.closeStream(is);
        }
    }

    @Transactional
    public boolean removeSignatureFromReceipt(Long cptID) {
        try {
            ContractPaymentTerm cpt = getRepository().findOne(cptID);
            Attachment oldReceipt = cpt.getAttachment(FILE_TAG_PAYMENTS_RECEIPT);
            InputStream is = PdfHelper.removeImageAtLastIndexBeforeText(Storage.getStream(oldReceipt), 0, "Client Signature");
            if (is != null) {
                oldReceipt.setTag(FILE_TAG_PAYMENTS_RECEIPT + "_old");
                attachementRepository.save(oldReceipt);
                Attachment newReceipt = Storage.storeTemporary("Payment Terms Form.pdf", is, FILE_TAG_PAYMENTS_RECEIPT, true);
                cpt.addAttachment(newReceipt);
                getRepository().save(cpt);
            } else {
                logger.log(Level.SEVERE, "cpt with no signature: " + cptID);
            }
        } catch (Exception e) {
            return false;
        }

        return true;
    }

    @JwtSecured
    @UsedBy(others = UsedBy.Others.CC_App)
    @PreAuthorize("hasPermission('ContractPaymentTerm','switchBankAccountAPI')")
    @GetMapping(value = "/switchingBankAccount/last-switching-info/{contractId}")
    public ResponseEntity switchBankAccountAPI(
            @PathVariable(name = "contractId") Contract contract) throws ParseException {

        Map<String, Object> r = switchingBankAccountService.getLastSwitchBankAccountInfo(contract);

        r.put("date", ccAppContentService.allowSwitchBankAccountFromBouncedPaymentOptions(contract) ? null : new Date()); // ACC-6966
        return ResponseEntity.ok(r);
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','/erp/switchingBankAccount/last-switching-info/{contractId}')")
    @RequestMapping(value = "/erp/switchingBankAccount/last-switching-info/{contractId}", method = RequestMethod.GET)
    public ResponseEntity switchBankAccount_ERP_API(@PathVariable(name = "contractId") Contract contract) throws ParseException {
        return ResponseEntity.ok(switchingBankAccountService.getLastSwitchBankAccountInfo(contract));
    }

    @NoPermission
    @GetMapping(value = "/erp/switchingBankAccount/last-switching-info-uuid/{contractUUID}")
    public ResponseEntity switchBankAccount_ERP_UUID_API(@PathVariable(name = "contractUUID") String uuid) throws ParseException {

        Contract contract = Setup.getRepository(ContractRepository.class).findByUuid(uuid);
        return ResponseEntity.ok(switchingBankAccountService.getLastSwitchBankAccountInfo(contract));
    }

    @JwtSecured
    @PreAuthorize("hasPermission('ContractPaymentTerm','getLastSwitchingDateAPI')")
    @GetMapping(value = "/switchingBankAccount/last-switching-date/{contractId}")
    public ResponseEntity<?> getLastSwitchingDateAPI(
            @PathVariable("contractId") Contract contract,
            @RequestParam(value = "diffInDays", defaultValue = "false") boolean diffInDays) throws ParseException {

        if (diffInDays) {
            return ResponseEntity.ok(switchingBankAccountService.getLastSwitchingDateInDays(contract));
        }

        return ResponseEntity.ok(ccAppContentService.allowSwitchBankAccountFromBouncedPaymentOptions(contract) ? null : new Date()); // ACC-6966
    }

    @JwtSecured
    @UsedBy(others = UsedBy.Others.CC_App)
    @PreAuthorize("hasPermission('ContractPaymentTerm','switchBankAccountAPI')")
    @PostMapping(value = "/switchingBankAccount/doSwitch")
    public ResponseEntity switchBankAccountAPI(
            @RequestParam(name = "id") String contractUUID,
            @RequestParam(name = "bouncedPaymentId", required = false) Payment bouncedPayment,
            @RequestParam(name = "notificationId", required = false) Long notificationId,
            @RequestParam(name = "eidPhoto", required = false) MultipartFile eidPhoto,
            @RequestParam(name = "ibanPhoto", required = false) MultipartFile ibanPhoto,
            @RequestParam(name = "accountNamePhoto", required = false) MultipartFile accountPhoto,
            @RequestPart(name = "signatures", required = false) List<MultipartFile> signatures,
            @RequestParam(name = "pendingOcr", required = false, defaultValue = "false") boolean pendingOcr) throws Exception {

        Setup.getApplicationContext().getBean(CCAppService.class).insertTrackingLog(contractUUID, CcAppAction.CHANGE_BANK_DETAILS);

        return ResponseEntity.ok(switchBankAccount(contractUUID, bouncedPayment, notificationId,
                eidPhoto, ibanPhoto, accountPhoto, signatures, pendingOcr));
    }

    @NoPermission
    @PostMapping(value = "/erp/switchingBankAccount/doSwitch")
    public ResponseEntity switchBankAccountFromErpAPI(
            @RequestParam(name = "id") String contractUUID,
            @RequestParam(name = "eidPhoto", required = false) MultipartFile eidPhoto,
            @RequestParam(name = "ibanPhoto", required = false) MultipartFile ibanPhoto,
            @RequestParam(name = "accountNamePhoto", required = false) MultipartFile accountPhoto,
            @RequestPart(name = "signatures", required = false) List<MultipartFile> signatures,
            @RequestParam(name = "pendingOcr", required = false, defaultValue = "false") boolean pendingOcr,
            @RequestParam(name = "requestedUrl", required = false) String requestedUrl) throws Exception {

        logger.info("Requested Url: " + (requestedUrl == null ? "NULL" : requestedUrl));

        return ResponseEntity.ok(switchBankAccount(contractUUID, null, null,
                eidPhoto, ibanPhoto, accountPhoto,
                signatures, pendingOcr));
    }

    public String switchBankAccount(
            String contractUUID,
            Payment bouncedPayment,
            Long notificationId,
            MultipartFile eidPhoto,
            MultipartFile ibanPhoto,
            MultipartFile accountPhoto,
            List<MultipartFile> signatures,
            boolean pendingOcr) throws Exception {

        Contract contract = !StringUtils.isEmpty(contractUUID) ? contractRep.findByUuid(contractUUID) : null;
        if (contract == null) throw new RuntimeException("Contract not Found");

        Attachment eidPhotoAttachment = contract.getAttachment(Contract.TEMP_EID_ATTACHMENT_TAG);
        Attachment ibanPhotoAttachment = contract.getAttachment(Contract.TEMP_IBAN_ATTACHMENT_TAG);
        Attachment accountPhotoAttachment = contract.getAttachment(Contract.TEMP_ACCOUNT_NAME_ATTACHMENT_TAG);

        // ACC-5820 #8 spouse sign from web page after upload bank info from cc app
        if (!pendingOcr) pendingOcr = !directDebitService.hasRejectionForAccountName(contract.getClient());

        if (!pendingOcr &&
                ((eidPhoto == null && eidPhotoAttachment == null) ||
                (ibanPhoto == null && ibanPhotoAttachment == null) ||
                (accountPhoto == null && accountPhotoAttachment == null))) {

            throw new RuntimeException("Please upload all required bank info (EID, IBAN, Account Name)");
        }

        if (bouncedPayment == null) {
            AccountingEntityProperty switchingBankAccountBouncedPaymentId =
                    accountingEntityPropertyRepository.findByKeyAndOriginAndDeletedFalse(
                            Contract.SWITCHING_BANK_ACCOUNT_BOUNCED_PAYMENT_ID, contract);

            if (switchingBankAccountBouncedPaymentId != null) {
                bouncedPayment = paymentRepository.findOne(Long.parseLong(
                        switchingBankAccountBouncedPaymentId.getValue()));
            }
        }

        if (bouncedPayment != null) {
            checkIfBouncedPaymentActionAvailable(bouncedPayment);

            //CMA-2112 mark the push notification as disabled
            if (notificationId != null) {
                PushNotificationRepository pushNotificationRepository = Setup.getRepository(PushNotificationRepository.class);
                PushNotification notification = pushNotificationRepository.findOne(notificationId);
                if (notification != null){
                    notification.setReceived(true);
                    notification.setDisabled(true);
                    notification.setLocation(NotificationLocation.INBOX);
                    pushNotificationRepository.save(notification);
                }
            }
        }

        List<String> signaturesIDs = new ArrayList();
        if (signatures != null) {
            for (MultipartFile signature : signatures) {
                signaturesIDs.add(Storage.storeTemporary(
                        signature, "temp_signature", true, true).getId().toString());
            }
        }

        eidPhotoAttachment = eidPhoto != null ?
                Storage.storeTemporary(eidPhoto, Contract.TEMP_EID_ATTACHMENT_TAG, false) :
                eidPhotoAttachment;
        ibanPhotoAttachment = ibanPhoto != null ?
                Storage.storeTemporary(ibanPhoto, Contract.TEMP_IBAN_ATTACHMENT_TAG, false) :
                ibanPhotoAttachment;
        accountPhotoAttachment = accountPhoto != null ?
                Storage.storeTemporary(accountPhoto, Contract.TEMP_ACCOUNT_NAME_ATTACHMENT_TAG, false) :
                accountPhotoAttachment;

        if (signatures == null || signatures.isEmpty()) {
            if (eidPhoto != null) {
                contract.deleteAttachmentByTag(eidPhotoAttachment.getTag());
                contract.addAttachment(eidPhotoAttachment);
            }
            if (ibanPhoto != null) {
                contract.deleteAttachmentByTag(ibanPhotoAttachment.getTag());
                contract.addAttachment(ibanPhotoAttachment);
            }
            if (accountPhoto != null) {
                contract.deleteAttachmentByTag(accountPhotoAttachment.getTag());
                contract.addAttachment(accountPhotoAttachment);
            }
            contract = contractRep.save(contract);

            AccountingEntityProperty switchingBankAccountDate = accountingEntityPropertyRepository
                    .findByKeyAndOriginAndDeletedFalse(Contract.SWITCHING_BANK_ACCOUNT_DATE, contract);

            if (switchingBankAccountDate == null) {
                switchingBankAccountDate = new AccountingEntityProperty();
                switchingBankAccountDate.setOrigin(contract);
                switchingBankAccountDate.setKey(Contract.SWITCHING_BANK_ACCOUNT_DATE);
                switchingBankAccountDate.setValue(DateUtil.formatDateDashed(new Date()));
                accountingEntityPropertyRepository.save(switchingBankAccountDate);

                if (bouncedPayment != null) {
                    AccountingEntityProperty switchingBankAccountBouncedPaymentId = new AccountingEntityProperty();
                    switchingBankAccountDate.setOrigin(bouncedPayment);
                    switchingBankAccountDate.setKey(Contract.SWITCHING_BANK_ACCOUNT_BOUNCED_PAYMENT_ID);
                    switchingBankAccountDate.setValue(bouncedPayment.getId().toString());
                    accountingEntityPropertyRepository.save(switchingBankAccountBouncedPaymentId);
                }
            } else {
                switchingBankAccountDate.setValue(DateUtil.formatDateDashed(new Date()));
                accountingEntityPropertyRepository.save(switchingBankAccountDate);
            }

            return "Done";
        } else {
            createBGTForSwitchingBankInfo(
                contract, bouncedPayment, eidPhotoAttachment,
                ibanPhotoAttachment, accountPhotoAttachment, signaturesIDs, pendingOcr);

            return "signing DD is being processed";
        }
    }

    @Transactional
    public void createBGTForSwitchingBankInfo(
        Contract contract, Payment bouncedPayment,
        Attachment eidPhotoAttachment, Attachment ibanPhotoAttachment, Attachment accountPhotoAttachment,
        List<String> signaturesIDs, Boolean pendingOcr) {

        backgroundTaskService.create(new BackgroundTask.builder(
                "switchingBankAccount_doSwitch_" + contract.getId() + "_" + new Date().getTime(),
                        "accounting",
                        "switchingBankAccountService",
                        "doSwitchBankInfo")
                        .withRelatedEntity("Contract", contract.getId())
                        .withParameters(
                                new Class<?>[]{String.class, Long.class,
                                        Long.class, Long.class, Long.class,
                                        List.class, Boolean.class},
                                new Object[]{contract.getUuid(), bouncedPayment != null ? bouncedPayment.getId() : null,
                                        eidPhotoAttachment.getId(), ibanPhotoAttachment.getId(),
                                        accountPhotoAttachment != null ? accountPhotoAttachment.getId() : null,
                                        signaturesIDs, pendingOcr})
                        .withQueue(BackgroundTaskQueues.SequentialQueue)
                        .build());
    }

    @JwtSecured
    @PreAuthorize("hasPermission('ContractPaymentTerm','swapBouncedPayment')")
    @PostMapping(value = "/switchingBankAccount/swapBouncedPayment")
    @Transactional
    public ResponseEntity<?> swapBouncedPayment(@RequestBody ObjectNode node) {
        if (!node.has("bouncedPaymentId")) throw new RuntimeException("invalid request");

        Payment bouncedPayment = paymentRepository.findOne(node.get("bouncedPaymentId").asLong());
        DirectDebit directDebit = bouncedPayment.getDirectDebit();

        DirectDebit ddImage = switchingBankAccountService.getPaymentDDImage(bouncedPayment);

        if (ddImage == null)
            throw new RuntimeException("Payment DD doesn't have an image");

        Payment paymentImage = switchingBankAccountService.getPaymentImage(bouncedPayment);

        if (paymentImage == null)
            throw new RuntimeException("Bounced Payment doesn't have an image");

        bouncedPayment.setDirectDebitId(ddImage.getId());
        paymentService.updatePayment(bouncedPayment);
        logger.info("update Bounced Payment, move it to the new DD, Payment#" + bouncedPayment.getId());

        paymentImage.setDirectDebitId(directDebit.getId());
        paymentImage.setStatus(PaymentStatus.DELETED);
        paymentService.updatePayment(paymentImage);
        logger.info("update new Payment Async, move it to the old DD, Payment#" + paymentImage.getId());

        return new ResponseEntity<>("Done", HttpStatus.OK);
    }

    public DirectDebit createDirectDebitForSwitchingNationalityRejectionFlow(DirectDebit directDebit, boolean useSignatures) throws Exception {

        DateTime ddAStartDate = new DateTime();
        int ontTimeDDMonthDuration = Integer.parseInt(getParameter(AccountingModule.PARAMETER_ONE_TIME_DD_MONTH_DURATION));

        Double ddAmount = switchingNationalityService.doesDDCoverSwitchingBothMonths(directDebit.getId()) && directDebit.getCategory().equals(DirectDebitCategory.B) ?
                (directDebit.getAmount() * 2) :
                directDebit.getAmount();

        DirectDebit newDD = contractPaymentTermServiceNew.addNewDD(directDebit.getContractPaymentTerm().getContract(), ddAStartDate.toDate(), ddAStartDate.plusMonths(ontTimeDDMonthDuration).toDate(),
                null, null, null,
                Math.floor(ddAmount), null, DirectDebitType.ONE_TIME,
                Setup.getItem("TypeOfPayment", "monthly_payment"), useSignatures, null, false, false, true, null, true);

        newDD = directDebitRepository.findOne(newDD.getId());

        return newDD;
    }

    // ACC-2917
    @PreAuthorize("hasPermission('ContractPaymentTerm','downloadSign')")
    @GetMapping(path = "/downloadSign/{uuid}")
    public void downloadAttachment(
            HttpServletResponse response,
            @PathVariable("uuid") String uuid) {

        final Attachment attachment = attachementRepository.findByUuid(uuid);
        if (attachment == null) return;
        createDownloadResponse(response,
                attachment.getName(),
                attachment.getExtension(),
                Storage.getStream(attachment));
    }

    public DirectDebit createDirectDebitForSwitchingBankAccountRejectionFlow(DirectDebit directDebit) throws Exception {
        DirectDebit newDD = contractPaymentTermServiceNew.addNewDD(directDebit.getContractPaymentTerm().getContract(), directDebit.getStartDate(), directDebit.getExpiryDate(),
                directDebit.getAdditionalDiscount(), directDebit.getNotes(), null,
                Math.floor(directDebit.getAmount()), null, directDebit.getType(),
                directDebit.getPaymentType(), true, null, false, false, true, null, true);

        ContractPaymentTerm oldCPT = directDebit.getContractPaymentTerm();

        if (newDD.getStatus().equals(DirectDebitStatus.IN_COMPLETE) || newDD.getMStatus().equals(DirectDebitStatus.IN_COMPLETE)) {

            Map<String, Object> signatureType = directDebitSignatureService.getLastSignatureType(oldCPT, true, false);

            List<Attachment> signatures = directDebitSignatureService
                    .getSignatureAttachmentsOnly((List<DirectDebitSignature>) signatureType.get("currentSignatures"));
            if(signatures != null) {
                Contract contract = directDebit.getContractPaymentTerm().getContract();

                ContractPaymentTerm newCPT = contract.getActiveContractPaymentTerm();

                Attachment eidPhoto = newCPT.getAttachment(ContractPaymentTermController.FILE_TAG_BANK_INFO_EID);
                Attachment ibanPhoto = newCPT.getAttachment(ContractPaymentTermController.FILE_TAG_BANK_INFO_IBAN);
                Attachment accountPhoto = newCPT.getAttachment(ContractPaymentTermController.FILE_TAG_BANK_INFO_ACCOUNT_NAME);

                contractPaymentTermServiceNew.signDDByClient(contract.getUuid(), contract,
                        eidPhoto, ibanPhoto, accountPhoto,
                        true, true, true,
                        null, null, null, signatures, false,
                        true,  null,
                        null, null, null);
            }
        }

        newDD = directDebitRepository.findOne(newDD.getId());

        return newDD;
    }

    @JwtSecured
    @UsedBy(others = UsedBy.Others.CC_App)
    @PreAuthorize("hasPermission('ContractPaymentTerm','switchingBankAccountGetPaymentTerm')")
    @GetMapping(value = "/switchingBankAccount/getPaymentTerm/{id}")
    public ResponseEntity<?> switchingBankAccountGetPaymentTerm(
            @PathVariable("id") Contract contract) {

        ContractPaymentTerm contractPaymentTerm = contract.getActiveContractPaymentTerm();

        List<DirectDebitStatus> validStatuses = Arrays.asList(
                DirectDebitStatus.PENDING, DirectDebitStatus.PENDING_DATA_ENTRY,
                DirectDebitStatus.CONFIRMED, DirectDebitStatus.IN_COMPLETE);

        List<DirectDebit> directDebits = directDebitRepository.findByContractPaymentTermAndStatusIn(
                contractPaymentTerm, validStatuses);

        if (directDebits.isEmpty() && contract.isPayingViaCreditCard()) {
            directDebits.addAll((List<DirectDebit>) Setup.getApplicationContext()
                    .getBean(ClientPayingViaCreditCardService.class)
                    .getFutureDirectDebitAndContractPayment(contractPaymentTerm).get("directDebits"));
        }

        if (directDebits.isEmpty()) {
            List<ContractPayment> payments = (List<ContractPayment>) contractPaymentTermServiceNew.
                    getDefaultDirectDebitPayments(contractPaymentTerm, new HashMap<>()).get("payments");

            directDebits = directDebitService
                    .getDirectDebitsOfPayments(payments, contractPaymentTerm, false);
        }

        HashMap<String, Object> response = new HashMap<>();
        response.put("desc", DDUtils.getSignPaymentInfoForCcApp(directDebits, contractPaymentTerm));

        // ACC-5163
        try {
            response.put("hasRejectionForAccountName", directDebitService
                    .hasRejectionForAccountName(contract.getClient()));
        } catch(Exception ex) {
            response.put("hasRejectionForAccountName", false);
        }


        return new ResponseEntity(response, HttpStatus.OK);
    }

    @JwtSecured
    @UsedBy(others = UsedBy.Others.CC_App)
    @PreAuthorize("hasPermission('ContractPaymentTerm','switchingBankAccount')")
    @RequestMapping(value = "/switchingBankAccount/sendSignSMS/{id}", method = RequestMethod.POST)
    public ResponseEntity<?> switchingBankAccountSendSignSMS(
            @PathVariable("id") Contract contract,
            @RequestParam("mobileNumber") String mobileNumber) {

        contract = contractRep.findOne(contract.getId());
        if (contract == null) throw new RuntimeException("contract not found");

        Map<String, String> parameters = new HashMap<>();
        parameters.put("sign_link", Setup.getApplicationContext()
                .getBean(Utils.class)
            .getSpouseSingDDLink(contract.getUuid()));
        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendClientSms(contract,
                        TemplateUtil.getTemplate("SIGN_DD_MESSAGE"),
                        parameters,
                        new HashMap<>(),
                        UaePhoneNormlizer.NormalizePhoneNumber(mobileNumber),
                        contract.getClient().getNormalizedSpouseWhatsappNumber(),
                        contract.getClient().getId(),
                        contract.getClient().getEntityType());


        return new ResponseEntity("message sent", HttpStatus.OK);
    }

    @JwtSecured
    @UsedBy(others = UsedBy.Others.CC_App)
    @PreAuthorize("hasPermission('ContractPaymentTerm','switchingBankAccount')")
    @RequestMapping(value = "/switchingBankAccount/getPastAndFeaturePayments/{id}", method = RequestMethod.GET)
    public ResponseEntity<?> switchingBankAccountGetPastAndFeaturePayments(@PathVariable("id") Client client) {
        client = clientRepository.findOne(client.getId());
        if (client == null)
            return new ResponseEntity("Client not found", HttpStatus.BAD_REQUEST);

        List<Contract> contracts = contractRep.findByClientOrderByCreationDateDesc(client);

        List<HashMap<String, String>> contractsResponseList = new ArrayList<>();

        for (Contract contract : contracts) {

            if (contract.getContractProspectType().getCode()
                    .equals(PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE))) {
                continue;
            }

            StringBuilder oneTimeDesc = new StringBuilder();
            StringBuilder directDebitDesc = new StringBuilder();

            if (contract.getStatus() != ContractStatus.ACTIVE)
                continue;

            ContractPaymentTerm contractPaymentTerm = contract.getActiveContractPaymentTerm();

            // CMA-576
            DirectDebit oldestDD = directDebitRepository.findTopByContractPaymentTermOrderById(contractPaymentTerm);
            List<DirectDebit> directDebits = new ArrayList();

            if (oldestDD != null && oldestDD.getDdBankInfoGroup() != null) {
                directDebits = directDebitRepository.getByContractPaymentTermAndDdBankInfoGroup(contractPaymentTerm, oldestDD.getDdBankInfoGroup());
            }

            DDUtils.setInitialDirectDebitsDescriptionForCCAPP(contractPaymentTerm, directDebits);

            boolean clientPayingVat = contract.getClientPaidVat() != null && contract.getClientPaidVat();
            for (DirectDebit directDebit : directDebits) {
                if (directDebit.getType() == DirectDebitType.ONE_TIME) {
                    List<ContractPayment> payments = directDebit.getPayments();
                    if (payments != null && payments.size() > 0) {
                        for (ContractPayment payment : payments) {
                            oneTimeDesc.append(PaymentHelper.getPaymentDescriptionForCCAPP(payment)).append(":")
                                    .append(PaymentHelper.getCC_APP_PaymentVatDescription(payment, clientPayingVat))
                                    .append("</br>");
                        }
                    }
                } else if (directDebit.getType() == DirectDebitType.MONTHLY) {
                    List<ContractPayment> payments = directDebit.getPayments();
                    if (payments != null && payments.size() > 0) {
                        ContractPayment payment = payments.get(0);

                        directDebitDesc.append(directDebit.getDescription()).append(":")
                                .append(PaymentHelper.getCC_APP_PaymentVatDescription(payment, clientPayingVat))
                                .append("</br>");
                    }
                }
            }

            oneTimeDesc.append(directDebitDesc);

            HashMap<String, String> contractTerms = new HashMap<>();
            contractTerms.put("maid_name", contract.getHousemaid() != null ? contract.getHousemaid().getName() : "");
            contractTerms.put("desc", oneTimeDesc.toString());
            contractsResponseList.add(contractTerms);

        }
        HashMap<String, Object> response = new HashMap<>();
        response.put("contracts", contractsResponseList);

        return new ResponseEntity(response, HttpStatus.OK);
    }

    @JwtSecured
    @UsedBy(others = UsedBy.Others.CC_App)
    @PreAuthorize("hasPermission('ContractPaymentTerm','switchingBankAccount')")
    @RequestMapping(value = "/switchingBankAccount/getOverChargedDetails/{id}", method = RequestMethod.GET)
    public ResponseEntity<?> switchingBankAccountGetOverChargedDetails(@PathVariable("id") Client client) {
        client = clientRepository.findOne(client.getId());
        if (client == null)
            return new ResponseEntity("Client not found", HttpStatus.BAD_REQUEST);

        List<Contract> contracts = contractRep.findByClientOrderByCreationDateDesc(client);


        List<HashMap<String, String>> contractsResponseList = new ArrayList<>();
        DecimalFormat df1 = new DecimalFormat("###,###,###");
        LocalDate now = LocalDate.now();

        PicklistItem monthlyPaymentType = getItem("TypeOfPayment", "monthly_payment");

        for (Contract contract : contracts) {

            if (contract.getContractProspectType().getCode()
                    .equals(PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE))) {
                continue;
            }

            boolean clientPayingVat = contract.getClientPaidVat() != null && contract.getClientPaidVat();

            SelectQuery<ClientRefundToDo> query = new SelectQuery(ClientRefundToDo.class);
            query.filterBy("contract", "=", contract);
            query.filterBy("client", "=", client);
            query.filterBy("requestType", "=", ClientRefundRequestType.DUPLICATED_PAYMENT);
            query.filterBy("creationDate", ">=", new DateTime().dayOfMonth().withMinimumValue().withTimeAtStartOfDay().toDate());
            List<ClientRefundToDo> clientRefundToDos = query.execute();

            if (clientRefundToDos != null && !clientRefundToDos.isEmpty()) {

                ClientRefundToDo clientRefundToDo = clientRefundToDos.get(0);

                StringBuilder desc = new StringBuilder();

                if (contract.getStatus() != ContractStatus.ACTIVE)
                    continue;

                HashMap<String, Object> contractWithPaymentWithClientRefund = new HashMap<>();
                contractWithPaymentWithClientRefund.put("contract", contract);

                List<Payment> payments = paymentRepository.findByContractAndTypeOfPayment(contract, monthlyPaymentType);

                List<Payment> paymentsOfCurrentMonth = payments.stream().filter(
                        p ->
                                Arrays.asList(PaymentStatus.RECEIVED, PaymentStatus.PDC).contains(p.getStatus())
                                        && (new LocalDate(p.getDateOfPayment()).getYear() == now.getYear())
                                        && (new LocalDate(p.getDateOfPayment()).getMonthOfYear() == now.getMonthOfYear())
                ).collect(Collectors.toList());

                List<Payment> PDCpaymentsOfCurrentMonth = paymentsOfCurrentMonth.stream().filter(
                        p ->
                                Arrays.asList(PaymentStatus.PDC).contains(p.getStatus())
                ).collect(Collectors.toList());

                List<Payment> receivedPaymentsOfCurrentMonth = paymentsOfCurrentMonth.stream().filter(
                        p ->
                                Arrays.asList(PaymentStatus.RECEIVED).contains(p.getStatus())
                ).collect(Collectors.toList());

                if (!receivedPaymentsOfCurrentMonth.isEmpty()) {
                    contractWithPaymentWithClientRefund.put("payment", receivedPaymentsOfCurrentMonth.get(0));
                } else if (!PDCpaymentsOfCurrentMonth.isEmpty()) {
                    contractWithPaymentWithClientRefund.put("payment", PDCpaymentsOfCurrentMonth.get(0));
                }

                if (contractWithPaymentWithClientRefund.containsKey("payment")) {
                    Payment payment = (Payment) contractWithPaymentWithClientRefund.get("payment");

                    desc.append("Your previous payment was supposed to be")
                            .append(PaymentHelper.getCC_APP_PaymentVatDescription(payment, clientPayingVat));
                }

                int days = Integer.parseInt(
                        Setup.getParameter(
                                Setup.getCurrentModule(),
                                AccountingModule.PARAMETER_REFUND_DAYS));

                double floor = Math.floor(clientRefundToDo.getAmount());

                desc.append(!desc.toString().isEmpty() ? " " : "")
                        .append("As we mentioned earlier, we've charged you an extra AED ")
                        .append(df1.format(floor))
                        .append(". ");

                if (!clientRefundToDo.getStatus().equals(ClientRefundStatus.PAID)) {
                    LocalDate localDate = new LocalDate(clientRefundToDo.getCreationDate()).plusDays(days);
                    desc.append("We'll refund that amount on ").append(DateUtil.formatClientFullDate(localDate.toDate()));
                } else {
                    LocalDate localDate = new LocalDate(clientRefundToDo.getStatusChangeDate());
                    desc.append("We've already transferred this amount to your bank account on ").append(DateUtil.formatClientFullDate(localDate.toDate()))
                            .append(". it may take up to 7 working days for the amount to reflect in your account.");
                }

                HashMap<String, String> contractTerms = new HashMap();
                contractTerms.put("maid_name", contract.getHousemaid() != null ? contract.getHousemaid().getName() : "");
                contractTerms.put("desc", desc.toString());
                contractsResponseList.add(contractTerms);

            } else {

                HashMap<String, String> contractTerms = new HashMap();

                StringBuilder desc = new StringBuilder();

                if (contract.getStatus() != ContractStatus.ACTIVE)
                    continue;

                HashMap<String, Object> contractWithPaymentWithoutClientRefund = new HashMap();
                contractWithPaymentWithoutClientRefund.put("contract", contract);

                logger.log(Level.SEVERE, "paymentsOfCurrentMonth contract id: " + contract.getId());


                List<Payment> payments = paymentRepository.findByContractAndTypeOfPayment(contract, monthlyPaymentType);

                logger.log(Level.SEVERE, "paymentsOfCurrentMonth payments size: " + payments.size());


                List<Payment> paymentsOfCurrentMonth = payments.stream().filter(
                        p ->
                                Arrays.asList(PaymentStatus.BOUNCED, PaymentStatus.RECEIVED, PaymentStatus.PDC, PaymentStatus.PRE_PDP).contains(p.getStatus())
                                        && (new LocalDate(p.getDateOfPayment()).getYear() == now.getYear())
                                        && (new LocalDate(p.getDateOfPayment()).getMonthOfYear() == now.getMonthOfYear())
                ).collect(Collectors.toList());

                logger.log(Level.SEVERE, "paymentsOfCurrentMonth size: " + paymentsOfCurrentMonth.size());

                for (Payment payment : paymentsOfCurrentMonth) {
                    logger.log(Level.SEVERE, "paymentsOfCurrentMonth id: " + payment.getId());
                    logger.log(Level.SEVERE, "paymentsOfCurrentMonth status: " + payment.getStatus());
                }


                List<Payment> bouncedPaymentsOfCurrentMonth = paymentsOfCurrentMonth.stream().filter(
                        p ->
                                Arrays.asList(PaymentStatus.BOUNCED).contains(p.getStatus())
                ).collect(Collectors.toList());

                logger.log(Level.SEVERE, "bouncedPaymentsOfCurrentMonth size: " + bouncedPaymentsOfCurrentMonth.size());

                List<Payment> PrePDPpaymentsOfCurrentMonth = paymentsOfCurrentMonth.stream().filter(
                        p ->
                                Arrays.asList(PaymentStatus.PRE_PDP).contains(p.getStatus())
                ).collect(Collectors.toList());

                logger.log(Level.SEVERE, "PrePDPpaymentsOfCurrentMonth size: " + PrePDPpaymentsOfCurrentMonth.size());

                List<Payment> PDCpaymentsOfCurrentMonth = paymentsOfCurrentMonth.stream().filter(
                        p ->
                                Arrays.asList(PaymentStatus.PDC).contains(p.getStatus())
                ).collect(Collectors.toList());

                logger.log(Level.SEVERE, "PDCpaymentsOfCurrentMonth size: " + PDCpaymentsOfCurrentMonth.size());


                List<Payment> receivedPaymentsOfCurrentMonth = paymentsOfCurrentMonth.stream().filter(
                        p ->
                                Arrays.asList(PaymentStatus.RECEIVED).contains(p.getStatus())
                ).collect(Collectors.toList());

                logger.log(Level.SEVERE, "receivedPaymentsOfCurrentMonth size: " + receivedPaymentsOfCurrentMonth.size());


                if (!bouncedPaymentsOfCurrentMonth.isEmpty()) {
                    contractWithPaymentWithoutClientRefund.put("payment", bouncedPaymentsOfCurrentMonth.get(0));
                } else if (!receivedPaymentsOfCurrentMonth.isEmpty()) {
                    contractWithPaymentWithoutClientRefund.put("payment", receivedPaymentsOfCurrentMonth.get(0));
                } else if (!PDCpaymentsOfCurrentMonth.isEmpty()) {
                    contractWithPaymentWithoutClientRefund.put("payment", PDCpaymentsOfCurrentMonth.get(0));
                } else if (!PrePDPpaymentsOfCurrentMonth.isEmpty()) {
                    contractWithPaymentWithoutClientRefund.put("payment", PrePDPpaymentsOfCurrentMonth.get(0));
                }

                if (contractWithPaymentWithoutClientRefund.containsKey("payment")) {
                    Payment payment = (Payment) contractWithPaymentWithoutClientRefund.get("payment");

                    desc.append("Your previous payment was supposed to be")
                            .append(PaymentHelper.getCC_APP_PaymentVatDescription(payment, clientPayingVat));
                }

                contractTerms.put("maid_name", contract.getHousemaid() != null ? contract.getHousemaid().getName() : "");
                contractTerms.put("desc", desc.toString());

                contractsResponseList.add(contractTerms);

            }
        }
        HashMap<String, Object> response = new HashMap<>();
        response.put("contracts", contractsResponseList);

        return new ResponseEntity(response, HttpStatus.OK);
    }

    @JwtSecured
    @UsedBy(others = UsedBy.Others.CC_App)
    @PreAuthorize("hasPermission('ContractPaymentTerm','switchingBankAccount/bounced-payment-action/is-available')")
    @RequestMapping(value = "/switchingBankAccount/bounced-payment-action/is-available", method = RequestMethod.GET)
    public ResponseEntity<?> isBouncedPaymentActionAvailable(@RequestParam("paymentId") Payment payment) {
        checkIfBouncedPaymentActionAvailable(payment);

        return okResponse();
    }

    public boolean checkIfBouncedPaymentActionAvailable(Payment payment) {
        if (payment.isReplaced()) {
            throw new RuntimeException("You are seeing this message because you have clicked on a button that no longer works. <REFRESH>");
        }

        return true;
    }

    @JwtSecured
    @UsedBy(others = UsedBy.Others.CC_App)
    @PreAuthorize("hasPermission('ContractPaymentTerm','getDDBankDetails')")
    @RequestMapping(value = "/switchingBankAccount/getDDBankDetails/{contractId}", method = RequestMethod.GET)
    public ResponseEntity getDDBankDetails(@PathVariable("contractId") Contract contract) {
        if (contract == null)
            throw new RuntimeException("Contract not Found");

        return ResponseEntity.ok(directDebitService.getDDBankDetails(contract));
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','cancel-oec-dds')")
    @RequestMapping(value = "/cancel-oec-dds/{contractId}", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> cancel_OEC_DDS(
            @PathVariable("contractId") Contract contract) throws Exception {

        logger.info("begining of cancel_OEC_DDS API contract ID is: "+contract);
        boolean canceledSuccessfully = false;
        boolean getIntoFlow = false;

        AccountingEntityProperty accountingEntityProperty = accountingEntityPropertyRepository.findByKeyAndOriginAndDeletedFalse(Contract.OEC_AMEND_DDS, contract);
        if (accountingEntityProperty == null) {
            throw new RuntimeException("There is no property for this contract "+contract.getId());
        }

        contract = contractRep.findOne(contract.getId());
        if(contract == null){
            throw new RuntimeException("Invalid contract ID");
        }

        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
        if (cpt == null || cpt.getAmendedDate() == null)
            throw new RuntimeException("Cpt is changed!");

        List<DirectDebit> dds = directDebitRepository.findByContractPaymentTermAndAddedByOecFlowOrderByCreationDate(cpt, true);
        logger.info("start cancel-oec-dds function dds count is "+dds.size());

        if (dds.isEmpty())
            throw new RuntimeException("DDs is changed!");

        DirectDebit firstDirectDebit = dds.get(0);
        logger.info("start OEC amend dd flow function firstDirectDebit "+firstDirectDebit.getId());

        if(firstDirectDebit.getDirectDebitRejectionToDo() == null){
            getIntoFlow = (firstDirectDebit.getStatus().equals(DirectDebitStatus.CONFIRMED)||firstDirectDebit.getMStatus().equals(DirectDebitStatus.CONFIRMED)) ||
                    (firstDirectDebit.getStatus().equals(DirectDebitStatus.REJECTED)&& firstDirectDebit.getMStatus().equals(DirectDebitStatus.REJECTED));

            if(getIntoFlow){
                logger.info("start OEC amend dd flow function firstDirectDebit is confirmed  "+firstDirectDebit.getId());
                oecAmendDDsService.closeOecAmendDDsFlow(contract.getId());
                canceledSuccessfully =  directDebitController.oecAmendCancelOldDDs(contract);
            } else if(firstDirectDebit.getStatus().equals(DirectDebitStatus.PENDING)&& firstDirectDebit.getMStatus().equals(DirectDebitStatus.PENDING)){
                logger.info("start OEC amend dd flow function firstDirectDebit is pending  "+firstDirectDebit.getId());
                if(firstDirectDebit.getDirectDebitFiles() != null && firstDirectDebit.getDirectDebitFiles().size() > 0 ){
                    boolean proceedSent = firstDirectDebit.getDirectDebitFiles() != null &&
                            firstDirectDebit.getDirectDebitFiles().stream().anyMatch(f -> f.getStatus().equals(DirectDebitFileStatus.SENT));
                    boolean pendingNotSent = firstDirectDebit.getDirectDebitFiles() != null &&
                            !firstDirectDebit.getDirectDebitFiles().isEmpty() &&
                            firstDirectDebit.getDirectDebitFiles().stream().allMatch(f -> f.getStatus().equals(DirectDebitFileStatus.NOT_SENT));

                    logger.info("start OEC amend dd flow function firstDirectDebit is proceedSent value is "+proceedSent);
                    logger.info("start OEC amend dd flow function firstDirectDebit is pendingNotSent value is "+pendingNotSent);
                    if(proceedSent){
                        getIntoFlow = true;
                        if(accountingEntityProperty != null){
                            accountingEntityProperty.setJobRunAndDDPending(true);
                            logger.info("start OEC amend dd flow function firstDirectDebit is pending after set value to 1 ");
                        }else {
                            throw new RuntimeException("start OEC amend dd flow function No property created for this contract "+firstDirectDebit.getContractPaymentTerm().getContract().getId());
                        }
                    } else if(pendingNotSent){
                        logger.info("in if pendingNotSent in firstDirectDebit section "+pendingNotSent);
                        getIntoFlow = true;
                        oecAmendDDsService.closeOecAmendDDsFlow(contract.getId());
                        canceledSuccessfully = directDebitController.oecAmendCancelOldDDs(contract);
                    }
                }
            }
         } else {
            DirectDebitRejectionToDo todo = firstDirectDebit.getDirectDebitRejectionToDo();
            DirectDebit lastDirectDebit = todo.getLastDirectDebit();
            logger.info("OEC amend dd flow function lastDirectDebit "+lastDirectDebit.getId());
            getIntoFlow = (lastDirectDebit.getStatus().equals(DirectDebitStatus.CONFIRMED)||lastDirectDebit.getMStatus().equals(DirectDebitStatus.CONFIRMED)) ||
                    (lastDirectDebit.getStatus().equals(DirectDebitStatus.REJECTED)&& lastDirectDebit.getMStatus().equals(DirectDebitStatus.REJECTED))
                    ||(lastDirectDebit.getStatus().equals(DirectDebitStatus.IN_COMPLETE)&& lastDirectDebit.getMStatus().equals(DirectDebitStatus.IN_COMPLETE));

            if(getIntoFlow){
                logger.info("start OEC amend dd flow function lastDirectDebit is confirmed  "+lastDirectDebit.getId());
                oecAmendDDsService.closeOecAmendDDsFlow(contract.getId());
               canceledSuccessfully = directDebitController.oecAmendCancelOldDDs(contract);
            } else if(lastDirectDebit.getStatus().equals(DirectDebitStatus.PENDING)&& lastDirectDebit.getMStatus().equals(DirectDebitStatus.PENDING)){
                logger.info("start OEC amend dd flow function rejection flow lastDirectDebit is pending  "+lastDirectDebit.getId());
                if(lastDirectDebit.getDirectDebitFiles() != null && lastDirectDebit.getDirectDebitFiles().size() > 0 ){
                    boolean proceedSent = lastDirectDebit.getDirectDebitFiles() != null &&
                            lastDirectDebit.getDirectDebitFiles().stream().anyMatch(f -> f.getStatus().equals(DirectDebitFileStatus.SENT));
                    boolean pendingNotSent = lastDirectDebit.getDirectDebitFiles() != null &&
                            !lastDirectDebit.getDirectDebitFiles().isEmpty() &&
                            lastDirectDebit.getDirectDebitFiles().stream().allMatch(f -> f.getStatus().equals(DirectDebitFileStatus.NOT_SENT));

                    getIntoFlow = true;

                    if(proceedSent){
                        if(accountingEntityProperty != null){
                            accountingEntityProperty.setJobRunAndDDPending(true);
                        } else {
                           throw new RuntimeException("start OEC amend dd flow function No property created for this contract " +
                                   firstDirectDebit.getContractPaymentTerm().getContract().getId());
                        }
                    } else if(pendingNotSent){
                            oecAmendDDsService.closeOecAmendDDsFlow(contract.getId());
                            canceledSuccessfully = directDebitController.oecAmendCancelOldDDs(contract);
                    }
                }
            }
        }
        if(canceledSuccessfully &&  getIntoFlow){
            accountingEntityPropertyRepository.deleteByKeyAndOrigin(Contract.OEC_AMEND_DDS, contract);
        }

        return okResponse();
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm','postponedExistingClientMigrationApiAcc4635')")
    @PostMapping("/postponedExistingClientMigrationApiAcc4635")
    @Transactional
    public ResponseEntity<?> postponedExistingClientMigrationApiAcc4635(
            @RequestBody List<Long> contractIds) {

        if(contractIds.isEmpty()) contractIds = clientRepository.findPostponedExistingMvClients();

        contractIds.forEach(c -> {
            try {
                logger.log(Level.SEVERE, "Contract id : {0}", c);
                Contract contract = contractRep.findOne(c);
                ContractPaymentTerm contractPaymentTerm = contract.getActiveContractPaymentTerm();

                logger.log(Level.SEVERE, "cpt id : {0}", contractPaymentTerm.getId());
                Map<String, Object> lastSignatureType = directDebitSignatureService.getLastSignatureType(
                        contract.getClient(), contractPaymentTerm.getEid(), true, false);
                List<DirectDebitSignature> signatures = (List<DirectDebitSignature>) lastSignatureType.get("currentSignatures");

                contractPaymentTermServiceNew.signDdPostponedContract(contractPaymentTerm,
                        directDebitSignatureService.getSignatureAttachmentsOnly(signatures),
                        false);
            } catch (IOException e) {
                e.printStackTrace();
            }
        });

        return okResponse();
    }

    @RequestMapping(value = "/insertnewcpt", method = RequestMethod.POST)
    @PreAuthorize("hasPermission('ContractPaymentTerm','insertNewCPT')")
    public ResponseEntity<?> insertNewCPT(
            @RequestPart(required = false) MultipartFile file,
            @RequestParam String email,
            @RequestParam(name = "changeHousemaid", required = false, defaultValue = "false") boolean changeHousemaid,
            @RequestParam(name = "changeReplacement", required = false, defaultValue = "false") boolean changeReplacement,
            @RequestParam(name = "changeReason", required = false, defaultValue = "false") boolean changeReason,
            @RequestParam(name = "changeFirstPayment", required = false, defaultValue = "false") boolean changeFirstPayment,
            @RequestParam(name = "contractId", required = false) Long contractId,
            @RequestParam(name = "housemaidId", required = false) Long housemaidId,
            @RequestParam(name = "cptId", required = false) Long termId)
            throws IOException {

        DataMigrationService insertNewCptService = Setup.getApplicationContext().getBean(DataMigrationService.class);
        String result;

        if (contractId == null && housemaidId == null && termId == null) {
            result = insertNewCptService.handleSheetNewCPTs(file, changeHousemaid, changeReplacement, changeReason, changeFirstPayment);
        } else {
            // validate all Ids
            String error = "";
            if (contractId == null) {
                error += "contractId not found";
            }
            if (termId == null) {
                error += !error.isEmpty() ? ", and " : "";
                error +=  "cptId not found";
            }
            if (housemaidId == null && changeHousemaid) {
                error += !error.isEmpty() ? ", and " : "";
                error += "changeHousemaid is true and housemaidId not found";
            }

            if(!error.isEmpty()) {
                throw new BusinessException("Error: " + error);
            }

            // create createNewCptHandleRow
            error = insertNewCptService.createNewCptHandleRow(contractId, termId, housemaidId, null, null, null);
            result = error != null && !error.isEmpty() ?
                    "Error: " + error + "</br>" :
                    "migrated successfully";
        }

        Setup.getMailService().sendEmail(Collections.singletonList(new Recipient(email, "")),
                new TextEmail("Edit CPT data",  result), true,
                "",null);
        logger.log(Level.INFO, "result: {0}", result);
        return new ResponseEntity<>("Done", HttpStatus.OK);
    }

    //ACC-4657
    @PreAuthorize("hasPermission('contractpaymentterm','swapBankInfoAttachments')")
    @RequestMapping(value = "/swapBankInfoAttachments", method = RequestMethod.POST)
    public ResponseEntity<?> swapBankInfoAttachments(@RequestBody Map<String, String> body) {
        if (body ==null || body.isEmpty())
            return new ResponseEntity("there are not attachments", HttpStatus.BAD_REQUEST);

        body.forEach((uuid, tag) -> {
            Attachment attachment = attachementRepository.findByUuid(uuid);
            if (attachment == null)
                return;
            attachment.setTag(tag);
            switch (tag) {
                case ContractPaymentTermController.FILE_TAG_BANK_INFO_EID:
                    attachment.setName("eid." + attachment.getExtension());
                    break;
                case ContractPaymentTermController.FILE_TAG_BANK_INFO_IBAN:
                    attachment.setName("iban." + attachment.getExtension());
                    break;
                case ContractPaymentTermController.FILE_TAG_BANK_INFO_ACCOUNT_NAME:
                    attachment.setName("account_name." + attachment.getExtension());
                    break;
            }
            attachementRepository.save(attachment);
        });

        return new ResponseEntity("Done", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('contractpaymentterm','acc6124DataCorrection')")
    @GetMapping(value = "/acc6124DataCorrection")
    public ResponseEntity<?> acc6124DataCorrection(
            @RequestParam(name = "startId", required = false, defaultValue = "-1") Long startId,
            @RequestParam(name = "clientId", required = false) Long clientId,
            @RequestParam(name = "attachmentId", required = false) Long attachmentId) throws InterruptedException {

        String result = contractPaymentTermHelper.extractNationalityAndGenderFromEid(startId - 1, clientId, attachmentId);
        logger.info("email text: " + result);

        return new ResponseEntity("Done", HttpStatus.OK);
    }

    @JwtSecured
    @GetMapping(value = "/deleteCreditCardToken/{id}")
    public ResponseEntity<?> deleteCreditCardToken(@PathVariable("id") Contract contract) {

        contractPaymentTermServiceNew.deleteCreditCardToken(contract.getActiveContractPaymentTerm());

        return okResponse();
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm', 'waivedInsurancePayment')")
    @GetMapping(value = "/waivedInsurancePayment/{contractId}")
    public ResponseEntity<?> waivedInsurancePayment(@PathVariable("contractId") Contract contract) {
        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();

        List<DirectDebit> dds = directDebitRepository.findInsuranceDDGeneratedAndNotCollected(
                contract,
                Stream.of(Collections.singletonList(DirectDebitStatus.NOT_APPLICABLE), DirectDebitService.notAllowedStatuses)
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList()));
        dds.forEach(d -> directDebitCancellationService
                .cancelWholeDD(d, DirectDebitCancellationToDoReason.WAIVED_PAYMENT));

        //Direct Debit Generation Plan
        directDebitGenerationPlanRepository
                .getDirectDebitGenerationPlanWithTypeInsurance(contract)
                .forEach(d -> directDebitGenerationPlanRepository.delete(d));

        //ContractPaymentConfirmationTodo
        FlowProcessorEntityRepository flowProcessorEntityRepository = Setup.getRepository(FlowProcessorEntityRepository.class);
        List<ContractPaymentConfirmationToDo> toDos = contractPaymentConfirmationToDoRepository.getTodosWithInsuranceWrapper(contract);
        toDos.forEach(toDo -> {
            logger.info("todo id:" + toDo.getId());
            toDo.getContractPaymentList().stream()
                    .filter(w -> w.getPaymentType().getCode().equals(AbstractPaymentTypeConfig.INSURANCE_TYPE_CODE))
                    .forEach(w -> contractPaymentWrapperRepository.delete(w));

            messagingService.createDisableNotificationBGT(
                    disablePushNotificationRepository.findActiveNotificationsByOwner(toDo.getId(), "contractPaymentConfirmationToDo")
                            .stream()
                            .map(PushNotification::getId).collect(Collectors.toList()),
                    "disable notification due to waived payment");

            toDo = contractPaymentConfirmationToDoRepository.findOne(toDo.getId());

            if(toDo.getContractPaymentList().isEmpty()) {

                FlowProcessorEntity reminderFlow = flowProcessorEntityRepository
                        .findByContractPaymentConfirmationToDoAndStoppedFalseAndCompletedFalse(toDo)
                        .stream().findFirst().orElse(null);

                if (reminderFlow != null) {
                    logger.log(Level.INFO, "Stop reminder flow id: " + reminderFlow.getId());
                    reminderFlow.setStopped(true);
                    flowProcessorEntityRepository.save(reminderFlow);
                }

                toDo.setDisabled(true);
            }

            contractPaymentConfirmationToDoRepository.save(toDo);
        });

        //contractPaymentType
        cpt.getContractPaymentTypes().stream()
                .filter(t-> t.getType().getCode().equals(AbstractPaymentTypeConfig.INSURANCE_TYPE_CODE))
                .forEach(t -> contractPaymentTypeRepository.delete(t));

        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('ContractPaymentTerm', 'getRequiredBankAttachments')")
    @GetMapping(value = "/getRequiredBankAttachments/{contractId}")
    public ResponseEntity<?> getRequiredBankAttachments(@PathVariable("contractId") Contract contract) {

        contract = contractRep.findOne(contract.getId());
        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
        logger.info("cpt id: " + cpt.getId());


        List<Map<String, Object>> requiredAttachments = new ArrayList<>();
        directDebitService.getMissingBankInfo(cpt)
                .forEach(missingName -> {

            String tag = null;
            switch (missingName) {
               case "EID":
                   tag = "bank_info_eid";
                   break;
                case "IBAN":
                    tag = "bank_info_iban";
                    break;
               case "Account name":
                   tag = "bank_info_account_name";
                   break;
            }

            if (tag != null) {
                Map m = new HashMap();
                m.put("name", missingName);
                m.put("tag", tag);
                requiredAttachments.add(m);
            }
        });

        Map<String, Object> response = new HashMap<String, Object>() {{
            put("accounting_todo_extra_info", new HashMap<String, Object>() {{
                put("attachments", requiredAttachments);
            }});
        }};

        logger.info("response: " + response);
        return ResponseEntity.ok(response);
    }
}