package com.magnamedia.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.magnamedia.core.configuration.ObjectMapperConfiguration;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.DirectDebitConfiguration;
import com.magnamedia.extra.StringUtils;
import com.magnamedia.repository.DirectDebitConfigurationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON>.<PERSON> on 5/2/2021.
 */

@RestController
@RequestMapping("/direct-debit-config")
public class DirectDebitConfigurationController extends BaseRepositoryController<DirectDebitConfiguration> {

    @Autowired
    private DirectDebitConfigurationRepository ddConfigurationRepo;

    @Override
    public BaseRepository<DirectDebitConfiguration> getRepository() {
        return ddConfigurationRepo;
    }

    @RequestMapping(value = {"/search"}, method = {RequestMethod.GET})
    @PreAuthorize("hasPermission('direct-debit-config','search')")
    public ResponseEntity<?> searchByLabel(@RequestParam("bankName") String bankName,
                                           Pageable pageable) {
        SelectQuery query = new SelectQuery(DirectDebitConfiguration.class);
        if (!StringUtils.isEmpty(bankName)) {
            query.filterBy("bank.name", "like", "%" + bankName + "%");
        }

        return ResponseEntity.ok(query.execute(pageable));
    }

    @RequestMapping(value = {"/update-all"}, method = {RequestMethod.POST})
    @PreAuthorize("hasPermission('direct-debit-config','update-all')")
    public ResponseEntity<?> searchByLabel(@RequestBody List<Map> records) {

        getObjectMapper().configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);

        for (Map record : records) {
            DirectDebitConfiguration ddConfiguration = getObjectMapper().convertValue(record, DirectDebitConfiguration.class);
            getRepository().save(ddConfiguration);
        }

        return ResponseEntity.ok("Done");
    }
}
