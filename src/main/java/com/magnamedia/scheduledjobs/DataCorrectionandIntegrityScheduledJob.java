package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.mail.MailService;
import com.magnamedia.core.mail.TemplateEmail;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.extra.ReportMail;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.report.InconsistencyReport;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Mar 16, 2018
 */
public class DataCorrectionandIntegrityScheduledJob implements MagnamediaJob {

    private List<InconsistencyReport> reportsData;

    ReportMail reportMail;

    private TemplateEngine templateEngine;

    private MailService mailservice;

    public DataCorrectionandIntegrityScheduledJob(){
        reportsData = Setup.getApplicationContext().getBeansOfType(InconsistencyReport.class).
                values().stream().collect(Collectors.toList());
        reportMail = Setup.getApplicationContext().getBean(ReportMail.class);
        templateEngine = Setup.getApplicationContext().getBean(TemplateEngine.class);
        mailservice = Setup.getMailService();
    }

    @Override
    public void run(Map<String, ?> map) {
        this.InvalidDataEmail();
    }

    public String getDebugInfo() {
        StringBuilder res = new StringBuilder();
        for (InconsistencyReport inconsistencyReport : reportsData) {
            inconsistencyReport.refreshData();
            res.append("<p>").append(inconsistencyReport.getForQuery()).append("</p>");

        }
        return res.toString();
    }

    public String InvalidDataEmail() {
        return InvalidDataEmail(false);
    }

    public String InvalidDataEmail(boolean withFix) {
        String emails = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_DATA_CORRECTION_AND_INTEGRITY_EMAILS);
        List<EmailRecipient> recipients = EmailHelper.getMailRecipients(emails);
        return InvalidDataEmail(withFix, recipients);
    }

    public String InvalidDataEmail(boolean withFix, List<EmailRecipient> recipients) {
        String subject = "ERP System - Accounting Data Correction and Integrity";

        StringBuilder tableBuilder = new StringBuilder("");
        for (InconsistencyReport inconsistencyReport : reportsData) {
            try {
                inconsistencyReport.refreshData();
                tableBuilder.append(reportMail.toHtmlTable(inconsistencyReport.getTable(), inconsistencyReport.getHeaders(), inconsistencyReport.getTitle(), 400));

                if (withFix || inconsistencyReport.defaultFix()) {
                    try {
                        inconsistencyReport.fix();
                        tableBuilder.append("<p> Fixes have been applied to these... </p>");
                    } catch (Throwable e) {
                        if (e instanceof UnsupportedOperationException) {
                            tableBuilder.append(String.format("<p> %s </p>", e.getMessage()));
                        } else {
                            tableBuilder.append(String.format("<p> %s </p>", ExceptionUtils.getStackTrace(e)));
                        }
                    }
                }

                tableBuilder.append("<br/><hr/>");
            } catch (Throwable e) {
                tableBuilder.append("<p>").append(ExceptionUtils.getStackTrace(e)).append("</p>");
            }
        }

        Map<String, Object> params = new HashMap<>();
        params.put("title", "Invalid Data Report");
        params.put("tableData", tableBuilder.toString());

        if (!recipients.isEmpty()) {
            mailservice.sendEmail(recipients,
                    new TemplateEmail(subject, "DataCorrectionandIntegrity", params),
                    null);
        }
        return "";
    }
}
