package com.magnamedia.report;

import com.magnamedia.entity.Bucket;
import com.magnamedia.entity.Transaction;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Oct 14, 2020
 *         Jirra ACC-2522
 */

public class AccountBalanceReport extends BaseReport {
    public String[] h = new String[] {"Name", "Bucket From Name", "Revenue Name", "Expense Name",
            "Bucket To Name", "Description", "Transaction Amount",
            "Date of Transaction", "Date of Creation", "Bucket Pre-Balance", "Bucket Balance"};
    private String[] headers;

    Table table;

    private Double initBalance;
    private Double transactionSum;
    private Double totalBalance;

    private List<Transaction> transactions;
    private Bucket bucket;

    public AccountBalanceReport(
            Double initBalance,
            Double transactionSum,
            Bucket bucket,
            List<Transaction> transactions,
            Double totalBalance) {

        TableTitle tt = new TableTitle("Statement of Account")
                .withAlign(Align.Center)
                .withBackColor(Color.Grey);

        headers = bucket != null ? h : Arrays.copyOf(h, h.length-2);

        List<Column> columnList = new ArrayList<>();
        for (String header : headers) {
            columnList.add(new Column(new ColumnTitle(header)
                    .withAlign(Align.Center)
                    .withBold(true)));
        }

        Column[] columns = new Column[headers.length];
        columnList.toArray(columns);

        this.table = new Table(tt, columns).withStyle("width: 100% !important");

        this.initBalance = initBalance;
        this.transactionSum = transactionSum;
        this.totalBalance = totalBalance;

        this.bucket = bucket;
        this.transactions = transactions;
    }

    @Override
    public void build() {
        if(this.bucket != null) {
            addSection(
                    new Text("",
                            "Bucket Balance: " + this.totalBalance)
                            .withAlign(Align.Left).withBold(true));
        }

        addSection(
                new Text("",
                        "Transaction Sum: " + this.transactionSum)
                        .withAlign(Align.Left).withBold(true));
        addSection(
                new Text("",
                        "Record Count: " + (this.transactions != null ? this.transactions.size() : 0))
                        .withAlign(Align.Left).withBold(true));

        addSection(table);

        if (transactions != null) {
            for (Transaction transaction : transactions) {
                addRow(transaction);
            }
        }
    }

    public void addRow(Transaction data) {
        String style = "font-size: 12px !important";
        Cell[] res = new Cell[headers.length];
        res[0] = new Cell("<a href='#!/accounting/add-edit-transactions/" + data.getId().toString() + "'>" + data.getId().toString() + "</a>").withStyle(style);
        res[1] = new Cell(data.getFromBucket() != null && data.getFromBucket().getName() != null ?
                data.getFromBucket().getName().replace("&", "&amp;") : "").withStyle(style);
        res[2] = new Cell(data.getRevenue() != null && data.getRevenue().getName() != null ?
                data.getRevenue().getName().replace("&", "&amp;") : "").withStyle(style);
        res[3] = new Cell(data.getExpense() != null && data.getExpense().getName() != null ?
                data.getExpense().getName().replace("&", "&amp;") : "").withStyle(style);
        res[4] = new Cell(data.getToBucket() != null && data.getToBucket().getName() != null ?
                data.getToBucket().getName().replace("&", "&amp;") : "").withStyle(style);
        res[5] = new Cell(data.getDescription() != null ?
                data.getDescription().replace("&", "&amp;") : "").withStyle(style);
        res[6] = new Cell(data.getAmount().toString()).withStyle(style);
        res[7] = new Cell(data.getDate().toString()).withStyle(style);
        res[8] = new Cell(data.getCreationDate().toString()).withStyle(style);

        if (bucket != null) {
            res[9] = new Cell(initBalance.toString()).withStyle(style);

            if (data.getFromBucket() != null && data.getFromBucket().getCode() != null &&
                    data.getFromBucket().getCode().equals(bucket.getCode())) {

                initBalance -= data.getAmount();
            }

            if (data.getToBucket() != null && data.getToBucket().getCode() != null &&
                    data.getToBucket().getCode().equals(bucket.getCode())) {

                initBalance += data.getAmount();
            }

            res[10] = new Cell(initBalance.toString()).withStyle(style);
        }

        table.addRow(res);
    }
}