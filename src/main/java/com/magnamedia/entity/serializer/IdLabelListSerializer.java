package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.core.entity.BaseEntity;
import java.io.IOException;
import java.util.List;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Aug 8, 2018
 */
public class IdLabelListSerializer extends JsonSerializer<List<BaseEntity>> {

	@Override
	public void serialize(List<BaseEntity> values, JsonGenerator gen, SerializerProvider serializers)
		throws IOException, JsonProcessingException {
		if (values == null || values.isEmpty()) {
			gen.writeNull();
			return;
		}
                gen.writeStartArray();
                for (BaseEntity value : values){
                    gen.writeStartObject();
                    gen.writeNumberField("id",
                                                             value.getId());
                    gen.writeStringField("label",
                                                             value.getLabel());
                    gen.writeEndObject();
                }
                gen.writeEndArray();
	}

}
