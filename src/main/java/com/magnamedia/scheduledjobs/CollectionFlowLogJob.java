package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.service.CollectionFlowLogService;

import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 3/12/2022
 **/

public class CollectionFlowLogJob implements MagnamediaJob {

    CollectionFlowLogService collectionFlowLogService;

    public CollectionFlowLogJob () {
        collectionFlowLogService = Setup.getApplicationContext().getBean(CollectionFlowLogService.class);
    }

    @Override
    public void run(Map<String, ?> parameters) {
        // first update old logs
        collectionFlowLogService.updateAllCurrentLogs();
        //second: create new logs
        collectionFlowLogService.generateAllCollectionFlowLogs();
    }
}
