package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.helper.ScheduledJobPaginationHelper;
import com.magnamedia.repository.DirectDebitRejectionToDoRepository;
import com.magnamedia.service.DirectDebitRejectionFlowService;

import java.util.Map;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 6/11/2022
 * this is job which should be run daily to close rejection to-do if the contract is not active anymore or the CPT is not active or the direct debit is rejected or expired or cancelled
 * or if it's related to DDB and there is a newer DDB cover the same period (not rejected or cancelled or expired)
 **/

public class ClosingDirectDebitRejectionTodosJob implements MagnamediaJob {

    private static final Logger logger = Logger.getLogger(ClosingDirectDebitRejectionTodosJob.class.getName());

    private final DirectDebitRejectionToDoRepository directDebitRejectionToDoRepository;
    private final DirectDebitRejectionFlowService directDebitRejectionFlowService;

    public ClosingDirectDebitRejectionTodosJob() {
        directDebitRejectionToDoRepository = Setup.getRepository(DirectDebitRejectionToDoRepository.class);
        directDebitRejectionFlowService = Setup.getApplicationContext().getBean(DirectDebitRejectionFlowService.class);
    }

    @Override
    public void run(Map<String, ?> parameters) {
        processDirectDebitRejectionTodos();
    }

    private void processDirectDebitRejectionTodos() {

        ScheduledJobPaginationHelper.processPaginated(
                "ClosingDirectDebitRejectionTodosJob", // Job name for logging
                directDebitRejectionToDoRepository::findActiveRejectionFlows, // Query Page fetcher - how to get paginated data
                this::processTodo // Entity processor - how to process each DirectDebitRejectionTodo
        );
    }

    private void processTodo(DirectDebitRejectionToDo t) {

        logger.info("DirectDebitRejectionToDo id: " + t.getId());
        if(!directDebitRejectionFlowService.todoExpired(t)) return;

        t.setCompleted(true);
        t.setStopped(true);
        directDebitRejectionToDoRepository.save(t);
    }
}