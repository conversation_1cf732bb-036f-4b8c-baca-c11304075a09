package com.magnamedia.entity.projection;

import java.sql.Date;
import java.util.Map;
import org.springframework.beans.factory.annotation.Value;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Apr 30, 2019
 */
public interface BankDirectDebitActivationRecordProjection {

    Long getId();
    @Value("#{target.getDirectDebitFile() != null ? "
                + "{id: target.getDirectDebitFile().getId(), "
                + "ddMethod: target.getDirectDebitFile().getDdMethod() != null ? target.getDirectDebitFile().getDdMethod().getValue() : null, "
                + "applicationId: target.getDirectDebitFile().getApplicationId(), "
                + "attachments: target.getDirectDebitFile().getSecuredAttachments(), "
                + "type: target.getDirectDebitFile().getDdFrequency() != null ?  target.getDirectDebitFile().getDdFrequency().getValue() : null, "
                + "contractPaymentTerm: (target.getDirectDebitFile().getDirectDebit() != null && target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm() != null ? "
                    + "{id: target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getId(), "
                    + "attachments: target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getAttachments(), "
                    + "contract: (target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract() != null ? "
                        + "{id: target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract().getId(), "
                        + "client: (target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract().getClient() != null ? "
                            + " {id: target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract().getClient().getId(), "
                            + "name: target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract().getClient().getName()}"
                            + ": null)} "
                        + ": null), "
                    + "bankName : target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getBankName()} "
                    + ": null)}"
                + ": null}")
    Map<?, ?> getDirectDebitFile();
    
    Integer getRowIndex();
    public Date getPresentmentDate();
    public String getBank();
    public String getContract();
    public String getAccount();
    public String getIban();
    public String getDdaRefNo();
    public String getStatus();
    public String getRejectionReason();
    public Double getAmount();
    public Date getStartDate();
    public Date getExpiryDate();
    public String getErrorMessage();
    public String getNextAction();
    public boolean isConfirmed();
    public boolean getProcessing();
}
