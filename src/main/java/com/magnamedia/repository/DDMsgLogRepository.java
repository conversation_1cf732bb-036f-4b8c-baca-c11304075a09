package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.DDMsgLog;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Feb 29, 2020
 *         Jirra ACC-1435
 */

@Repository
public interface DDMsgLogRepository extends BaseRepository<DDMsgLog> {

    DDMsgLog findFirstByContractPaymentTerm(ContractPaymentTerm contractPaymentTerm);

    List<DDMsgLog> findByContractPaymentTermAndActive(ContractPaymentTerm contractPaymentTerm, Boolean active);
}
