package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.entity.Payment;
import java.io.IOException;
import java.util.List;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Apr 18, 2019
 * ACC-599
 */
public class PaymentsJsonSerializer extends JsonSerializer<List<Payment>>  {

    @Override
    public void serialize(
            List<Payment> values,
            JsonGenerator gen,
            SerializerProvider serializers)
            throws IOException, JsonProcessingException {
        if (values == null) {
            gen.writeNull();
            return;
        }

        gen.writeStartArray();
        for (Payment value : values){
            (new PaymentJsonSerializer()).serialize(value, gen, serializers);
        }
        gen.writeEndArray();
    }
}
