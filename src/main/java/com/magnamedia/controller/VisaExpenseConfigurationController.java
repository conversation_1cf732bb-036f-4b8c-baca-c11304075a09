package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Expense;
import com.magnamedia.entity.VisaExpenseConfiguration;
import com.magnamedia.module.type.PaymentType;
import com.magnamedia.repository.ExpenseRepository;
import com.magnamedia.repository.VisaExpenseConfigurationRepository;
import com.magnamedia.workflow.visa.ExpensePurpose;
import com.sun.istack.logging.Logger;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@RequestMapping("/visaExpenseConfiguration")
@RestController
public class VisaExpenseConfigurationController extends BaseRepositoryController<VisaExpenseConfiguration> {
    private final Logger logger = Logger.getLogger(VisaExpenseConfigurationController.class);

    @Autowired
    private VisaExpenseConfigurationRepository visaExpenseConfigurationRepository;
    @Autowired
    private ExpenseRepository expenseRepository;

    @Override
    public BaseRepository<VisaExpenseConfiguration> getRepository() {
        return visaExpenseConfigurationRepository;
    }


    @Override
    protected ResponseEntity<?> createEntity(VisaExpenseConfiguration entity) {

        if (visaExpenseConfigurationRepository.existsByEmployeeTypeAndNewEmployeeAndExpensePurposeAndPaymentTypeAndIdNot(
                entity.getEmployeeType(), entity.isNewEmployee(), entity.getExpensePurpose(), entity.getPaymentType(), -1L))
            throw new BusinessException("There is already a configuration with the same conditions");

        return super.createEntity(entity);
    }

    @Override
    protected ResponseEntity<?> updateEntity(VisaExpenseConfiguration entity) {

        if (visaExpenseConfigurationRepository.existsByEmployeeTypeAndNewEmployeeAndExpensePurposeAndPaymentTypeAndIdNot(
                entity.getEmployeeType(), entity.isNewEmployee(), entity.getExpensePurpose(), entity.getPaymentType(), entity.getId()))
            throw new BusinessException("There is already a configuration with the same conditions");

        return super.updateEntity(entity);
    }

    @PreAuthorize("hasPermission('visaExpenseConfiguration','getSearchInfo')")
    @GetMapping(value = "/getSearchInfo")
    public ResponseEntity<?> getSearchInfo() {

        Map<String, Object> r = new HashMap<>();

        Map<String, String> m = new HashMap<>();
        VisaExpenseConfiguration.EmployeeType[] array = VisaExpenseConfiguration.EmployeeType.values();
        for (VisaExpenseConfiguration.EmployeeType employeeType: array) {
            m.put(employeeType.getValue(), employeeType.getLabel());
        }
        r.put("employeeTypes", m);

        m = new HashMap<>();
        ExpensePurpose[] array1 = ExpensePurpose.values();
        for (ExpensePurpose expensePurpose: array1) {
            m.put(expensePurpose.name(), expensePurpose.getLabel());
        }
        r.put("expensePurposes", m);

        r.put("paymentTypes", PaymentType.values());
        r.put("expenses", Setup.getRepository(ExpenseRepository.class).findIdAndNameByCode());

        return ResponseEntity.ok(r);
    }

    @PreAuthorize("hasPermission('visaExpenseConfiguration','search')")
    @GetMapping(value = "/page/search")
    public ResponseEntity<?> search(
            Pageable pageable,
            @RequestParam(name = "employeeType", required = false) VisaExpenseConfiguration.EmployeeType employeeType,
            @RequestParam(name = "newEmployee", required = false) Boolean newEmployee,
            @RequestParam(name = "expensePurpose", required = false) ExpensePurpose expensePurpose,
            @RequestParam(name = "paymentType", required = false) PaymentType paymentType,
            @RequestParam(name = "expenseId", required = false) Long expenseId) {

        SelectQuery<VisaExpenseConfiguration> q = new SelectQuery<>(VisaExpenseConfiguration.class);

        if (employeeType != null) {
            q.filterBy("employeeType", "=", employeeType);
        }

        if (newEmployee != null) {
            q.filterBy("newEmployee", "=", newEmployee);
        }

        if (expensePurpose != null) {
            q.filterBy("expensePurpose", "=", expensePurpose);
        }

        if (paymentType != null) {
            q.filterBy("paymentType", "=", paymentType);
        }

        if (expenseId != null) {
            q.filterBy("expense.id", "=", expenseId);
        }

        return ResponseEntity.ok(q.execute(pageable));
    }

    @PreAuthorize("hasPermission('visaExpenseConfiguration','initializeVisaExpenseConfigurations')")
    @PostMapping(value = "/initializeVisaExpenseConfigurations")
    @Transactional
    public ResponseEntity<?> initializeVisaExpenseConfigurations(
            MultipartFile file,
            @RequestParam(name = "deleteOld", required = false, defaultValue = "false") boolean deleteOld) throws IOException {


        if (deleteOld) {
            visaExpenseConfigurationRepository.deleteAll();
        }

        initializeVisaExpenseConfigurationsReadSheetData(file, "Production");
        initializeVisaExpenseConfigurationsReadSheetData(file, "ACC-6557");
        initializeVisaExpenseConfigurationsReadSheetData(file, "ACC-6912");

        return ResponseEntity.ok("Done");
    }

    private void initializeVisaExpenseConfigurationsReadSheetData(MultipartFile file, String sheetName) throws IOException {

        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        XSSFSheet sheet = workbook.getSheet(sheetName);

        if (sheet == null) {
            logger.warning("Sheet not found");
            return ;
        }
        logger.info( "Sheet Name: " + sheetName);

        for (Row row : sheet) {
            if (row.getRowNum() == 0) continue;
            logger.info("Row Num: " + row.getRowNum());
            int index = (int) row.getCell(0).getNumericCellValue();

            if (index == -1) break;
            ExpensePurpose expensePurpose = ExpensePurpose.valueOf(row.getCell(2).getStringCellValue());
            PaymentType paymentType = PaymentType.valueOf(row.getCell(15).getStringCellValue());

            {
                String newCcExpenseName =  row.getCell(3).getStringCellValue();
                String newCcExpenseCode =  row.getCell(4).getStringCellValue();
                createNewVisaExpenseConfigurationForAcc6912(newCcExpenseCode, newCcExpenseName, expensePurpose, paymentType,
                        VisaExpenseConfiguration.EmployeeType.MAID_CC, true);
            }

            {
                String renewCcExpenseName =  row.getCell(5).getStringCellValue();
                String renewCcExpenseCode =  row.getCell(6).getStringCellValue();
                createNewVisaExpenseConfigurationForAcc6912(renewCcExpenseCode, renewCcExpenseName, expensePurpose, paymentType,
                        VisaExpenseConfiguration.EmployeeType.MAID_CC, false);
            }

            {
                String newMvExpenseName =  row.getCell(7).getStringCellValue();
                String newMvExpenseCode =  row.getCell(8).getStringCellValue();
                createNewVisaExpenseConfigurationForAcc6912(newMvExpenseCode, newMvExpenseName, expensePurpose, paymentType,
                        VisaExpenseConfiguration.EmployeeType.MAID_VISA, true);
            }

            {
                String renewMvExpenseName =  row.getCell(9).getStringCellValue();
                String renewMvExpenseCode =  row.getCell(10).getStringCellValue();
                createNewVisaExpenseConfigurationForAcc6912(renewMvExpenseCode, renewMvExpenseName, expensePurpose, paymentType,
                        VisaExpenseConfiguration.EmployeeType.MAID_VISA, false);
            }

            {
                String newOfficeStaffExpenseName =  row.getCell(11).getStringCellValue();
                String newOfficeStaffExpenseCode =  row.getCell(12).getStringCellValue();
                createNewVisaExpenseConfigurationForAcc6912(newOfficeStaffExpenseCode, newOfficeStaffExpenseName, expensePurpose, paymentType,
                        VisaExpenseConfiguration.EmployeeType.OFFICE_STAFF, true);
            }

            {
                String renewOfficeStaffExpenseName =  row.getCell(13).getStringCellValue();
                String renewOfficeStaffExpenseCode =  row.getCell(14).getStringCellValue();
                createNewVisaExpenseConfigurationForAcc6912(renewOfficeStaffExpenseCode, renewOfficeStaffExpenseName, expensePurpose, paymentType,
                        VisaExpenseConfiguration.EmployeeType.OFFICE_STAFF, false);
            }
        }
    }

    private void createNewVisaExpenseConfigurationForAcc6912(
            String expenseCode, String expenseName, ExpensePurpose expensePurpose,
            PaymentType paymentType, VisaExpenseConfiguration.EmployeeType employeeType, boolean newEmployee) {

        if (expenseCode.equals("-")) return;

        Expense expense = expenseRepository.findOneByCode(expenseCode);

        if (expense == null)
            throw new BusinessException("Expense code not found , code: " + expenseCode +
                    ", name in the sheet: " + expenseName);

        if (!expense.getName().equals(expenseName))
            throw new BusinessException("Expense name on sheet did not match expense name in ERP, " +
                    "name: " + expense.getName() +", code: " + expense.getCode() +
                    ", name in the sheet: " + expenseName);

        VisaExpenseConfiguration configuration = new VisaExpenseConfiguration();
        configuration.setExpense(expense);
        configuration.setExpensePurpose(expensePurpose);
        configuration.setEmployeeType(employeeType);
        configuration.setNewEmployee(newEmployee);
        configuration.setPaymentType(paymentType);
        this.createEntity(configuration);
    }
}