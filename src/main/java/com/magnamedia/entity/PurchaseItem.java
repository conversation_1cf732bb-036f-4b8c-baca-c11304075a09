package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.ItemInPurchaseItemSerializer;
import com.magnamedia.module.type.PurchaseItemInOrderStatus;
import com.magnamedia.module.type.PurchaseItemSupplierStatus;
import com.magnamedia.repository.PurchaseItemRepository;
import com.magnamedia.service.ConsumptionRateService;

import javax.persistence.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.logging.Logger;

/**
 * Mohammad <PERSON> (Feb 01, 2021)
 */
@Entity
public class PurchaseItem extends BaseEntity {
    @JsonSerialize(using = IdLabelSerializer.class)
    @ManyToOne(fetch = FetchType.LAZY)
    private PurchasingToDo purchasingToDo;

    @JsonSerialize(using = ItemInPurchaseItemSerializer.class)
    @ManyToOne(fetch = FetchType.LAZY)
    private Item item;

    @JsonSerialize(using = IdLabelSerializer.class)
    @ManyToOne(fetch = FetchType.LAZY)
    private Supplier currentSupplier;
    private BigDecimal unitPrice;
    private String sendToGetBetterPriceNote;
    private String managerNotes;
    private BigDecimal quantity;

    private BigDecimal packageSize;
    private BigDecimal packagePrice;

    @Enumerated(EnumType.STRING)
    private PurchaseItemSupplierStatus supplierStatus = PurchaseItemSupplierStatus.PENDING;

    @Enumerated(EnumType.STRING)
    private PurchaseItemInOrderStatus itemInOrderStatus;

    @JsonSerialize(using = IdLabelSerializer.class)
    @ManyToOne(fetch = FetchType.LAZY)
    private PurchaseOrder purchaseOrder;

    private BigDecimal initialCycleInventory;
    private BigDecimal consumptionRate;
    private BigDecimal theoreticalConsumption;

    private BigDecimal updatedConsumptionRate;
    private BigDecimal updatedInitialCycleInventory;

    public PurchaseItem() {
    }

    public PurchaseItem(Item item, BigDecimal quantity) {
        this.item = item;
        this.quantity = quantity;

        Logger.getLogger(PurchaseItem.class.getName()).info("computing initialCycleInventory");
        this.initialCycleInventory = item.getInitialCycleInventory();
        this.consumptionRate = item.getConsumptionRate();

        Logger.getLogger(PurchaseItem.class.getName()).info("computing theoreticalConsumption");
        this.theoreticalConsumption = Setup.getApplicationContext().getBean(ConsumptionRateService.class)
                .calculateTheoreticalConsumption(this);

        Logger.getLogger(PurchaseItem.class.getName()).info("computing updatedConsumptionRate");
        this.updatedConsumptionRate = consumptionRate;
        this.updatedInitialCycleInventory = theoreticalConsumption;
    }

    public PurchaseItemInOrderStatus getItemInOrderStatus() {
        return itemInOrderStatus;
    }

    public void setItemInOrderStatus(PurchaseItemInOrderStatus itemInOrderStatus) {
        this.itemInOrderStatus = itemInOrderStatus;
    }

    public PurchaseOrder getPurchaseOrder() {
        return purchaseOrder;
    }

    public void setPurchaseOrder(PurchaseOrder purchaseOrder) {
        this.purchaseOrder = purchaseOrder;
    }

    public String getManagerNotes() {
        return managerNotes;
    }

    public void setManagerNotes(String managerNotes) {
        this.managerNotes = managerNotes;
    }

    public String getSendToGetBetterPriceNote() {
        return sendToGetBetterPriceNote;
    }

    public void setSendToGetBetterPriceNote(String sendToGetBetterPriceNote) {
        this.sendToGetBetterPriceNote = sendToGetBetterPriceNote;
    }

    public PurchasingToDo getPurchasingToDo() {
        return purchasingToDo;
    }

    public void setPurchasingToDo(PurchasingToDo purchasingToDo) {
        this.purchasingToDo = purchasingToDo;
    }

    public PurchaseItemSupplierStatus getSupplierStatus() {
        return supplierStatus;
    }

    public void setSupplierStatus(PurchaseItemSupplierStatus supplierStatus) {
        this.supplierStatus = supplierStatus;
    }

    public Item getItem() {
        return item;
    }

    public void setItem(Item item) {
        this.item = item;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public Supplier getCurrentSupplier() {
        return currentSupplier;
    }

    public void setCurrentSupplier(Supplier currentSupplier) {
        this.currentSupplier = currentSupplier;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getPackageSize() {
        return packageSize;
    }

    public void setPackageSize(BigDecimal packageSize) {
        this.packageSize = packageSize;
    }

    public BigDecimal getPackagePrice() {
        return packagePrice;
    }

    public void setPackagePrice(BigDecimal packagePrice) {
        this.packagePrice = packagePrice;
    }

    public void calculateUnitPrice() {
        this.unitPrice = this.packagePrice
                .divide(this.packageSize, 2, RoundingMode.HALF_UP)
                .setScale(2, RoundingMode.CEILING);
    }

    public BigDecimal getInitialCycleInventory() {
        return initialCycleInventory;
    }

    public void setInitialCycleInventory(BigDecimal initialCycleInventory) {
        this.initialCycleInventory = initialCycleInventory;
    }

    public BigDecimal getConsumptionRate() {
        return consumptionRate;
    }

    public void setConsumptionRate(BigDecimal consumptionRate) {
        this.consumptionRate = consumptionRate;
    }

    public BigDecimal getTheoreticalConsumption() {
        return theoreticalConsumption;
    }

    public void setTheoreticalConsumption(BigDecimal theoreticalConsumption) {
        this.theoreticalConsumption = theoreticalConsumption;
    }

    @JsonIgnore
    public BigDecimal getActualConsumption() {
        Logger.getLogger(PurchaseItem.class.getName()).info("getActualConsumption");

        if (item == null) return new BigDecimal("0");

        if (item.getConsumptionRate().compareTo(this.consumptionRate) != 0) {
            List<PurchaseItem> itemList = Setup.getRepository(PurchaseItemRepository.class).findTop2ByItemOrderByCreationDateDesc(item);

            if (itemList.size() == 2) {
                return itemList.get(1).getUpdatedInitialCycleInventory().subtract(item.getQuantity()).setScale(2, RoundingMode.HALF_EVEN);
            }
        }
        BigDecimal initial = item.getInitialCycleInventory();
        if (initial == null) return new BigDecimal("0");
        return initial.subtract(item.getQuantity()).setScale(2, RoundingMode.HALF_EVEN);
    }

    public BigDecimal getUpdatedConsumptionRate() {
        return updatedConsumptionRate;
    }

    public void setUpdatedConsumptionRate(BigDecimal previousConsumptionRate) {
        this.updatedConsumptionRate = previousConsumptionRate;
    }

    public BigDecimal getUpdatedInitialCycleInventory() {
        return updatedInitialCycleInventory;
    }

    public void setUpdatedInitialCycleInventory(BigDecimal previousInitialCycleInventory) {
        this.updatedInitialCycleInventory = previousInitialCycleInventory;
    }
}
