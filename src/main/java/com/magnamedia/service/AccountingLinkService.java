package com.magnamedia.service;


import com.magnamedia.core.Setup;
import com.magnamedia.core.type.CoreParameter;
import com.magnamedia.entity.AccountingLink;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.repository.AccountingLinkRepository;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;


@Service
public class AccountingLinkService {

    protected static final Logger logger = Logger.getLogger(AccountingLinkService.class.getName());

    private static AccountingLinkRepository getAccountingLinkRepository() {
        return Setup.getRepository(AccountingLinkRepository.class);
    }

    public static AccountingLink getOrCreateNewLink(AccountingLink.AccountingLinkBuilder builder) {

        AccountingLink a = builder.build();

//        if (a.getId() == null) {
//            expireOldLinks(
//                    a.getRelatedEntityId(),
//                    a.getRelatedEntityType(),
//                    a.getType(),
//                    AccountingLink.AccountingLinkExpirationReason.NEW_LINK_ADDED);
//        }

        return a;
    }

    /*public static void expireOldLinks(
            Long relatedEntityId,
            String relatedEntityType,
            AccountingLink.AccountingLinkType type,
            AccountingLink.AccountingLinkExpirationReason reason) {
        logger.info("relatedEntityId: " + relatedEntityId +
                "; relatedEntityType: " + relatedEntityType +
                "; type: " + type +
                "; reason:" + reason);

        List<AccountingLink> l =  getAccountingLinkRepository().findByRelatedEntityIdAndRelatedEntityTypeAndTypeAndStatus(
                relatedEntityId, relatedEntityType, type, AccountingLink.AccountingLinkStatus.PENDING);

        if (l.isEmpty()) return;

        l.forEach(a -> {
            a.setStatus(AccountingLink.AccountingLinkStatus.EXPIRED);
            a.setExpirationReason(reason);
        });

        getAccountingLinkRepository().save(l);
    }

    public static AccountingLink.AccountingLinkStatus getLinkStatus(String uuid) {
        logger.info("link uuid: " + uuid);
        AccountingLink a = getAccountingLinkRepository().findByUuid(uuid);

        return a == null ? null: a.getStatus();
    }

    public static void markLinkAsDone(AccountingLink a) {
        logger.info("link id: " + a.getId());
        if (!a.getStatus().equals(AccountingLink.AccountingLinkStatus.PENDING)) return;

        a.setStatus(AccountingLink.AccountingLinkStatus.DONE);
        getAccountingLinkRepository().save(a);
    }*/

    public static String getSignDdLink(Map<String, Object> signMap, String parameters, boolean withShortLink) {

        ContractPaymentTerm cpt = (ContractPaymentTerm) signMap.get("cpt");
        String link = Setup.getCoreParameter(CoreParameter.PUBLIC_LINK_BASE)
                + "/modules/accounting/vat-app/#!/sign-dd-v3?uid=" + cpt.getContract().getUuid() + parameters;
        if (!withShortLink) return link;

        AccountingLink a = getOrCreateNewLink(
                new AccountingLink.AccountingLinkBuilder()
                        .AccountingLink(
                                cpt.getId(),
                                cpt.getEntityType(),
                                AccountingLink.AccountingLinkType.SIGN_DD_WEB_PAGE,
                                link)
                        .setContractId(cpt.getContract().getId())
                        .setCptId(cpt.getId())
                        .setIgnoreDuplication(true)
                        .setAdditionalInfo((Map<String, Object>) signMap.getOrDefault("additionalInfo", new HashMap<>())));

        return a.getShortenedLink();
    }
}