package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.DirectDebitSignature;
import com.magnamedia.entity.Payment;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.service.BouncingFlowService;
import com.magnamedia.service.DirectDebitSignatureService;
import org.joda.time.DateTime;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jun 13, 2020
 *          ACC-2055
 */

// bounced sub flow -> CC clients with Cancelled/Expired contracts bounced payment
// if contract is cancelled, we want money -> call executeBFForAcOrExContracts
@BusinessRule(entity = Payment.class, events = {BusinessEvent.AfterCreate, BusinessEvent.AfterUpdate},
        fields = {"id", "dateOfPayment", "typeOfPayment.id", "amountOfPayment", "contract.id", "status",
                "directDebit.id", "directDebitFile.id", "bouncedPaymentLogs.id", "methodOfPayment", "trials",
                "isProRated", "requiredForUnfitToWork", "pBF3BrChecked"})
public class PaymentBouncingFlowBR3 implements BusinessAction<Payment> {

    private static final Logger logger = Logger.getLogger(PaymentBouncingFlowBR3.class.getName());
    private static final String prefix = "MMM ";

    @Override
    public boolean validate(Payment entity, BusinessEvent event) {
        logger.log(Level.SEVERE, "PaymentBouncingFlowBR3 Validation.");
        logger.log(Level.SEVERE, prefix + "payment id: " + (entity.isNewInstance() ? "new instance" : entity.getId()));

        if (entity.ispBF3BrChecked()) return false;
        if (entity.getStatus() == null) return false;
        if (entity.getContract() == null || entity.getContract().getId() == null) return false;
        
        Contract contract = Setup.getRepository(ContractRepository.class).findOne(entity.getContract().getId());
        if (contract == null) return false;

        if (Setup.getApplicationContext().getBean(BouncingFlowService.class)
                .isBouncingFlowStopped(entity)) {

            logger.log(Level.SEVERE, "Bouncing Flow is Stopped");
            return false;
        }
        
        HistorySelectQuery<Payment> historyQuery = new HistorySelectQuery(Payment.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.filterByChanged("status");
        historyQuery.sortBy("lastModificationDate", false, true);
        historyQuery.setLimit(1);
        
        List<Payment> oldPayments = historyQuery.execute();
        Payment old = !oldPayments.isEmpty() ? oldPayments.get(0) : null;

        DirectDebit directDebit = null;
        if (entity.getDirectDebit() != null && entity.getDirectDebit().getId() != null)
            directDebit = Setup.getRepository(DirectDebitRepository.class).findOne(entity.getDirectDebit().getId());

        return contract.getContractProspectType().getCode().equals(PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE)) &&
                Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED).contains(contract.getStatus()) &&
                entity.getStatus().equals(PaymentStatus.BOUNCED) &&
                (old == null || old.getStatus() == null || !old.getStatus().equals(PaymentStatus.BOUNCED)) &&
                (directDebit == null ||
                        ((directDebit.getManualDdfFile() == null || !directDebit.getMStatus().equals(DirectDebitStatus.CONFIRMED)) &&
                                directDebit.getCategory().equals(DirectDebitCategory.B)));

    }

    @Override
    public Map execute(Payment entity, BusinessEvent even) {
        logger.log(Level.SEVERE, "PaymentBouncingFlowBR3 Execution.");
        DirectDebitRepository ddRepository = Setup.getRepository(DirectDebitRepository.class);
        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
        BouncingFlowService bouncingFlowService = Setup.getApplicationContext().getBean(BouncingFlowService.class);
        DirectDebitSignatureService directDebitSignatureService = Setup.getApplicationContext().getBean(DirectDebitSignatureService.class);

        Map map = new HashMap();
        map.put("pBF3BrChecked", true);

        Contract contract = contractRepository.findOne(entity.getContract().getId());

        //If we don't want money from the client
        if (!bouncingFlowService.doWeWantMoneyFromClient(contract, true, null)) {
            return map;
        }

        List<DirectDebitSignature> signatures = null;
        // if client owes us money
        if (entity.getMethodOfPayment() != null && entity.getMethodOfPayment().equals(PaymentMethod.CHEQUE)) {
            // ACC-2054 send an email to George to report the cheque to the police, and nothing else
            if (contractRepository.existsByStatusAndClient(ContractStatus.ACTIVE, contract.getClient())) {
                contract = contractRepository.findFirstOneByClientAndStatusInOrderByCreationDateDesc(contract.getClient(), new ContractStatus[]{ContractStatus.ACTIVE});

                Map<String, Object> signatureType = directDebitSignatureService
                        .getLastSignatureType(contract, contract.getClient().getEid(), true, false);
                if ((Boolean) signatureType.get("useApprovedSignature")) {
                    signatures = (List<DirectDebitSignature>) signatureType.get("currentSignatures");
                }
            } else {
                // send police warning, messaging setup handles this
                logger.log(Level.SEVERE, "No Approved/Pending Signatures");
                return map;
            }
        } else {
            Map<String, Object> signatureType = directDebitSignatureService
                    .getLastSignatureType(contract.getClient(), contract.getClient().getEid(), true, true);

            // ACC-2503
            if (!(Boolean) signatureType.get("useApprovedSignature")) {
                logger.log(Level.SEVERE, "No Approved Signature");// ACC-2503
                return map;
            } else {
                signatures = (List<DirectDebitSignature>) signatureType.get("currentSignatures");
            }

        }

        DirectDebit directDebit = null;
        if (entity.getDirectDebit() != null && entity.getDirectDebit().getId() != null)
            directDebit = ddRepository.findOne(entity.getDirectDebit().getId());

        // Client has PDC DDs -> do nothing
        if (directDebit != null && directDebit.getAutoDdfFile() != null && directDebit.getStatus().equals(DirectDebitStatus.CONFIRMED)
                && !directDebit.getExpiryDate().before(new DateTime(entity.getDateOfPayment()).plusMonths(1).withDayOfMonth(1).withTimeAtStartOfDay().toDate())) {
            logger.log(Level.SEVERE, prefix + "Client has PDC DDs -> do nothing");
            return map;
        }

        map.putAll(bouncingFlowService.paymentGetBouncedStartDirectDebitProcess(entity, signatures, directDebit));
        return map;
    }
}
