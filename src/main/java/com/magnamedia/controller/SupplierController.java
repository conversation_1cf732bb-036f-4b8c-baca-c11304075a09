package com.magnamedia.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.ibanvalidator.SwiftCodeValidationResponse;
import com.magnamedia.core.helper.ibanvalidator.ValidateIBANService;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.security.ViewScope;
import com.magnamedia.entity.Supplier;
import com.magnamedia.entity.projection.SupplierProjection;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ExpensePaymentMethod;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.extra.SupplierCSVProjection;
import com.magnamedia.helper.CsvHelper;
import com.magnamedia.repository.SupplierRepository;
import com.magnamedia.scheduledjobs.PurchasingInitialReminderToStockKeeperJob;
import com.magnamedia.scheduledjobs.PurchasingOtherReminderToShockKeeperJob;
import com.magnamedia.scheduledjobs.PurchasingTriggerJob;
import com.magnamedia.scheduledjobs.SalesBinderUpdateDataJob;
import com.magnamedia.service.QueryService;
import com.magnamedia.service.SalesBinderService;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Mohammad Nosairat (Jan 26, 2021)
 */
@RestController
@RequestMapping("/supplier")
public class SupplierController extends BaseRepositoryController<Supplier> {
    
    @Autowired
    private ProjectionFactory projectionFactory;
    @Autowired
    private ValidateIBANService validateIBANService;

    @Override
    public ResponseEntity<?> createEntity(Supplier entity) {
        validateCreation(entity);
        return super.createEntity(entity);
    }

    public void validateCreation(Supplier entity) {
        checkIfNameExists(entity.getName());
        validateSwiftCode(entity);
    }

    public void validateSwiftCode(Supplier entity){
        if (ExpensePaymentMethod.BANK_TRANSFER.equals(entity.getPaymentMethod()) && entity.getInternational()) {

            if(entity.getSwift() == null || entity.getSwift().isEmpty()) {
                throw new BusinessException("Swift code required");
            }

            SwiftCodeValidationResponse response = validateIBANService.validateSwiftCode(entity.getSwift());
            if (response == null || response.getData() == null || !response.isSuccess()) {
                throw new BusinessException("Swift code is not valid");
            }
        }
    }
    @Override
    public ResponseEntity<?> update(@RequestBody ObjectNode objectNode) throws IOException {
        if (this.checkPermission("update")) {
            Supplier updated = this.parse(objectNode);
            Supplier origin = supplierRepository.findOne(updated.getId());

            validateUpdate(updated, origin);
            origin.updatePaymentDetail();

            super.update(origin, updated, objectNode);

            return this.updateEntity(origin);
        } else {
            return this.unauthorizedReponse();
        }
    }

    public void validateUpdate(Supplier updated, Supplier origin) {
        if (CurrentRequest.getUser() == null || !CurrentRequest.getUser().hasPosition(AccountingModule.SUPPLIER_EDITOR_POSITION)) {
            throw new BusinessException("You don't have permission to edit supplier");
        }
        if (updated.getName() != null && !origin.getName().equals(updated.getName())) {
            checkIfNameExists(updated.getName());
        }
        validateSwiftCode(updated);
    }

    private void checkIfNameExists(String supplierName) {
        if (QueryService.existsEntity(Supplier.class, "e.active = true and " +
                        "regexp_replace(lower(trim(e.name)), ' +', ' ') = regexp_replace(lower(trim(:p0)), ' +', ' ')",
                new Object[]{supplierName})) {
            throw new BusinessException("Supplier Name already exists");
        }
    }

    private boolean updatedPaymentDetails(Supplier supplier, Supplier entity) {
        if (entity.getPaymentMethod() != supplier.getPaymentMethod())
            return true;
        if (entity.getAccountName() != null
                && supplier.getAccountName() != null
                && !entity.getAccountName().equals(supplier.getAccountName()))
            return true;
        if (entity.getAccountNumber() != null
                && supplier.getAccountNumber() != null
                && !entity.getAccountNumber().equals(supplier.getAccountNumber()))
            return true;
        if (entity.getIban() != null
                && supplier.getIban() != null
                && !entity.getIban().equals(supplier.getIban()))
            return true;
        if (entity.getMobileNumber() != null
                && supplier.getMobileNumber() != null
                && !entity.getMobileNumber().equals(supplier.getMobileNumber()))
            return true;
        if (entity.getSwift() != null
                && supplier.getSwift() != null
                && !entity.getSwift().equals(supplier.getSwift()))
            return true;
        if (entity.getAddress() != null
                && supplier.getAddress() != null
                && !entity.getAddress().equals(supplier.getAddress()))
            return true;

        return false;
    }

    @Autowired
    SupplierRepository supplierRepository;
    @Autowired
    SalesBinderService salesBinderService;

    @NoPermission
    @RequestMapping("/update-sales-binder-data")
    public ResponseEntity<?> updateSalesBinderData() {
        SalesBinderUpdateDataJob salesBinderUpdateDataJob = new SalesBinderUpdateDataJob();
        salesBinderUpdateDataJob.runJob();
        return ResponseEntity.ok().build();
    }

    @NoPermission
    @RequestMapping("/get-supplier-list")
    public ResponseEntity<?> getSupplierList(
            @RequestParam(name = "search", required = false) String search) {

        SelectQuery<Supplier> query = new SelectQuery(Supplier.class);
        if(search != null) {
            query.filterBy("name", "like", "%" + search + "%");
        }

        return ResponseEntity.ok(query.execute().stream()
                .map(s -> new Object() {
                    public Long id = s.getId();
                    public String label = s.getName();
                    public Boolean vatRegistered = s.getVatRegistered();
                }).collect(Collectors.toList()));
    }

    @NoPermission
    @RequestMapping("/get-salesbinder-supplier-list")
    public ResponseEntity<?> getSalesbinderSupplierList(
            @RequestParam(required = false, value = "searchQuery") String searchQuery, Pageable pgbl) {
        //List<Supplier> suppliers = supplierRepository.findBySupplierIdNotNull();
        SelectQuery<Supplier> query = new SelectQuery<>(Supplier.class);
        if (searchQuery != null && !searchQuery.isEmpty())
            query.filterBy("name","LIKE","%" + searchQuery + "%");
        query.filterBy("supplierId","is not null", null);
        return new ResponseEntity<>(
                query.execute(pgbl).map(
                        obj -> projectionFactory.createProjection(
                                SupplierProjection.class, obj)),
                HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('supplier','purchasingTriggerJob')")
    @RequestMapping("/purchasingTriggerJob")
    public ResponseEntity<?> purchasingTriggerJob() {
        PurchasingTriggerJob purchasingTriggerJob = new PurchasingTriggerJob();
        purchasingTriggerJob.run(null);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasPermission('supplier','purchasingOtherReminderToShockKeeperJob')")
    @RequestMapping("/purchasingOtherReminderToShockKeeperJob")
    public ResponseEntity<?> purchasingOtherReminderToShockKeeperJob() {
        PurchasingOtherReminderToShockKeeperJob purchasingOtherReminderToShockKeeperJob = new PurchasingOtherReminderToShockKeeperJob();
        purchasingOtherReminderToShockKeeperJob.run(null);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasPermission('supplier','purchasingInitialReminderToStockKeeperJob')")
    @RequestMapping("/purchasingInitialReminderToStockKeeperJob")
    public ResponseEntity<?> purchasingInitialReminderToStockKeeperJob() {
        PurchasingInitialReminderToStockKeeperJob purchasingInitialReminderToStockKeeperJob = new PurchasingInitialReminderToStockKeeperJob();
        purchasingInitialReminderToStockKeeperJob.run(null);
        return ResponseEntity.ok().build();
    }

    @NoPermission
    @RequestMapping(value = {"/search/page"}, method = {RequestMethod.GET})
    @ResponseBody
    @JsonView(value = {ViewScope.Normal.class})
    public ResponseEntity<?> searchPage(
            @RequestParam(required = false, value = "searchQuery") String searchQuery, Pageable pgbl) {
        
        if (checkPermission("list")) {
            CurrentRequest.authorize();
            if (searchQuery == null || searchQuery.isEmpty())
                return listEntities(pgbl);
            else
                return searchEntitiesByLabel(searchQuery,pgbl);
        } else {
            return unauthorizedReponse();
        }
    }

    @Override
    public BaseRepository<Supplier> getRepository() {
        return supplierRepository;
    }

    @NoPermission
    @GetMapping(value = "/checkSwift/{swiftCode}")
    public ResponseEntity<?> checkSwiftCode(@PathVariable("swiftCode") String swiftCode) {
        Map<String, Object> r = new HashMap<String, Object>() {{
            put("valid", false);
        }};

        try {
            SwiftCodeValidationResponse response = validateIBANService.validateSwiftCode(swiftCode);

            if (response == null || response.getData() == null || !response.isSuccess() ) return ResponseEntity.ok(r);
                r.put("valid", true);
                r.put("bankName", response.getData().getBank() != null ?
                        response.getData().getBank().getName() : "");
                r.put("branchCountryName", response.getData().getCountry() != null ?
                        response.getData().getCountry().getName() : "");
                r.put("branchCityName", response.getData().getCity() != null ?
                        response.getData().getCity().getName() : "");

        } catch (Exception e) {
            e.printStackTrace();
        }

        return ResponseEntity.ok(r);
    }

    @PreAuthorize("hasPermission('supplier', 'exportToCsv')")
    @GetMapping(value = "/exportToCsv")
    public void exportToCsv(@RequestParam(name = "supplierName", required = false) String supplierName,
                            @RequestParam(name = "active", required = false) Boolean active,
                            HttpServletResponse response) {

        SelectQuery<Supplier> query = new SelectQuery<>(Supplier.class);

        if (supplierName != null && !supplierName.isEmpty()) {
            query.filterBy("name", "like", "%" + supplierName + "%");
        }

        if (active != null) {
            query.filterBy("active", "=", active);
        }

        List<Supplier> suppliers = query.execute();
        InputStream inputStream = null;
        String fileName = "Suppliers.csv";
        try {
            String[] namesOrdered = {"id", "name", "paymentMethod", "financialName", "internationalAsString",
                    "ibanForSupplier", "accountNumberForSupplier", "swiftForSupplier", "vatRegisteredAsString"};

            String[] headers = {"Supplier Id", "Supplier Name", "Supplier Payment method", "Financial Name", "Local / International",
                    "IBAN", "Account Number", "Swift", "VAT Registered"};

            File excelFile = CsvHelper.generateCsv(suppliers, SupplierCSVProjection.class,
                    headers, namesOrdered, fileName);

            inputStream = new FileInputStream(excelFile);
            if (inputStream != null) {
                createDownloadResponse(response, fileName, inputStream);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            StreamsUtil.closeStream(inputStream);
        }
    }

    @PreAuthorize("hasPermission('supplier', 'updateSupplierActiveValueToFalse')")
    @PostMapping("/updateSupplierActiveValueToFalse")
    public ResponseEntity<?> updateSupplierActiveValueToFalse(MultipartFile file) throws IOException {

        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        XSSFSheet sheet = workbook.getSheet("Non-Active Suppliers");

        if (sheet == null) {
            logger.info("Sheet: not found");
            throw new BusinessException("Sheet: not found");
        }

        for (Row row : sheet) {

            if (row.getRowNum() == 0) continue;
            if (row.getCell(0) == null) break;

            logger.info("Row Num: " + row.getRowNum());

            Long id = (long) row.getCell(0).getNumericCellValue();

            Supplier supplier = supplierRepository.findOne(id);

            if (supplier == null) continue;

            supplier.setActive(false);
            supplierRepository.save(supplier);
        }
        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('supplier', 'getActiveSuppliers')")
    @GetMapping(value = "/getActiveSuppliers")
    public ResponseEntity<?> getActiveSuppliers(@RequestParam(value = "search", required = false) String search, Pageable pageable) {

        SelectQuery<Supplier> query = new SelectQuery<>(Supplier.class);
        if (search != null && !search.isEmpty()) {
            query.filterBy("name","like","%" + search + "%");
        }

        query.filterBy("active","=", true);

        return ResponseEntity.ok(query.execute(pageable));
    }
}
