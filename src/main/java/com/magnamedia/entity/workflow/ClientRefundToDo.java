package com.magnamedia.entity.workflow;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.controller.PayrollAccountantTodoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.AfterInsert;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.entity.workflow.WorkflowEntity;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.workflow.FormField;
import com.magnamedia.entity.*;
import com.magnamedia.entity.serializer.IdLabelListSerializer;
import com.magnamedia.helper.UserHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.CooQuestionRepository;
import com.magnamedia.repository.PaymentRequestPurposeRepository;
import com.magnamedia.repository.PayrollAccountantTodoRepository;
import com.magnamedia.service.AccountantTodoService;
import com.magnamedia.service.ClientRefundService;
import com.magnamedia.workflow.type.*;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Formula;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.sql.Date;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Logger;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on Dec 06, 2020
 *          ACC-2845
 */

@Entity
@Table(name = "CLIENT_REFUND")
public class ClientRefundToDo extends WorkflowEntity {
    protected static final Logger logger = Logger.getLogger(ClientRefundToDo.class.getName());

    // ACC-8308
    public final static String PROOF_ATTACHMENT_TAG = "proof_attachment";

    public ClientRefundToDo() {
        super("");
    }

    public ClientRefundToDo(String string) {
        super(string);
    }


    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Contract contract;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Client client;

    @JsonSerialize(using = IdLabelSerializer.class)
    @ManyToOne(fetch = FetchType.LAZY)
    private PaymentRequestPurpose purpose;

    @Column(nullable = false)
    private Double amount;

    @Column
    private Integer numberOfMonthlyPayments;

    @JsonSerialize(using = IdLabelSerializer.class)
    @ManyToOne(fetch = FetchType.LAZY)
    private Complaint complaint;

    @Enumerated(EnumType.STRING)
    private ClientRefundPaymentMethod methodOfPayment;

    @Enumerated(EnumType.STRING)
    private ClientRefundStatus status;

    @Column
    private String accountName;

    @Column
    private String iban;

    @Column
    private String eid;

    @Column
    private String description;

    @Lob
    private String notes;

    @Column
    private Date statusChangeDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private User managerActionBy;

    @Enumerated(EnumType.STRING)
    private ClientRefundTodoManagerAction managerAction;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private User ceoActionBy;

    @Enumerated(EnumType.STRING)
    private ClientRefundTodoManagerAction ceoAction;

    @Enumerated(EnumType.STRING)
    private ClientRefundRequestType requestType;

    @JsonIgnore
    @OneToMany(mappedBy = "clientRefundToDo", fetch = FetchType.LAZY)
    List<PayrollAccountantTodo> payrollAccountantTodos;

    @Column
    private Integer numberOfUnusedDays;

    @Column
    private Integer numberOfUsedDays;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem partialRefundForCancellationPaymentMethod;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private ClientRefundToDo parent;

    // ACC-3105
    @Lob
    private String managerNotes;

    // ACC-3105
    @Lob
    private String smsTemplatesToSendOnConfirmation;

    // ACC-3134
    @Transient
    private boolean ignoreRequestedByConstraint = false;

    // ACC-3168
    @Column(columnDefinition = "boolean default false")
    private boolean conditionalRefund = false;

    @JsonSerialize(using = IdLabelListSerializer.class)
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "CLIENT_REFUNDS_REQUIRED_PAYMENTS",
            joinColumns = @JoinColumn(name = "CLIENT_REFUND_ID", referencedColumnName = "ID"),
            inverseJoinColumns = @JoinColumn(name = "PAYMENT_ID", referencedColumnName = "ID"))
    private List<Payment> requiredPayments;

    @Transient
    private boolean checkForRequiredPayments = Boolean.TRUE;

    // ACC-3189
    @ManyToOne
    @JsonSerialize(using = IdLabelSerializer.class)
    private Transaction transaction;

    @Column
    private Long relatedPaymentId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem refundRejectReason;

    @Lob
    private String rejectionNotes;

    @Column(columnDefinition = "boolean default false")
    private boolean automaticRefund = false;

    @Column
    private String flowTriggered;

    @Transient
    private boolean forceCreateBankTransfer = false;

    @Column(columnDefinition = "int default 0")
    private int usedWeeks = 0;

    @Column(columnDefinition = "int default 0")
    private int fullUsedWeeks = 0;
    // ACC-5818
    boolean lenient = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem leniencyType;

    @Column
    private Long housemaidPayrollLogId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem howMuchWeRefundOption; // pickList code Absconder_Maid_Refund_Options

    @Column
    private Date pendingSalaryRecord;

    public Long getHousemaidPayrollLogId() {
        return housemaidPayrollLogId;
    }

    public void setHousemaidPayrollLogId(Long housemaidPayrollLogId) {
        this.housemaidPayrollLogId = housemaidPayrollLogId;
    }

    @Column
    private String transferReference;

    @NotAudited
    @Formula("(CREATION_DATE)")
    @Basic(fetch = FetchType.LAZY)
    private java.util.Date toCreationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private ContractPaymentConfirmationToDo contractPaymentConfirmationToDo;

    @Transient
    private boolean forceUpdateTransferRefOfPayment = false;

    @Column
    private String bankName;

    public Contract getContract() {
        return contract;
    }

    public void setContract(Contract contract) {
        this.contract = contract;
    }

    public Client getClient() {
        return client;
    }

    public void setClient(Client client) {
        this.client = client;
    }

    public PaymentRequestPurpose getPurpose() {
        return purpose;
    }

    public void setPurpose(PaymentRequestPurpose purpose) {
        this.purpose = purpose;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Integer getNumberOfMonthlyPayments() {
        return numberOfMonthlyPayments;
    }

    public void setNumberOfMonthlyPayments(Integer numberOfMonthlyPayments) {
        this.numberOfMonthlyPayments = numberOfMonthlyPayments;
    }

    public Complaint getComplaint() {
        return complaint;
    }

    public void setComplaint(Complaint complaint) {
        this.complaint = complaint;
    }

    public ClientRefundPaymentMethod getMethodOfPayment() {
        return methodOfPayment;
    }

    public void setMethodOfPayment(ClientRefundPaymentMethod methodOfPayment) {
        this.methodOfPayment = methodOfPayment;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getIban() {
        return iban;
    }

    public void setIban(String iban) {
        this.iban = iban;
    }

    public String getEid() {
        return eid;
    }

    public void setEid(String eid) {
        this.eid = eid;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public ClientRefundStatus getStatus() {
        return status;
    }

    public void setStatus(ClientRefundStatus clientRefundStatus) {

        boolean changedToPaid = clientRefundStatus.equals(ClientRefundStatus.PAID) &&
                (this.status != null && this.status.equals(ClientRefundStatus.PENDING));

        this.status = clientRefundStatus;

        if (changedToPaid) {
            Setup.getApplicationContext()
                    .getBean(ClientRefundService.class)
                    .changedToPaid(this);
        }
    }

    public Date getStatusChangeDate() {
        return statusChangeDate;
    }

    public void setStatusChangeDate(Date statusChangeDate) {
        this.statusChangeDate = statusChangeDate;
    }

    public ClientRefundTodoManagerAction getManagerAction() {
        return managerAction;
    }

    public void setManagerAction(ClientRefundTodoManagerAction managerAction) {
        this.managerAction = managerAction;
    }

    public ClientRefundTodoManagerAction getCeoAction() {
        return ceoAction;
    }

    public void setCeoAction(ClientRefundTodoManagerAction ceoAction) {
        this.ceoAction = ceoAction;
    }

    public ClientRefundRequestType getRequestType() {
        return requestType;
    }

    public void setRequestType(ClientRefundRequestType requestType) {
        this.requestType = requestType;
    }

    public User getManagerActionBy() {
        return managerActionBy;
    }

    public void setManagerActionBy(User managerActionBy) {
        this.managerActionBy = managerActionBy;
    }

    public User getCeoActionBy() {
        return ceoActionBy;
    }

    public void setCeoActionBy(User ceoActionBy) {
        this.ceoActionBy = ceoActionBy;
    }

    public ClientRefundToDo getParent() {
        return parent;
    }

    public void setParent(ClientRefundToDo parent) {
        this.parent = parent;
    }

    public String getManagerNotes() {
        return managerNotes;
    }

    public void setManagerNotes(String managerNotes) {
        this.managerNotes = managerNotes;
    }

    public String getSmsTemplatesToSendOnConfirmation() {
        return smsTemplatesToSendOnConfirmation;
    }

    public void setSmsTemplatesToSendOnConfirmation(String smsTemplatesToSendOnConfirmation) {
        this.smsTemplatesToSendOnConfirmation = smsTemplatesToSendOnConfirmation;
    }

    public void setSmsTemplatesToSendOnConfirmationFromList(List<String> smsTemplatesToSendOnConfirmations) {
        if (smsTemplatesToSendOnConfirmations == null) return;

        this.smsTemplatesToSendOnConfirmation = "";
        boolean addComma = false;
        for (String smsTemplate : smsTemplatesToSendOnConfirmations) {
            this.smsTemplatesToSendOnConfirmation += (addComma ? "," : "");
            this.smsTemplatesToSendOnConfirmation += smsTemplate;
            addComma = true;
        }
    }


    public List<String> getSmsTemplatesToSendOnConfirmationAsList() {
        return !StringUtils.isEmpty(smsTemplatesToSendOnConfirmation) ?
                Arrays.asList(smsTemplatesToSendOnConfirmation.split(",")) : new ArrayList();
    }

    public void addSmsTemplateToSendOnConfirmation(String smsTemplatesToSendOnConfirmation) {
        List<String> smsTemplates = this.getSmsTemplatesToSendOnConfirmationAsList();
        smsTemplates.add(smsTemplatesToSendOnConfirmation);
        this.setSmsTemplatesToSendOnConfirmationFromList(smsTemplates);
    }

    public boolean isIgnoreRequestedByConstraint() {
        return ignoreRequestedByConstraint;
    }

    public void setIgnoreRequestedByConstraint(boolean ignoreRequestedByConstraint) {
        this.ignoreRequestedByConstraint = ignoreRequestedByConstraint;
    }

    public boolean isConditionalRefund() {
        return conditionalRefund;
    }

    public void setConditionalRefund(boolean conditionalRefund) {
        this.conditionalRefund = conditionalRefund;
    }

    public List<Payment> getRequiredPayments() {
        return requiredPayments;
    }

    public void setRequiredPayments(List<Payment> requiredPayments) {
        this.requiredPayments = requiredPayments;
    }

    public boolean isCheckForRequiredPayments() {
        return checkForRequiredPayments;
    }

    public void setCheckForRequiredPayments(boolean checkForRequiredPayments) {
        this.checkForRequiredPayments = checkForRequiredPayments;
    }

    public Transaction getTransaction() {
        return transaction;
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
    }

    public boolean getNeedsCooApproval() {
        if (purpose == null) return false;
        ClientRefundSetup setup = purpose.getUniquePurposeSetup();
        return setup != null && setup.doesNeedCeoApproval(this.amount);
    }

    public String getRejectionNotes() {
        return rejectionNotes;
    }

    public void setRejectionNotes(String rejectionNotes) {
        this.rejectionNotes = rejectionNotes;
    }

    @Transient
    @JsonIgnore
    public String getAllNotes() {
        String allNotes = !StringUtils.isEmpty(getRequesterUserName()) && !StringUtils.isEmpty(notes) ?
                (getRequesterUserName() + ": " + notes)
                : "";

        allNotes += managerActionBy != null && !StringUtils.isEmpty(managerNotes) ?
                ((!allNotes.isEmpty() ? System.lineSeparator() : "") + managerActionBy.getUsername() + ": " + managerNotes)
                : "";

        return allNotes;
    }

    public List<PayrollAccountantTodo> getPayrollAccountantTodos() {
        return payrollAccountantTodos;
    }

    public void setPayrollAccountantTodos(List<PayrollAccountantTodo> payrollAccountantTodos) {
        this.payrollAccountantTodos = payrollAccountantTodos;
    }

    public Integer getNumberOfUnusedDays() {
        return numberOfUnusedDays;
    }

    public void setNumberOfUnusedDays(Integer numberOfUnusedDays) {
        this.numberOfUnusedDays = numberOfUnusedDays;
    }

    public Integer getNumberOfUsedDays() {
        return numberOfUsedDays;
    }

    public void setNumberOfUsedDays(Integer numberOfUsedDays) {
        this.numberOfUsedDays = numberOfUsedDays;
    }

    public PicklistItem getPartialRefundForCancellationPaymentMethod() {
        return partialRefundForCancellationPaymentMethod;
    }

    public void setPartialRefundForCancellationPaymentMethod(PicklistItem partialRefundForCancellationPaymentMethod) {
        this.partialRefundForCancellationPaymentMethod = partialRefundForCancellationPaymentMethod;
    }

    public String getRequesterUserName() {
        User requester = getRequesterUser();
        return requester == null ? "" : requester.getUsername();
    }

    public User getRequesterUser() {
        return getParent() == null ? getCreator() : getParent().getCreator();
    }

    public String getRequesterName() {
        User requester = getRequesterUser();
        return requester == null ? "" : requester.getName();
    }

    public boolean isProofUploaded() {
        if (getPurpose() == null) return false;

        PaymentRequestPurpose purpose = Setup.getRepository(PaymentRequestPurposeRepository.class)
                .findOne(getPurpose().getId());
        ClientRefundSetup setup = purpose.getUniquePurposeSetup();
        return setup != null && setup.getRequireAttachment();
    }

    public boolean getCanDoManagerAction() {

        if (getTaskName() == null ||
                !getTaskName().equals(ClientRefundTodoType.WAITING_MANAGER_APPROVAL.toString()) ||
                !getStatus().equals(ClientRefundStatus.PENDING) ||
                getManagerAction() != null || getPurpose() == null) return false;

        PaymentRequestPurpose purpose = Setup.getRepository(PaymentRequestPurposeRepository.class)
                .findOne(getPurpose().getId());

        ClientRefundSetup setup = purpose.getUniquePurposeSetup(
                getPartialRefundForCancellationPaymentMethod() == null ?
                        null : getPartialRefundForCancellationPaymentMethod().getId());

        User approver = Setup.getApplicationContext()
                .getBean(ClientRefundService.class)
                .getApproverBySetup(setup, getMethodOfPayment());

        return approver != null && approver.equals(CurrentRequest.getUser());
    }

    public boolean getCanDoCeoAction() {
        return getTaskName() != null &&
                getTaskName().equals(ClientRefundTodoType.WAITING_COO_APPROVAL.toString()) &&
                getStatus().equals(ClientRefundStatus.PENDING) && getCeoAction() == null &&
                UserHelper.hasFamilyRefundCooPosition();
    }

    public ComplaintType getComplaintType() {
        return getComplaint() != null ? getComplaint().getPrimaryType() : null;
    }

    public Long getRelatedPaymentId() {
        return relatedPaymentId;
    }

    public void setRelatedPaymentId(Long relatedPaymentId) {
        this.relatedPaymentId = relatedPaymentId;
    }

    @AfterInsert
    public void afterInsert() {
        PaymentRequestPurpose requestPurpose = Setup.getRepository(PaymentRequestPurposeRepository.class)
                .findOne(purpose.getId());

        logger.info("ID: " + this.getId() + "; Task Name: " + this.getTaskName() +
                "Purpose: " + (requestPurpose != null ?
                "ID: " + requestPurpose.getId() + ", Name: " + requestPurpose.getName() :
                "NULL"));

        PayrollAccountantTodo payrollAccountantTodo = new PayrollAccountantTodo();
        payrollAccountantTodo.setClientRefundToDo(this);
        Setup.getApplicationContext().getBean(PayrollAccountantTodoController.class)
                .createEntity(payrollAccountantTodo);

        Setup.getApplicationContext()
                .getBean(ClientRefundService.class)
                    .sendNotificationToManagerUponRefundCreated(this, requestPurpose);
    }

    @JsonIgnore
    public User getApprover() {
        if (purpose == null) return null;
        PaymentRequestPurpose requestPurpose = Setup.getRepository(PaymentRequestPurposeRepository.class)
                .findOne(purpose.getId());
        ClientRefundSetup setup = requestPurpose.getUniquePurposeSetup();
        return Setup.getApplicationContext().getBean(ClientRefundService.class).getApproverBySetup(setup, getMethodOfPayment());
    }

    // ACC-3189
    @JsonIgnore
    public String getDisplayId() {
        return "Rx" + getId();
    }

    @Override
    public List<FormField> getForm(String taskName) {
        return null;
    }

    @Override
    public String getFinishedTaskName() {
        return null;
    }

    @Transient
    private CooQuestion.QuestionedPage cooQuestionedPage;

    public CooQuestion.QuestionedPage getCooQuestionedPage() {
        return cooQuestionedPage;
    }

    public void setCooQuestionedPage(CooQuestion.QuestionedPage cooQuestionedPage) {
        this.cooQuestionedPage = cooQuestionedPage;
    }

    @JsonIgnore
    public List<CooQuestion> getCooQuestions() {
        if (this.cooQuestionedPage == null) return new ArrayList();

        return Setup.getRepository(CooQuestionRepository.class).findByRelatedEntityAndQuestionedPage(this, this.cooQuestionedPage);
    }

    @JsonIgnore
    public boolean isAllQuestionsAnswered() {
        if (this.cooQuestionedPage == null) return false;

        List<CooQuestion> cooQuestions = getCooQuestions();

        return !cooQuestions.isEmpty() && !cooQuestions.stream().anyMatch(q -> !q.isAnswered());
    }

    @JsonIgnore
    public boolean isOneQuestionAnswered() {
        if (this.cooQuestionedPage == null) return false;

        return getCooQuestions().stream().filter(q -> q.isAnswered()).count() >= 1l;
    }

    @JsonIgnore
    public boolean isNoneQuestionAnswered() {
        if (this.cooQuestionedPage == null) return false;

        return getCooQuestions().stream().filter(q -> q.isAnswered()).count() == 0l;
    }

    public PicklistItem getHowMuchWeRefundOption() {
        return howMuchWeRefundOption;
    }

    public void setHowMuchWeRefundOption(PicklistItem howMuchWeRefundOption) {
        this.howMuchWeRefundOption = howMuchWeRefundOption;
    }

    public Date getPendingSalaryRecord() {
        return pendingSalaryRecord;
    }

    public void setPendingSalaryRecord(Date pendingSalaryRecord) {
        this.pendingSalaryRecord = pendingSalaryRecord;
    }

    public PicklistItem getRefundRejectReason() {
        return refundRejectReason;
    }

    public void setRefundRejectReason(PicklistItem refundRejectReason) {
        this.refundRejectReason = refundRejectReason;
    }

    public boolean isAutomaticRefund() {
        return automaticRefund;
    }

    public void setAutomaticRefund(boolean automaticRefund) {
        this.automaticRefund = automaticRefund;
    }

    public String getFlowTriggered() { return flowTriggered; }

    public void setFlowTriggered(String flowTriggered) { this.flowTriggered = flowTriggered; }

    public int getUsedWeeks() { return usedWeeks; }

    public void setUsedWeeks(int usedWeeks) { this.usedWeeks = usedWeeks; }

    public int getFullUsedWeeks() { return fullUsedWeeks; }

    public void setFullUsedWeeks(int fullUsedWeeks) { this.fullUsedWeeks = fullUsedWeeks; }

    public boolean isForceCreateBankTransfer() {
        return forceCreateBankTransfer;
    }

    public void setForceCreateBankTransfer(boolean forceCreateBankTransfer) {
        this.forceCreateBankTransfer = forceCreateBankTransfer;
    }

    public boolean isLenient() { return lenient; }

    public void setLenient(boolean lenient) { this.lenient = lenient; }

    public PicklistItem getLeniencyType() { return leniencyType; }

    public void setLeniencyType(PicklistItem leniencyType) { this.leniencyType = leniencyType; }

    public String getTransferReference() { return transferReference; }

    public void setTransferReference(String transferReference) { this.transferReference = transferReference; }

    public java.util.Date getToCreationDate(){ return toCreationDate; }

    public ContractPaymentConfirmationToDo getContractPaymentConfirmationToDo() { return contractPaymentConfirmationToDo; }

    public void setContractPaymentConfirmationToDo(ContractPaymentConfirmationToDo contractPaymentConfirmationToDo) {
        this.contractPaymentConfirmationToDo = contractPaymentConfirmationToDo;
    }

    public boolean isForceUpdateTransferRefOfPayment() { return forceUpdateTransferRefOfPayment; }

    public void setForceUpdateTransferRefOfPayment(boolean forceUpdateTransferRefOfPayment) { this.forceUpdateTransferRefOfPayment = forceUpdateTransferRefOfPayment; }

    public String getBankName() { return bankName; }

    public void setBankName(String bankName) { this.bankName = bankName; }
}