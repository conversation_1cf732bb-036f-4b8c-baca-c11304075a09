package com.magnamedia.service;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Notification;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.mail.*;
import com.magnamedia.core.notification.NotificationService;
import com.magnamedia.core.type.EmailReceiverType;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.Supplier;
import com.magnamedia.entity.workflow.*;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ExpensePaymentMethod;
import com.magnamedia.module.type.ExpensePaymentStatus;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.module.type.SalaryCurrency;
import com.magnamedia.repository.*;
import com.magnamedia.workflow.type.*;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ketrawi
 *         Acc-2826
 */

@Service
public class AccountantTodoService {

    protected static final Logger logger = Logger.getLogger(AccountantTodoService.class.getName());

    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private PayrollAccountantTodoRepository payrollAccountantTodoRepository;
    @Autowired
    private ClientRefundTodoRepository clientRefundTodoRepository;
    @Autowired
    private ExpensePaymentRepository expensePaymentRepository;

    @Autowired
    private MailService mailService;

    public void sendMoneyTransferEmail(String beneficiary, String mobile, Double amount) {
        Map<String, Object> params = new HashMap<>();
        params.put("title", "Bank Transfer to Money Transfer Agency");
        params.put("beneficiary", beneficiary);
        params.put("mobile", mobile);
        params.put("city", "Dubai");
        params.put("amount", "AED " + String.format("%,.2f", amount));

        List<EmailRecipient> recipients = EmailHelper.getMailRecipients(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_CLIENT_REFUND_AGENCY_EMAIL));

        if (recipients.size() > 0) {
            mailService.sendEmail(recipients,
                    new TemplateEmail("Bank Transfer to Money Transfer Agency",
                            "MoneyTransferAgencyEmail", params),
                    null);
        }
    }
    
    //ACC-5358
    public void updateBankTransferInfo(PayrollAccountantTodo todo) {
        if (todo.getIban() != null && todo.getAccountName() != null && todo.getAccountNumber() != null) return;

        if (todo.getClientRefundToDo() == null && todo.getExpensePayment() == null) return;

        if (todo.getClientRefundToDo() != null) {
            if (!todo.getClientRefundToDo().getMethodOfPayment()
                    .equals(ClientRefundPaymentMethod.BANK_TRANSFER)) return;
            if (todo.getAccountName() == null) {
                todo.setAccountName(todo.getClientRefundToDo().getClient().getAccountName());
            }
            if (todo.getIban() == null) {
                todo.setIban(todo.getClientRefundToDo().getClient().getClientIBAN());
            }
        } else if (todo.getExpensePayment() != null) {
            if (!todo.getExpensePayment().getMethod().equals(ExpensePaymentMethod.BANK_TRANSFER)
                    || todo.getExpensePayment().getBeneficiaryType() == null) return;

            switch (todo.getExpensePayment().getBeneficiaryType()) {
                case SUPPLIER:
                    Supplier supplier = Setup.getRepository(SupplierRepository.class)
                            .findOne(todo.getExpensePayment().getBeneficiaryId());
                    if (supplier == null) return;

                    if (todo.getIban() == null) {
                        todo.setIban(supplier.getIban());
                    }
                    if (todo.getAccountName() == null) {
                        todo.setAccountName(supplier.getAccountName());
                    }
                    if (todo.getAccountNumber() == null) {
                        todo.setAccountNumber(supplier.getAccountNumber());
                    }
                    if (todo.getSwift() == null) {
                        todo.setSwift(supplier.getSwift());
                    }
                    if (todo.getAddress() == null) {
                        todo.setAddress(supplier.getAddress());
                    }
                    break;
                case OFFICE_STAFF:
                    OfficeStaff staff = Setup.getRepository(OfficeStaffRepository.class)
                            .findOne(todo.getExpensePayment().getBeneficiaryId());
                    if (staff == null) return;

                    if (todo.getIban() == null) {
                        todo.setIban(staff.getIbanDestination());
                    }
                    if (todo.getSwift() == null) {
                        todo.setSwift(staff.getSwiftDestination());
                    }
                    if (todo.getAccountName() == null) {
                        todo.setAccountName(staff.getAccountNameDestination());
                    }
                    if (todo.getAccountNumber() == null) {
                        todo.setAccountNumber(staff.getAccountNumberDestination());
                    }
                    if (todo.getAddress() == null) {
                        todo.setAddress(staff.getFullAddressDestination());
                    }
                    break;
                default:
                    return;
            }
        }

        payrollAccountantTodoRepository.silentSave(todo);
    }
}