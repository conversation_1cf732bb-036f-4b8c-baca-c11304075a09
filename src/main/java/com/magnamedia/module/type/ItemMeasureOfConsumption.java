package com.magnamedia.module.type;

/**
 * <PERSON> (Jan 31, 2021)
 */
public enum ItemMeasureOfConsumption {
    NONE("NONE"),
    PER_NUMBER_OF_MAIDS_IN_ACCOMMODATION("Per number of maids in accommodation"),
    PER_NUMBER_OF_ASSIGNED_ROOMS("Per number of assigned rooms"),
    PER_NUMBER_OF_NEWLY_JOINED_MAIDS("Per number of newly joined maids"),
    PER_NUMBER_OF_SICK_MAIDS("Per number of sick maids"),
    PER_NUMBER_OF_MAIDS_WHO_WENT_WITH_CLIENT_FROM_ACCOMMODATION("Per number of maids who went with client from accommodation"),
    PER_NUMBER_OF_MAIDS_WHO_RETURNED_FROM_CLIENT_TO_ACCOMMODATION("Per number of maids who returned from clients to accommodation");

    ItemMeasureOfConsumption(String label) {
        this.label = label;
    }

    private String label;

    public String getLabel() {
        return label;
    }

    @Override
    public String toString() {
        return label;
    }
}
