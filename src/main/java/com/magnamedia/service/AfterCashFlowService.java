package com.magnamedia.service;

import com.magnamedia.controller.DirectDebitCancelationToDoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PushNotification;
import com.magnamedia.core.entity.Tag;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.notification.AppAction;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.extra.CcNotificationTemplateCode;
import com.magnamedia.extra.MvNotificationTemplateCode;
import com.magnamedia.extra.Utils;
import com.magnamedia.helper.PushNotificationHelper;
import com.magnamedia.module.type.DDMessagingSubType;
import com.magnamedia.module.type.DDMessagingType;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.*;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Service
public class AfterCashFlowService {
    private static final Logger logger = Logger.getLogger(AfterCashFlowService.class.getName());

    @Autowired
    private ContractPaymentConfirmationToDoService contractPaymentConfirmationToDoService;
    @Autowired
    private DirectDebitCancelationToDoController directDebitCancelationToDoController;
    @Autowired
    private MessagingService pushNotificationService;
    @Autowired
    private ContractPaymentRepository contractPaymentRepository;
    @Autowired
    private ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository;
    @Autowired
    private FlowProcessorEntityRepository flowProcessorEntityRepository;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private FlowProcessorMessagingService flowProcessorMessagingService;
    @Autowired
    private InterModuleConnector moduleConnector;
    @Autowired
    private DisablePushNotificationRepository disablePushNotificationRepository;
    @Autowired
    private PushNotificationHelper pushNotificationHelper;
    @Autowired
    private DirectDebitRepository directDebitRepository;
    @Autowired
    private DirectDebitService directDebitService;
    @Autowired
    private ContractService contractService;
    @Autowired
    private ExtensionFlowService extensionFlowService;

    public ContractPaymentConfirmationToDo messagingFilter(
            SelectQuery<DDMessaging> query,
            FlowProcessorEntity entity,
            FlowSubEventConfig subEvent) {

        ContractPaymentConfirmationToDo t = null;

        if (entity.getCurrentFlowRun() < subEvent.getFlowEventConfig().getMaxFlowRuns() &&
                Setup.getRepository(DDMessagingRepository.class)
                        .existsByEventAndSubTypeAndTrialsAndRemindersAndSendPayTabMessageTrueAndDeletedFalse(
                                DDMessagingType.ClientPaidCashAndNoSignatureProvided, DDMessagingSubType.NO_SIGNATURE,
                                String.valueOf(entity.getIncrementedTrials()), String.valueOf(entity.getIncrementedReminders()))) {

            logger.log(Level.SEVERE, "Trials: {0}", entity.getIncrementedTrials());

            boolean sendPayTabMessage = !Setup.getApplicationContext().getBean(FlowProcessorService.class)
                    .nextMonthPaymentReceived(entity.getContract());

            if (sendPayTabMessage) {
                logger.info("sendPayTabMessage true");

                if (getPaymentTodo(entity.getContractPaymentTerm()) == null) {
                    try {
                        t = addConfirmationTodoForPayment(entity.getContract());
                    } catch (Exception ex) {
                        throw new RuntimeException("IPAM error 'messagingFilter' while adding confirmation todo");
                    }
                }
            }

            query.filterBy("sendPayTabMessage", "=", sendPayTabMessage);
        }

        return t;
    }

    @Transactional
    public void resetFlow(Long flowProcessorEntityId) {

        logger.log(Level.INFO, "Reset flow, id : {0}", flowProcessorEntityId);
        FlowProcessorEntity flowProcessorEntity = flowProcessorEntityRepository.findOne(flowProcessorEntityId);

        flowProcessorEntity.setTrials(1);
        flowProcessorEntity.setCurrentFlowRun(flowProcessorEntity.getCurrentFlowRun() + 1);
//        flowProcessorEntity.setLastExecutionDate(new DateTime(flowProcessorEntity.getLastExecutionDate())
//                .plusMonths(1).dayOfMonth().withMinimumValue().withTimeAtStartOfDay().toDate());
        flowProcessorEntity.setReminders(1);
        flowProcessorEntityRepository.save(flowProcessorEntity);
    }

    @Transactional
    public ContractPaymentConfirmationToDo addConfirmationTodoForPayment(Contract c) {
        logger.info("contract id: {0}" + c.getId());

        return contractPaymentConfirmationToDoService
                .createMonthlyCreditCardForNextMonth(c,
                        ContractPaymentConfirmationToDo.Source.AFTER_CASH_FLOW);
    }

    public void createPaymentAfterPaidSuccess(ContractPaymentConfirmationToDo toDo) throws Exception {

        boolean changeRecurringPaymentToReceived = Setup.getApplicationContext()
                .getBean(PaymentService.class)
                .changeRecurringPaymentToReceived(toDo.getContractPaymentList().get(0));

        if (!changeRecurringPaymentToReceived) {
            logger.log(Level.INFO, "After cash flow create new payment, todo id: {0}", toDo.getId());
            contractPaymentConfirmationToDoService.createPayment(
                    toDo.getContractPaymentList().get(0),
                    toDo.getAttachments(), PaymentStatus.RECEIVED, true);
        }

        disableOldNotification(toDo.getContractPaymentTerm().getContract());

        sendThankYouMessage(toDo);
    }

    public void sendThankYouMessage(ContractPaymentConfirmationToDo toDo) {
        logger.log(Level.INFO, "todo id: {0}", toDo.getId());

        Contract contract = toDo.getContractPaymentTerm().getContract();

        Map<String, AppAction> cta = new HashMap<>();
        cta.put("sign_now", flowProcessorMessagingService.getSignNowButton("Sign Now", contract));

        Map<String, String> parameters = new HashMap<>();
        Map<String, Object> signDDMap = new HashMap<>();
        signDDMap.put("cpt", toDo.getContractPaymentTerm());
        signDDMap.put("additionalInfo", new HashMap<String, Object>() {{
            put("sentFrom", "IPAM_sendThankYouMessage");
            put("contractPaymentConfirmationToDoId", toDo.getId());
        }});
        parameters.put("link_send_dd_details", Setup.getApplicationContext().getBean(Utils.class).getSingDDLink(signDDMap));

        String notificationTemplate = contract.isMaidCc() ?
                CcNotificationTemplateCode.CC_PAYTAB_THANKS_MESSAGE_NOTIFICATION.toString() :
                MvNotificationTemplateCode.MV_PAYTABS_THANKS_MESSAGE_NOTIFICATION.toString();
        logger.log(Level.SEVERE, "sendThankYouMessage CC -> sending Notification");

        pushNotificationService.sendMessageToClient(contract,
                parameters,
                cta,
                contract.getId(),
                "Contract",
                TemplateUtil.getTemplate(notificationTemplate));
    }

    public void disableThankYouMessage(Contract c) {
        List<PushNotification> notifications = Setup.getRepository(DisablePushNotificationRepository.class)
                .findActiveNotifications(c.getClient().getId().toString(),
                        c.getId(),
                        Arrays.asList("PayTab_Thanks_Message_Notification_CC",
                                "PayTab_Thanks_Message_Notification_MV",
                                CcNotificationTemplateCode.CC_PAYTAB_THANKS_MESSAGE_NOTIFICATION.toString(),
                                MvNotificationTemplateCode.MV_PAYTABS_THANKS_MESSAGE_NOTIFICATION.toString()));

        if (!notifications.isEmpty()) {
            Setup.getApplicationContext().getBean(MessagingService.class)
                    .createDisableNotificationBGT(notifications.stream().map(BaseEntity::getId).collect(Collectors.toList())
                        , "Client submits the documents or next flow message is sent");
        }
    }

    /*public void createAfterCashFlowExpertTodo(Contract contract, DDMessaging ddMessaging) {
        logger.info("contract ID: " + (contract == null ? "" : contract.getId()));

        if (contract == null || contract.getId() == null || ddMessaging == null){
            logger.info("exiting createExpertTodo");
            return;
        }

        Map requestBody = new HashMap();
        requestBody.put("reasonToCall", "Last reminder to sign DD");
        requestBody.put("initialNote", "");
        requestBody.put("type", VoiceResolverToDoReason.INCOMPLETE_DD.toString());

        moduleConnector.postJson(
                "/clientmgmt/voiceResolverToDo/createexperttodo/" + contract.getId(),
                requestBody, Map.class);
    }*/
    
    public void disableOldNotification(Contract c) {
        List<PushNotification> clientNotifications = disablePushNotificationRepository
                .findActiveNotificationsByDDMessagingType(c.getClient().getId().toString(),
                        DDMessagingType.ClientPaidCashAndNoSignatureProvided, c.getId());
        pushNotificationHelper.stopDisplaying(clientNotifications);
    }

    public boolean validateFlow(ContractPaymentTerm cpt) {
        logger.log(Level.INFO, "IPAM is running for ContractPaymentTerm: {0}; contract status: {1}, " +
                "contract prospect: {2}", new Object [] {cpt.getId(),
                cpt.getContract().getStatus(), cpt.getContractProspectType().getCode()});

        // ACC-8662
        if (contractService.hasPreventCreateOtherDds(cpt.getContract())) {
            logger.info("contract has Prevent Create Other Dds -> exiting");
            return false;
        }

        if (directDebitService.isRequiredBankInfoExist(cpt)) {

            logger.warning("Exiting IPAM -> client provided bank info");
            return false;
        }

        // ACC-6951
        if (directDebitService.contractHasOpenMainDdcToDo(cpt.getContract().getId())) {

            logger.warning("Exiting IPAM -> client has initial DDC todo open");
            return false;
        }

        return true;
    }

    // ACC-5156
    public void reactivateFlow(ContractPaymentTerm cpt) {
        logger.log(Level.INFO, "cpt id : {0}", cpt.getId());

        FlowProcessorEntity f = flowProcessorEntityRepository
                .findFirstByFlowEventConfig_NameAndContractPaymentTerm_ContractOrderByCreationDateDesc(
                        FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED, cpt.getContract());

        reactivateFlow(f);
    }


    public void reactivateFlow(FlowProcessorEntity f) {
        if(f == null) return;
        logger.info("flow id : " + f.getId());

        if(f.isCompleted()) return;
        if(!f.isStopped()) return;

        f.setStopped(false);
        flowProcessorEntityRepository.save(f);

        resetFlow(f.getId());
    }

    boolean existsRunningFlow(ContractPaymentTerm cpt) {
        return Setup.getApplicationContext().getBean(FlowProcessorService.class)
            .existsRunningFlow(FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED,
                Arrays.asList(
                    FlowSubEventConfig.FlowSubEventName.NO_SIGNATURE,
                    FlowSubEventConfig.FlowSubEventName.NO_SIGNATURE_WITH_ONE_PAYMENT_ONLY), cpt);
    }

    public ContractPaymentConfirmationToDo getPaymentTodo(ContractPaymentTerm cpt) {
        return contractPaymentConfirmationToDoRepository
                .findFirstByContractPaymentTerm_ContractAndSourceAndShowOnERPFalseAndDisabledFalseAndCreationDateGreaterThan(
                        cpt.getContract(),
                        ContractPaymentConfirmationToDo.Source.AFTER_CASH_FLOW,
                        new LocalDate().withDayOfMonth(1).toDate());
    }

    public void applyAfterProcessFlowSubEventConfig(FlowProcessorEntity entity, Map<String, Object> m) {
        // Start Extension Flow if got termination message and ExtensionFlow is active for this contract
        if (m.containsKey("startExtensionFlow")) {
            extensionFlowService.startExtensionFlow(entity.getContractPaymentTerm(), entity);
        }
    }

    public boolean checkAndSwitchToPayingViaCC(FlowProcessorEntity flowProcessorEntity) {

        int xPaidPayments;
        if (flowProcessorEntity.getContract().isMaidCc()) {
            Tag xPaidPaymentsTagCC = flowProcessorEntity.getFlowEventConfig().getTagValue("cc_ipam_x_paid_payments_to_convert_paying_cc");
            xPaidPayments = xPaidPaymentsTagCC != null ? Integer.parseInt(xPaidPaymentsTagCC.getValue()) : 2;
        } else {
            Tag xPaidPaymentsTagMV = flowProcessorEntity.getFlowEventConfig().getTagValue("mv_ipam_x_paid_payments_to_convert_paying_cc");
            xPaidPayments = xPaidPaymentsTagMV != null ? Integer.parseInt(xPaidPaymentsTagMV.getValue()) : 5;
        }

        return checkAndSwitchToPayingViaCC(flowProcessorEntity, xPaidPayments);
    }

    public boolean checkAndSwitchToPayingViaCC(FlowProcessorEntity flowProcessorEntity, int xPaidPayments) {
        if (contractPaymentConfirmationToDoRepository.countByContractPaymentTerm_ContractAndSourceAndShowOnERPTrue(
                flowProcessorEntity.getContract(),
                ContractPaymentConfirmationToDo.Source.AFTER_CASH_FLOW) < xPaidPayments) return false;

        flowProcessorEntity.setCompleted(true);
        flowProcessorEntityRepository.save(flowProcessorEntity);
        contractService.updatePayingViaCreditCardFlag(flowProcessorEntity.getContract(), true);
        return true;
    }

    public boolean handleScheduledContractForTermination(FlowProcessorEntity entity, Map<String, Object> m) {
        if (extensionFlowService.isEligibleForExtensionFlow(entity)) {
            m.put("startExtensionFlow", true);
            m.put("scheduledDateOfTermination", new LocalDate(entity.getContract().getPaidEndDate()).isAfter(new LocalDate()) ?
                    entity.getContract().getPaidEndDate() :
                    new Date());
            return false;
        }

        return true;
    }
}