package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.controller.DDMessagingToDoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.AfterInsert;
import com.magnamedia.core.annotation.BeforeDelete;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.extra.PaymentHelper;
import com.magnamedia.entity.workflow.VoiceResolverToDo;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.*;
import org.hibernate.annotations.Where;
import org.hibernate.envers.NotAudited;
import org.joda.time.LocalDate;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.*;
import javax.validation.constraints.Min;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Dec 9, 2018
 *  ACC-329 ACC-984
 * 
 */
@Entity
@Where(clause = "IS_DELETED = false")
public class DirectDebit extends BaseEntity implements Cloneable{
    private static final Logger logger = Logger.getLogger(DirectDebit.class.getName());

    @Column(nullable = false)
    @Temporal(TemporalType.DATE)
    private Date startDate;

    @Column(nullable = false)
    @Temporal(TemporalType.DATE)
    private Date expiryDate;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private DirectDebitStatus status = DirectDebitStatus.IN_COMPLETE;

    @Column
    private String rejectionReason;

    @Column(nullable = false)
    @Min(0)
    private double amount;

    // ACC-1435
    @Column
    private Double suggestedAmount;

//    @JsonIgnore
//    @NotAudited
//    @OneToMany(mappedBy = "directDebit",
//            fetch = FetchType.LAZY,
//            cascade = CascadeType.ALL)
//    private List<BankDirectDebitActivationRecord> records;

    @JsonIgnore
    @NotAudited
    @OneToMany(mappedBy = "directDebit",
            fetch = FetchType.LAZY)
    private List<DirectDebitFile> directDebitFiles;

    //ACC-825
    @JsonIgnore
    @NotAudited
    @OneToMany(mappedBy = "directDebit",
            fetch = FetchType.LAZY,
            cascade = CascadeType.ALL)
    private List<ContractPayment> contractPayments;

    @ManyToOne
//    @JsonSerialize(using = IdLabelSerializer.class)
    private ContractPaymentTerm contractPaymentTerm;

    // ACC-573
    @Column
    @Lob
    private String notes;

    // ACC-429
    @Transient
    private Boolean uploaded = false;

    // ACC-1340
    @Column
    private Boolean nonCompletedInfo = false;

    @Transient
    private Boolean nonFileCompleted = false;

    @Transient
    private Date resultDate;

    // ACC-606
    @Column
    private Double additionalDiscount;

    @Column
    @Lob
    private String additionalDiscountNotes;

    // ACC-984
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    @Column(nullable = false)
    private Boolean isDeleted = false;

    // ACC-1131
    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private DirectDebitType type = DirectDebitType.MONTHLY;

    @Transient
    private Boolean isSigned;

    // ACC-1435
    @Column
    private Long ddBankInfoGroup;

    // ACC-3136
    @Lob
    private String dataEntryNotes;

    @Column
    @Enumerated(EnumType.STRING)
    private DirectDebitSource source;

    @JsonIgnore
    @OneToMany(mappedBy = "directDebit", fetch = FetchType.LAZY)
    private List<ContractPayment> payments;

    @JsonIgnore
    @OneToOne(fetch = FetchType.LAZY)
    private DirectDebitFile manualDdfFile;

    @JsonIgnore
    @OneToOne(fetch = FetchType.LAZY)
    private DirectDebitFile autoDdfFile;

    // ACC-1435
    @Column
    private String eid;

    @Column
    private String ibanNumber;

    @Column
    private String bankName;

    // ACC-1377
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    private PicklistItem bank;

    // ACC-1435
    @Column
    private String accountName;

    // ACC-1340
    @Column
    private Boolean confirmedBankInfo = Boolean.FALSE;

    // ACC-1587 from here
//    @Column(nullable = true)
//    @Temporal(TemporalType.TIMESTAMP)
//    private Date presentmentDate;

    @Column(name = "M_STATUS", nullable = false)
    @Enumerated(EnumType.STRING)
    private DirectDebitStatus MStatus = DirectDebitStatus.IN_COMPLETE;

    @Column
    private Boolean prorated;

    @Column
    private Date proratedFrom;

    @Column
    private Date proratedTo;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private DirectDebitCategory category;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private DirectDebitRejectionToDo directDebitRejectionToDo;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private DirectDebitRejectionToDo directDebitBouncingRejectionToDo;

    //for debugging issue
    @Column(columnDefinition = "boolean default false")
    private boolean bouncingRejectionTodoAdded = false;
    
    //ACC-3597
    @Column(columnDefinition = "boolean default false")
    private boolean addedByOecFlow = false;

    @Column
    @Enumerated(EnumType.STRING)
    private DirectDebitRejectCategory rejectCategory;

    @Column
    @Enumerated(EnumType.STRING)
    private DirectDebitRejectCategory bouncingRejectCategory;

    // ACC-1587 to here
//
//    public Date getPresentmentDate() {
//        return presentmentDate;
//    }
//
//    public void setPresentmentDate(Date presentmentDate) {
//        this.presentmentDate = presentmentDate;
//    }

    // ACC-2107
    @Column(columnDefinition = "boolean default true")
    private boolean generateRelatedPayments = true;

    // ACC-2024
    @Column(columnDefinition = "int default 0")
    private int inCompleteDDReminder = 0; // we will use this flag for IN_COMPLETE_DD_FLOW

    @Column(columnDefinition = "int default 0")
    private int inCompleteDDTrials = 0; // we will use this flag for IN_COMPLETE_DD_FLOW

    @Column(columnDefinition = "boolean default false")
    private boolean sendIncompleteDDInfoMessages = false; // we will use this flag for IN_COMPLETE_DD_FLOW

    @Transient
    private Date contractScheduleDateOfTermination; // we will use this flag for IN_COMPLETE_DD_FLOW

    // ACC-1778
    @Column
    private String description;

    // ACC-1778
    @Column(columnDefinition = "boolean default false")
    private boolean addedManuallyFromClientProfile = false;

    // acc-2476
    @Column(columnDefinition = "boolean default false")
    private boolean rejectedBySalesScreen = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private DirectDebit imageForDD;

    // ACC-2933
    @Column(columnDefinition = "boolean default false")
    private boolean requiredAfterSwitching = false;

    // ACC-3296
    @Column(columnDefinition = "boolean default true")
    private boolean generateManualDDFs = true;

    @Transient
    private DirectDebitConfiguration dDConfiguration;
    
    
    // ACC-3974
    @Column(columnDefinition = "double default 0")
    private double oecSalary;

    @Column
    private Date confirmBankInfoDate;

    @Column(columnDefinition = "boolean default false")
    private boolean hidden = false;

    @Column(nullable = false)
    private String applicationId;

    public String getApplicationId() {return applicationId;}

    public void setApplicationId(String applicationId) {this.applicationId = applicationId;}

    @Column
    private Long relatedEntityId;

    @Column
    private String relatedEntityType;

    @Column
    private Long ddcId;

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    public DirectDebitStatus getStatus() {
        return status;
    }

    public void setStatus(DirectDebitStatus status) {
        this.status = status;
    }

    public String getRejectionReason() {
        return rejectionReason;
    }

    public void setRejectionReason(String rejectionReason) {
        this.rejectionReason = rejectionReason;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

//    public List<BankDirectDebitActivationRecord> getRecords() {
//        return records;
//    }
//
//     ACC-1435
//    public List<BankDirectDebitActivationRecord> getBankDirectDebitActivationRecords() {
//        return records;
//    }
//
//    public void setRecords(List<BankDirectDebitActivationRecord> records) {
//        this.records = records;
//    }

    public List<DirectDebitFile> getDirectDebitFiles() {
        return directDebitFiles;
    }

    public void setDirectDebitFiles(List<DirectDebitFile> directDebitFiles) {
        this.directDebitFiles = directDebitFiles;
    }

    public List<ContractPayment> getContractPayments() {
        return contractPayments;
    }

    public void setContractPayments(List<ContractPayment> contractPayments) {
        this.contractPayments = contractPayments;
    }

    public ContractPaymentTerm getContractPaymentTerm() {
        return contractPaymentTerm;
    }

    public void setContractPaymentTerm(ContractPaymentTerm contractPaymentTerm) {
        this.contractPaymentTerm = contractPaymentTerm;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public Double getAdditionalDiscount() {
        return additionalDiscount;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public void setAdditionalDiscount(Double additionalDiscount) {
        this.additionalDiscount = additionalDiscount;
    }

    public String getAdditionalDiscountNotes() {
        return additionalDiscountNotes;
    }

    public void setAdditionalDiscountNotes(String additionalDiscountNotes) {
        this.additionalDiscountNotes = additionalDiscountNotes;
    }

    //ask osamah
//    public boolean getUploaded() {
//        try {
//            return (this.directDebitFiles != null &&
//                            !this.directDebitFiles.isEmpty() &&
//                            (this.directDebitFiles.stream()
//                                    .anyMatch(x -> !x.getStatus().equals(DirectDebitFileStatus.NOT_SENT)
//                                            || this.directDebitFiles.stream()
//                                            .anyMatch(y -> y.getRecords() != null && !y.getRecords().isEmpty()))));
//        } catch (Exception ex) {
//            logger.log(Level.SEVERE, ex.getMessage());
//            return false;
//        }
//    }

    public Boolean getNonCompletedInfo() {
        return nonCompletedInfo;
    }

    public Boolean getNonFileCompleted() {
        return this.directDebitFiles != null &&
                !this.directDebitFiles.isEmpty() &&
                this.directDebitFiles.stream()
                        .anyMatch(x -> x.getStatus().equals(DirectDebitFileStatus.NOT_COMPLETED));
    }

//    public Date getResultDate() {
//        if (this.records != null && !this.records.isEmpty()) {
//            Date a = this.records.stream().map(x -> x.getCreationDate()).max(Date::compareTo).get();
//            return a;
//        } else
//            return this.resultDate;
//    }

    public DirectDebitType getType() {
        return type;
    }

    public void setType(DirectDebitType type) {
        this.type = type;
    }

    public Boolean getIsSigned() {
        return isSigned;
    }

    public void setIsSigned(Boolean isSigned) {
        this.isSigned = isSigned;
    }

    public DirectDebitSource getSource() {
        return source;
    }

    public void setSource(DirectDebitSource source) {
        this.source = source;
    }

    public List<ContractPayment> getPayments() {
        return payments;
    }

    public void setPayments(List<ContractPayment> payments) {
        this.payments = payments;
    }

    public PicklistItem getBank() {
        return bank;
    }

    public void setBank(PicklistItem bank) {
        if(bank != null && (this.bank == null || !bank.equals(this.bank)) && getMStatus() != null) {
            setMStatus(getMStatus());
        }
        this.bank = bank;
    }

    public Boolean getConfirmedBankInfo() {
        return confirmedBankInfo == null ? false : confirmedBankInfo;
    }

    public void setConfirmedBankInfo(Boolean confirmedBankInfo) {
        this.confirmedBankInfo = confirmedBankInfo;
        // ACC-5055
        if (confirmedBankInfo && getConfirmBankInfoDate() == null)
            setConfirmBankInfoDate(new Date());
    }

    public Double getSuggestedAmount() {
        return suggestedAmount;
    }

    public void setSuggestedAmount(Double suggestedAmount) {
        this.suggestedAmount = suggestedAmount;
    }

    public void setUploaded(Boolean uploaded) {
        this.uploaded = uploaded;
    }

    public void setNonCompletedInfo(Boolean nonCompletedInfo) {
        this.nonCompletedInfo = nonCompletedInfo;
    }

    public void setNonFileCompleted(Boolean nonFileCompleted) {
        this.nonFileCompleted = nonFileCompleted;
    }

    public void setResultDate(Date resultDate) {
        this.resultDate = resultDate;
    }

    public Boolean getDeleted() {
        return isDeleted;
    }

    public void setDeleted(Boolean deleted) {
        isDeleted = deleted;
    }

    public Boolean getSigned() {
        return isSigned;
    }

    public void setSigned(Boolean signed) {
        isSigned = signed;
    }

    public Long getDdBankInfoGroup() {
        return ddBankInfoGroup;
    }

    public void setDdBankInfoGroup(Long ddBankInfoGroup) {
        this.ddBankInfoGroup = ddBankInfoGroup;
    }

    @JsonIgnore
    public String getDataEntryNotes() {
        return dataEntryNotes;
    }

    public void setDataEntryNotes(String dataEntryNotes) {
        this.dataEntryNotes = dataEntryNotes;
    }

    public String getEid() {
        return eid;
    }

    public void setEid(String eid) {
        this.eid = eid;
    }

    public String getIbanNumber() {
        return ibanNumber;
    }

    public void setIbanNumber(String ibanNumber) {
        this.ibanNumber = ibanNumber;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public DirectDebitStatus getMStatus() {
        return MStatus;
    }
    public void setMStatus(DirectDebitStatus MStatus) {
        // ACC-5457
        if (getCategory() != null &&
                getCategory().equals(DirectDebitCategory.B) &&
                !isGenerateManualDDFsFromConfig() &&
                (getDirectDebitFiles() == null ||
                        getDirectDebitFiles().isEmpty() ||
                        getDirectDebitFiles().stream().noneMatch(ddf -> ddf.getDdMethod().equals(DirectDebitMethod.MANUAL)))) {

            this.MStatus = DirectDebitStatus.NOT_APPLICABLE;
            return;
        }

        this.MStatus = MStatus;
        if(getCategory() != null && getCategory().equals(DirectDebitCategory.A)) setStatus(MStatus);
    }

    public DirectDebitStatus getmStatus() {
        return getMStatus();
    }

    public void setmStatus(DirectDebitStatus MStatus) {
        setMStatus(MStatus);
    }

    public Boolean getProrated() {
        return prorated != null && prorated;
    }

    public void setProrated(Boolean prorated) {
        this.prorated = prorated;
    }

    public Date getProratedFrom() {
        return proratedFrom;
    }

    public void setProratedFrom(Date proratedFrom) {
        this.proratedFrom = proratedFrom;
    }

    public Date getProratedTo() {
        return proratedTo;
    }

    public void setProratedTo(Date proratedTo) {
        this.proratedTo = proratedTo;
    }

    public DirectDebitCategory getCategory() {
        return category;
    }

    public void setCategory(DirectDebitCategory category) {
        this.category = category;
    }

    // ACC-786
    @JsonIgnore
    public PaymentMethod getPaymentMethod() {
        return PaymentMethod.DIRECT_DEBIT;
    }

    @JsonIgnore
    public PicklistItem getPaymentType() {
        if (this.contractPayments != null
                && !this.contractPayments.isEmpty()) {

            logger.info("CP: " + this.contractPayments.get(0).getId());
            return this.contractPayments.get(0).getPaymentType();
        }

        return null;
    }

    @JsonIgnore
    public PicklistItem getPaymentSubType() {
        if (this.contractPayments != null
                && !this.contractPayments.isEmpty()) {

            logger.info("CP: " + this.contractPayments.get(0).getId());
            return this.contractPayments.get(0).getSubType();
        }

        return null;
    }

    @JsonIgnore
    public int getPaymentsCount() {
        if (this.contractPayments != null
                && !this.contractPayments.isEmpty()) {
            return this.contractPayments.size();
        }
        return 0;
    }

    public DirectDebitRejectionToDo getDirectDebitRejectionToDo() {
        return directDebitRejectionToDo;
    }

    public void setDirectDebitRejectionToDo(DirectDebitRejectionToDo directDebitRejectionToDo) {
        this.directDebitRejectionToDo = directDebitRejectionToDo;
    }

    public DirectDebitRejectionToDo getDirectDebitBouncingRejectionToDo() {
        return directDebitBouncingRejectionToDo;
    }

    public void setDirectDebitBouncingRejectionToDo(DirectDebitRejectionToDo directDebitBouncingRejectionToDo) {
        logger.log(Level.SEVERE, "REJECTION TODO ABD DEBUG, directDebitBouncingRejectionToDo: " + (
                directDebitBouncingRejectionToDo != null ? directDebitBouncingRejectionToDo.getId() : null));

        this.directDebitBouncingRejectionToDo = directDebitBouncingRejectionToDo;
        setBouncingRejectionTodoAdded(directDebitBouncingRejectionToDo != null);
    }

    public DirectDebitRejectCategory getRejectCategory() {
        return rejectCategory;
    }

    public void setRejectCategory(DirectDebitRejectCategory rejectCategory) {
        this.rejectCategory = rejectCategory;
    }

    public DirectDebitRejectCategory getBouncingRejectCategory() {
        return bouncingRejectCategory;
    }

    public void setBouncingRejectCategory(DirectDebitRejectCategory bouncingRejectCategory) {
        this.bouncingRejectCategory = bouncingRejectCategory;
    }

    public boolean isGenerateRelatedPayments() {
        return generateRelatedPayments;
    }

    public void setGenerateRelatedPayments(boolean generateRelatedPayments) {
        this.generateRelatedPayments = generateRelatedPayments;
    }

    public int getInCompleteDDReminder() {
        return inCompleteDDReminder;
    }

    public void setInCompleteDDReminder(int inCompleteDDReminder) {
        this.inCompleteDDReminder = inCompleteDDReminder;
    }

    public int getInCompleteDDTrials() {
        return inCompleteDDTrials;
    }

    public void setInCompleteDDTrials(int inCompleteDDTrials) {
        this.inCompleteDDTrials = inCompleteDDTrials;
    }

    public boolean isSendIncompleteDDInfoMessages() {
        return sendIncompleteDDInfoMessages;
    }

    public void setSendIncompleteDDInfoMessages(boolean sendIncompleteDDInfoMessages) {
        this.sendIncompleteDDInfoMessages = sendIncompleteDDInfoMessages;
    }

    public Date getContractScheduleDateOfTermination() {
        if (this.getContractPaymentTerm() != null && this.getContractPaymentTerm().getContract() != null && this.getContractPaymentTerm().getContract().getScheduledDateOfTermination() != null) {
            return this.getContractPaymentTerm().getContract().getScheduledDateOfTermination();
        }

        return contractScheduleDateOfTermination;
    }

    public void setContractScheduleDateOfTermination(Date contractScheduleDateOfTermination) {
        this.contractScheduleDateOfTermination = contractScheduleDateOfTermination;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public boolean isAddedManuallyFromClientProfile() {
        return addedManuallyFromClientProfile;
    }

    public void setAddedManuallyFromClientProfile(boolean addedManuallyFromClientProfile) {
        this.addedManuallyFromClientProfile = addedManuallyFromClientProfile;
    }

    public boolean isBouncingRejectionTodoAdded() {
        return bouncingRejectionTodoAdded;
    }

    public void setBouncingRejectionTodoAdded(boolean bouncingRejectionTodoAdded) {
        this.bouncingRejectionTodoAdded = bouncingRejectionTodoAdded;
    }
    
    public boolean isAddedByOecFlow() {
        return addedByOecFlow;
    }

    public void setAddedByOecFlow(boolean addedByOecFlow) {
        this.addedByOecFlow = addedByOecFlow;
    }
    
    @JsonIgnore
    public String getStatusLabel() {
        if (this.getCategory().equals(DirectDebitCategory.A)) {
            return "N/A";
        }
        return this.getStatus().getLabel();
    }

    @JsonIgnore
    public String getStatusValue() {
        if (this.getCategory().equals(DirectDebitCategory.A)) {
            return "N/A";
        }
        return this.getStatus().getValue();
    }

    public DirectDebit getImageForDD() {
        return imageForDD;
    }

    public void setImageForDD(DirectDebit imageForDD) {
        this.imageForDD = imageForDD;
    }

    public void validate() {
        if (getStartDate() == null)
            throw new RuntimeException("Start Date is mandatory.");
        if (getExpiryDate() == null)
            throw new RuntimeException("Expiry Date is mandatory.");
        if (getStatus() == null)
            throw new RuntimeException("Status is mandatory.");
        if (getMStatus() == null)
            throw new RuntimeException("M-Status is mandatory.");
    }

    @AfterInsert
    private void afterInsert() {

        if (Setup.getApplicationContext()
                .getBean(DirectDebitService.class)
                .handleCreateNonMonthlyDdToBeCollectedByCreditCard(this)) return;

        Setup.getApplicationContext()
                .getBean(DirectDebitService.class)
                .handleCreateNonMonthlyDdForPayingViaCcFlows(this);
    }

    @BeforeInsert
    public void validateInsert() {
        if (getStatus() == null) {
            setStatus(this.getNonCompletedInfo() ? DirectDebitStatus.IN_COMPLETE : (this.getConfirmedBankInfo() ?
                    DirectDebitStatus.PENDING : DirectDebitStatus.PENDING_DATA_ENTRY));
        }
        if (getMStatus() == null) {
            setMStatus(this.getNonCompletedInfo() ? DirectDebitStatus.IN_COMPLETE : (this.getConfirmedBankInfo() ?
                    DirectDebitStatus.PENDING : DirectDebitStatus.PENDING_DATA_ENTRY));
        }
        validate();
    }

    @BeforeUpdate
    public void validateUpdate() {
        validate();

        DirectDebit old = Setup.getApplicationContext().getBean(DirectDebitRepository.class).findOne(getId());

        if (!getStartDate().equals(old.getStartDate())) {
            logger.log(Level.SEVERE, "startDates are not equal this.startDate = " + this.getStartDate() + ", old.startDate = " + old.getStartDate());
            throw new RuntimeException("Start Date is not updatable.");
        }
        
        if (!getExpiryDate().equals(old.getExpiryDate())) {
            logger.log(Level.SEVERE, "expiryDates are not equal this.expiryDate = " + this.getExpiryDate() + ", old.expiryDate = " + old.getExpiryDate());
            throw new RuntimeException("Expiry Date is not updatable.");
        }
    }

    // ACC-1435 ACC-2507
    @BeforeDelete
    public void beforeDelete() {
        if (!this.getStatus().equals(DirectDebitStatus.CANCELED) &&
                !this.getStatus().equals(DirectDebitStatus.REJECTED) &&
                !this.getStatus().equals(DirectDebitStatus.IN_COMPLETE) &&
                !this.getMStatus().equals(DirectDebitStatus.CANCELED) &&
                !this.getMStatus().equals(DirectDebitStatus.REJECTED) &&
                !this.getMStatus().equals(DirectDebitStatus.IN_COMPLETE)) {
            
            throw new RuntimeException("Sorry you can't delete it should be CANCELED, REJECTED, OR INCOMPLETE");
        }

        if (this.getDirectDebitFiles() != null) {
            List<DirectDebitFile> forBouncingFiles = this.getDirectDebitFiles()
                    .stream()
                    .filter(ddf -> ddf.getForBouncingPayment() != null && ddf.getForBouncingPayment())
                    .collect(Collectors.toList());

            if (forBouncingFiles != null && forBouncingFiles.size() > 0) {
                throw new RuntimeException("Sorry you can't delete it there is for bouncing files on this direct debit");
            }
        }

        if (this.getDirectDebitRejectionToDo() != null) {
            DirectDebitRejectionToDo todo = this.getDirectDebitRejectionToDo();
            todo.setStopped(true);
            todo.setCompleted(true);
            Setup.getRepository(DirectDebitRejectionToDoRepository.class).save(todo);
        }
        
        Boolean existOtherActiveFlows = Setup.getApplicationContext().getBean(DirectDebitRejectionFlowService.class)
                .existOtherWaitingClientSignatureFlow(
                        this.getContractPaymentTerm().getContract(), Arrays.asList(this.getId()));
        
        if(!existOtherActiveFlows) {
            Setup.getApplicationContext().getBean(DDMessagingToDoController.class)
                    .closeRelatedToDos(this.getContractPaymentTerm().getContract().getUuid(), 
                            Arrays.asList(DDMessagingType.DirectDebitRejected, DDMessagingType.ClientPaidCashAndNoSignatureProvided,
                                    DDMessagingType.IncompleteDDRejectedByDataEntry));
        }
    }

    public DirectDebitFile getManualDdfFile() {
        return manualDdfFile;
    }

    public void setManualDdfFile(DirectDebitFile manualDdfFile) {
        this.manualDdfFile = manualDdfFile;
    }

    public DirectDebitFile getAutoDdfFile() {
        return autoDdfFile;
    }

    public void setAutoDdfFile(DirectDebitFile autoDdfFile) {
        this.autoDdfFile = autoDdfFile;
    }

    public void setRejectedBySalesScreen(boolean rejectedBySalesScreen) {
        this.rejectedBySalesScreen = rejectedBySalesScreen;
    }

    public boolean getRejectedBySalesScreen() {
        return rejectedBySalesScreen;
    }

    public boolean isRequiredAfterSwitching() {
        return requiredAfterSwitching;
    }

    public void setRequiredAfterSwitching(boolean requiredAfterSwitching) {
        this.requiredAfterSwitching = requiredAfterSwitching;
    }
    
    public boolean isGenerateManualDDFs() {
        return generateManualDDFs;
    }

    public void setGenerateManualDDFs(boolean generateManualDDFs) {
        this.generateManualDDFs = generateManualDDFs;
    }

    public boolean isGenerateManualDDFsFromConfig() {
        return this.generateManualDDFs && getDdConfiguration().isCreateManualForDDB();
    }
    
    @Override
    public DirectDebit clone() {
        try {
            return (DirectDebit) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }

    public DirectDebit clone(Date startDate) {
        return clone(startDate, new HashMap<>());
    }

    public DirectDebit clone(Date startDate, Map<String ,Object> map) {
        DirectDebit dd = new DirectDebit();
        dd.setStatus(DirectDebitStatus.IN_COMPLETE);
        dd.setMStatus(DirectDebitStatus.IN_COMPLETE);
        dd.setAccountName(this.accountName);
        dd.setBank(this.bank);
        dd.setBankName(this.bankName);
        dd.setContractPaymentTerm(this.getContractPaymentTerm().getContract().getActiveContractPaymentTerm());
        dd.setEid(this.eid);
        dd.setIbanNumber(this.ibanNumber);
        dd.setConfirmedBankInfo(this.confirmedBankInfo);
        dd.setNonCompletedInfo(this.nonCompletedInfo);
        dd.setSource(this.source);
        dd.setAdditionalDiscount(this.additionalDiscount);
        dd.setAdditionalDiscountNotes(this.additionalDiscountNotes);
        dd.setAmount(this.amount);
        dd.setCategory(map.containsKey("directDebitCategory") ?
                (DirectDebitCategory) map.get("directDebitCategory") :
                this.getCategory());
        dd.setDdBankInfoGroup(this.ddBankInfoGroup);
        dd.setDataEntryNotes(this.dataEntryNotes);
        dd.setExpiryDate(map.containsKey("expiryDate") ?
                new java.sql.Date(((LocalDate) map.get("expiryDate")).toDate().getTime()) :
                this.getExpiryDate());
        dd.setDirectDebitRejectionToDo(this.directDebitRejectionToDo);
        dd.setStartDate(map.containsKey("ddStartDate") ?
                new java.sql.Date(((LocalDate) map.get("ddStartDate")).toDate().getTime()) :
                startDate);
        dd.setSuggestedAmount(this.suggestedAmount);
        dd.setRequiredAfterSwitching(this.requiredAfterSwitching);
        dd.setAddedByOecFlow(this.addedByOecFlow);
        dd.setType(map.containsKey("directDebitType") ?
                (DirectDebitType) map.get("directDebitType") :
                getType().equals(DirectDebitType.WEEKLY) ? DirectDebitType.DAILY : this.getType()); // ACC-2550
        dd.setNonFileCompleted(this.nonFileCompleted);
        dd.setProrated(this.prorated);
        dd.setNotes(this.notes);
        dd.setProratedFrom(this.proratedFrom);
        dd.setProratedTo(this.proratedTo);
        dd.setResultDate(this.resultDate);
        dd.setUploaded(this.uploaded);
        dd.setImageForDD(this.getImageForDD());
        dd.setAttachments(this.getAttachments());
        dd.setDdcId(this.getDdcId());
        dd.setHidden(this.isHidden());

        List<ContractPayment> cps = this.getContractPayments();
        cps.stream().filter(cp -> cp.getDate().before(startDate))
                .forEach(cp -> cp.setDirectDebit(null));

        this.setPayments(new ArrayList<>());
        this.setContractPayments(new ArrayList<>());
        DirectDebitRepository repository = Setup.getRepository(DirectDebitRepository.class);
        repository.save(dd);

        cps = cps.stream().filter(cp -> !cp.getDate().before(startDate))
                .collect(Collectors.toList());
        for (ContractPayment cp : cps) {
            cp.setDirectDebit(dd);
        }

        dd = repository.findOne(dd.getId());
        dd.setPayments(cps);

        return dd;
    }

    public DirectDebit clone(DirectDebitStatus status) {
        return clone(status, new HashMap<>());
    }

    public DirectDebit clone(DirectDebitStatus status, Map<String ,Object> map) {
        DirectDebit dd = clone(this.getStartDate(), map);
        dd.setStatus(status);
        dd.setMStatus(status);
        dd.setSigned(true);
        return dd;
    }

    public void cloneChildDds(DirectDebit newDD, int trialNumber) {
        cloneChildDds(newDD, trialNumber, true);
    }

    public void cloneChildDds(DirectDebit newDD, int trialNumber, boolean useOldFiles) {
        int ddfCount = Math.min(
                this.getDdConfiguration().getNumberOfGeneratedDDs(),
                Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_NUMBER_OF_DIRECT_DEBIT_SIGNATURES)));

        int manualCount = ddfCount, automaticCount = ddfCount;

        // ACC-8459
        if (useOldFiles) {
            for(DirectDebitFile ddf : this.getDirectDebitFiles()) {
                if (getCategory().equals(DirectDebitCategory.B) &&
                        ddf.getDdMethod().equals(DirectDebitMethod.MANUAL) &&
                        (!newDD.isGenerateManualDDFsFromConfig() ||
                                !getDdConfiguration().isIncludeManualInDDBFlow())) {

                    logger.info("Manual DDF -> step over it");
                    continue;
                }

                if((ddf.getDdMethod().equals(DirectDebitMethod.MANUAL) && manualCount > 0) ||
                        (ddf.getDdMethod().equals(DirectDebitMethod.AUTOMATIC) && automaticCount > 0)){

                    Setup.getRepository(DirectDebitFileRepository.class)
                            .save(ddf.cloneToNewDD(newDD, trialNumber));
                }

                if(ddf.getDdMethod().equals(DirectDebitMethod.MANUAL)) manualCount--;
                if(ddf.getDdMethod().equals(DirectDebitMethod.AUTOMATIC)) automaticCount--;
            }
        }

        if(automaticCount <= 0 && manualCount <= 0) return;

        List<DirectDebitSignature> currentSignatures =
                (List<DirectDebitSignature>) Setup.getApplicationContext().getBean(DirectDebitSignatureService.class)
                        .getLastSignatureType(this.getContractPaymentTerm(), true, false).get("currentSignatures");

        if(automaticCount > 0 && newDD.getCategory().equals(DirectDebitCategory.B)) {
            Setup.getApplicationContext().getBean(DirectDebitService.class)
                    .createDirectDebitFiles(newDD, currentSignatures, DirectDebitMethod.AUTOMATIC, automaticCount);
        }

        if(manualCount > 0 && (newDD.getCategory().equals(DirectDebitCategory.A) ||
                newDD.isGenerateManualDDFsFromConfig() && getDdConfiguration().isIncludeManualInDDBFlow())) {

            Setup.getApplicationContext().getBean(DirectDebitService.class)
                    .createDirectDebitFiles(newDD, currentSignatures, DirectDebitMethod.MANUAL, manualCount);
        }
    }

    @PrePersist
    void debug() {
        logger.log(Level.INFO, "isBouncingRejectionTodoAdded: " + isBouncingRejectionTodoAdded() +
                "; getDirectDebitBouncingRejectionToDo: " + (getDirectDebitBouncingRejectionToDo() == null));

        if (isBouncingRejectionTodoAdded() && getDirectDebitBouncingRejectionToDo() == null)
            throw new RuntimeException("Bouncing Rejection Problem!!");
    }

    @JsonIgnore
    public boolean isExpired() {
        return isStatusIn(Arrays.asList(DirectDebitStatus.EXPIRED));
    }

    @JsonIgnore
    public boolean isStatusIn(List<DirectDebitStatus> excludeStatuses) {
        if (excludeStatuses == null) return false;

        boolean isItIn = (category.equals(DirectDebitCategory.A) && excludeStatuses.contains(this.MStatus)) ||
                (category.equals(DirectDebitCategory.B) && excludeStatuses.contains(this.MStatus) && excludeStatuses.contains(status));

        logger.info("DD Status: " + status + "; M_Status: " + this.MStatus + "; DD In: " + isItIn);
        return isItIn;
    }
    
    public DirectDebitConfiguration getDdConfiguration() {
        if (this.dDConfiguration != null) return this.dDConfiguration;

        if (this.getBank() == null) {
            logger.warning("DD#" + this.getId() + ", has No Bank -> will use the default configuration");
            return DirectDebitConfiguration.newInstance(this.getBank());
        }

        logger.warning("DD#" + this.getId() + ", has Bank = " + this.getBank().getId());
        DirectDebitConfigurationRepository ddConfigurationRepo = Setup.getRepository(DirectDebitConfigurationRepository.class);
        DirectDebitConfiguration ddConfiguration = ddConfigurationRepo.findFirstByBank(this.getBank());

        if (ddConfiguration == null) {
            logger.warning("DD#" + this.getId() + ", has No Configuration -> create one");
            ddConfiguration = DirectDebitConfiguration.newInstance(this.getBank());
            ddConfiguration = ddConfigurationRepo.save(ddConfiguration);
        }

        logger.info("DD#" + this.getId() + ", has Configuration# = " + ddConfiguration.getId());

        this.dDConfiguration = ddConfiguration;
        return ddConfiguration;
    }

    public void setDdConfiguration(DirectDebitConfiguration dDConfiguration) {
        this.dDConfiguration = dDConfiguration;
    }

    public double getOecSalary() {
        return oecSalary;
    }

    public void setOecSalary(double oecSalary) {
        this.oecSalary = oecSalary;
    }

    public Date getConfirmBankInfoDate() { return confirmBankInfoDate; }

    public void setConfirmBankInfoDate(Date confirmBankInfoDate) { this.confirmBankInfoDate = confirmBankInfoDate; }

    public boolean isHidden() { return hidden; }

    public void setHidden(boolean hidden) { this.hidden = hidden; }

    public Long getRelatedEntityId() { return relatedEntityId; }

    public void setRelatedEntityId(Long relatedEntityId) { this.relatedEntityId = relatedEntityId; }

    public String getRelatedEntityType() { return relatedEntityType; }

    public void setRelatedEntityType(String relatedEntityType) { this.relatedEntityType = relatedEntityType; }

    public Long getDdcId() { return ddcId; }

    public void setDdcId(Long ddcId) { this.ddcId = ddcId; }

    ///////////////////////////////////////////////////////////////////
/////////////////////  BRs Realted Functions  /////////////////////
///////////////////////////////////////////////////////////////////

    @Transactional
    public void createRelatedPayments(PaymentStatus status , DirectDebitFile directDebitFile){
        DirectDebit directDebit = this;
        PaymentRepository paymentRepository = Setup.getRepository(PaymentRepository.class);
        List<ContractPayment> contractPayments= directDebit.getPayments();
        if(directDebit.getContractPaymentTerm()==null || directDebit.getContractPaymentTerm().getContract()==null)
            return;
        Contract contract = Setup.getRepository(ContractRepository.class).findOne(contractPaymentTerm.getContract().getId());
        boolean vatPaidByClient = contract.getClientPaidVat();

        // Added By ANAS ALKASSAR FOR CM-1300
        // Make "Pro-Rated + 1 Month" payments as One Payment

        if(contractPayments!=null && contractPayments.size() == 2 && contract.getProRatedPlusMonth()!=null && contract.getProRatedPlusMonth()
                && type!=null && type.equals(DirectDebitType.ONE_TIME)){
            ContractPayment smallAmountContractPayment = contractPayments.get(0);
            ContractPayment bigAmountContractPayment = contractPayments.get(1);
            if(smallAmountContractPayment.getAmount()>bigAmountContractPayment.getAmount()){
                smallAmountContractPayment = contractPayments.get(1);
                bigAmountContractPayment = contractPayments.get(0);
            }
            if(smallAmountContractPayment.getIsProRated() || bigAmountContractPayment.getIsProRated()){
                Double oldAmount = bigAmountContractPayment.getAmount();
                bigAmountContractPayment.setAmount(oldAmount + smallAmountContractPayment.getAmount());
                bigAmountContractPayment.setIsProRated(true);

                contractPayments.clear();
                contractPayments.add(bigAmountContractPayment);
            }
        }

        if (contractPayments == null) {
            return;
        }

        PaymentService paymentService = Setup.getApplicationContext().getBean(PaymentService.class);
        for (ContractPayment cp : contractPayments) {
            logger.info("Contract payments info: " +
                    "id:" + cp.getId() +
                    "; Amount:" + cp.getAmount() +
                    "; AdditionalDiscountAmount:" + cp.getAdditionalDiscountAmount() +
                    "; IncludeWorkerSalary:" + cp.getIncludeWorkerSalary() +
                    "; WorkerSalary:" + contract.getWorkerSalary() +
                    "; OecSalary:" + getOecSalary());

            Payment payment = new Payment();
            payment.setWorkerSalary(this.getOecSalary());
            payment.setDirectDebitId(directDebit.getId());
            payment.setAutomaticallyAdded(true);
            payment.setContract(contract);
            payment.setAmountOfPayment(cp.getAmount());
            payment.setMethodOfPayment(PaymentMethod.DIRECT_DEBIT);
            payment.setTypeOfPayment(cp.getPaymentType());
            payment.setIsProRated(cp.getIsProRated());
            payment.setAffectsPaidEndDate(cp.getAffectsPaidEndDate());
            payment.setDiscount(cp.getDiscountAmount());
            payment.setSubType(cp.getSubType());
            PicklistItem bank = null;
            if(this.bank!=null && this.bank.getId()!=null)
                bank=Setup.getRepository(PicklistItemRepository.class).findOne(this.bank.getId());
            payment.setBankName(bank);
            payment.setStatus(status);
            payment.setIncludeWorkerSalary(cp.getIncludeWorkerSalary());
            payment.setVatPaidByClient(cp.getVatPaidByClient()!=null?cp.getVatPaidByClient():vatPaidByClient);
            payment.setDateOfPayment(new java.sql.Date(cp.getDate().getTime()));
            if(directDebitFile!=null)
            {
                payment.setDirectDebitFileId(directDebitFile.getId());
            }
            if(cp.getReplaceOf()!=null && cp.getReplaceOf().getId()!=null){
                payment.setIsReplacement(true);
                Payment replacedPayment = paymentRepository.findOne(cp.getReplaceOf().getId());
                payment.setReplacementFor(replacedPayment);
                payment.setWorkerSalary(replacedPayment.getWorkerSalary());
                payment.setIsInitial(replacedPayment.getIsInitial());
                payment.setIsProRated(replacedPayment.getIsProRated());

            }
            if (status.equals(PaymentStatus.PRE_PDP)) {
                paymentService.createPaymentSilent(payment);
            } else {
                paymentService.forceCreatePayment(payment);
            }

            //if(status.equals(PaymentStatus.PDC))
            //    copyAttachmentsToPayment(p);
        }
    }

    @Transactional
    public void transferPaymentsToPdpOrCreate(DirectDebitFile directDebitFile){
        DirectDebit directDebit = this;
        PaymentRepository paymentRepository = Setup.getRepository(PaymentRepository.class);
        List<Payment> prePdpPayments =  paymentRepository.findByDirectDebitIdAndStatus(directDebit.getId(), PaymentStatus.PRE_PDP);
        List<Payment> ddPayments =  paymentRepository.findByDirectDebitId(directDebit.getId());

        if((prePdpPayments == null || prePdpPayments.isEmpty()) && (ddPayments == null || ddPayments.isEmpty()))
            createRelatedPayments(PaymentStatus.PDC , directDebitFile);
        else if(prePdpPayments != null && !prePdpPayments.isEmpty()){
            PaymentService paymentService = Setup.getApplicationContext().getBean(PaymentService.class);
            for (Payment payment : prePdpPayments) {
                payment.setStatus(PaymentStatus.PDC);
                payment.setDirectDebitFileId(directDebitFile.getId());
                //copyAttachmentsToPayment(payment);
                paymentService.updatePaymentSilent(payment);
            }
        }
    }

    @Transactional
    public void deleteRelatedPrePdpAndPdpPayments() {
        logger.info("id : " + getId());
        deleteRelatedPrePdpPayments();

        Setup.getApplicationContext()
                .getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "applySilentSaveOnPayment_" + getId(),
                        "accounting",
                        "paymentService",
                        "applySilentSaveOnPayment")
                        .withRelatedEntity("DirectDebit", getId())
                        .withParameters(
                                new Class[] {Long.class, String.class},
                                new Object[] {getId(), PaymentStatus.PDC.toString()})
                        .withQueue(BackgroundTaskQueues.SequentialQueue)
                        .withDelay(120L * 1000L)
                        .build());
    }

    @Transactional
    public void deleteRelatedPrePdpPayments() {
        logger.info("id : " + getId());

        Setup.getApplicationContext()
                .getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "applySilentSaveOnPayment_" + getId(),
                        "accounting",
                        "paymentService",
                        "applySilentSaveOnPayment")
                        .withRelatedEntity("DirectDebit", getId())
                        .withParameters(
                                new Class[] {Long.class, String.class},
                                new Object[] {getId(), PaymentStatus.PRE_PDP.toString()})
                        .withQueue(BackgroundTaskQueues.SequentialQueue)
                        .withDelay(120L * 1000L)
                        .build());
    }

    @Transactional
    public void closeRelatedVoiceResolverTodos() {
        if(this.getContractPaymentTerm() == null || this.getContractPaymentTerm().getId()==null)
            return;

        Contract contract = Setup.getRepository(ContractRepository.class).findOne(this.getContractPaymentTerm().getContract().getId());
        VoiceResolverToDoRepository voiceResolverToDoRepository = Setup.getRepository(VoiceResolverToDoRepository.class);
        List<VoiceResolverToDo> voiceResolverToDos
                = voiceResolverToDoRepository.findOpenByContractAndReason(contract,
                VoiceResolverToDoReason.PAYMENT_EXPIRY_INDEFINITE_AGREEMENT);

        for (VoiceResolverToDo voiceResolverToDo : voiceResolverToDos) {
            if (voiceResolverToDo.getComplaint() == null) {
                voiceResolverToDo.setTaskName("");
                voiceResolverToDo.setCompleted(Boolean.TRUE);
                voiceResolverToDoRepository.save(voiceResolverToDos);

            }
            //if (voiceResolverToDo.getComplaint() == null && (voiceResolverToDo.getReason()==null || !voiceResolverToDo.getReason().equals(VoiceResolverToDoReason.COMPLAINT))) {
            voiceResolverToDo.setTaskName("");
            voiceResolverToDo.setCompleted(Boolean.TRUE);
            voiceResolverToDoRepository.save(voiceResolverToDo);
            //}
        }
    }
}
