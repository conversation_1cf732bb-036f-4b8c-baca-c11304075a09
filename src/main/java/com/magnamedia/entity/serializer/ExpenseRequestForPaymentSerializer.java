package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.entity.BasePLNode;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;

import java.io.IOException;
import java.util.List;

/**
 * <PERSON> (Jan 21, 2021)
 */
public class ExpenseRequestForPaymentSerializer extends JsonSerializer<List<ExpenseRequestTodo>> {

    @Override
    public void serialize(List<ExpenseRequestTodo> values,
                          JsonGenerator gen,
                          SerializerProvider serializers) throws IOException {
        gen.writeStartArray();

        if (values != null) {
            for (ExpenseRequestTodo value : values) {
                writeNode(value, gen);
            }
        }
        gen.writeEndArray();
    }

    private void writeNode(ExpenseRequestTodo value, JsonGenerator gen) throws IOException {
        gen.writeStartObject();
        gen.writeNumberField("id",
                value.getId());
        gen.writeStringField("expense",
                (value.getExpense() != null ? value.getExpense().getName() : ""));
        gen.writeStringField("relatedTo",
                String.valueOf((value.getRelatedToId() != null ? value.getRelatedToId() : value.getRelatedToId())));
        gen.writeObjectField("amount", value.getAmount());
        gen.writeEndObject();

    }
}
