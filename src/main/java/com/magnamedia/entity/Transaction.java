package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.controller.TransactionsController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.AfterInsert;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.serialize.IdSerializer;
import com.magnamedia.entity.serializer.IdOnlySerializer;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.mastersearch.SearchInheritedFields;
import com.magnamedia.mastersearch.SearchInnerFields;
import com.magnamedia.mastersearch.Searchable;
import com.magnamedia.mastersearch.SearchableField;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.TransactionEntityType;
import com.magnamedia.module.type.VatType;
import com.magnamedia.repository.*;
import com.magnamedia.service.ExpensePaymentService;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.sql.Date;
import java.time.LocalDate;
import java.time.Period;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.magnamedia.module.AccountingModule.*;

/**
 * <AUTHOR> Alalwani <<EMAIL>>
 * <AUTHOR> Kanaan <<EMAIL>>
 */
@Entity
@SearchInheritedFields(fields = {"id"})
@Searchable(showunName = "Transactions", order = 2, permissionCode = "ManageTransactions")
@Table(
        indexes = {
                @Index(columnList = "paymentId", unique = false),
                @Index(columnList = "paymentOrderId", unique = false),
                @Index(columnList = "pnlValueDate", unique = false),
                @Index(columnList = "expensePaymentId", unique = false),
                @Index(columnList = "CREATION_DATE") // ACC-2406
        })
public class Transaction extends BaseEntity {

    @SearchableField(headerName = "Amount", order = 2, searched = false)
    private Double amount = 0.0;

    @Column
    private java.util.Date pnlValueDate;

    @Lob
    @SearchableField(headerName = "Description", order = 3, searched = false)
    private String description;

    @SearchableField(headerName = "Quotation Number", order = 1, searched = false)
    private String quotationNumber;

    private Date date;

    private Boolean previouslyUnknown = false;

    @Enumerated(EnumType.STRING)
    @Column(length = 32, columnDefinition = "varchar(32) default 'UNKNOWN'")
    private TransactionEntityType transactionType;    //HOUSEMAID, CLEANER, OFFICE_STAFF, CLIENT, PROSPECT, CONTACT, CONTRACT, RESERVATION, UNKNOWN

    @NotAudited
    @OneToMany(cascade = CascadeType.ALL,
            fetch = FetchType.LAZY,
            mappedBy = "transaction")
    private List<HousemaidTransaction> housemaids;

    @NotAudited
    @OneToMany(cascade = CascadeType.ALL,
            fetch = FetchType.LAZY,
            mappedBy = "transaction")
    private List<OfficeStaffTransaction> officeStaffs;

    @NotAudited
    @OneToMany(cascade = CascadeType.ALL,
            fetch = FetchType.LAZY,
            mappedBy = "transaction")
    private List<ClientTransaction> clients;

    @NotAudited
    @OneToMany(cascade = CascadeType.ALL,
            fetch = FetchType.LAZY,
            mappedBy = "transaction")
    private List<FreedomOperatorTransaction> freedomOperators;

    @NotAudited
    @OneToMany(cascade = CascadeType.ALL,
            fetch = FetchType.LAZY,
            mappedBy = "transaction")
    private List<ContractTransaction> contracts;

    @NotAudited
    @OneToMany(cascade = CascadeType.ALL,
            fetch = FetchType.LAZY,
            mappedBy = "transaction")
    private List<SalesTransaction> sales;

    @NotAudited
    @OneToMany(cascade = CascadeType.ALL,
            fetch = FetchType.LAZY,
            mappedBy = "transaction")
    private List<MaidsAtCandidateWATransaction> applicants;

    @OneToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdOnlySerializer.class)
    private CancelRequestExpense cancelRequestExpense;

    @OneToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdOnlySerializer.class)
    private NewRequestExpense newRequestExpense;

    @OneToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdOnlySerializer.class)
    private RepeatEIDRequestExpense repeatEIDRequestExpense;

    @OneToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdOnlySerializer.class)
    private RenewRequestExpense renewRequestExpense;

    @OneToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdOnlySerializer.class)
    private ContractModificationExpense contractModificationExpense;

    @OneToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdOnlySerializer.class)
    private ModifyVisaRequestExpense modifyVisaRequestExpense;

    @OneToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdOnlySerializer.class)
    private UnpaidLeaveExpense unpaidLeaveExpense;

    @OneToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdOnlySerializer.class)
    private ModifyPersonInformationExpense modifyPersonInformationExpense;

    //Jirra ACC-430
    @Column
    private String businessObjectId;

    @Column
    private String businessObjectType;

    @Transient
    private String visaExpenseName;

    //Jirra ACC-751
    @Column
    private Double vatAmount = 0D;

    //Jirra ACC-960
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem license;

    //Jirra ACC-1995
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdSerializer.class)
    private BankStatementFile bankStatementFile;

    @Column
    @Enumerated(EnumType.STRING)
    private VatType vatType;

    //Jirra ACC-373
    @Transient
    private boolean matchedWithExpectedWireTransfer;

    //Jirra ACC-1238
    @Column
    private Long paymentId;

    @Transient
    @JsonSerialize(using = IdLabelSerializer.class)
    private Payment payment;

    //Jirra ACC-1389
    @Column(columnDefinition = "boolean default false")
    private Boolean accrual;

    @Column
    private Date accrualFromDate;

    @Column
    private Date accrualToDate;

    @NotAudited
    @OneToMany(cascade = CascadeType.ALL,
            fetch = FetchType.LAZY,
            mappedBy = "transaction")
    private List<TransactionDetails> transactionDetails;

    //Jira ACC-1727
    @Column(columnDefinition = "boolean default false")
    private boolean isAutomatic;

    //Jirra ACC-2466
    @Column
    private Boolean missingTaxInvoice;

    @Column(columnDefinition = "boolean default true")
    private boolean creationTriggeredAutomatically = true;

    //Jirra ACC-3315
    @Column(columnDefinition = "boolean default false")
    private boolean doneByCoo = Boolean.FALSE;

    // ACC-2913
    @Column
    private Long expensePaymentId;

    @Transient
    @JsonIgnore
    private String bucketPreBalance;

    @Transient
    @JsonIgnore
    private String bucketBalance;

    @Transient
    private  Long visaStatementTransactionId;

    public boolean isMatchedWithExpectedWireTransfer() {
        ExpectedWireTransferRepository expectedWireTransferRepository =
                Setup.getRepository(ExpectedWireTransferRepository.class);
        List<ExpectedWireTransfer> expectedWireTransfers =
                expectedWireTransferRepository.findByTransaction(this);
        return (expectedWireTransfers != null && expectedWireTransfers.size() > 0);
    }

    public String getVisaExpenseName() {
        if (cancelRequestExpense != null)
            return cancelRequestExpense.getName();
        if (newRequestExpense != null)
            return newRequestExpense.getName();
        if (renewRequestExpense != null)
            return renewRequestExpense.getName();
        if (repeatEIDRequestExpense != null)
            return repeatEIDRequestExpense.getName();
        return "";
    }

    public String getTransactionFor() {
        String transactionFor = "";
        switch (this.getTransactionType()) {
            case CLEANER:
            case CONTACT:
                transactionFor = this.getTransactionType().toString() + " " + sales.stream().map(sales -> sales.getSalesforceName()).collect(Collectors.toList());
                break;
            case CONTRACT:
                transactionFor = this.getTransactionType().toString() + " " + contracts.stream().map(contract -> contract.getContract().getId()).collect(Collectors.toList());
                break;
            case CLIENT:
            case PROSPECT:
                transactionFor = this.getTransactionType().toString() + " " + clients.stream().map(client -> client.getClient().getName()).collect(Collectors.toList());
                break;
            case HOUSEMAID:
                transactionFor = this.getTransactionType().toString() + " " + housemaids.stream().map(maid -> maid.getHousemaid().getName()).collect(Collectors.toList());
                break;
            case OFFICE_STAFF:
                transactionFor = this.getTransactionType().toString() + " " + officeStaffs.stream().map(staff -> staff.getOfficeStaff().getName()).collect(Collectors.toList());
                break;
            case FREEDOM_OPERATOR:
                transactionFor = this.getTransactionType().toString() + " " + freedomOperators.stream().map(freedomOperator -> freedomOperator.getFreedomOperator().getName()).collect(Collectors.toList());
                break;
            case APPLICANT:
                transactionFor = this.getTransactionType().toString() + " " + applicants.stream().map(applicant -> applicant.getApplicant().getName()).collect(Collectors.toList());
                break;
            case UNKNOWN:
                transactionFor = this.getTransactionType().toString();
                break;
        }
        return transactionFor;
    }

    public List<ClientTransaction> getProspects() {
        if (clients == null) return new ArrayList();
        return clients;
    }

    public void setProspects(List<ClientTransaction> client) {
        this.clients = client;
    }

    public TransactionEntityType getTransactionType() {
        return transactionType;
    }

    public List<FreedomOperatorTransaction> getFreedomOperators() {
        if (freedomOperators == null) freedomOperators = new ArrayList();
        return freedomOperators;
    }

    public void setFreedomOperators(List<FreedomOperatorTransaction> freedomOperators) {
        this.freedomOperators = freedomOperators;
    }

    public void setTransactionType(TransactionEntityType transactionType) {
        this.transactionType = transactionType;
    }

    public BankStatementFile getBankStatementFile() {
        return bankStatementFile;
    }

    public void setBankStatementFile(BankStatementFile bankStatementFile) {
        this.bankStatementFile = bankStatementFile;
    }

    public List<HousemaidTransaction> getHousemaids() {
        if (housemaids == null) housemaids = new ArrayList();
        return housemaids;
    }

    public void setHousemaids(List<HousemaidTransaction> housemaids) {
        this.housemaids = housemaids;
    }

    public List<OfficeStaffTransaction> getOfficeStaffs() {
        if (officeStaffs == null) officeStaffs = new ArrayList();
        return officeStaffs;
    }

    public void setOfficeStaffs(List<OfficeStaffTransaction> officeStaffs) {
        this.officeStaffs = officeStaffs;
    }

    public List<ContractTransaction> getContracts() {
        if (contracts == null) contracts = new ArrayList();
        return contracts;
    }

    public void setContracts(List<ContractTransaction> contracts) {
        this.contracts = contracts;
    }

    public List<SalesTransaction> getSales() {
        if (sales == null) sales = new ArrayList();
        return sales;
    }

    public void setSales(List<SalesTransaction> sales) {
        this.sales = sales;
    }

    public List<MaidsAtCandidateWATransaction> getApplicants() {
        if (applicants == null) applicants = new ArrayList();
        return applicants;
    }

    public void setApplicants(List<MaidsAtCandidateWATransaction> applicants) {
        this.applicants = applicants;
    }

    @Enumerated(EnumType.STRING)
    @SearchableField(headerName = "Payment Type", order = 4, searched = false)
    private PaymentMethod paymentType;

    @SearchInnerFields(fields = {"code", "name"})
    @SearchableField(headerName = "From Bucket", order = 6)
    @ManyToOne(fetch = FetchType.LAZY)
    private Bucket fromBucket;

    @SearchInnerFields(fields = {"code", "name"})
    @SearchableField(headerName = "To Bucket", order = 8)
    @ManyToOne(fetch = FetchType.LAZY)
//    @JsonSerialize(using = IdLabelSerializer.class)
    private Bucket toBucket;

    @SearchInnerFields(fields = {"code", "name"})
    @SearchableField(headerName = "Expense", order = 10)
    @ManyToOne(fetch = FetchType.LAZY)
//    @JsonSerialize(using = IdLabelSerializer.class)
    private Expense expense;

    @SearchInnerFields(fields = {"code", "name"})
    @SearchableField(headerName = "Revenue", order = 8)
    @ManyToOne(fetch = FetchType.LAZY)
//    @JsonSerialize(using = IdLabelSerializer.class)
    private Revenue revenue;

    //    @NotNull
    private Boolean chequesNotClearedAmount;

    //Jirra ACC-2057
    @Column
    private Long paymentOrderId;

    @Transient
    @JsonSerialize(using = IdLabelSerializer.class)
    private PaymentOrder paymentOrder;

    //	@ManyToMany(cascade = CascadeType.ALL)
//	private Collection<Quotation> quotations;
    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public String getDescription() {
        boolean secured = this.getIsDescriptionSecured();
        logger.info("ID: " + getId() + "; secured: " + secured);

        if (secured) return "***DESCRIPTION SECURED***";
        return description;
    }

    public void setDescription(String description) {
        if (this.getIsDescriptionSecured())
            return; //Description cannot be updated if the user has no permission

        this.description = description;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public PaymentMethod getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(PaymentMethod paymentType) {
        this.paymentType = paymentType;
    }

    public Bucket getFromBucket() {
        return fromBucket;
    }

    public void setFromBucket(Bucket fromBucket) {
        this.fromBucket = fromBucket;
    }

    public Bucket getToBucket() {
        return toBucket;
    }

    public void setToBucket(Bucket toBucket) {
        this.toBucket = toBucket;
    }

    public Expense getExpense() {
        return expense;
    }

    public void setExpense(Expense expense) {
        this.expense = expense;
    }

    public Revenue getRevenue() {
        return revenue;
    }

    public void setRevenue(Revenue revenue) {
        this.revenue = revenue;
    }

    public Boolean getPreviouslyUnknown() {
        return previouslyUnknown;
    }

    public void setPreviouslyUnknown(Boolean previouslyUnknown) {
        this.previouslyUnknown = previouslyUnknown;
    }

    public String getQuotationNumber() {
        return quotationNumber;
    }

    public void setQuotationNumber(String quotationNumber) {
        this.quotationNumber = quotationNumber;
    }

    public java.util.Date getPnlValueDate() {
        return pnlValueDate;
    }

    public void setPnlValueDate(java.util.Date pnlValueDate) {
        this.pnlValueDate = pnlValueDate;
    }

    @BeforeInsert
    @BeforeUpdate
    public final void beforeCreateUpdateTrans() {
        if (this.pnlValueDate == null)
            pnlValueDate = this.date;
        if (this.license == null)
            this.license = PicklistHelper.getItem(
                    PICKLIST_TRANSACTION_LICENSE, PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM);


//        if (this.license != null) {
//            PicklistItemRepository picklistItemRepository =
//                    Setup.getRepository(PicklistItemRepository.class);
//            PicklistItem licenseItem = picklistItemRepository.findOne(license.getId());
//            if (licenseItem.getCode().equalsIgnoreCase("no_vat")) {
//                if (this.vatAmount != null && this.vatAmount != 0) {
//                    throw new RuntimeException("can't add vat amount for no vat license");
//                }
//                if (this.vatType != null) {
//                    throw new RuntimeException("can't add vat type for no vat license");
//                }
//            }
//
        PicklistItemRepository picklistItemRepository =
                Setup.getRepository(PicklistItemRepository.class);
        license = picklistItemRepository.findOne(license.getId());

//            else if (this.getId() != null) { // before update
//                List<Attachment> attachments = this.getAttachments();
//                boolean isVatAttachmentUploaded = false;
//                for (Attachment attachment : attachments) {
//                    if (attachment.getTag().startsWith("VAT_")) {
//                        isVatAttachmentUploaded = true;
//                        break;
//                    }
//                }
//                if ((this.license != null && (this.license.getCode().equals(PicklistHelper.getItem(PICKLIST_TRANSACTION_LICENSE, PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM).getCode())
//                        || this.license.getCode().equals(PicklistHelper.getItem(PICKLIST_TRANSACTION_LICENSE, PICKLIST_TRANSACTION_LICENSE_STORAGE_ITEM).getCode()))
//                        && !isVatAttachmentUploaded && this.getMissingTaxInvoice() != null && this.getMissingTaxInvoice())
//                        || this.getLicense() != null && this.getLicense().getCode().equalsIgnoreCase("no_vat")
//                        && this.getMissingTaxInvoice() != null && !this.getMissingTaxInvoice()) {
//
//                } else {
//                    throw new RuntimeException("Missing Tax Invoice value should be unchecked if License is No Vat, or checked if License is Mustaqeem or Storage and no Vat Attachment is uploaded");
//                }
//            }
//
//        }

        checkChangeStatusPermission();
        checkIntegrity();


        this.initLists();
        this.insertTransactionDetails();
    }

    @AfterInsert
    public void checkAttachment() {
        Storage.updateAttachements(this);
        if (this.license != null && (this.license.getCode().equals(PicklistHelper.getItem(
                PICKLIST_TRANSACTION_LICENSE, PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM).getCode()) ||
                this.license.getCode().equals(PicklistHelper.getItem(
                        PICKLIST_TRANSACTION_LICENSE, PICKLIST_TRANSACTION_LICENSE_STORAGE_ITEM).getCode()))) {

            List<Attachment> attachments = this.getAttachments();
            boolean isVatAttachmentUploaded = false;
            for (Attachment attachment : attachments) {
                if (attachment.getTag() == null)
                    continue;
                if (attachment.getTag().toUpperCase().startsWith("VAT_") ||
                        attachment.getTag().toUpperCase().startsWith("PAYMENT_TAX_INVOICE") ||
                        attachment.getTag().toUpperCase().startsWith("PAYMENT_TAX_CREDIT_NOTE") ||
                        attachment.getTag().toUpperCase().startsWith("TR_PAYMENT_TAX_INVOICE") ||
                        attachment.getTag().toUpperCase().startsWith("TR_PAYMENT_TAX_CREDIT_NOTE") ||
                        attachment.getTag().toUpperCase().startsWith("RECEIPT -")) {
                    isVatAttachmentUploaded = true;
                    break;
                }
            }
            setMissingTaxInvoice(!isVatAttachmentUploaded);
        } else {
            this.setMissingTaxInvoice(false);
        }
    }

    public Boolean getChequesNotClearedAmount() {
        return chequesNotClearedAmount;
    }

    public void setChequesNotClearedAmount(Boolean chequesNotClearedAmount) {
        this.chequesNotClearedAmount = chequesNotClearedAmount;
    }

    public CancelRequestExpense getCancelRequestExpense() {
        return cancelRequestExpense;
    }

    public void setCancelRequestExpense(CancelRequestExpense cancelRequestExpense) {
        this.cancelRequestExpense = cancelRequestExpense;
    }

    public NewRequestExpense getNewRequestExpense() {
        return newRequestExpense;
    }

    public void setNewRequestExpense(NewRequestExpense newRequestExpense) {
        this.newRequestExpense = newRequestExpense;
    }

    public RepeatEIDRequestExpense getRepeatEIDRequestExpense() {
        return repeatEIDRequestExpense;
    }

    public void setRepeatEIDRequestExpense(RepeatEIDRequestExpense repeatEIDRequestExpense) {
        this.repeatEIDRequestExpense = repeatEIDRequestExpense;
    }

    public RenewRequestExpense getRenewRequestExpense() {
        return renewRequestExpense;
    }

    public void setRenewRequestExpense(RenewRequestExpense renewRequestExpense) {
        this.renewRequestExpense = renewRequestExpense;
    }

    public String getBusinessObjectId() {
        return businessObjectId;
    }

    public void setBusinessObjectId(String businessObjectId) {
        this.businessObjectId = businessObjectId;
    }

    public String getBusinessObjectType() {
        return businessObjectType;
    }

    public void setBusinessObjectType(String businessObjectType) {
        this.businessObjectType = businessObjectType;
    }

    //Jirra ACC-1147
    public Double getVatAmount() {
        if (vatAmount != null)
            return Math.floor(vatAmount * 100) / 100;
        else
            return vatAmount;
    }

    public void setVatAmount(Double vatAmount) {
        this.vatAmount = vatAmount;
    }

    public List<ClientTransaction> getClients() {
        if (clients == null) clients = new ArrayList();
        return clients;
    }

    public void setClients(List<ClientTransaction> clients) {
        this.clients = clients;
    }

    public PicklistItem getLicense() {
        return license;
    }

    public void setLicense(PicklistItem license) {
        this.license = license;
    }

    public VatType getVatType() {
        return vatType;
    }

    public void setVatType(VatType vatType) {
        this.vatType = vatType;
    }

    public Long getPaymentId() {
        return paymentId;
    }

    public void setPaymentId(Long paymentId) {
        this.paymentId = paymentId;
    }

    public Payment getPayment() {
        if (this.paymentId != null) {
            this.payment = Setup.getRepository(PaymentRepository.class).findOne(paymentId);
        }

        return this.payment;
    }

    public void setPayment(Payment payment) {
        this.payment = payment;
    }

    public void checkIntegrity() {
        if (this.license != null) {
            PicklistItemRepository picklistItemRepository =
                    Setup.getRepository(PicklistItemRepository.class);
            PicklistItem licenseItem = picklistItemRepository.findOne(license.getId());
            if (licenseItem.getCode().equalsIgnoreCase(TransactionsController.TRANSACTION_LICENSE_NO_VAT)) {
                if (this.vatAmount != null && this.vatAmount != 0) {
                    throw new RuntimeException("Can't add vat amount for no vat license");
                }
                if (this.vatType != null) {
                    throw new RuntimeException("Can't add vat type for no vat license");
                }
            }
        }
        if (this.amount == null) this.amount = 0D;

        if (this.vatAmount == null) this.vatAmount = 0D;
        else // ACC-1147
            this.vatAmount = Math.floor(vatAmount * 100) / 100;

        if (this.vatAmount != 0D && this.vatType == null)
            throw new RuntimeException("Vat type could not be null.");

        this.amount = (double) Math.round(this.amount * 100) / 100;

        if (this.transactionType == null) {
            this.transactionType = TransactionEntityType.UNKNOWN;
        }

        if (!((fromBucket != null && toBucket != null && expense == null && revenue == null)
                || (fromBucket != null && expense != null && toBucket == null && revenue == null)
                || (revenue != null && toBucket != null && fromBucket == null && expense == null))) {

            throw new RuntimeException("Allowed transactions are only: From bucket to expense OR From revenue to bucket OR from bucket to bucket");
        }
    }

    public Boolean getIsDescriptionSecured() {
        // ACC-5182
        // ACC-8094
        return Setup.getApplicationContext()
                .getBean(ExpensePaymentService.class)
                .bucketOrExpenseSecure(
                        getExpense() != null && getExpense().getIsSecure(),
                        getFromBucket() != null && getFromBucket().getIsSecure(),
                        getToBucket() != null && getToBucket().getIsSecure());
    }
    
    private static final Logger logger =
            Logger.getLogger(Transaction.class.getName());

    // ACC-505 ACC-505
    public void checkChangeStatusPermission() {
        if (couldChangeDates()) return;

        org.joda.time.LocalDate lockDate = new org.joda.time.LocalDate().minusMonths(1).dayOfMonth().withMinimumValue();

        if (new org.joda.time.LocalDate(getCreationDate()).isBefore(lockDate)) {
            String action = (this.getId() == null) ? "create" : "edit";
            throw new RuntimeException(
                    "You cannot " + action + " this transaction, it is locked since " +
                            new org.joda.time.LocalDate(getCreationDate())
                                    .plusMonths(2).dayOfMonth().withMinimumValue().toString("yyyy-MM-dd"));
        }
    }

    //Jirra ACC-505
    public boolean couldChangeDates() {
        return CurrentRequest.getUser() != null &&
                CurrentRequest.getUser().hasPosition(AccountingModule.CHANGE_TRANSACTIONS_DATES_ADMIN_POSITION);
    }

    //Jirra ACC-1238
    // TODO: 2/25/2020 inform osamah about this
//    @Override
//    public List<Attachment> getAttachments() {
//        if (this.payment != null)
//            return payment.getAttachments();
//
//        return super.getAttachments();
//    }

    public Boolean getAccrual() {
        return accrual;
    }

    public void setAccrual(Boolean accrual) {
        this.accrual = accrual;
    }

    public Date getAccrualFromDate() {
        return accrualFromDate;
    }

    public void setAccrualFromDate(Date accrualFromDate) {
        this.accrualFromDate = accrualFromDate;
    }

    public Date getAccrualToDate() {
        return accrualToDate;
    }

    public void setAccrualToDate(Date accrualToDate) {
        this.accrualToDate = accrualToDate;
    }

    public List<TransactionDetails> getTransactionDetails() {
        return transactionDetails;
    }

    public void setTransactionDetails(List<TransactionDetails> transactionDetails) {
        this.transactionDetails = transactionDetails;
    }

    public boolean isAutomatic() {
        return isAutomatic;
    }

    public void setAutomatic(boolean automatic) {
        isAutomatic = automatic;
    }

    @Transient
    private boolean passInitLists = false; // to be removed after ACC-8358

    public boolean isPassInitLists() { return passInitLists; }

    public void setPassInitLists(boolean passInitLists) { this.passInitLists = passInitLists; }

    //Jirra ACC-1338
    private void initLists() {
        if (isPassInitLists()) return;

        if (!this.isNewInstance()) {
            HistorySelectQuery<Transaction> historySelectQuery = new HistorySelectQuery(Transaction.class);
            historySelectQuery.filterBy("id", "=", this.getId());
            historySelectQuery.sortBy("lastModificationDate", false, true);
            historySelectQuery.setLimit(1);
            List<Transaction> oldTransactions = historySelectQuery.execute();
            if (oldTransactions != null && !oldTransactions.isEmpty()) {
                Transaction old = oldTransactions.get(0);
                // delete old
                switch (old.getTransactionType()) {
                    case CLEANER:
                    case CONTACT:
                        Logger.getLogger(Transaction.class.getName()).log(Level.SEVERE, "MMM: {0}", this.getSales() == null ? 0 : this.getSales().size());
                        BaseRepository deletionRepository = Setup.getRepository(SalesTransactionRepository.class);
                        SelectQuery<SalesTransaction> query = new SelectQuery(SalesTransaction.class);
                        query.filterBy("transaction.id", "=", old.getId());
                        List toBeDeleted = query.execute();
                        deletionRepository.delete(toBeDeleted);
                        break;
                    case CLIENT:
                    case PROSPECT:
                        Logger.getLogger(Transaction.class.getName()).log(Level.SEVERE, "MMM: {0}", this.getClients() == null ? 0 : this.getClients().size());
                        deletionRepository = Setup.getRepository(ClientTransactionRepository.class);
                        query = new SelectQuery(ClientTransaction.class);
                        query.filterBy("transaction.id", "=", old.getId());
                        toBeDeleted = query.execute();
                        deletionRepository.delete(toBeDeleted);

                        break;
                    case CONTRACT:
                        Logger.getLogger(Transaction.class.getName()).log(Level.SEVERE, "MMM: {0}", this.getContracts() == null ? 0 : this.getContracts().size());
                        deletionRepository = Setup.getRepository(ContractTransactionRepository.class);
                        query = new SelectQuery(ContractTransaction.class);
                        query.filterBy("transaction.id", "=", old.getId());
                        toBeDeleted = query.execute();
                        deletionRepository.delete(toBeDeleted);

                        break;
                    case HOUSEMAID:
                        Logger.getLogger(Transaction.class.getName()).log(Level.SEVERE, "MMM: {0}", this.getHousemaids() == null ? 0 : this.getHousemaids().size());
                        deletionRepository = Setup.getRepository(HousemaidTransactionRepository.class);
                        query = new SelectQuery(HousemaidTransaction.class);
                        query.filterBy("transaction.id", "=", old.getId());
                        toBeDeleted = query.execute();
                        deletionRepository.delete(toBeDeleted);

                        break;
                    case OFFICE_STAFF:
                        Logger.getLogger(Transaction.class.getName()).log(Level.SEVERE, "MMM: {0}", this.getOfficeStaffs() == null ? 0 : this.getOfficeStaffs().size());
                        deletionRepository = Setup.getRepository(OfficestaffTransactionRepository.class);
                        query = new SelectQuery(OfficeStaffTransaction.class);
                        query.filterBy("transaction.id", "=", old.getId());
                        toBeDeleted = query.execute();
                        deletionRepository.delete(toBeDeleted);

                        break;
                    case FREEDOM_OPERATOR:
                        Logger.getLogger(Transaction.class.getName()).log(Level.SEVERE, "MMM: {0}", this.getFreedomOperators() == null ? 0 : this.getFreedomOperators().size());
                        deletionRepository = Setup.getRepository(FreedomOperatorTransactionRepository.class);
                        query = new SelectQuery(FreedomOperatorTransaction.class);
                        query.filterBy("transaction.id", "=", old.getId());
                        toBeDeleted = query.execute();
                        deletionRepository.delete(toBeDeleted);

                        break;
                    case APPLICANT:
                        Logger.getLogger(Transaction.class.getName()).log(Level.SEVERE, "MMM: {0}", this.getApplicants() == null ? 0 : this.getApplicants().size());
                        deletionRepository = Setup.getRepository(MaidsAtCandidateWATransactionRepository.class);
                        query = new SelectQuery(MaidsAtCandidateWATransaction.class);
                        query.filterBy("transaction.id", "=", old.getId());
                        toBeDeleted = query.execute();
                        deletionRepository.delete(toBeDeleted);

                        break;
                }
            }
        }

        // insert new

        Logger.getLogger(Transaction.class.getName()).log(Level.SEVERE, "MMM: {0}", this.getClients() == null ? 0 : this.getClients().size());
        Logger.getLogger(Transaction.class.getName()).log(Level.SEVERE, "MMM: {0}", this.getApplicants() == null ? 0 : this.getApplicants().size());
        switch (this.getTransactionType()) {
            case CLEANER:
            case CONTACT:
                contracts = new ArrayList();
                clients = new ArrayList();
                officeStaffs = new ArrayList();
                housemaids = new ArrayList();
                freedomOperators = new ArrayList();
                applicants = new ArrayList();

                if (sales != null)
                    for (SalesTransaction salesTransaction : sales) {
                        salesTransaction.setTransaction(this);
                    }

                break;
            case CLIENT:
            case PROSPECT:
                Logger.getLogger(Transaction.class.getName()).log(Level.SEVERE, "MMM: {0}", this.getClients() == null ? 0 : this.getClients().size());
                contracts = new ArrayList();
                officeStaffs = new ArrayList();
                housemaids = new ArrayList();
                sales = new ArrayList();
                freedomOperators = new ArrayList();
                applicants = new ArrayList();

                if (clients != null)
                    for (ClientTransaction clientTransaction : clients) {
                        clientTransaction.setTransaction(this);
                    }

                break;
            case CONTRACT:
                clients = new ArrayList();
                officeStaffs = new ArrayList();
                housemaids = new ArrayList();
                sales = new ArrayList();
                freedomOperators = new ArrayList();
                applicants = new ArrayList();

                if (contracts != null)
                    for (ContractTransaction contractTransaction : contracts) {
                        contractTransaction.setTransaction(this);
                    }

                break;
            case HOUSEMAID:
                clients = new ArrayList();
                contracts = new ArrayList();
                officeStaffs = new ArrayList();
                sales = new ArrayList();
                freedomOperators = new ArrayList();
                applicants = new ArrayList();

                if (housemaids != null)
                {
                    //MC-109 sync if only one
                    if (housemaids.size() == 1) {
                        housemaids.get(0).setAmount(this.getAmount());
                    }

                    // validate sum
                    double totalHousemaidAmount = housemaids.stream()
                            .mapToDouble(ht -> {
                                return ht.getAmount() != null ? ht.getAmount() : 0.0;
                            })
                            .sum();

                    if (Math.abs(totalHousemaidAmount - this.getAmount()) > EPSILON) {
                        throw new RuntimeException("The total of the assigned amounts must match the main transaction amount.");
                    }

                    for (HousemaidTransaction housemaidTransaction : housemaids) {
                        housemaidTransaction.setTransaction(this);
                    }
                }
                break;
            case OFFICE_STAFF:
                clients = new ArrayList();
                contracts = new ArrayList();
                housemaids = new ArrayList();
                sales = new ArrayList();
                freedomOperators = new ArrayList();
                applicants = new ArrayList();

                if (officeStaffs != null)
                    for (OfficeStaffTransaction officeStaffTransaction : officeStaffs) {
                        officeStaffTransaction.setTransaction(this);
                    }

                break;
            case FREEDOM_OPERATOR:
                clients = new ArrayList();
                contracts = new ArrayList();
                housemaids = new ArrayList();
                sales = new ArrayList();
                officeStaffs = new ArrayList();
                applicants = new ArrayList();

                if (freedomOperators != null)
                    for (FreedomOperatorTransaction freedomOperatorTransaction : freedomOperators) {
                        freedomOperatorTransaction.setTransaction(this);
                    }

                break;
            case APPLICANT:
                clients = new ArrayList();
                contracts = new ArrayList();
                housemaids = new ArrayList();
                sales = new ArrayList();
                officeStaffs = new ArrayList();
                freedomOperators = new ArrayList();

                if (applicants != null)
                    for (MaidsAtCandidateWATransaction applicantTransaction : applicants) {
                        applicantTransaction.setTransaction(this);
                    }

                break;
            case UNKNOWN:
                clients = new ArrayList();
                contracts = new ArrayList();
                housemaids = new ArrayList();
                sales = new ArrayList();
                officeStaffs = new ArrayList();
                freedomOperators = new ArrayList();
                applicants = new ArrayList();
                break;

        }


//        if (!(clients instanceof PersistentBag)) {
//            List<ClientTransaction> clientTransactionList = new ArrayList(clients);
//            clients.clear();
//            for (ClientTransaction clientTransaction : clientTransactionList) {
//                if (clientTransaction.isNewInstance())
//                    clientTransaction.setReconciliationTransaction(this);
//                else
//                    clientTransaction = Setup.getRepository(ClientTransactionRepository.class).
//                            getOne(clientTransaction.getId());
//
//                clients.add(clientTransaction);
//            }
//        }
//
//        if (save)
//            transactionRepository.save(this);
//        orphanRemoval();
    }

    private void insertTransactionDetails() {
        if (this.accrual == null || !this.accrual) {
            if (!isNewInstance()) {
                TransactionDetailsRepository transactionDetailsRepository = Setup.getRepository(TransactionDetailsRepository.class);
                SelectQuery<TransactionDetails> query = new SelectQuery(TransactionDetails.class);
                query.filterBy("transaction.id", "=", this.getId());
                List toBeDeleted = query.execute();
                if (toBeDeleted != null && !toBeDeleted.isEmpty())
                    transactionDetailsRepository.delete(toBeDeleted);
                this.transactionDetails = new ArrayList();
            }
            return;
        }

        if (this.accrualFromDate == null || this.accrualToDate == null)
            throw new RuntimeException("(Accrual From Date) and (Accrual To Date) must not be null");
        if (!this.accrualToDate.after(this.accrualFromDate))
            throw new RuntimeException("(Accrual From Date) should be before (Accrual To Date).");

        if (!isNewInstance()) {
            HistorySelectQuery<Transaction> historySelectQuery = new HistorySelectQuery(Transaction.class);
            historySelectQuery.filterBy("id", "=", this.getId());
            historySelectQuery.sortBy("lastModificationDate", false, true);
            historySelectQuery.setLimit(1);
            List<Transaction> oldTransactions = historySelectQuery.execute();
            if (oldTransactions != null && !oldTransactions.isEmpty()) {
                Transaction old = oldTransactions.get(0);

                if (!this.getAmount().equals(old.getAmount()) || !this.getAccrualFromDate().equals(old.getAccrualFromDate())
                        || !this.getAccrualToDate().equals(old.getAccrualToDate())) {
                    TransactionDetailsRepository transactionDetailsRepository = Setup.getRepository(TransactionDetailsRepository.class);
                    SelectQuery<TransactionDetails> query = new SelectQuery(TransactionDetails.class);
                    query.filterBy("transaction.id", "=", this.getId());
                    List toBeDeleted = query.execute();
                    transactionDetailsRepository.delete(toBeDeleted);
                } else return;
            }
        }

        this.transactionDetails = new ArrayList();

        LocalDate start = this.accrualFromDate.toLocalDate(),
                end = this.accrualToDate.toLocalDate();

        LocalDate next = start.minusMonths(1);

        Integer index = 0;
        int diffInMonth = Period.between(start, end).getMonths();
        Double average = this.amount / (diffInMonth + 1);

        while ((next = next.plusMonths(1)).isBefore(end.plusMonths(1))) {
            TransactionDetails transactionDetails = new TransactionDetails();
            transactionDetails.setAccrualDate(Date.valueOf(next.withDayOfMonth(1)));
            transactionDetails.setAverageAmount(average);
            transactionDetails.setTransactionAmount(index == 0 ? this.amount : 0);
            transactionDetails.setProfitAdjustment(transactionDetails.getAverageAmount() - transactionDetails.getTransactionAmount());
            transactionDetails.setTransaction(this);

            this.transactionDetails.add(transactionDetails);
            index++;
        }
    }
//    private void orphanRemoval() {
//        ClientTransactionRepository clientTransactionRepository = Setup.getRepository(ClientTransactionRepository.class);
//        SelectQuery<ClientTransaction> query = new SelectQuery(ClientTransaction.class);
//        query.filterBy("transaction.id", "=", this.getId());
//        List<ClientTransaction> clientTransactionList = query.execute();
//        List<ClientTransaction> toBeDeleted = new ArrayList();
//
//        for (ClientTransaction clientTransaction : clientTransactionList)
//            if (!this.clients.contains(clientTransaction))
//                toBeDeleted.add(clientTransaction);
//
//        clientTransactionRepository.delete(toBeDeleted);
//    }


    public void setPaymentOrder(PaymentOrder paymentOrder) {
        this.paymentOrder = paymentOrder;
    }

    public PaymentOrder getPaymentOrder() {
        if (this.paymentOrderId != null) {
            PaymentOrderRepository paymentOrderRepository =
                    Setup.getRepository(PaymentOrderRepository.class);
            this.paymentOrder = paymentOrderRepository.findOne(paymentOrderId);
        }

        return this.paymentOrder;
    }

    public Long getPaymentOrderId() {
        return paymentOrderId;
    }

    public void setPaymentOrderId(Long paymentOrderId) {
        this.paymentOrderId = paymentOrderId;
    }

    public void setMissingTaxInvoice(Boolean missingTaxInvoice) {
        this.missingTaxInvoice = missingTaxInvoice;
    }

    public Boolean getMissingTaxInvoice() {
        return missingTaxInvoice;
    }

    public boolean isCreationTriggeredAutomatically() {
        return creationTriggeredAutomatically;
    }

    public void setCreationTriggeredAutomatically(boolean creationTriggeredAutomatically) {
        this.creationTriggeredAutomatically = creationTriggeredAutomatically;
    }

    public boolean isDoneByCoo() {
        return doneByCoo;
    }

    public void setDoneByCoo(boolean doneByCoo) {
        this.doneByCoo = doneByCoo;
    }

    public Long getExpensePaymentId() {
        return expensePaymentId;
    }

    public void setExpensePaymentId(Long expensePaymentId) {
        this.expensePaymentId = expensePaymentId;
    }

    @Transient
    private CooQuestion.QuestionedPage cooQuestionedPage;

    public CooQuestion.QuestionedPage getCooQuestionedPage() {
        return cooQuestionedPage;
    }

    public void setCooQuestionedPage(CooQuestion.QuestionedPage cooQuestionedPage) {
        this.cooQuestionedPage = cooQuestionedPage;
    }

    @JsonIgnore
    public List<CooQuestion> getCooQuestions() {
        if (this.cooQuestionedPage == null) return new ArrayList();

        return Setup.getRepository(CooQuestionRepository.class).findByRelatedEntityAndQuestionedPage(this, this.cooQuestionedPage);
    }

    @JsonIgnore
    public boolean isAllQuestionsAnswered() {
        if (this.cooQuestionedPage == null) return false;

        List<CooQuestion> cooQuestions = getCooQuestions();

        return !cooQuestions.isEmpty() && !cooQuestions.stream().anyMatch(q -> !q.isAnswered());
    }

    @JsonIgnore
    public boolean isOneQuestionAnswered() {
        if (this.cooQuestionedPage == null) return false;

        return getCooQuestions().stream().filter(q -> q.isAnswered()).count() >= 1l;
    }

    @JsonIgnore
    public boolean isNoneQuestionAnswered() {
        if (this.cooQuestionedPage == null) return false;

        return getCooQuestions().stream().filter(q -> q.isAnswered()).count() == 0l;
    }

    public ContractModificationExpense getContractModificationExpense() { return contractModificationExpense; }

    public void setContractModificationExpense(ContractModificationExpense contractModificationExpense) {
        this.contractModificationExpense = contractModificationExpense;
    }

    public ModifyVisaRequestExpense getModifyVisaRequestExpense() { return modifyVisaRequestExpense; }

    public void setModifyVisaRequestExpense(ModifyVisaRequestExpense modifyVisaRequestExpense) {
        this.modifyVisaRequestExpense = modifyVisaRequestExpense;
    }

    public String getBucketPreBalance() { return bucketPreBalance; }

    public void setBucketPreBalance(String bucketPreBalance) { this.bucketPreBalance = bucketPreBalance; }

    public String getBucketBalance() { return bucketBalance; }

    public void setBucketBalance(String bucketBalance) { this.bucketBalance = bucketBalance; }

    public Long getContractId() {
        Payment payment = this.getPayment();
        return payment != null && payment.getContract() != null ? payment.getContract().getId() : null;
    }

    public Long getClientId() {
        Payment payment = this.getPayment();
        return payment != null && payment.getContract() != null && payment.getContract().getClient() != null ? payment.getContract().getClient().getId() : null;
    }

    public UnpaidLeaveExpense getUnpaidLeaveExpense() {
        return unpaidLeaveExpense;
    }

    public void setUnpaidLeaveExpense(UnpaidLeaveExpense unpaidLeaveExpense) {
        this.unpaidLeaveExpense = unpaidLeaveExpense;
    }

    public Long getVisaStatementTransactionId() {
        return visaStatementTransactionId;
    }

    public void setVisaStatementTransactionId(Long visaStatementTransactionId) {
        this.visaStatementTransactionId = visaStatementTransactionId;
    }

    public ModifyPersonInformationExpense getModifyPersonInformationExpense() {
        return modifyPersonInformationExpense;
    }

    public void setModifyPersonInformationExpense(ModifyPersonInformationExpense modifyPersonInformationExpense) {
        this.modifyPersonInformationExpense = modifyPersonInformationExpense;
    }
}