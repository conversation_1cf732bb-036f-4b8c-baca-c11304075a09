package com.magnamedia.controller;

import com.magnamedia.core.annotation.Searchable;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.TransactionPostingRule;
import com.magnamedia.entity.projection.TransactionPostingRuleProjection;
import com.magnamedia.repository.TransactionPostingRuleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR> masod <<EMAIL>>
 *         Created on Dec 21, 2019
 *         ACC-1230
 */
@RestController
@RequestMapping("/transactionpostingrules")
public class TransactionPostingRuleController extends BaseRepositoryController<TransactionPostingRule> {

    @Autowired
    private TransactionPostingRuleRepository transactionPostingRuleRepository;

    @Autowired
    private ProjectionFactory projectionFactory;

//    @PreAuthorize("hasPermission('transactionpostingrules','advancesearch')")
//    @RequestMapping(value = "/advancesearch/page",
//            method = RequestMethod.POST)
//    public ResponseEntity<?> advanceSearch(
//            @RequestBody List<FilterItem> filters,
//            Pageable pageable) {
//        SelectQuery<TransactionPostingRule> query =
//                new SelectQuery(TransactionPostingRule.class);
//        //Process Filters
//        SelectFilter selectFilter = new SelectFilter();
//        for (FilterItem filter : filters) {
//            selectFilter = selectFilter.and(filter.getSelectFilter(TransactionPostingRule.class));
//        }
//        query.filterBy(selectFilter);
//
//        //Sorting
//        if (pageable.getSort() != null) {
//            for (Sort.Order order : pageable.getSort()) {
//                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
//            }
//        } else {
//            query.sortBy("creationDate", false, false);
//        }
//
//        return new ResponseEntity<>(
//                query.execute(pageable),
//                HttpStatus.OK);
//    }

    @PreAuthorize("hasPermission('transactionpostingrules','advancesearch')")
    @RequestMapping(value = "/advancesearch/page",
            method = RequestMethod.GET)
    @Searchable(fieldName = "typeOfPayment",
            label = "Type of Payment",
            entity = TransactionPostingRule.class,
            apiKey = "transactionpostingrules_management",
            valuesApi = "/public/picklist/items/TypeOfPayment")
    public ResponseEntity<?> advanceSearch(
            Pageable pageable) {

        SelectQuery<TransactionPostingRule> query = new SelectQuery(TransactionPostingRule.class);
        query.filterBy(CurrentRequest.getSearchFilter());

        return new ResponseEntity(
                query.execute(pageable).map(obj
                        -> projectionFactory.createProjection(
                        TransactionPostingRuleProjection.class, obj)),
                HttpStatus.OK);
    }

    //Jirra ACC-2705
    @PreAuthorize("hasPermission('transactionpostingrules','activate-rule')")
    @RequestMapping(value = "/activate-rule/{id}",
            method = RequestMethod.POST)
    public ResponseEntity activateRule(@PathVariable("id") TransactionPostingRule transactionPostingRule) {
        int result = 1;
        HttpStatus httpStatus = HttpStatus.OK;

        if (transactionPostingRule.getExpense() != null && (transactionPostingRule.getExpense().getDisabled() || transactionPostingRule.getExpense().getDeleted())) {
            result = 0;
            httpStatus = HttpStatus.BAD_REQUEST;
        } else {
            transactionPostingRule.setActive(true);
            transactionPostingRuleRepository.save(transactionPostingRule);
        }

        return new ResponseEntity(result, httpStatus);
    }

    //Jirra ACC-2705
    @PreAuthorize("hasPermission('transactionpostingrules','deactivate-rule')")
    @RequestMapping(value = "/deactivate-rule/{id}",
            method = RequestMethod.POST)
    public ResponseEntity deActivateRule(@PathVariable("id") TransactionPostingRule transactionPostingRule) {
        transactionPostingRule.setActive(false);
        transactionPostingRuleRepository.save(transactionPostingRule);

        return new ResponseEntity("Done", HttpStatus.OK);
    }

    @Override
    public BaseRepository<TransactionPostingRule> getRepository() {
        return transactionPostingRuleRepository;
    }
}
