package com.magnamedia.entity.projection;

import org.springframework.beans.factory.annotation.Value;

import java.sql.Date;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on June 30, 2020
 *         Jirra ACC-2154
 */

public interface ChequeTransactionCSVProjection {

    @Value("#{(target.getPayment() != null && target.getPayment().getBankName() != null )? target.getPayment().getBankName().getName():''}")
    String getBankName();

    @Value("#{(target.getPayment() != null)? target.getPayment().getChequeName():''}")
    String getChequeName();

    @Value("#{(target.getPayment() != null)? target.getPayment().getChequeNumber():''}")
    String getChequeNumber();

    @Value("#{(target.getPayment() != null && target.getPayment().getStatus() != null )? target.getPayment().getStatus().getValue():''}")
    String getStatus();

    @Value("#{(target.getPayment() != null)? target.getPayment().getCreationDate():null}")
    java.util.Date getCreationDate();

    @Value("#{(target.getPayment() != null )? target.getPayment().getId():''}")
    Long getPaymentName();

    @Value("#{(target.getPayment() != null && target.getPayment().getContract()!=null)?"
            + "(target.getPayment().getContract().getId()):''}")
    String getContractName();

    @Value("#{(target.getPayment() != null && target.getPayment().getContract()!=null &&"
            + "target.getPayment().getContract().getClient()!=null)?"
            + "(target.getPayment().getContract().getClient().getName()):''}")
    String getClientName();

    @Value("#{(target.getPayment()!=null && target.getPayment().getContract()!=null &&"
            + "target.getPayment().getContract().getContractProspectType()!=null)?"
            + "(target.getPayment().getContract().getContractProspectType().getName()):''}")
    String getContractProspectType();

    @Value("#{(target.getPayment()!=null && target.getPayment().getTypeOfPayment()!=null)?"
            + "(target.getPayment().getTypeOfPayment().getName()):''}")
    String getTypeOfPayment();

    @Value("#{(target.getPayment()!=null && target.getPayment().getReasonOfBouncingCheque()!=null)?"
            + "(target.getPayment().getReasonOfBouncingCheque().getName()):''}")
    String getReasonOfBouncingCheque();

    @Value("#{(target.getPayment()!=null && target.getPayment().getMethodOfPayment()!=null)?"
            + "(target.getPayment().getMethodOfPayment().getValue()):''}")
    String getMethodOfPayment();

    @Value("#{(target.getPayment()!=null && target.getPayment().getAmountOfPayment()!=null)?"
            + "(target.getPayment().getAmountOfPayment()):''}")
    Double getAmountOfPayment();

    @Value("#{(target.getPayment()!=null )?"
            + "(target.getPayment().getDateOfPayment()): null }")
    Date getDateOfPayment();

    @Value("#{(target.getPayment()!=null && target.getPayment().getVatPaidByClient()!=null)?"
            + "(target.getPayment().getVatPaidByClient()):''}")
    Boolean getVatPaidByClient();

    String getNote();

}