package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Payment;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.service.BouncingFlowService;
import com.magnamedia.service.payment.PaymentDeletionRules;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jun 13, 2020
 *         Jirra ACC-2055
 */

// bounced sub flow -> CC clients with Cancelled/Expired contracts bounced payment
// if contract is cancelled, we do not want money -> change status to delete or
@BusinessRule(entity = Payment.class, events = {BusinessEvent.AfterCreate, BusinessEvent.AfterUpdate},
        fields = {"id", "status", "contract.id", "methodOfPayment", "requiredForUnfitToWork", "replaced", "dateOfPayment"})
//, "pBF4BrChecked" to add
public class PaymentBouncingFlowBR4 implements BusinessAction<Payment> {

    private static final Logger logger =
            Logger.getLogger(PaymentBouncingFlowBR3.class.getName());
    private static final String prefix = "MMM ";

    @Override
    public boolean validate(Payment entity, BusinessEvent event) {
        logger.log(Level.SEVERE, "PaymentBouncingFlowBR4 Validation.");
        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);

//        if (entity.ispBF4BrChecked()) return false;

        logger.log(Level.SEVERE, prefix + "payment id: " + (entity.isNewInstance() ? "new instance" : entity.getId()));
        logger.log(Level.SEVERE, prefix + "payment status: " + entity.getStatus());
        logger.log(Level.SEVERE, prefix + "payment method: " + entity.getMethodOfPayment());

        HistorySelectQuery<Payment> historyQuery = new HistorySelectQuery(Payment.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.filterByChanged("status");
        historyQuery.sortBy("lastModificationDate", false, true);
        historyQuery.setLimit(1);

        List<Payment> oldPayments = historyQuery.execute();
        Payment old = null;

        if (oldPayments != null && !oldPayments.isEmpty()) {
            old = oldPayments.get(0);
        }

        if (entity.getStatus() == null || entity.getMethodOfPayment() == null)
            return false;

        if (entity.getContract() == null || entity.getContract().getId() == null) return false;
        Contract contract = contractRepository.findOne(entity.getContract().getId());
        if (contract == null) return false;

        logger.log(Level.SEVERE, prefix + "contract ID: " + contract.getId());

        BouncingFlowService bouncingFlowService = Setup.getApplicationContext().getBean(BouncingFlowService.class);
        if (bouncingFlowService.isBouncingFlowStopped(entity)) {
            logger.log(Level.SEVERE, "Bouncing Flow is Stopped");
            return false;
        }

        return contract.getContractProspectType().getCode().equals(PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE)) &&
                Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED).contains(contract.getStatus()) &&
                entity.getStatus().equals(PaymentStatus.BOUNCED) &&
                (old == null || old.getStatus() == null || !old.getStatus().equals(PaymentStatus.BOUNCED));
    }

    @Override
    public Map execute(Payment entity, BusinessEvent even) {
        logger.log(Level.SEVERE, "PaymentBouncingFlowBR4 Execution.");

        Map map = new HashMap();
//        map.put("pBF4BrChecked", true);
        map.putAll(Setup.getApplicationContext()
                .getBean(PaymentDeletionRules.class)
                .handlePayment(entity));
        return map;
    }
}
