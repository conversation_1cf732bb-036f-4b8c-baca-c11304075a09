package com.magnamedia.entity.projection;

import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.TransactionEntityType;
import com.magnamedia.module.type.VatType;
import org.springframework.beans.factory.annotation.Value;

import java.sql.Date;
import java.util.List;
import java.util.Map;

public interface TransactionProjectionAcc7274 {
    Long getId();

    java.util.Date getPnlValueDate();

    @Value("#{(target.getFromBucket()!=null)?{id:target.getFromBucket().getId(), name:target.getFromBucket().getName(), code:target.getFromBucket().getCode(),cardNumber:target.getFromBucket().getCardNumber()}:{id:\"\"}}")
    Map<?, ?> getFromBucket();

    @Value("#{(target.getRevenue()!=null)?{id:target.getRevenue().getId(), name:target.getRevenue().getName(), code:target.getRevenue().getCode()}:{id:\"\"}}")
    Map<?, ?> getRevenue();

    @Value("#{(target.getExpense()!=null)?{id:target.getExpense().getId(), name:target.getExpense().getNameLabel(), code:target.getExpense().getCodeLabel()}:{id:\"\"}}")
    Map<?, ?> getExpense();

    @Value("#{(target.getToBucket()!=null)?{id:target.getToBucket().getId(), name:target.getToBucket().getName(), code:target.getToBucket().getCode(),cardNumber:target.getToBucket().getCardNumber()}:{id:\"\"}}")
    Map<?, ?> getToBucket();

    String getDescription();

    PaymentMethod getPaymentType();

    Double getAmount();

    Date getDate();

    java.util.Date getCreationDate();

    Boolean getPreviouslyUnknown();

    TransactionEntityType getTransactionType();

    Boolean getIsDescriptionSecured();

    List<Attachment> getAttachments();

    //Jirra ACC-751
    VatType getVatType();

    Double getVatAmount();

    //Jirra ACC-960
    PicklistItem getLicense();

    Long getPaymentId();
    Long getContractId();
    Long getClientId();
}
