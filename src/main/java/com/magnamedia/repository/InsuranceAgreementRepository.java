package com.magnamedia.repository;

import com.magnamedia.core.entity.workflow.WorkFlowTaskHistory;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.InsuranceAgreement;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;

import org.springframework.data.repository.query.Param;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Sep 20, 2018
 */
@Repository
public interface InsuranceAgreementRepository extends BaseRepository<InsuranceAgreement> {

    @Query("select ia from InsuranceAgreement ia " +
            "where ia.startDate <= ?1 and ia.endDate >= ?2 " +
            "order by ia.endDate desc")
    List<InsuranceAgreement> findTopByStartDateLessThanEqualAndEndDateGreaterThanEqual(Date startDate, Date endDate, Pageable pageable);

    InsuranceAgreement findTopByOrderByEndDateDesc();

    // ACC-818 ACC-1208
    @Query(nativeQuery = true,
            value = "SELECT H.ID, H.NAME, WH.TASK_MOVE_OUT_DATE, NR.WORKER_TYPE_ID, NR.NEW_EID_NUMBER, H.PASSPORT_NUMBER, H.LANDED_IN_DUBAI_DATE " +
                    "FROM HOUSEMAIDS H INNER JOIN NEWREQUESTS NR ON NR.HOUSEMAID_ID = H.ID " +
                    "  AND NR.ID = (SELECT R.ID FROM NEWREQUESTS R INNER JOIN WORKFLOWTASKHISTORYS RH ON RH.TASK_ID = R.ID AND RH.TASK_NAME = 'Prepare insurance application' " +
                    "  AND RH.TASK_MOVE_OUT_DATE IS NOT NULL AND RH.TYPE = 'com.magnamedia.entity.NewRequest' " +
                    "  WHERE R.HOUSEMAID_ID = H.ID ORDER BY R.CREATION_DATE DESC LIMIT 1) " +
                    "INNER JOIN WORKFLOWTASKHISTORYS WH ON WH.TASK_ID = NR.ID AND WH.TASK_NAME = 'Prepare insurance application' AND " +
                    "WH.TASK_MOVE_OUT_DATE IS NOT NULL AND WH.TYPE = 'com.magnamedia.entity.NewRequest' " +
                    "  AND WH.ID = (SELECT W.ID FROM WORKFLOWTASKHISTORYS W WHERE W.TASK_ID = NR.ID AND W.TASK_NAME = 'Prepare insurance application' " +
                    "  AND W.TYPE = 'com.magnamedia.entity.NewRequest' " +
                    "  AND W.TASK_MOVE_OUT_DATE IS NOT NULL ORDER BY W.TASK_MOVE_OUT_DATE ASC LIMIT 1) " +
                    "WHERE (:maidName IS NULL OR (:maidName IS NOT NULL AND ((H.NAME IS NOT NULL AND H.NAME LIKE :maidName) OR (H.PASSPORT_NUMBER IS NOT NULL AND H.PASSPORT_NUMBER LIKE :maidName)))) " +
                    "  AND (:t1 IS NULL OR (:t1 IS NOT NULL AND WH.TASK_MOVE_OUT_DATE >= :t1)) " +
                    "  AND (:t2 IS NULL OR (:t2 IS NOT NULL AND WH.TASK_MOVE_OUT_DATE < :t2)) " +
                    "UNION " +
                    "SELECT H.ID, H.NAME, NR.REACTIVATION_RECEIVED_EMAIL_DATE, NR.WORKER_TYPE_ID, NR.NEW_EID_NUMBER, H.PASSPORT_NUMBER, H.LANDED_IN_DUBAI_DATE " +
                    "FROM HOUSEMAIDS H INNER JOIN NEWREQUESTS NR ON NR.HOUSEMAID_ID = H.ID " +
                    "  AND NR.ID = (SELECT R.ID FROM NEWREQUESTS R WHERE R.HOUSEMAID_ID = H.ID AND R.REACTIVATION_RECEIVED_EMAIL_DATE IS NOT NULL ORDER BY R.CREATION_DATE DESC LIMIT 1) " +
                    "WHERE (:maidName IS NULL OR (:maidName IS NOT NULL AND ((H.NAME IS NOT NULL AND H.NAME LIKE :maidName) OR (H.PASSPORT_NUMBER IS NOT NULL AND H.PASSPORT_NUMBER LIKE :maidName)))) " +
                    "  AND (:t1 IS NULL OR (:t1 IS NOT NULL AND NR.REACTIVATION_RECEIVED_EMAIL_DATE >= :t1)) " +
                    "  AND (:t2 IS NULL OR (:t2 IS NOT NULL AND NR.REACTIVATION_RECEIVED_EMAIL_DATE < :t2))")
    List<Object[]> findMaidDebitNoteList(@Param("t1") Timestamp t1, @Param("t2") Timestamp t2, @Param("maidName") String maidName);

    @Query(nativeQuery = true,
            value = "SELECT H.ID, H.NAME, WH.TASK_MOVE_OUT_DATE, NR.WORKER_TYPE_ID, NR.NEW_EID_NUMBER, H.PASSPORT_NUMBER, H.DATE_OF_TERMINATION " +
                    "FROM HOUSEMAIDS H INNER JOIN CANCELREQUESTS CR ON CR.HOUSEMAID_ID = H.ID " +
                    "  AND CR.ID = (SELECT R.ID FROM CANCELREQUESTS R INNER JOIN WORKFLOWTASKHISTORYS RH ON RH.TASK_ID = R.ID AND RH.TASK_NAME = 'Cancel Insurance' " +
                    "  AND RH.TASK_MOVE_OUT_DATE IS NOT NULL AND RH.TYPE = 'com.magnamedia.entity.CancelRequest' WHERE R.HOUSEMAID_ID = H.ID ORDER BY R.CREATION_DATE DESC LIMIT 1) " +
                    "INNER JOIN NEWREQUESTS NR ON NR.ID = CR.NEW_REQUEST_ID " +
                    "INNER JOIN WORKFLOWTASKHISTORYS WH ON WH.TASK_ID = CR.ID AND WH.TASK_NAME = 'Cancel Insurance' AND " +
                    "WH.TASK_MOVE_OUT_DATE IS NOT NULL AND WH.TYPE = 'com.magnamedia.entity.CancelRequest' " +
                    "  AND WH.ID = (SELECT W.ID FROM WORKFLOWTASKHISTORYS W WHERE W.TASK_ID = CR.ID AND W.TASK_NAME = 'Cancel Insurance' " +
                    "  AND W.TASK_MOVE_OUT_DATE IS NOT NULL AND W.TYPE = 'com.magnamedia.entity.CancelRequest' ORDER BY W.TASK_MOVE_OUT_DATE ASC LIMIT 1) " +
                    "WHERE (:maidName IS NULL OR (:maidName IS NOT NULL AND ((H.NAME IS NOT NULL AND H.NAME LIKE :maidName) OR (H.PASSPORT_NUMBER IS NOT NULL AND H.PASSPORT_NUMBER LIKE :maidName)))) " +
                    "  AND (:t1 IS NULL OR (:t1 IS NOT NULL AND WH.TASK_MOVE_OUT_DATE >= :t1)) " +
                    "  AND (:t2 IS NULL OR (:t2 IS NOT NULL AND WH.TASK_MOVE_OUT_DATE < :t2))")
    List<Object[]> findMaidCreditNoteList(@Param("t1") Timestamp t1, @Param("t2") Timestamp t2, @Param("maidName") String maidName);

    // ACC-1208
    @Query(nativeQuery = true,
            value = "SELECT OS.ID, OS.NAME, WH.TASK_MOVE_OUT_DATE, 'Office Staff', NR.NEW_EID_NUMBER, OS.PASSPORT_ID, OS.STARTING_DATE " +
                    "FROM OFFICESTAFFS OS INNER JOIN NEWREQUESTS NR ON NR.OFFICE_STAFF_ID = OS.ID " +
                    "  AND NR.ID = (SELECT R.ID FROM NEWREQUESTS R INNER JOIN WORKFLOWTASKHISTORYS RH ON RH.TASK_ID = R.ID AND RH.TASK_NAME = 'Prepare insurance application' " +
                    "  AND RH.TASK_MOVE_OUT_DATE IS NOT NULL AND RH.TYPE = 'com.magnamedia.entity.NewRequest' " +
                    "  WHERE R.OFFICE_STAFF_ID = OS.ID ORDER BY R.CREATION_DATE DESC LIMIT 1) " +
                    "INNER JOIN WORKFLOWTASKHISTORYS WH ON WH.TASK_ID = NR.ID AND WH.TASK_NAME = 'Prepare insurance application' AND " +
                    "WH.TASK_MOVE_OUT_DATE IS NOT NULL AND WH.TYPE = 'com.magnamedia.entity.NewRequest' " +
                    "  AND WH.ID = (SELECT W.ID FROM WORKFLOWTASKHISTORYS W WHERE W.TASK_ID = NR.ID AND W.TASK_NAME = 'Prepare insurance application' " +
                    "  AND W.TYPE = 'com.magnamedia.entity.NewRequest' AND W.TASK_MOVE_OUT_DATE IS NOT NULL ORDER BY W.TASK_MOVE_OUT_DATE ASC LIMIT 1) " +
                    "WHERE (:maidName IS NULL OR (:maidName IS NOT NULL AND ((OS.NAME IS NOT NULL AND OS.NAME LIKE :maidName) OR (OS.PASSPORT_ID IS NOT NULL AND OS.PASSPORT_ID LIKE :maidName)))) " +
                    "  AND (:t1 IS NULL OR (:t1 IS NOT NULL AND WH.TASK_MOVE_OUT_DATE >= :t1)) " +
                    "  AND (:t2 IS NULL OR (:t2 IS NOT NULL AND WH.TASK_MOVE_OUT_DATE < :t2)) " +
                    "UNION " +
                    "SELECT OS.ID, OS.NAME, NR.REACTIVATION_RECEIVED_EMAIL_DATE, 'Office Staff', NR.NEW_EID_NUMBER, OS.PASSPORT_ID, OS.STARTING_DATE " +
                    "FROM OFFICESTAFFS OS INNER JOIN NEWREQUESTS NR ON NR.OFFICE_STAFF_ID = OS.ID " +
                    "  AND NR.ID = (SELECT R.ID FROM NEWREQUESTS R WHERE R.OFFICE_STAFF_ID = OS.ID AND R.REACTIVATION_RECEIVED_EMAIL_DATE IS NOT NULL ORDER BY R.CREATION_DATE DESC LIMIT 1) " +
                    "WHERE (:maidName IS NULL OR (:maidName IS NOT NULL AND ((OS.NAME IS NOT NULL AND OS.NAME LIKE :maidName) OR (OS.PASSPORT_ID IS NOT NULL AND OS.PASSPORT_ID LIKE :maidName)))) " +
                    "  AND (:t1 IS NULL OR (:t1 IS NOT NULL AND NR.REACTIVATION_RECEIVED_EMAIL_DATE >= :t1)) " +
                    "  AND (:t2 IS NULL OR (:t2 IS NOT NULL AND NR.REACTIVATION_RECEIVED_EMAIL_DATE < :t2))")
    List<Object[]> findOfficeStaffDebitNoteList(@Param("t1") Timestamp t1, @Param("t2") Timestamp t2, @Param("maidName") String maidName);

    @Query(nativeQuery = true,
            value = "SELECT OS.ID, OS.NAME, WH.TASK_MOVE_OUT_DATE, 'Office Staff', NR.NEW_EID_NUMBER, OS.PASSPORT_ID, OS.TERMINATION_DATE " +
                    "FROM OFFICESTAFFS OS INNER JOIN CANCELREQUESTS CR ON CR.OFFICE_STAFF_ID = OS.ID " +
                    "  AND CR.ID = (SELECT R.ID FROM CANCELREQUESTS R INNER JOIN WORKFLOWTASKHISTORYS RH ON RH.TASK_ID = R.ID AND RH.TASK_NAME = 'Cancel Insurance' " +
                    "  AND RH.TASK_MOVE_OUT_DATE IS NOT NULL AND RH.TYPE = 'com.magnamedia.entity.CancelRequest' WHERE R.OFFICE_STAFF_ID = OS.ID ORDER BY R.CREATION_DATE DESC LIMIT 1) " +
                    "INNER JOIN NEWREQUESTS NR ON NR.ID = CR.NEW_REQUEST_ID " +
                    "INNER JOIN WORKFLOWTASKHISTORYS WH ON WH.TASK_ID = CR.ID AND WH.TASK_NAME = 'Cancel Insurance' AND " +
                    "WH.TASK_MOVE_OUT_DATE IS NOT NULL AND WH.TYPE = 'com.magnamedia.entity.CancelRequest' " +
                    "  AND WH.ID = (SELECT W.ID FROM WORKFLOWTASKHISTORYS W WHERE W.TASK_ID = CR.ID AND W.TASK_NAME = 'Cancel Insurance' " +
                    "  AND W.TASK_MOVE_OUT_DATE IS NOT NULL AND W.TYPE = 'com.magnamedia.entity.CancelRequest' ORDER BY W.TASK_MOVE_OUT_DATE ASC LIMIT 1) " +
                    "WHERE (:maidName IS NULL OR (:maidName IS NOT NULL AND ((OS.NAME IS NOT NULL AND OS.NAME LIKE :maidName) OR (OS.PASSPORT_ID IS NOT NULL AND OS.PASSPORT_ID LIKE :maidName)))) " +
                    "  AND (:t1 IS NULL OR (:t1 IS NOT NULL AND WH.TASK_MOVE_OUT_DATE >= :t1)) " +
                    "  AND (:t2 IS NULL OR (:t2 IS NOT NULL AND WH.TASK_MOVE_OUT_DATE < :t2))")
    List<Object[]> findOfficeStaffCreditNoteList(@Param("t1") Timestamp t1, @Param("t2") Timestamp t2, @Param("maidName") String maidName);


    // ACC-1862
    @Query("select W " +
            "from WorkFlowTaskHistory W " +
            "INNER JOIN RenewRequest NR ON NR.id = W.taskId  " +
            "WHERE W.taskMoveOutDate IS NOT NULL " +
            "AND W.type like 'com.magnamedia.entity.RenewRequest' " +
            "AND W.taskName LIKE 'Upload Contract to Tasheel' "+
            "AND NR.id =  ?1 ")
    List<WorkFlowTaskHistory> findCompleteRenewRequest(Long renewRequestId);
}