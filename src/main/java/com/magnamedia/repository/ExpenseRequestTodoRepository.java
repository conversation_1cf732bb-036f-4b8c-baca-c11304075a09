package com.magnamedia.repository;

import com.magnamedia.core.entity.User;
import com.magnamedia.core.repository.workflow.WorkflowRepository;
import com.magnamedia.entity.Expense;
import com.magnamedia.entity.ExpenseRelatedTo;
import com.magnamedia.entity.LogisticsWorkOrder;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.module.type.ExpenseBeneficiaryType;
import com.magnamedia.module.type.ExpensePaymentMethod;
import com.magnamedia.module.type.ExpenseRequestType;
import com.magnamedia.workflow.type.ExpenseRequestStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 *         Created on Jan 18, 2021
 *         Jirra ACC-2913
 */
@Repository
public interface ExpenseRequestTodoRepository extends WorkflowRepository<ExpenseRequestTodo> {


    List<ExpenseRequestTodo> findByBeneficiaryIdAndPaymentMethodAndStatus(Long beneficiaryId, ExpensePaymentMethod invoiced, ExpenseRequestStatus pendingPayment);
    
    List<ExpenseRequestTodo> findByExpenseOrderByCreationDateAsc(Expense expense);
    List<ExpenseRequestTodo> findByExpensePayment(ExpensePayment expensePayment);

    List<ExpenseRequestTodo> findByExpenseAndCreationDateBetweenAndIdNotInOrderByCreationDateAsc(Expense expense, Date fromDate, Date toDate, List<Long> ids);
    List<ExpenseRequestTodo> findByExpenseAndCreationDateBetweenOrderByCreationDateAsc(Expense expense, Date fromDate, Date toDate);

    List<ExpenseRequestTodo> findByExpenseAndInvoiceNumberOrderByCreationDateAsc(Expense expense, String invoiceNumber);

    Page<ExpenseRequestTodo> findByTaskNameAndRelatedToType(String taskName, ExpenseRelatedTo.ExpenseRelatedToType relatedToType, Pageable pageable);

    Page<ExpenseRequestTodo> findByTaskNameAndRelatedToTypeAndExpense_RequestedFrom_CodeIn(String taskName, ExpenseRelatedTo.ExpenseRelatedToType relatedToType, List<String> codes, Pageable pageable);

    @Query("SELECT exR FROM ExpenseRequestTodo exR "
            + "JOIN exR.expense ex "
            + "LEFT JOIN exR.expense.requestedFrom rf "
            + "where exR.taskName = :taskName and (exR.relatedToType <> 'MAID' "
            + "or exR.expense.requestedFrom is null "
            + "or exR.expense.requestedFrom.code not in :codes) "
            + "and exR.linkedToFopRequest = false")
    Page<ExpenseRequestTodo> findByTaskNameAndExpenseAndNotRelatedToMaidOrRelatedToMaidAnd_RequestedFrom_CodeNotIn(@Param("taskName") String taskName, @Param("codes") List<String> codes, Pageable pageable);

    @Query("select todo.expense.caption, todo.creationDate, todo.amount, todo.loanAmount, todo.paymentMethod, todo.status, todo.notes " +
            "from ExpenseRequestTodo todo " +
            "where ((todo.relatedToId = ?1 and todo.relatedToType = 'MAID') or " +
                "(todo.beneficiaryId = ?1 and todo.beneficiaryType = 'MAID'))")
    List<Object[]> findByRelatedToIdAndRelatedToType(Long relatedToId);

    Page<ExpenseRequestTodo> findByTaskName(String taskName, Pageable pageable);

    List<ExpenseRequestTodo> findByTaskNameAndApproveHolder(String taskName, User user);

    List<ExpenseRequestTodo> findByBeneficiaryIdAndStatusIn(Long beneficiaryId, List<ExpenseRequestStatus> statuses);

    List<ExpenseRequestTodo> findByRelatedToIdAndStatusIn(Long relatedToId, List<ExpenseRequestStatus> statuses);

    List<ExpenseRequestTodo> findByPaymentMethodAndStatus(ExpensePaymentMethod invoiced, ExpenseRequestStatus pendingPayment);

    List<ExpenseRequestTodo> findByPaymentMethodAndStatusAndExpensePaymentIsNull(ExpensePaymentMethod invoiced, ExpenseRequestStatus pendingPayment);

    List<ExpenseRequestTodo> findByBeneficiaryIdAndExpenseToPostAndPaymentMethodAndStatus(Long beneficiaryId, Expense expense, ExpensePaymentMethod invoiced, ExpenseRequestStatus pendingPayment);

    //List<ExpenseRequestTodo> findByBeneficiaryIdAndExpenseToPostAndPaymentMethodAndStatusAndExpensePaymentIsNull(Long beneficiaryId, Expense expense, ExpensePaymentMethod invoiced, ExpenseRequestStatus pendingPayment);

    List<ExpenseRequestTodo> findByCovidLaserTestTaxiOrderAndExpenseRequestType(LogisticsWorkOrder logisticsWorkOrder, ExpenseRequestType expenseRequestType);

    @Query("select todo from ExpenseRequestTodo todo " +
            "join fetch todo.expense e "+
            "where ((todo.beneficiaryId = ?1 and todo.beneficiaryType = 'MAID') or " +
                "(todo.relatedToId = ?1 and todo.relatedToType = 'MAID')) and e.code = ?2")
    List<ExpenseRequestTodo> findByBeneficiaryIdAndBeneficiaryTypeAndExpense_Code(
            Long beneficiaryId, String expenseCode);
}
