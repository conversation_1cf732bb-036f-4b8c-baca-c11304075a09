package com.magnamedia.module;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.GPTTemplatePromptMessage;
import com.magnamedia.core.entity.Picklist;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.helper.chatai.ChatMessageRole;
import com.magnamedia.core.helper.chatai.OpenAiMessageContentType;

import java.util.ArrayList;
import java.util.List;

public class ChatAITemplateService {

    public static void createTemplates() {
        validateExtractedSignatureByGPT();
    }

    private static void validateExtractedSignatureByGPT() {

        GPTTemplatePromptMessage msg = new GPTTemplatePromptMessage(
                "Please answer with just 'YES' if this image include valid human signature else please answer with just 'NO'",
                ChatMessageRole.USER);
        msg.setType(OpenAiMessageContentType.text);

        GPTTemplatePromptMessage msg2 = new GPTTemplatePromptMessage("@img@",
                ChatMessageRole.USER);
        msg2.setType(OpenAiMessageContentType.image_file);

        List<GPTTemplatePromptMessage> messages = new ArrayList<>();
        messages.add(msg);
        messages.add(msg2);

        new Template.GPTTemplateBuilder()
                .template("gpt_validate_extracted_signature",
                        "recognize if an attachment contains valid signature",
                        "recognize if an attachment contains valid signature")
                .isConversational()
                .gptTemperature(0.0)
                .gptTopP(0.9)
                .gptModel(Setup.getOrCreateItem(Picklist.GPT_MODELS, "gpt-4-vision-preview"))
                .messages(messages)
                .build();
    }
}
