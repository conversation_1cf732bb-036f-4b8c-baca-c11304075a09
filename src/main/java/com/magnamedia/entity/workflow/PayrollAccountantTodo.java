package com.magnamedia.entity.workflow;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.controller.PayrollAccountantTodoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.entity.workflow.WorkflowEntity;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.workflow.FormField;
import com.magnamedia.entity.*;
import com.magnamedia.entity.maidsatv2.actions.employeragreement.SendRequestForApprovalAction;
import com.magnamedia.entity.payroll.logging.OfficeStaffPayrollLog;
import com.magnamedia.extra.StringUtils;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.NumberFormatter;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ExpenseBeneficiaryType;
import com.magnamedia.module.type.ExpenseApprovalMethod;
import com.magnamedia.module.type.SalaryCurrency;
import com.magnamedia.repository.CooQuestionRepository;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.repository.SalariesAccessRepository;
import com.magnamedia.repository.SupplierRepository;
import com.magnamedia.service.CurrencyExchangeSevice;
import com.magnamedia.workflow.type.PayrollAccountantTodoManagerAction;
import com.magnamedia.workflow.type.PayrollAccountantTodoType;

import javax.persistence.*;
import java.sql.Date;
import java.util.*;

/**
 * <AUTHOR> Ketrawi
 *         Acc-2826
 */

@Entity
public class PayrollAccountantTodo extends WorkflowEntity {

    public PayrollAccountantTodo() {
        super(null);
    }

    @Column
    private String label;

    @Column(columnDefinition = "double default 0")
    private Double amount = 0.0;

    @Column(columnDefinition = "double default 0")
    private Double charges = 0.0;

    @Column(columnDefinition = "double default 0")
    private Double vat = 0.0;

    @Column(columnDefinition = "double default 0")
    private Double total = 0.0;

    @Column(columnDefinition = "double default 0")
    private Double totalInAED; // PAY-503

    @Enumerated(EnumType.STRING)
    private SalaryCurrency currency;

    @Column
    private String accountName;

    @Column
    private String iban;

    @Column
    private String accountNumber; // ACC-3473

    @Column
    private String mobileNumber;

    @Column
    private String swift;

    @Column
    private String address;

    @Column
    private Boolean international;

    @Column
    private Date payrollMonth;

    @Column
    private String rejectionNotes;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private ClientRefundToDo clientRefundToDo;

    // ACC-3442
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private SendRequestForApprovalAction sendRequestForApprovalAction;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private User managerActionBy;

    @Enumerated(EnumType.STRING)
    private PayrollAccountantTodoManagerAction managerAction = PayrollAccountantTodoManagerAction.PENDING;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private User ceoActionBy;

    @Enumerated(EnumType.STRING)
    private PayrollAccountantTodoManagerAction ceoAction = PayrollAccountantTodoManagerAction.PENDING;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem itemType;

    @Column(columnDefinition = "boolean default false")
    private Boolean singleOfficeStaff = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private ExpensePayment expensePayment;

    // ACC-3315
    @Column(columnDefinition = "boolean default false")
    private boolean doneByCoo = Boolean.FALSE;

    @Transient
    @JsonIgnore
    private Supplier supplier;

    @Column
    private String bankName;

    @Transient
    private String bankCountry;

    @Transient
    private String bankCity;

    public ExpensePayment getExpensePayment() {
        return expensePayment;
    }

    public void setExpensePayment(ExpensePayment expensePayment) {
        this.expensePayment = expensePayment;
    }

    public boolean isDoneByCoo() {
        return doneByCoo;
    }

    public void setDoneByCoo(boolean doneByCoo) {
        this.doneByCoo = doneByCoo;
    }

    public String getToDoLabel() {
        return this.label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Double getAmount() {
        return NumberFormatter.twoDecimalPoints(amount);
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getCharges() {
        return NumberFormatter.twoDecimalPoints(charges);
    }

    public void setCharges(Double charges) {
        this.charges = charges;
    }

    public SalaryCurrency getCurrency() {
        return currency;
    }

    public void setCurrency(SalaryCurrency currency) {
        this.currency = currency;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getIban() {
        return iban;
    }

    public void setIban(String iban) {
        this.iban = iban;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getSwift() {
        return swift;
    }

    public void setSwift(String swift) {
        this.swift = swift;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Boolean getInternational() {
        return international;
    }

    public void setInternational(Boolean international) {
        this.international = international;
    }

    public Date getPayrollMonth() {
        return payrollMonth;
    }

    public void setPayrollMonth(Date payrollMonth) {
        this.payrollMonth = payrollMonth;
    }

    public String getMonth() {
        return this.payrollMonth != null ? DateUtil.formatMonth(payrollMonth) : "";
    }

    public ClientRefundToDo getClientRefundToDo() {
        return clientRefundToDo;
    }

    public void setClientRefundToDo(ClientRefundToDo clientRefundToDo) {
        this.clientRefundToDo = clientRefundToDo;
    }

    public SendRequestForApprovalAction getSendRequestForApprovalAction() {
        return sendRequestForApprovalAction;
    }

    public void setSendRequestForApprovalAction(SendRequestForApprovalAction sendRequestForApprovalAction) {
        this.sendRequestForApprovalAction = sendRequestForApprovalAction;
    }

    public User getManagerActionBy() {
        return managerActionBy;
    }

    public void setManagerActionBy(User managerActionBy) {
        this.managerActionBy = managerActionBy;
    }

    public PayrollAccountantTodoManagerAction getManagerAction() {
        return managerAction;
    }

    public void setManagerAction(PayrollAccountantTodoManagerAction managerAction) {
        this.managerAction = managerAction;
    }

    public User getCeoActionBy() {
        return ceoActionBy;
    }

    public void setCeoActionBy(User ceoActionBy) {
        this.ceoActionBy = ceoActionBy;
    }

    public PayrollAccountantTodoManagerAction getCeoAction() {
        return ceoAction;
    }

    public void setCeoAction(PayrollAccountantTodoManagerAction ceoAction) {
        this.ceoAction = ceoAction;
    }

    public PicklistItem getItemType() {
        return itemType;
    }

    public void setItemType(PicklistItem itemType) {
        this.itemType = itemType;
    }

    public Boolean getSingleOfficeStaff() {
        return singleOfficeStaff;
    }

    public void setSingleOfficeStaff(Boolean singleOfficeStaff) {
        this.singleOfficeStaff = singleOfficeStaff;
    }

    @JsonIgnore
    public String getDueSince() {
        int seconds = DateUtil.secondsBetween(DateUtil.now(), this.getCreationDate());
        int numberOfDays = seconds / 86400;
        int numberOfHours = (seconds % 86400) / 3600;
        int numberOfMinutes = ((seconds % 86400) % 3600) / 60;

        String period = "";
        period += numberOfDays > 0 ? (numberOfDays == 1 ? numberOfDays + " day " : numberOfDays + " days ") : "";
        period += numberOfHours > 0 ? (numberOfHours == 1 ? numberOfHours + " hour " : numberOfHours + " hours ") : "";
        period += numberOfMinutes > 0 ? (numberOfMinutes == 1 ? numberOfMinutes + " min" : numberOfMinutes + " mins") : "";

        return period.isEmpty() ? " 0 min" : period;
    }

    @JsonIgnore
    public String getBeneficiary() {
        if (this.getTaskName() == null || this.getTaskName().trim().equals("")) return null;

        PayrollAccountantTodoType todoType = PayrollAccountantTodoType.valueOf(this.getTaskName());
        String result = null;

        switch (todoType) {
            case WPS:
            case INTERNATIONAL_TRANSFER:
            case LOCAL_TRANSFER:
            case EXPENSE_MONEY_TRANSFER:
                result = Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_CLIENT_REFUND_AGENCY_NAME);;
                break;
            case PENSION_AUTHORITY:
                result = Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_CLIENT_REFUND_PENSION_AUTHORITY_NAME);
                break;
            case BANK_TRANSFER:
                result = getOfficeStaffName();
                break;
            case EXPENSE_BANK_TRANSFER:
                if (clientRefundToDo != null) {
                    Client client = clientRefundToDo.getClient();
                    if (client != null) {
                        result = client.getName();
                    }
                }
                if (expensePayment != null)
                    result = expensePayment.getBeneficiaryName();

                if (sendRequestForApprovalAction != null) {
                    MaidsAtCandidateWA maidsAtCandidateWA = sendRequestForApprovalAction.getMaidsAtCandidateWA();
                    if (maidsAtCandidateWA != null) {
                        result = maidsAtCandidateWA.getName();
                    }
                }

                break;
        }
        return result;
    }

    @JsonIgnore
    public String getDescription() {
        if (getTaskName() == null || getTaskName().trim().equals("")) return null;

        PayrollAccountantTodoType todoType = PayrollAccountantTodoType.valueOf(getTaskName());
        String result = null;
        switch (todoType) {
            case WPS:
            case INTERNATIONAL_TRANSFER:
            case LOCAL_TRANSFER:
                result = getLabel() + ", Payroll for " + this.getMonth();
                break;
            case BANK_TRANSFER:
                result = "Payroll for " + this.getMonth();
                break;
            case PENSION_AUTHORITY:
                result = getOfficeStaffName();
                break;
            case EXPENSE_BANK_TRANSFER:
            case EXPENSE_MONEY_TRANSFER:
                if (clientRefundToDo != null) {
                    Client client = clientRefundToDo.getClient();
                    if (client != null) {
                        result = "Refund C/" + client.getName() + System.lineSeparator();
                        result += "Rx" + clientRefundToDo.getId() + System.lineSeparator();
                        result += "Purpose/" + (clientRefundToDo.getPurpose() != null ? clientRefundToDo.getPurpose().getName() : "");
                    }
                }
                if (expensePayment != null)
                    result = expensePayment.getDescription();
                if (sendRequestForApprovalAction != null)
                    result = String.format("%s Pay to Employer, %s, %s",
                            "Mt" + sendRequestForApprovalAction.getId().toString(),
                            sendRequestForApprovalAction.getBeneficiaryName(),
                            sendRequestForApprovalAction.getNotes());
                break;
        }
        return result;
    }

    @JsonIgnore
    public Double getAmountInForeignCurrency() {
        if (this.getTaskName() == null || this.getTaskName().trim().equals(""))
            return null;

        if (getTaskName().equals(PayrollAccountantTodoType.BANK_TRANSFER.toString()) &&
                this.getSingleOfficeStaff()) {
            SelectQuery<OfficeStaffPayrollLog> staffQuery = new SelectQuery<>(OfficeStaffPayrollLog.class);
            staffQuery.filterBy("payrollAccountantTodo.id", "=", this.getId());
            List<OfficeStaffPayrollLog> result = staffQuery.execute();

            if (!result.isEmpty() && !result.get(0).getOfficeStaff()
                    .getSalaryCurrency().equals(SalaryCurrency.AED)) {

                return NumberFormatter.twoDecimalPoints(result.get(0).getTotalSalary());
            }
        }

        if (expensePayment != null && this.getTotal() != null &&
                expensePayment.getCurrency() != null &&
                !expensePayment.getCurrency().equals(Setup.getApplicationContext()
                        .getBean(CurrencyExchangeSevice.class).getLocalCurrency())) {

            return NumberFormatter.twoDecimalPoints(this.getTotal());
        }

        return null;
    }

    @JsonIgnore
    public Double getAmountAED() {
        if (getTaskName() == null || getTaskName().trim().equals("")) {
            if (getTotal() == null) return null;
            return NumberFormatter.twoDecimalPoints(getTotal());
        }

        if (getTaskName().equals(PayrollAccountantTodoType.BANK_TRANSFER.toString())) {
            if (!getSingleOfficeStaff()) return null;

            SelectQuery<OfficeStaffPayrollLog> staffQuery = new SelectQuery<>(OfficeStaffPayrollLog.class);
            staffQuery.filterBy("payrollAccountantTodo.id", "=", this.getId());
            List<OfficeStaffPayrollLog> result = staffQuery.execute();
            if (result.isEmpty()) return null;

            if (result.get(0).getOfficeStaff().getSalaryCurrency().equals(SalaryCurrency.AED)) {
                return NumberFormatter.twoDecimalPoints(result.get(0).getTotalSalary());
            }
            return null;
        }

        if(expensePayment != null && expensePayment.getCurrency() != null &&
                !expensePayment.getCurrency().equals(Setup.getApplicationContext()
                        .getBean(CurrencyExchangeSevice.class).getLocalCurrency())) {

            return null;
        }

        if (getTotal() == null) return null;
        return NumberFormatter.twoDecimalPoints(getTotal());
    }

    public void setTotal(Double total) {
        this.total = total;
    }

    public Double getTotal() {
        Double t = total;
        if (t == null || t.equals(0.0))
            t = amount + charges + vat;
        return NumberFormatter.twoDecimalPoints(t);
    }

    public Double getTotalInAED() {
        return totalInAED;
    }

    public void setTotalInAED(Double totalInAED) {
        this.totalInAED = totalInAED;
    }

    public void recalculateTotal() {
        total = (amount != null ? amount : 0.0) +
                (charges != null ? charges : 0.0) +
                (vat != null ? vat : 0.0);
    }

    public Double getVat() {
        return vat;
    }

    public void setVat(Double vat) {
        this.vat = vat;
    }

    @JsonIgnore
    public String getForeignCurrency() {
        if (this.getSingleOfficeStaff()) {
            SelectQuery<OfficeStaffPayrollLog> staffQuery = new SelectQuery(OfficeStaffPayrollLog.class);
            staffQuery.filterBy("payrollAccountantTodo.id", "=", this.getId());
            List<OfficeStaffPayrollLog> result = staffQuery.execute();

            if (result.isEmpty()) return null;
            return result.get(0).getOfficeStaff().getSalaryCurrency().toString();
        }

        if(expensePayment != null && expensePayment.getCurrency() != null &&
                !expensePayment.getCurrency().equals(Setup.getApplicationContext()
                        .getBean(CurrencyExchangeSevice.class).getLocalCurrency())) {

            return expensePayment.getCurrency().getName();
        }

        return null;
    }

    @JsonIgnore
    public String getRequester() {
        User requester = getRequesterUser();
        return requester != null ? requester.getName() : "";
    }

    @JsonIgnore
    public User getRequesterUser() {
        if (StringUtils.isEmpty(this.getTaskName()))
            return null;
        PayrollAccountantTodoType todoType = PayrollAccountantTodoType.valueOf(this.getTaskName());
        User result = null;
        if (todoType.equals(PayrollAccountantTodoType.EXPENSE_BANK_TRANSFER) ||
                todoType.equals(PayrollAccountantTodoType.EXPENSE_MONEY_TRANSFER)) {
            if (clientRefundToDo != null)
                result = clientRefundToDo.getRequesterUser();
            if (expensePayment != null)
                result = expensePayment.getRequester();
            if (sendRequestForApprovalAction != null)
                result = sendRequestForApprovalAction.getCreator();
        }
        return result;
    }

    @JsonIgnore
    public String getApprovalFlow() {
        String approverName = "";

        if (this.getExpensePayment() != null) {
            return this.getExpensePayment().getApprovedBy();
        }

        User approver = getApprover();
        if (approver != null) {
            approverName = approver.getUsername();
        } else {
            if (!StringUtils.isEmpty(this.getTaskName())) {
                PayrollAccountantTodoType todoType = PayrollAccountantTodoType.valueOf(this.getTaskName());
                if (todoType.equals(PayrollAccountantTodoType.EXPENSE_BANK_TRANSFER) ||
                        todoType.equals(PayrollAccountantTodoType.EXPENSE_MONEY_TRANSFER)) {
                    if (this.getClientRefundToDo() != null) {
                        User coo = this.getClientRefundToDo().getCeoActionBy();
                        User manager = this.getClientRefundToDo().getManagerActionBy();
                        if (coo == null && manager == null) {
                            approverName = "Auto-approved";
                        }
                    }
                }
            }
        }

        return approverName;
    }

    @JsonIgnore
    public User getApprover() {
        PayrollAccountantTodoType todoType = PayrollAccountantTodoType.valueOf(this.getTaskName());
        User approver = null;
        if (todoType.equals(PayrollAccountantTodoType.EXPENSE_BANK_TRANSFER) ||
                todoType.equals(PayrollAccountantTodoType.EXPENSE_MONEY_TRANSFER)) {
            if (this.getClientRefundToDo() != null) {
                User coo = this.getClientRefundToDo().getCeoActionBy();
                User manager = this.getClientRefundToDo().getManagerActionBy();
                approver = manager != null ? manager : coo;
            }
        }
        return approver;
    }

    public String getRejectionNotes() {
        return rejectionNotes;
    }

    public void setRejectionNotes(String rejectionNotes) {
        this.rejectionNotes = rejectionNotes;
    }

    @JsonIgnore
    public String getOfficeStaffName() {
        if (!getSingleOfficeStaff()) return "";

        SelectQuery<OfficeStaffPayrollLog> staffQuery = new SelectQuery<>(OfficeStaffPayrollLog.class);
        staffQuery.filterBy("payrollAccountantTodo.id", "=", this.getId());
        List<OfficeStaffPayrollLog> result = staffQuery.execute();
        if (result.isEmpty()) return "";

        return result.get(0).getOfficeStaff().getName();
    }

    @JsonIgnore
    public Double getOfficeStaffAmount() {
        if (!getSingleOfficeStaff()) return null;

        SelectQuery<OfficeStaffPayrollLog> staffQuery = new SelectQuery<>(OfficeStaffPayrollLog.class);
        staffQuery.filterBy("payrollAccountantTodo.id", "=", this.getId());
        List<OfficeStaffPayrollLog> result = staffQuery.execute();
        if (result.isEmpty()) return null;

        return result.get(0).getTotalSalary();
    }


    @JsonIgnore
    public String getManagerUserName() {
        return getManagerActionBy() == null ? "" : getManagerActionBy().getFullName();
    }

    @BeforeInsert
    @BeforeUpdate
    private void updateItemType() {
        if (getTaskName() == null || getTaskName().trim().equals("")) return;
        PayrollAccountantTodoType todoType = PayrollAccountantTodoType.valueOf(getTaskName());

        PicklistItem result = null;
        switch (todoType) {
            case WPS:
                result = PicklistHelper.getItem("accountant_todo_types", "wps_transfer");
                break;
            case BANK_TRANSFER:
                result = PicklistHelper.getItem("accountant_todo_types", "office_staff_salary");
                break;
            case LOCAL_TRANSFER:
                result = PicklistHelper.getItem("accountant_todo_types", "salary_local_transfer");
                break;
            case PENSION_AUTHORITY:
                result = PicklistHelper.getItem("accountant_todo_types", "pay_pension");
                break;
            case INTERNATIONAL_TRANSFER:
                result = PicklistHelper.getItem("accountant_todo_types", "international_salaries_transfer");
                break;
            case EXPENSE_BANK_TRANSFER:
            case EXPENSE_MONEY_TRANSFER:
                if (clientRefundToDo != null)
                    result = PicklistHelper.getItem("accountant_todo_types", "refund_client");
                if (expensePayment != null)
                    result = PicklistHelper.getItem("accountant_todo_types", "expense_payment");
                if (sendRequestForApprovalAction != null)
                    result = PicklistHelper.getItem("accountant_todo_types", "employer_agreement");
                break;
        }

        this.setItemType(result);
    }

    // ACC-3152
    @JsonIgnore
    public String getTodoCategory() {
        return PayrollAccountantTodoController.ToDoCategory.PAYROLL_ACCOUNTANT_TODO.toString();
    }


    @Override
    public String getFinishedTaskName() {
        return null;
    }

    @Override
    public List<FormField> getForm(String string) {
        return null;
    }

    @Override
    public String getLabel() {
        super.getLabel();
        return this.label;
    }

    @Transient
    private CooQuestion.QuestionedPage cooQuestionedPage;

    public CooQuestion.QuestionedPage getCooQuestionedPage() {
        return cooQuestionedPage;
    }

    public void setCooQuestionedPage(CooQuestion.QuestionedPage cooQuestionedPage) {
        this.cooQuestionedPage = cooQuestionedPage;
    }

    @JsonIgnore
    public List<CooQuestion> getCooQuestions() {
        if (this.cooQuestionedPage == null) return new ArrayList();

        return Setup.getRepository(CooQuestionRepository.class).findByRelatedEntityAndQuestionedPage(this, this.cooQuestionedPage);
    }

    @JsonIgnore
    public boolean isAllQuestionsAnswered() {
        if (this.cooQuestionedPage == null) return false;

        List<CooQuestion> cooQuestions = getCooQuestions();

        return !cooQuestions.isEmpty() && !cooQuestions.stream().anyMatch(q -> !q.isAnswered());
    }

    @JsonIgnore
    public boolean isOneQuestionAnswered() {
        if (this.cooQuestionedPage == null) return false;

        return getCooQuestions().stream().filter(q -> q.isAnswered()).count() >= 1l;
    }

    @JsonIgnore
    public boolean isNoneQuestionAnswered() {
        if (this.cooQuestionedPage == null) return false;

        return getCooQuestions().stream().filter(q -> q.isAnswered()).count() == 0l;
    }

    @JsonIgnore
    public boolean shouldAmountBeSecured() {
        if (this.getTaskName() != null
                && (PayrollAccountantTodoType.valueOf(this.getTaskName()).equals(PayrollAccountantTodoType.EXPENSE_BANK_TRANSFER)
                || PayrollAccountantTodoType.valueOf(this.getTaskName()).equals(PayrollAccountantTodoType.EXPENSE_MONEY_TRANSFER)))
            return false;

        SalariesAccessRepository repository = Setup.getRepository(SalariesAccessRepository.class);
        User user = CurrentRequest.getUser();

        boolean authorized = user == null || "guest".equals(user.getLoginName()) || repository.countByUser(user) > 0;

        return !authorized;
    }

    public String getType() {
        PicklistItem type = getItemType();
        return type == null ? "" : type.getName();
    }

    // ACC-4654
    @JsonIgnore
    public PicklistItem getContractProspectType(){
        if(clientRefundToDo == null) return null;
        return clientRefundToDo.getContract().getContractProspectType();
    }

    @JsonIgnore
    public Map<String, String> getBeneficiaryType() {
        if (clientRefundToDo != null && clientRefundToDo.getClient() != null) return getMapBeneficiaryType("REFUNDS", "");
        if (expensePayment != null) {
            return expensePayment.getReplenishmentTodo() != null ?
                    getMapBeneficiaryType("BUCKET_REPLENISHMENT", expensePayment.getReplenishmentTodo().getBucket() != null ?
                            expensePayment.getReplenishmentTodo().getBucket().getName() : "") :
                    expensePayment.getBeneficiaryType() == ExpenseBeneficiaryType.OFFICE_STAFF ? getMapBeneficiaryType("EXPENSES_OFFICE_STAFF", "") :
                            expensePayment.getBeneficiaryType() == ExpenseBeneficiaryType.SUPPLIER ? getMapBeneficiaryType("EXPENSES_SUPPLIER", "") :
                                    expensePayment.getBeneficiaryType() == ExpenseBeneficiaryType.MAID ? getMapBeneficiaryType("EXPENSES_MAID", "") :
                                    getMapBeneficiaryType("", "");
        }

        return getMapBeneficiaryType("", "");
    }

    @JsonIgnore
    public Map<String, String> getMapBeneficiaryType(String beneficiaryType, String beneficiaryName) {
        return new HashMap<String, String>() {{
            put("beneficiaryType", beneficiaryType);
            put("beneficiaryName", beneficiaryName);
        }};
    }

    @JsonIgnore
    public Long getBeneficiaryTypeId() {
        if (getClientRefundToDo() != null && getClientRefundToDo().getClient() != null) return getClientRefundToDo().getClient().getId();
        if (getExpensePayment() != null) {
            return getExpensePayment().getReplenishmentTodo() == null ? getExpensePayment().getBeneficiaryId() :
                    getExpensePayment().getReplenishmentTodo().getBucket() != null ?
                            getExpensePayment().getReplenishmentTodo().getBucket().getId() : null;
        }
        return null;
    }

    public Supplier getSupplier() {
        if (supplier == null && getExpensePayment() != null &&
                ExpenseBeneficiaryType.SUPPLIER.equals(getExpensePayment().getBeneficiaryType())) {
            supplier = Setup.getRepository(SupplierRepository.class).findOne(getExpensePayment().getBeneficiaryId());
        }
        return supplier;
    }

    public void setSupplier(Supplier supplier) { this.supplier = supplier; }

    public String getBankName() {
        if (bankName != null && !bankName.isEmpty()) return bankName;
        if (getClientRefundToDo() != null && getClientRefundToDo().getClient() != null) {
            List<Object[]> rows = Setup.getRepository(DirectDebitRepository.class)
                    .getClientAccountInfo(getClientRefundToDo().getClient().getId());

            return rows != null && !rows.isEmpty() && rows.get(0)[3] != null ? rows.get(0)[3].toString() : "";
        }

        if (getSupplier() != null) {
            return getSupplier().getBankName();
        }

        return "";
    }

    public void setBankName(String bankName) { this.bankName = bankName; }

    public String getBankCountry() {
        return getSupplier() == null ? "" : getSupplier().getBankCountryName();
    }

    public String getBankCity() {
        return getSupplier() == null ? "" : getSupplier().getBankCityName();
    }
}