package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.repository.BaseRepositoryParent;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.entity.RecurringCreditCardPaymentsIssue;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.FlowSubEventConfigRepository;
import com.magnamedia.repository.RecurringCreditCardPaymentsIssueRepository;
import com.magnamedia.service.ClientPayingViaCreditCardService;
import com.magnamedia.service.FlowProcessorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RequestMapping("/recurringCreditCardPaymentsIssue")
@RestController
public class RecurringCreditCardPaymentsIssueController extends BaseRepositoryController<RecurringCreditCardPaymentsIssue>  {

    @Autowired
    private RecurringCreditCardPaymentsIssueRepository recurringCreditCardPaymentsIssueRepository;
    @Autowired
    private FlowSubEventConfigRepository flowSubEventConfigRepository;

    @Override
    public BaseRepositoryParent<RecurringCreditCardPaymentsIssue> getRepository() { return recurringCreditCardPaymentsIssueRepository; }

    @Override
    protected ResponseEntity<?> createEntity(RecurringCreditCardPaymentsIssue entity) {
        checkEntity(entity);
        return super.createEntity(entity);
    }

    @Override
    protected ResponseEntity<?> updateEntity(RecurringCreditCardPaymentsIssue entity) {
        checkEntity(entity);
        return super.updateEntity(entity);
    }

    private void checkEntity(RecurringCreditCardPaymentsIssue entity) {
        List<RecurringCreditCardPaymentsIssue> l = recurringCreditCardPaymentsIssueRepository.findByErrorCodeAndSubEvent(
                entity.getErrorCode().getId(), entity.getId());
        if (!l.isEmpty()) {
           throw new BusinessException("This error code included in another flow: " + l.get(0).getSubEvent().getName());
       }
    }

    @PreAuthorize("hasPermission('recurringCreditCardPaymentsIssue','getTheErrorCodeNotSelectedAndTheSubFlow')")
    @GetMapping("/getTheErrorCodeNotSelectedAndTheSubFlow")
    public ResponseEntity<?> getTheErrorCodeNotSelectedAndTheSubFlow() {

        Map<String, Object> r = new HashMap<>();
        r.put("errorCode", Setup.getRepository(PicklistRepository.class).findByCode(AccountingModule.PICKLIST_CREDIT_CARD_ERROR_CODES).getItems());
        r.put("subFlow", flowSubEventConfigRepository.findAllSubFlowsByFlowEventNameAndFlowSubEventNamesIn(
                    FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card,
                        ClientPayingViaCreditCardService.recurringFailureFlows.stream()
                                .filter(subEventName -> !subEventName.name().endsWith("_MV"))
                                .collect(Collectors.toList()))
                .stream()
                .map(s -> new HashMap<String, Object>() {{
                    put("id", s.getId());
                    put("name", s.getName().getMessagingSubType().getLabel());
                }}).collect(Collectors.toList()));

        return ResponseEntity.ok(r);
    }
}