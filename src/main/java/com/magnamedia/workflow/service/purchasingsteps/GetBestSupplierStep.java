package com.magnamedia.workflow.service.purchasingsteps;

import com.magnamedia.entity.PurchasingToDo;
import com.magnamedia.module.type.PurchaseRequestStatus;
import com.magnamedia.workflow.type.PurchasingToDoType;
import org.springframework.stereotype.Service;

/**
 * <PERSON> (Feb 01, 2021)
 */
@Service
public class GetBestSupplierStep extends PurchasingAbstractStep<PurchasingToDo> {
    public GetBestSupplierStep() {
        this.setId(PurchasingToDoType.PM_GET_BEST_SUPPLIER.toString());
    }

    @Override
    public void onDone(PurchasingToDo entity) {
            super.onDone(entity);
            addNewTask(entity, PurchasingToDoType.PA_CONFIRM_BEST_SUPPLIER.toString());
    }
}
