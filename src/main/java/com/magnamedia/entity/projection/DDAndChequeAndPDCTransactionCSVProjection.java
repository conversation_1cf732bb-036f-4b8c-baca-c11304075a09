package com.magnamedia.entity.projection;

import com.magnamedia.module.type.BankTransactionType;
import com.magnamedia.module.type.VatType;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on June 30, 2020
 * Jirra ACC-2154
 */

public interface DDAndChequeAndPDCTransactionCSVProjection {

    public String getDescription();

    @Value("#{(target.getRevenue() != null)? target.getRevenue().getName():''}")
    public String getRevenue();

    @Value("#{(target.getExpense() != null)? target.getExpense().getCodeLabel():target.getExpenseCode()}")
    public String getExpense();

    public Date getDate();

    public Double getTransactionAmount();

    public BankTransactionType getBankTransactionType();

    VatType getVatType();

    Double getVatAmount();

    String getReason();

    String getForEntity();

    String getForName();

    String getToBucket();

    String getFromBucket();

    String getPaymentDetail();

    @Value("#{(target.getContract() != null && target.getContract().getClient() !=null)? "
            + "target.getContract().getClient().getName() "
            + ": target.getClient()}")
    String getClientName();

    @Value("#{(target.getPayment()!=null)?"
            + "('Payment-' + target.getPayment().getId()):''}")
    String getPaymentId();

    String getNote();

}