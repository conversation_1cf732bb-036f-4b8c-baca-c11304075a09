package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.HousemaidTransaction;
import com.magnamedia.entity.Transaction;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Feb 8, 2020
 *         Jirra ACC-1338
 */

@Repository
public interface HousemaidTransactionRepository extends BaseRepository<HousemaidTransaction> {

    List<HousemaidTransaction> findByTransaction(Transaction t);
}
