package com.magnamedia.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.controller.VisaRequestExpenseController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.entity.BackgroundTaskStatus;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.*;
import com.magnamedia.extra.*;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.TransactionEntityType;
import com.magnamedia.repository.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Service
public class VisaStatementService {
    protected static final Logger logger = Logger.getLogger(VisaStatementService.class.getName());

    @Autowired
    private VisaStatementRepository visaStatementRepository;
    @Autowired
    private NewVisaRequestExpenseRepository newVisaRequestExpenseRepository;
    @Autowired
    private VisaStatementTransactionRepository visaStatementTransactionRepository;
    @Autowired
    private NoqodiStatementRecordRepository noqodiStatementRecordRepository;
    @Autowired
    private AmwalStatementRecordRepository amwalStatementRecordRepository;
    @Autowired
    private VisaExpenseService expenseService;
    @Autowired
    private AccountingEntityPropertyRepository accountingEntityPropertyRepository;
    @Autowired
    private BackgroundTaskService backgroundTaskService;
    @Autowired
    private ObjectMapper objectMapper;

    private static final int BATCH_SIZE = 50;

    @Transactional
    public void createAllVisaTransactions(Map<String, Object> payload) {
        VisaStatement statement = visaStatementRepository.findOne(Long.parseLong(payload.get("entityId").toString()));
        if (statement == null) return;

        Attachment attachment = null;
        if (statement.getAttachmentsCount() > 0){
            attachment = statement.getAttachments().get(0);
        }

        if (attachment == null) return;

        try (InputStream inputStream = Storage.getStream(attachment)) {
            Workbook workbook;
            try {
                workbook = new XSSFWorkbook(inputStream);
            } catch (Exception e) {
                workbook = new HSSFWorkbook(inputStream);
            }

            if (workbook == null) return;

            logger.info("start parsing records for statement id : " + statement.getId());
            if (VisaStatementType.Amwal.equals(statement.getType())) {
                extractAmwalRecordsAndMappingThem(statement, workbook.getSheetAt(0));
            } else if (VisaStatementType.Noqodi.equals(statement.getType())) {
                extractNoqodiRecordsAndMappingThem(statement, workbook.getSheetAt(0));
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE,"Error while parsing file " + attachment.getName() + " for visaStatement #" + statement.getId(), e);
            throw new RuntimeException("Error while parsing file " + attachment.getName());
        }

        statement.setStatus(VisaStatementStatus.PENDING);
        visaStatementRepository.save(statement);
    }

    /** Handle Noqodi Records */
    public void extractNoqodiRecordsAndMappingThem(VisaStatement visaStatement, Sheet sheet) throws ParseException {
        logger.info("Starting optimized Noqodi records extraction");

        List<NoqodiStatementRecord> records = new ArrayList<>();

        // 1.Parse Noqodi Records and store them in records list
        parseNoqodiRecords(sheet, visaStatement, records);

        // 2.Save records
        records = noqodiStatementRecordRepository.saveAll(records);

        // 3.Create Brothers and master for common records based on Transaction Number
        handleNoqodiBrothersRecords(records);

        // 4.Process Transactions
        processNoqodiTransactions(visaStatement, records.stream()
                .map(NoqodiStatementRecord::getTransactionNumber).collect(Collectors.toList()));

        logger.info("Noqodi Processing Finished");
    }

    private void parseNoqodiRecords(Sheet sheet, VisaStatement visaStatement, List<NoqodiStatementRecord> records) throws ParseException {
        DataFormatter formatter = new DataFormatter();
        NumberFormat nf_in = NumberFormat.getNumberInstance(Locale.ENGLISH);

        Iterator<Row> rowIterator = sheet.iterator();

        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            String srNumber = formatter.formatCellValue(row.getCell(0)).trim();
            if (!StringUtils.isNumeric(srNumber)) continue;

            String transactionDateString = formatter.formatCellValue(row.getCell(1)).trim();
            Date transactionDate = DateUtil.parseNoqodiDate(transactionDateString);
            String transactionNumber = formatter.formatCellValue(row.getCell(2)).trim();
            String description = formatter.formatCellValue(row.getCell(3)).trim();
            Double debit = !formatter.formatCellValue(row.getCell(4)).trim().isEmpty() ?
                    nf_in.parse(formatter.formatCellValue(row.getCell(4)).trim()).doubleValue() : 0.0;
            Double credit = !formatter.formatCellValue(row.getCell(5)).trim().isEmpty() ?
                    nf_in.parse(formatter.formatCellValue(row.getCell(5)).trim()).doubleValue() : 0.0;
            Double balance = !formatter.formatCellValue(row.getCell(6)).trim().isEmpty() ?
                    nf_in.parse(formatter.formatCellValue(row.getCell(6)).trim()).doubleValue() : 0.0;

            NoqodiStatementRecord record = new NoqodiStatementRecord();
            record.setSrNumber(srNumber);
            record.setTransactionDate(transactionDate);
            record.setTransactionNumber(transactionNumber);
            record.setDescription(description);
            record.setDebit(debit);
            record.setCredit(credit);
            record.setBalance(balance);
            record.setStatement(visaStatement);

            records.add(record);
        }

        logger.info("Parsing Operation Finished for visaStatementId : " + visaStatement.getId() +
                " with records size : " + records.size());
    }

    private void handleNoqodiBrothersRecords(List<NoqodiStatementRecord> records) {
        //Grouping records based on transactionNumber to figure out the brothers for each record
        Map<String, List<NoqodiStatementRecord>> brothersMap = records.stream()
                .collect(Collectors.groupingBy(NoqodiStatementRecord::getTransactionNumber));

        for (Map.Entry<String, List<NoqodiStatementRecord>> entry : brothersMap.entrySet()) {
            List<NoqodiStatementRecord> brothersList = entry.getValue();

            if (brothersList != null && brothersList.size() > 1) {
                logger.info("Brothers found, size: " + brothersList.size() + " for transaction number: " + entry.getKey());
                createMasterRecordNoqodi(brothersList);
            }
        }
    }

    // Process Transactions
    private void processNoqodiTransactions(VisaStatement visaStatement, List<String> transactionNumbers) {

        // Pre-Load all expenses to avoid N+1 queries
        Map<String, List<Object[]>> expenseMap = preloadExpenses(transactionNumbers);
        PicklistItem maidVisa = PicklistHelper.getItem(AccountingModule.PICKLIST_PROSPECTTYPE,
                "maidvisa.ae_prospect");

        Long lastId = -1L;
        Page<NoqodiStatementRecord> p;
        int processedCount = 0;

        do {
            p = noqodiStatementRecordRepository.findByStatementAndIsFinal(lastId, visaStatement, false, PageRequest.of(0, BATCH_SIZE));
            if (p.isEmpty()) break;

            // Process records in current page
            for (NoqodiStatementRecord record : p.getContent()) {
                VisaStatementTransaction t = new VisaStatementTransaction();

                if (record.getCredit() > 0) {
                    t.setStatement(visaStatement);
                    t.setAmount(record.getCredit());
                    t.setCredit(true);
                    t.setReferenceNumber(record.getTransactionNumber());
                    t.setRowRecordDate(record.getTransactionDate());
                    if (record.getAmounts() != null && !record.getAmounts().isEmpty()) {
                        t.setAmounts(record.getAmounts());
                    }

                    if (record.getDescription() != null && record.getDescription().toLowerCase()
                            .contains("ADCB Purchase Credit".toLowerCase())) {

                        t.setType(VisaStatementTransactionType.Ignore);
                        t.setFinished(true);
                    } else {
                        t.setType(VisaStatementTransactionType.MissingFromERP);
                    }

                } else if (record.getDebit() > 0) {
                    // Use pre-Loaded expense data
                    List<Object[]> visaExpenses = expenseMap.getOrDefault(record.getTransactionNumber(), Collections.emptyList());

                    t.setRowRecordDate(record.getTransactionDate());
                    t.setReferenceNumber(record.getTransactionNumber());
                    t.setAmount(record.getDebit());
                    t.setStatement(visaStatement);

                    if (record.getAmounts() != null && !record.getAmounts().isEmpty()) {
                        t.setAmounts(record.getAmounts());
                    }

                    if (visaExpenses.isEmpty()) {
                        t.setType(VisaStatementTransactionType.MissingFromERP);
                    } else {
                        t = new VisaStatementTransaction(visaExpenses.get(0), visaStatement, record.getDebit(), maidVisa);
                        t.setType(record.getDebit() != null && record.getDebit().equals(getErpFullAmount(visaExpenses.get(0))) ?
                                VisaStatementTransactionType.Matched :
                                VisaStatementTransactionType.SameReferenceNumberButDifferentAmount);
                    }
                }

                t = visaStatementTransactionRepository.save(t);
                record.setTransaction(t);
                noqodiStatementRecordRepository.save(record);
            }

            if(!p.isEmpty())
                lastId = p.getContent().get(p.getContent().size() - 1).getId();
            processedCount += p.getContent().size();

        } while (!p.isEmpty());

        expenseMap.clear();
        logger.info("Completed processing " + processedCount + " transaction records");
    }

    public void createMasterRecordNoqodi(List<NoqodiStatementRecord> records) {
        double amount = 0;
        double amountCredit = 0;
        StringBuilder amounts = new StringBuilder();
        StringBuilder amountsCredit = new StringBuilder();
        NoqodiStatementRecord master = new NoqodiStatementRecord();

        for (NoqodiStatementRecord statementRecord : records) {
            amount += statementRecord.getDebit();
            amountCredit+= statementRecord.getCredit();
            amounts.append(statementRecord.getDebit()).append(";");
            amountsCredit.append(statementRecord.getCredit()).append(";");
            statementRecord.setMaster(master);
            statementRecord.setFinal(true);
        }

        master.setTransactionNumber(records.get(0).getTransactionNumber());
        master.setTransactionDate(records.get(0).getTransactionDate());
        master.setDebit(amount);
        master.setCredit(amountCredit);
        master.setAmounts(amount >0  ? amounts.toString() : amountsCredit.toString());
        master.setStatement(records.get(0).getStatement());

        noqodiStatementRecordRepository.save(master);
        noqodiStatementRecordRepository.saveAll(records);
    }

    /** Handle Amwal Records */
    public void extractAmwalRecordsAndMappingThem(VisaStatement visaStatement, Sheet sheet) throws ParseException {
        logger.info("Starting optimized Amwal records extraction");

        List<AmwalStatementRecord> records = new ArrayList<>();

        // 1.Parse Amwal Records and save them into records list
        parseAmwalRecords(sheet, visaStatement, records);

        // 2.Process Transactions
        processAmwalTransactions(visaStatement, records);

        // 3.Auto Confirm Matched Records
        createConfirmMatchedTransactionsBackgroundTask(visaStatement);

        logger.info("Amwal Processing Finished");
    }

    private void parseAmwalRecords(Sheet sheet, VisaStatement visaStatement, List<AmwalStatementRecord> records) throws ParseException {
        DataFormatter formatter = new DataFormatter();
        NumberFormat nf_in = NumberFormat.getNumberInstance(Locale.ENGLISH);
        Iterator<Row> rowIterator = sheet.iterator();

        if (rowIterator.hasNext()) {
            // Skip header
            rowIterator.next();
        }
        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            String transactionDateString = formatter.formatCellValue(row.getCell(0)).trim();
            if (transactionDateString.isEmpty()) break;

            Date transactionDate = DateUtil.parseAmwalDate(transactionDateString);
            String transactionType = formatter.formatCellValue(row.getCell(1)).trim();
            String transactionNumber = formatter.formatCellValue(row.getCell(2)).trim();
            String transactionStatue = formatter.formatCellValue(row.getCell(3)).trim();
            Double oldBalance = !formatter.formatCellValue(row.getCell(4)).trim().isEmpty() ?
                    nf_in.parse(formatter.formatCellValue(row.getCell(4)).trim()).doubleValue() : 0.0;
            Double availableBalance = !formatter.formatCellValue(row.getCell(5)).trim().isEmpty() ?
                    nf_in.parse(formatter.formatCellValue(row.getCell(5)).trim()).doubleValue() : 0.0;
            Double amount = !formatter.formatCellValue(row.getCell(6)).trim().isEmpty() ?
                    nf_in.parse(formatter.formatCellValue(row.getCell(6)).trim()).doubleValue() : 0.0;

            AmwalStatementRecord record = new AmwalStatementRecord();
            record.setStatement(visaStatement);
            record.setTransactionDate(transactionDate);
            record.setTransactionType(transactionType);
            record.setTransactionNumber(transactionNumber);
            record.setTransactionStatue(transactionStatue);
            record.setOldBalance(oldBalance);
            record.setAvailableBalance(availableBalance);
            record.setAmount(amount);

            records.add(record);
        }

        logger.info("Parsing operation Finished for visaStatementId : " + visaStatement.getId() +
                " with records size : " + records.size());
    }

    // Process Transactions
    private void processAmwalTransactions(VisaStatement visaStatement, List<AmwalStatementRecord> records) {

        int rowCount = 0;
        PicklistItem maidVisa = PicklistHelper.getItem(AccountingModule.PICKLIST_PROSPECTTYPE,
                "maidvisa.ae_prospect");
        String thresholdStr = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_AMWAL_WALLET_AUTO_ADJUST_THRESHOLD);

        // Pre-Load all expenses
        Map<String, List<Object[]>> expenseMap = preloadExpenses(records.stream()
                .map(AmwalStatementRecord::getTransactionNumber).collect(Collectors.toList()));
        for (AmwalStatementRecord record : records) {

            // Use pre-loaded expense data instead of individual database calls
            List<Object[]> visaExpenses = expenseMap.getOrDefault(record.getTransactionNumber(), Collections.emptyList());

            VisaStatementTransaction t = new VisaStatementTransaction();
            t.setStatement(visaStatement);
            t.setAmount(record.getAmount());
            t.setReferenceNumber(record.getTransactionNumber());
            t.setRowRecordDate(record.getTransactionDate());

            if (visaExpenses.isEmpty()) {
                t.setType(VisaStatementTransactionType.MissingFromERP);
            } else {
                t = new VisaStatementTransaction(visaExpenses.get(0), visaStatement, t.getAmount(), maidVisa);

                // Get ERP amount
                double erpAmount = getErpFullAmount(visaExpenses.get(0));

                // Check if amounts match exactly
                if (record.getAmount() != null && record.getAmount().equals(erpAmount)) {
                    t.setType(VisaStatementTransactionType.Matched);
                } else {
                    // Check if the difference is less than the threshold for auto-adjustment
                    if (Math.abs(record.getAmount() - erpAmount) <= Double.parseDouble(thresholdStr)) {
                        // Auto-adjust the ERP amount
                        VisaExpense expense = expenseService.getVisaExpenseByType(
                                t.getVisaExpenseType(), t.getVisaRequestExpenseID());
                        expense.setAmount(record.getAmount());
                        expense.setCharge(null);
                        expense.setVatCharge(null);
                        expenseService.saveVisaExpenseByType(t.getVisaExpenseType(), expense);

                        // Set as Matched
                        t.setType(VisaStatementTransactionType.Matched);
                    } else {
                        // Difference is too large for auto-adjustment
                        t.setType(VisaStatementTransactionType.SameReferenceNumberButDifferentAmount);
                    }
                }
            }

            t = visaStatementTransactionRepository.save(t);
            record.setTransaction(t);
            amwalStatementRecordRepository.save(record);
        }

        expenseMap.clear();
        logger.info("Completed processing " + rowCount + " Amwal records");
    }

    public double getErpFullAmount(Object[] visaExpense) {
        return ((double) Math.round((
                (visaExpense[2] == null ? 0 : (Double) visaExpense[2]) +
                (visaExpense[3] == null ? 0 : (Double) visaExpense[3]) +
                (visaExpense[4] == null ? 0 : (Double) visaExpense[4])) * 100) / 100) ;
    }

    /**
     * Pre-loads all expenses for fast lookup
     * This eliminates the N+1 query problem by loading all data upfront
     */
    private Map<String, List<Object[]>> preloadExpenses(List<String> transactionsNumbers) {
        logger.info("Pre-loading expenses");

        Map<String, List<Object[]>> expenseMap = new HashMap<>();

        logger.info("Found " + transactionsNumbers.size() + " unique reference numbers");

        if (transactionsNumbers.isEmpty()) {
            return expenseMap;
        }

        // Use optimized batch query to reduce database calls
        for (int i = 0; i < transactionsNumbers.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, transactionsNumbers.size());
            List<String> batch = transactionsNumbers.subList(i, endIndex).stream()
                    .filter(ref -> ref != null && !ref.trim().isEmpty())
                    .collect(Collectors.toList());

            if (!batch.isEmpty()) {
                // Use the new optimized batch query
                List<Object[]> batchResults = newVisaRequestExpenseRepository
                        .findByExpenseReferenceNumbersAndCreationDateBatch(batch);

                // Group results by reference number
                for (Object[] result : batchResults) {
                    String refNumber = (String) result[0];
                    // Create expense array without the reference number (shift indices)
                    // To match the result of `findByExpenseReferenceNumberAndCreationDate`
                    Object[] expense = new Object[result.length - 1];
                    System.arraycopy(result, 1, expense, 0, expense.length);

                    expenseMap.computeIfAbsent(refNumber, k -> new ArrayList<>()).add(expense);
                }
            }
        }

        logger.info("Pre-loaded " + expenseMap.size() + " expense mappings with " +
                   expenseMap.values().stream().mapToInt(List::size).sum() + " total expenses");
        return expenseMap;
    }

    /**
     * Creates a background task to confirm all matched transactions for a visa statement
     *
     * @param visaStatement The visa statement
     */
    public void createConfirmMatchedTransactionsBackgroundTask(VisaStatement visaStatement) {
        if (visaStatement == null) return;

        Map<String, Object> payload = new HashMap<>();
        payload.put("entityId", visaStatement.getId().toString());

        if (QueryService.existsEntity( BackgroundTask.class, "e.name = :p0 and e.status not in :p1",
                new Object[]{ "Confirm_Visa_Statement_Transactions_Records", Arrays.asList(
                        BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed) })) {
            //CREATE PROPERTY TO BE RUN IN AccountingModuleMainJob
            try {
                AccountingEntityProperty a = new AccountingEntityProperty();
                a.setKey(AccountingModule.RUN_BACKGROUND_TASK_IN_SEQUENTIAL);
                a.setPurpose(UploadStatementEntityType.ConfirmVisaStatementTransactions.toString());
                a.setValue(objectMapper.writeValueAsString(payload));
                accountingEntityPropertyRepository.save(a);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            // Create the background task
            Setup.getApplicationContext()
                    .getBean(BackgroundTaskService.class)
                    .create(new BackgroundTask.builder(
                            UploadStatementEntityType.ConfirmVisaStatementTransactions.toString(),
                            "accounting",
                            UploadStatementEntityType.ConfirmVisaStatementTransactions.getTargetBean(),
                            UploadStatementEntityType.ConfirmVisaStatementTransactions.getTargetMethod())
                            .withRelatedEntity("VisaStatement", visaStatement.getId())
                            .withParameters(new Class[] { Map.class }, new Object[] { payload })
                            .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                            .build());
        }
    }

    /**
     * called from BackgroundTaskHelper.createBGTPreConfirmAllVisaStatementRecords
     * create Background task for each record related into VisaStatement
     * @param payload
     * */
    public void createBGTPreConfirmAllVisaStatementRecords(Map<String, Object> payload) {
        VisaStatement visaStatement = visaStatementRepository.findOne(Long.parseLong(payload.get("entityId").toString()));

        List<Map<String, Object>> l = visaStatementTransactionRepository.findRecordsToBeConfirmed(
                visaStatement, VisaStatementTransactionType.Matched);

        if (l.isEmpty()) {
            throw new BusinessException("there isn't any matched records to be confirmed");
        }

        l.forEach(v -> backgroundTaskService.create(new BackgroundTask.builder(
                UploadStatementEntityType.ConfirmVisaStatementTransaction.toString(),
                "accounting",
                UploadStatementEntityType.ConfirmVisaStatementTransaction.getTargetBean(),
                UploadStatementEntityType.ConfirmVisaStatementTransaction.getTargetMethod())
                .withRelatedEntity("VisaStatement",
                        Long.parseLong(payload.get("entityId").toString()))
                .withParameters(
                        new Class[] { Long.class },
                        new Object[] { v.get("visaStatementTransactionId") })
                .withQueue(BackgroundTaskQueues.HeavyOperationsQueue)
                .build()));
    }


    /**
     * executed using BGT for confirm record
     * @param visaStatementTransactionId
     * */
    public void confirmTransaction(Long visaStatementTransactionId) {
        VisaStatementTransaction v = visaStatementTransactionRepository.findOne(visaStatementTransactionId);
        logger.info("visa statement transaction id : " + v.getId());

        Transaction transaction = new Transaction();
        transaction.setDate(new java.sql.Date(new java.util.Date().getTime()));
        transaction.setDescription(v.getDescription());
        transaction.setAmount(v.getAmount());
        transaction.setExpense(v.getExpense());
        transaction.setFromBucket(v.getFromBucket());
        transaction.setPaymentType(PaymentMethod.CARD);

        if (v.getEmployeeType().equals(EmployeeType.Officestaff)) {
            transaction.setTransactionType(TransactionEntityType.OFFICE_STAFF);
            OfficeStaffTransaction officeStaffTransaction = new OfficeStaffTransaction();
            officeStaffTransaction.setOfficeStaff(v.getOfficeStaff());
            transaction.setOfficeStaffs(Collections.singletonList(officeStaffTransaction));
        } else {
            transaction.setTransactionType(TransactionEntityType.HOUSEMAID);
            HousemaidTransaction housemaidTransaction = new HousemaidTransaction();
            housemaidTransaction.setHousemaid(v.getHousemaid());
            transaction.setHousemaids(Collections.singletonList(housemaidTransaction));
        }

        ResponseEntity<?> response = confirm(v, transaction);
        logger.info("after confirm records : " + v.getId() + " , response : " + response.getStatusCode());
    }

    public ResponseEntity<?> confirm(
            VisaStatementTransaction visaStatementTransaction,
            Transaction transaction) {

        visaStatementTransaction.setFinished(true);
        visaStatementTransaction.setExpense(transaction.getExpense());
        visaStatementTransaction.setDescription(transaction.getDescription());
        visaStatementTransaction.setFromBucket(transaction.getFromBucket());

        ResponseEntity<?> response = Setup.getApplicationContext().getBean(VisaRequestExpenseController.class)
                .addVisaRequestExpenseTransaction(visaStatementTransaction.getVisaRequestExpenseID(),
                        visaStatementTransaction.getVisaExpenseType(), transaction);

        transaction = Setup.getRepository(TransactionRepository.class).findOne(transaction.getId());
        visaStatementTransaction.setTransaction(transaction);
        visaStatementTransactionRepository.save(visaStatementTransaction);

        refreshStatement(visaStatementTransaction.getStatement());
        return response;
    }

    public void refreshStatement(VisaStatement statement) {
        if (statement == null || !statement.getCanBeDeleted()) return ;

        //ACC-9272 Call update API using IMC for handle OptimisticLockingFailureException
        Map body = new HashMap();
        body.put("id", statement.getId());
        body.put("canBeDeleted", Boolean.FALSE);
        Setup.getApplicationContext().getBean(InterModuleConnector.class)
                .postJsonAsync("accounting/visaStatement/update", body);
    }
}