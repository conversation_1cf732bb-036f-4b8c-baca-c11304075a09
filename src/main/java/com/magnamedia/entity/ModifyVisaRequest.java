package com.magnamedia.entity;

import com.magnamedia.core.workflow.FormField;

import javax.persistence.Entity;
import java.util.List;


@Entity
public class ModifyVisaRequest extends VisaRequest<ModifyVisaRequest, ModifyVisaRequestNote, ModifyVisaRequestExpense> {

    public ModifyVisaRequest(String startTaskName) {
        super(startTaskName);
    }

    public ModifyVisaRequest() {
        super("");
    }

    @Override
    public String getFinishedTaskName() {
        return "";
    }

    @Override
    public List<FormField> getForm(String taskName) {
        return null;
    }

}
