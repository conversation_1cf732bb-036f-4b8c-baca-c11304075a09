package com.magnamedia.entity;

import com.magnamedia.workflow.visa.ExpensePurpose;
import javax.persistence.Entity;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Sep 17, 2017
 */
@Entity
public class NewRequestExpense extends VisaExpense<NewRequest> {

    public NewRequestExpense() {
        super(null,
                null);
    }

    public NewRequestExpense(NewRequest request,
            ExpensePurpose purpose) {
        super(request,
                purpose);
    }

    @Override
    public String getVisaExpenseType() {
        return "NewRequestExpense";
    }
}
