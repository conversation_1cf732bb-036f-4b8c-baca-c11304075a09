package com.magnamedia.repository;

import com.magnamedia.core.repository.workflow.WorkflowRepository;
import com.magnamedia.entity.MaintenanceRequest;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;

/**
 * <PERSON> (Feb 07, 2021)
 */
@Repository
public interface MaintenanceRequestRepository extends WorkflowRepository<MaintenanceRequest> {
    MaintenanceRequest findTop1ByExpenseRequestTodoOrderByCreationDateDesc(ExpenseRequestTodo entity);

    List<MaintenanceRequest> findByCompletedFalseAndStoppedFalseAndTaskNameIgnoreCaseIn(List<String> taskNames);
}
