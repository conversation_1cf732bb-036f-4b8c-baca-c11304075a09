package com.magnamedia.controller;

import com.magnamedia.core.controller.workflow.WorkflowController;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Bucket;
import com.magnamedia.entity.projection.BucketReplenishmentProjection;
import com.magnamedia.entity.workflow.BucketReplenishmentTodo;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.type.BucketReplenishmentTodoStatus;
import com.magnamedia.repository.BucketReplenishmentTodoRepository;
import com.magnamedia.repository.BucketRepository;
import com.magnamedia.service.AccountBalanceService;
import com.magnamedia.service.ExpenseNotificationService;
import com.magnamedia.workflow.service.BucketReplenishmentFlow;
import com.magnamedia.workflow.type.BucketReplenishmentTodoType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 1/23/2021
 */
@RestController
@RequestMapping("/bucket-replenishment")
public class BucketReplenishmentController extends WorkflowController<BucketReplenishmentTodo, BucketReplenishmentFlow> {

    @Autowired
    ProjectionFactory projectionFactory;

    @Autowired
    BucketReplenishmentTodoRepository bucketReplenishmentTodoRepository;

    @Autowired
    BucketRepository bucketRepository;

    @Autowired
    private AccountBalanceService balanceService;

    @Autowired
    ExpenseNotificationService expenseNotificationService;

    @Override
    protected SelectFilter filter(SelectFilter selectFilter, String s, List<String> list, Map<String, String> map) {
        return selectFilter;
    }

    @Override
    protected Class<?> getProjectionClass() {
        return BucketReplenishmentProjection.class;
    }

    @Override
    public BaseRepository<BucketReplenishmentTodo> getRepository() {
        return bucketReplenishmentTodoRepository;
    }

    @Override
    public ResponseEntity<?> createEntity(BucketReplenishmentTodo entity) {
        entity.setTaskName(BucketReplenishmentTodoType.BUCKET_REPLENISHMENT_WAITING_FOR_APPROVE.toString());
        entity.setTransGuardService(entity.getBucket() != null ? entity.getBucket().getTransGuardService() : false);
        expenseNotificationService.expenseToDoCreatedEmail(entity.getEntityType(), entity.getTaskName());
        return super.createEntity(entity);
    }

    @PreAuthorize("hasPermission('BucketReplenishmentController','approveRequest')")
    @RequestMapping(value = "/approveRequest", method = RequestMethod.GET)
    public ResponseEntity<?> approveRequest(@RequestParam(name = "id", required = false) Long id) {
        BucketReplenishmentTodo todo = getRepository().findOne(id);
        approveRequest(todo);
        return okResponse();
    }

    private void approveRequest(BucketReplenishmentTodo todo) {
        todo.setAuditManagerApproved(true);
        this.completeTask(todo, BucketReplenishmentTodoType.BUCKET_REPLENISHMENT_WAITING_FOR_APPROVE.toString());
    }

    @PreAuthorize("hasPermission('BucketReplenishmentController','rejectRequest')")
    @RequestMapping(value = "/rejectRequest", method = RequestMethod.GET)
    public ResponseEntity<?> rejectRequest(@RequestParam(name = "id", required = false) Long id) {
        BucketReplenishmentTodo todo = getRepository().findOne(id);
        todo.setAuditManagerApproved(false);
        this.completeTask(todo, BucketReplenishmentTodoType.BUCKET_REPLENISHMENT_WAITING_FOR_APPROVE.toString());
        return okResponse();
    }

    @PreAuthorize("hasPermission('BucketReplenishmentController','doneRequest')")
    @RequestMapping(value = "/doneRequest", method = RequestMethod.GET)
    public ResponseEntity<?> doneRequest(@RequestParam(name = "id", required = false) Long id) {
        BucketReplenishmentTodo todo = getRepository().findOne(id);
        todo.setTransGuardDone(true);
        this.completeTask(todo, BucketReplenishmentTodoType.BUCKET_REPLENISHMENT_WAITING_FOR_APPROVE.toString());
        return okResponse();
    }

    @PreAuthorize("hasPermission('BucketReplenishmentController','getRequests')")
    @RequestMapping(value = "/getRequests", method = RequestMethod.GET)
    public ResponseEntity<?> getRequests(Pageable page) {
        SelectQuery<BucketReplenishmentTodo> query = new SelectQuery(BucketReplenishmentTodo.class);

        SelectFilter orFilter1 = new SelectFilter("status", "=", BucketReplenishmentTodoStatus.PENDING);

        SelectFilter orFilter2 = new SelectFilter("auditManagerApproved", "=", Boolean.TRUE)
                .and("completed", "=", Boolean.FALSE)
                .and("transGuardService", "=", Boolean.TRUE)
                .and("transGuardDone", "=", Boolean.FALSE);

        SelectFilter or = orFilter1.or(orFilter2);
        query.filterBy(or);

        Page<BucketReplenishmentTodo> result = query.execute(page);


        Page<BucketReplenishmentProjection> projectedResult = result.map(e -> {
            Bucket bucket = balanceService.setBucketBalanceBasedOnTransaction(e.getBucket());
            e.setBucketBalance(bucket.getBalance());
            return projectionFactory.createProjection(BucketReplenishmentProjection.class, e);
        });
        return new ResponseEntity<>(projectedResult, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('BucketReplenishmentController','addApprovedRequest')")
    @RequestMapping(value = "/addApprovedRequest/{bucketId}", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> addApprovedRequest(@PathVariable Long bucketId, @RequestBody Map<String, Object> map) {
        Map<String, Object> body = (Map<String, Object>) map.get("body");
        Bucket bucket = bucketRepository.findOne(bucketId);
        Double amount = Double.valueOf((String) body.get("amount"));
        Bucket fromBucket = null;
        if(body.containsKey("fromBucketId") && body.get("fromBucketId") != null){
            Long fromBucketId = Long.valueOf((Integer) body.get("fromBucketId")) ;
            fromBucket = bucketRepository.findOne(fromBucketId);
        }
        if (bucket == null)
            throw new RuntimeException("can not find bucket");
        if(!bucket.getAutoReplenishment() && fromBucket == null)
            throw new RuntimeException("fromBucket is required, the bucket Replenishment is not active!");
        BucketReplenishmentTodo todo = new BucketReplenishmentTodo();
        todo.setAmount(amount);
        todo.setAutoRequested(false);
        todo.setBucket(bucket);
        fromBucket = fromBucket == null ? bucket.getRefillerBucket(): fromBucket;
        todo.setFillFrom(fromBucket);
        todo.setRequestDate(new Date());
        todo = (BucketReplenishmentTodo) this.createEntity(todo).getBody();

        todo = getRepository().findOne(todo.getId());
        approveRequest(todo);

        return okResponse();
    }

    @PreAuthorize("hasPermission('BucketReplenishmentController','searchRequests')")
    @RequestMapping(value = "/searchRequests", method = RequestMethod.GET)
    public ResponseEntity<?> searchRequests(Pageable page,
                                            @RequestParam(value = "fromDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date fromDate,
                                            @RequestParam(value = "toDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date toDate,
                                            @RequestParam(value = "bucketId", required = false) Long bucketId) {
        SelectQuery<BucketReplenishmentTodo> query = new SelectQuery<>(BucketReplenishmentTodo.class);
        if (bucketId != null) {
            query.leftJoin("bucket");
            query.filterBy("bucket.id", "=", bucketId);
        }

        if (fromDate != null) {
            fromDate = DateUtil.getStartDayValue(fromDate);
            query.filterBy("requestDate", ">=", fromDate);
        }

        if (toDate != null) {
            toDate = DateUtil.getEndDayValue(toDate);
            query.filterBy("requestDate", "<=", toDate);
        }

        Page<BucketReplenishmentTodo> result = query.execute(page);
        Page projectedResult = result.map(e -> projectionFactory.createProjection(BucketReplenishmentProjection.class, e));
        return new ResponseEntity<>(projectedResult, HttpStatus.OK);
    }


    @PreAuthorize("hasPermission('bucket-replenishment','create-bucket-replenishment-request-if-needed')")
    @GetMapping(value = "/create-bucket-replenishment-request-if-needed/{bucketId}")
    public ResponseEntity createBucketReplenishmentRequestIfNeededAPI(@PathVariable("bucketId") Bucket bucket) {

        return createBucketReplenishmentRequestIfNeeded(bucket, null);
    }

    @Transactional
    public ResponseEntity createBucketReplenishmentRequestIfNeeded(
            Bucket bucket, Double bucketBalance) {

        if (bucketBalance == null) {
            // bucketBalance = balanceService.addAccountBalance(bucket);
            bucket = balanceService.setBucketBalanceBasedOnTransaction(bucket);
            if (bucket == null) return badRequestResponse();
            bucketBalance = bucket.getBalance();
        }


        if (bucketBalance >= bucket.getReplenishmentLevel()) {
            logger.info("Bucket# " + bucket.getCode() + "doesn't need Replenishment");
            logger.info("Bucket# " + bucket.getCode() + ", Balance = " + bucketBalance + ", Replenishment Level = " + bucket.getReplenishmentLevel());

            return ResponseEntity.ok("Bucket# " + bucket.getCode() + "doesn't need Replenishment" +
                    ", Balance = " + bucketBalance + ", Replenishment Level = " + bucket.getReplenishmentLevel());
        }

        BucketReplenishmentTodo todo = new BucketReplenishmentTodo();
        todo.setBucket(bucket);
        todo.setFillFrom(bucket.getRefillerBucket());
        todo.setAutoRequested(true);
        todo.setAmount(bucket.getLevelAfterReplenishment() - bucketBalance);
        todo.setRequestDate(new Date());
        todo.setStatus(BucketReplenishmentTodoStatus.PENDING);
        todo.setTodoName("Auto Replenishment for " + bucket.getName() + " " + new Date().toString());

        return ResponseEntity.ok(createEntity(todo));
    }
}