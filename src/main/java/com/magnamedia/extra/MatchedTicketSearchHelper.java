/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.extra;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.entity.Ticket;
import com.magnamedia.module.type.OperationType;
import java.util.Date;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
public class MatchedTicketSearchHelper {
    private Date purchaseDate;
    private Ticket.TicketType ticketType;
    private OperationType StatementType;
    private Double Fare;
    private Double margin;
    private PicklistItem cardUsed;
    private PicklistItem airLineCompany;
    private Date operationDate;
    private String Company;
    private Double amount;

    public Date getPurchaseDate() {
        return purchaseDate;
    }

    public void setPurchaseDate(Date purchaseDate) {
        this.purchaseDate = purchaseDate;
    }

    public Ticket.TicketType getTicketType() {
        return ticketType;
    }

    public void setTicketType(Ticket.TicketType ticketType) {
        this.ticketType = ticketType;
    }

    public OperationType getStatementType() {
        return StatementType;
    }

    public void setStatementType(OperationType StatementType) {
        this.StatementType = StatementType;
    }


    public Double getFare() {
        return Fare;
    }

    public void setFare(Double Fare) {
        this.Fare = Fare;
    }

    public Double getMargin() {
        return margin;
    }

    public void setMargin(Double margin) {
        this.margin = margin;
    }

    public PicklistItem getCardUsed() {
        return cardUsed;
    }

    public void setCardUsed(PicklistItem cardUsed) {
        this.cardUsed = cardUsed;
    }

    public PicklistItem getAirLineCompany() {
        return airLineCompany;
    }

    public void setAirLineCompany(PicklistItem airLineCompany) {
        this.airLineCompany = airLineCompany;
    }

   

    

    public Date getOperationDate() {
        return operationDate;
    }

    public void setOperationDate(Date operationDate) {
        this.operationDate = operationDate;
    }

    public String getCompany() {
        return Company;
    }

    public void setCompany(String Company) {
        this.Company = Company;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }
    
    
    
    
}
