package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.dto.salesbinder.ItemDto;
import com.magnamedia.module.type.OrderCycle;
import com.magnamedia.repository.CategoryRepository;
import com.magnamedia.service.ConsumptionRateService;

import javax.persistence.*;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <PERSON> (Jan 25, 2021)
 * nickName SalesBinderItem
 */

@Entity
public class Item extends BaseEntity {
    @Label
    private String name;
    @Column(unique = true)
    private String itemId;
    @Lob
    private String description;

    private BigDecimal quantity;
    private BigDecimal quantityOnCycleBegin = new BigDecimal("0");
    private Integer threshold;
    private Double cost;
    private Double price;
    private String sku;
    private String serialNumber;
    private String barcode;
    private String itemNumber;
    private String categoryIdStr;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Supplier lastSupplier;
    private Double lastPrice;
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Supplier bestSupplier;
    private Double bestPrice;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Category category;

    @Transient
    private String categoryOrderCycle;

    @ManyToOne(fetch = FetchType.LAZY)
    private UnitOfMeasure unitOfMeasure;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem measureOfConsumption;//ItemMeasureOfConsumption
    
    @Column(name="CONSUMPTION_RATE", precision = 18, scale = 10)
    private BigDecimal consumptionRate = new BigDecimal("0");
//    private BigDecimal initialCycleInventory;

    public Item() {
    }

    public Item(ItemDto dto, UnitOfMeasure unitOfMeasure) {
        this.name = dto.getName();
        this.itemId = dto.getItemId();
        this.description = dto.getDescription();
        this.quantity = dto.getQuantity();
        this.barcode = dto.getBarcode();
        this.categoryIdStr = dto.getCategoryId();
        this.cost = dto.getCost();
        this.itemNumber = dto.getItemNumber();
        this.price = dto.getPrice();
        this.serialNumber = dto.getSerialNumber();
        this.sku = dto.getSku();
        this.threshold = dto.getThreshold();
        this.unitOfMeasure = unitOfMeasure;
        category = Setup.getRepository(CategoryRepository.class).findByCategoryId(getCategoryIdStr());

    }

    public BigDecimal getQuantityOnCycleBegin() {
        return quantityOnCycleBegin;
    }

    public void setQuantityOnCycleBegin(BigDecimal quantityOnCycleBegin) {
        this.quantityOnCycleBegin = quantityOnCycleBegin;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public Integer getThreshold() {
        return threshold;
    }

    public void setThreshold(Integer threshold) {
        this.threshold = threshold;
    }

    public Category getCategory() {
        return category;
    }

    public void setCategory(Category category) {
        this.category = category;
    }


    public String getCategoryOrderCycle() {
        return (category != null && category.getOrderCycle() != null) ? category.getOrderCycle().getCode() : "";
    }

    public Double getCost() {
        return cost;
    }

    public void setCost(Double cost) {
        this.cost = cost;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getItemNumber() {
        return itemNumber;
    }

    public void setItemNumber(String itemNumber) {
        this.itemNumber = itemNumber;
    }

    public String getCategoryIdStr() {
        return categoryIdStr;
    }

    public void setCategoryIdStr(String categoryIdStr) {
        this.categoryIdStr = categoryIdStr;
    }

    public UnitOfMeasure getUnitOfMeasure() {
        return unitOfMeasure;
    }

    public String getUnitOfMeasureShortName() {
        if (unitOfMeasure == null) return null;
        return unitOfMeasure.getShortName();
    }

    public void setUnitOfMeasure(UnitOfMeasure unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public PicklistItem getMeasureOfConsumption() {
        return measureOfConsumption;
    }

    public void setMeasureOfConsumption(PicklistItem measureOfConsumption) {
        this.measureOfConsumption = measureOfConsumption;
    }

    public BigDecimal getConsumptionRate() {
        return consumptionRate;
    }

    public void setConsumptionRate(BigDecimal consumptionRate) {
        this.consumptionRate = consumptionRate;
    }

    public BigDecimal getInitialCycleInventory() {
        return Setup.getApplicationContext().getBean(ConsumptionRateService.class).calculateInitialCycleInventory(measureOfConsumption, consumptionRate, getOrderCycleDays()).setScale(2, RoundingMode.HALF_EVEN);
    }


    @JsonIgnore
    public BigDecimal getOrderCycleDays() {
        if (category == null || category.getOrderCycle() == null) return new BigDecimal(0);
        if (category.getOrderCycle().getCode().equals(OrderCycle.WEEKLY.getName()))
            return new BigDecimal(7);
        if (category.getOrderCycle().getCode().equals(OrderCycle.MONTHLY.getName()))
            return new BigDecimal("30.4");

        return new BigDecimal("0");
    }

    public Supplier getLastSupplier() {
        return lastSupplier;
    }

    public void setLastSupplier(Supplier lastSupplier) {
        this.lastSupplier = lastSupplier;
    }

    public Double getLastPrice() {
        return lastPrice;
    }

    public void setLastPrice(Double lastPrice) {
        this.lastPrice = lastPrice;
    }

    public Supplier getBestSupplier() {
        return bestSupplier;
    }

    public void setBestSupplier(Supplier bestSupplier) {
        this.bestSupplier = bestSupplier;
    }

    public Double getBestPrice() {
        return bestPrice;
    }

    public void setBestPrice(Double bestPrice) {
        this.bestPrice = bestPrice;
    }

    @JsonIgnore
    public BigDecimal getQuantityToOrder() {
        BigDecimal initialCycleInventory = getInitialCycleInventory();
        if (initialCycleInventory == null ||
                initialCycleInventory.compareTo(BigDecimal.ZERO) <= 0) return new BigDecimal(0);
        if (getQuantity() == null) return new BigDecimal(0);

        return initialCycleInventory.subtract(getQuantity());
    }
}
