package com.magnamedia.service;

import com.magnamedia.core.entity.Template;
import com.magnamedia.extra.CcSmsTemplateCode;
import com.magnamedia.helper.PicklistHelper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

@Service
public class CcAppSmsTemplate {
    public ArrayList<Template> getMessageTemplates() {
        return new ArrayList<Template>() {
            {

                add(new Template(CcSmsTemplateCode.CC_PAYMENT_EXPIRY_4_1_1_SMS.toString(), false,
                    "The Bank Payment Form that you’ve previously signed expires soon.To avoid interruptions in your " +
                        "service, we'll amend and resubmit your Bank Payment Forms to the bank. Please remember, " +
                        "you can stop the service and cancel your Bank Payment Forms at anytime by returning the maid " +
                        "to our center. If you have any questions, please reach us at: @chat_with_us_link@ .",
                    "In case of an unused signature, the message will be sent to the client"));

                add(new Template(CcSmsTemplateCode.CC_PAYMENT_EXPIRY_4_1_2_SMS.toString(), false,
                    "The Bank Payment Form that you signed expires on @ExpiryDate@. To continue your service, " +
                        "please click here to sign the new Bank Payment Form: @link_send_dd_details@ ." +
                        "Your monthly payments will remain AED @monthly_fee_of_nationality@ per month.",
                    "The message that will be sent to the client if there are no unused signatures"));
    
                add(new Template(CcSmsTemplateCode.CC_ACCOUNTING_PAY_VIA_CREDIT_CARD_SMS.toString(), false, "ACTION REQUIRED:\nTo pay your overdue payment of AED @amount@ via credit card, please click on the following link: @paytab_link@", "SMS contains the Credit Card Paying link"));

                add(new Template(CcSmsTemplateCode.CC_SIGN_DD_MESSAGE_SMS.toString(), false, "ACTION REQUIRED:\nYour spouse has decided to pay using a different bank account and has requested that we send you a link to sign for the monthly payments. Please click on the following link to sign: " +
                    "@spouse_sing_dd_Link@", "the message that will be sent to phone to sign"));

                add(new Template(CcSmsTemplateCode.CC_DD_PENDING_INFO_SMS.toString(), false,
                    "@greetings@, please click on the following link @link_send_dd_details@ using your phone, " +
                        "and complete your Bank Payment Form.",
                    "DD status is Pending and sub-status is Pending to Receive DD Info/Signature from Client, At 10 AM"));

                add(new Template(CcSmsTemplateCode.CC_CLIENT_REFUND_BANK_TRANSFER_DETAILS_SMS.toString(), false,
                    "We just sent you AED @amount@ by bank transfer. Please expect to receive the amount to your bank account within 7 business days. Click here to view the proof of transfer: " +
                    "@proof_of_transfer_link@. Thank you.", "Sent when Client's Bank Transfer is completed"));

                add(new Template(CcSmsTemplateCode.CC_ACCOUNTING_NOT_OWED_MONEY_FROM_CLIENT_8_1_2_SMS.toString(), false,
                    "Unfortunately, the bank didn't process the cancellation of your future payments on time. " +
                        "You were charged AED @amount@. Don't worry; we’ll send you that amount within the next 7 business days.",
                    "send to the Client If we receive an amount from the client when he doesn't owe us any amount"));

                add(new Template(CcSmsTemplateCode.CC_ACCOUNTING_WRONGLY_CHARGED_MONEY_ON_CLIENT_8_1_3_SMS.toString(), false,
                    "You were charged AED @amount@ today. But don’t worry, we’ll transfer you the same amount by @scheduled_termination_date@.",
                    "IF his contract is “scheduled for termination” AND we don’t need that amount"));

                add(new Template(CcSmsTemplateCode.CC_ACCOUNTING_OWE_MONEY_TO_CLIENT_8_1_1_SMS.toString(), false,
                    "We still owe you AED @remaining_balance@. We’ll send you the amount within the next 7 business days.",
                    "Client if we owe him money, and we need to transfer him the amount (For DD Only)"));

                add(new Template(CcSmsTemplateCode.CC_PAYTAB_THANKS_MESSAGE_SMS.toString(), false,
                    "Thank you for settling your payment for your maid's service. We just want to remind you that " +
                        "we can't accept Credit or Debit Card payments anymore.\n" +
                        "Once you have your Emirates ID and IBAN ready, would you be so kind as to complete your " +
                        "Monthly Bank Payment Form by clicking on “the following link: @link_send_dd_details@",
                    "After flow cash, upon receive paytab payment thanks message"));
    
                //ACC-3843
                add(new Template(CcSmsTemplateCode.CC_PAYMENT_NOT_RECEIVED_OR_BOUNCED_8_1_5_SMS.toString(), false,
                    "We’re sorry you’re disappointed, we won’t charge you for this month. Please tell us if there’s " +
                        "anything more we can do.",
                    "If (the client returned the maid before the end of the month and then he requested to cancel " +
                        "after the 1st of next month. Right after cancellation) AND (payment is not received or bounced)"));
    
                add(new Template(CcSmsTemplateCode.CC_PAYMENT_RECEIVED_8_1_5_SMS.toString(), false,
                    "We’re sorry you’re disappointed, we won’t charge you for this month. We'll refund the full " +
                        "amount to your bank account within 5 business days. Please tell us if there’s anything more " +
                        "we can do",
                    "If (the client returned the maid before the end of the month and then he requested to cancel " +
                        "after the 1st of next month. Right after cancellation) AND (payment is received)"));
    
                add(new Template(CcSmsTemplateCode.CC_PAYMENT_UNDER_PROCESSING_8_1_5_SMS.toString(), false,
                    "We’re sorry you’re disappointed, we won’t charge you for this month. Unfortunately, your " +
                        "payment is under process, if you are charged any amount, we'll refund the full amount to your bank " +
                        "account within 5 business days. Please tell us if there’s anything more we can do.",
                    "If (the client returned the maid before the end of the month and then he requested to cancel " +
                        "after the 1st of next month. Right after cancellation) AND (payment is under processing)"));

                add(new Template(CcSmsTemplateCode.CC_PAYMENT_NOT_RECEIVED_OR_BOUNCED_8_1_6_SMS.toString(), false,
                    "We’re sorry you’re disappointed, we won’t charge you for the days you hired the maid this " +
                        "month. Please tell us if there’s anything more we can do.",
                    "If (the client cancels within the first 3 days of the month) AND (payment is not received or bounced)"));

                add(new Template(CcSmsTemplateCode.CC_PAYMENT_RECEIVED_8_1_6_SMS.toString(), false,
                    "We’re sorry you’re disappointed, we won’t charge you for the days you hired the maid this month. " +
                        "We'll refund the full amount to your bank account within 5 business days. Please tell us if " +
                        "there’s anything more we can do.",
                    "If (the client cancels within the first 3 days of the month) AND (payment is received) AND (we don’t need money from the client)"));

                add(new Template(CcSmsTemplateCode.CC_PAYMENT_UNDER_PROCESSING_8_1_6_SMS.toString(), false,
                    "We’re sorry you’re disappointed, we won’t charge you for the days you hired the maid this month." +
                        " Unfortunately, your payment is under process, if you are charged any amount, we'll refund " +
                        "the full amount to your bank account within 5 business days. Please tell us if there’s " +
                        "anything more we can do.",
                    "If (the client cancels within the first 3 days of the month) AND (payment is under processing)"));

                //ACC-4905
                add(new Template(CcSmsTemplateCode.CC_PAYMENT_REMINDER_THANKS_MESSAGE_SMS.toString(),false,
                        "We've received your payment of AED @amount@. Thank you for using maids.cc",
                        "On receiving a card payment to extend for one more month"));
                add(new Template(CcSmsTemplateCode.CC_SIGNING_OFFER_PAYMENT_RECEIVED_THANKS_MESSAGE_SMS.toString(),false,
                        "We've received your payment for this month by card. Please note that we're unable to accept " +
                                "credit card payments in future.\nPlease whenever your Emirates ID and IBAN are ready, would you be so kind as to complete " +
                                "your Monthly Bank Payment Form by clicking on the following link: @link_send_dd_details@",
                        "On receiving a card payment to extend for one more month"));

                // ACC-4591
                add(new Template(CcSmsTemplateCode.CC_ACCOUNTING_PAY_ACCOMMODATION_FEE_SMS.toString(),false,
                    "Dear @client_name@, Thank you for using maids.cc. To pay your maid’s accommodation fee of AED @amount@ by " +
                        "Credit card, please click on this link: @paytabs_link@",
                    "Send SMS to client for pay your maid’s accommodation fee"));
                add(new Template(CcSmsTemplateCode.CC_ACCOUNTING_PAY_CC_TO_MV_SMS.toString(),false,
                    "Dear @client_name@, Thank you for using maids.cc. To hire your maid under the new payment plan and to " +
                        "pay the transfer fee of AED @amount@ by Credit card, please click @paytabs_link@",
                    "Send SMS to client for pay pay CC to MV"));
                add(new Template(CcSmsTemplateCode.CC_ACCOUNTING_PAY_MONTHLY_PAYMENT_SMS.toString(),false,
                    "Dear @client_name@, Thank you for using maids.cc. To pay your monthly payment of AED @amount@ by Credit card, " +
                        "please click: @paytabs_link@",
                    "Send SMS to client for pay Monthly Payment"));
                add(new Template(CcSmsTemplateCode.CC_ACCOUNTING_PAY_OVERSTAY_FEES_SMS.toString(),false,
                    "Dear @client_name@, Thank you for using maids.cc. To pay your maid's overstay fines of AED @amount@ by Credit card, " +
                        "please click: @paytabs_link@",
                    "Send SMS to client for pay Overstay fees"));
                add(new Template(CcSmsTemplateCode.CC_ACCOUNTING_PAY_PCR_TEST_SMS.toString(),false,
                    "Dear @client_name@, Thank you for using maids.cc. To pay your maid's PCR fees of " +
                        "AED @amount@ by Credit card, please click: @paytabs_link@",
                    "Send SMS to client for pay PCR Test"));
                add(new Template(CcSmsTemplateCode.CC_ACCOUNTING_PAY_URGENT_VISA_CHARGES_SMS.toString(),false,
                    "Dear @client_name@, Thank you for using maids.cc. To pay your maid's urgent visa " +
                        "fees of AED @amount@ by Credit card, please click: @paytabs_link@",
                    "Send SMS to client for pay Urgent Visa Charges"));
                add(new Template(CcSmsTemplateCode.CC_ACCOUNTING_PAY_INSURANCE_SMS.toString(),false,
                    "Dear @client_name@, Thank you for using maids.cc. To pay your maid's insurance fees of " +
                        "AED @amount@ by Credit card, please click: @paytabs_link@",
                    "Send SMS to client for pay Urgent Insurance"));
                add(new Template(CcSmsTemplateCode.CC_ACCOUNTING_PAY_OTHER_PAYMENTS_TYPES_SMS.toString(),false,
                    "Dear @client_name@, Thank you for using maids.cc. To pay AED @amount@ by Credit card, " +
                        "please click: @paytabs_link@",
                    "Send SMS to client for pay Other payments types"));
            }
        };
    }
}
