package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.type.template.ChannelSpecificSettingType;

import java.io.IOException;

/**
 * <AUTHOR> on 2017-07-20
 *
 */
public class TemplateSerilizer extends JsonSerializer<Template> {

    /* (non-Javadoc)
	 * @see com.fasterxml.jackson.databind.JsonSerializer#serialize(java.lang.Object, com.fasterxml.jackson.core.JsonGenerator, com.fasterxml.jackson.databind.SerializerProvider)
     */
    @Override
    public void serialize(Template value,
            JsonGenerator gen,
            SerializerProvider serializers)
            throws IOException, JsonProcessingException {
        if (value == null) {
            gen.writeNull();
            return;
        }
        gen.writeStartObject();
        gen.writeNumberField("id", value.getId());
        gen.writeStringField("label", value.getLabel());
        gen.writeStringField("name", value.getName());
        gen.writeStringField("text", value.getText());
        gen.writeStringField("priority", value.isChannelExist(ChannelSpecificSettingType.Notification) &&
                value.getChannelSetting(ChannelSpecificSettingType.Notification.toString()).getMessageDelayType() != null ?
                value.getChannelSetting(ChannelSpecificSettingType.Notification.toString()).getMessageDelayType().getName() : "");

        gen.writeEndObject();
    }

}
