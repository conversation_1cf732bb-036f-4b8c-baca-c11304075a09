package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Replacement;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Jul 24, 2019
 *         Jirra ACC-737
 */
@Repository
public interface ReplacementRepository extends BaseRepository<Replacement> {

    List<Replacement> findByOldHousemaid(Housemaid housemaid);

    List<Replacement> findByNewHousemaid(Housemaid housemaid);

    List<Replacement> findByNewHousemaidAndContract(Housemaid housemaid, Contract contract);

    List<Replacement> findByOldHousemaidAndContract(Housemaid housemaid, Contract contract);

    List<Replacement> findByOldHousemaidOrderByCreationDateDesc(Housemaid housemaid);

    @Query("SELECT rep from Replacement rep " +
            "INNER JOIN rep.complaint cp " +
            "INNER JOIN cp.primaryType tp " +
            "WHERE rep.oldHousemaid = :housemaid AND tp.causeFaultReplacement = true")
    List<Replacement> findFaultyReplacementsByOldHousemaid(@Param("housemaid") Housemaid housemaid);

    //Jirra ACC-1435
    Boolean existsByContract(Contract contract);

    //Jirra ACC-1435
    @Query("SELECT R.oldHousemaid FROM Replacement R WHERE R.contract = :contract and R.oldHousemaid is not null and "
            + "R.creationDate = (SELECT MAX(R1.creationDate) FROM Replacement R1 WHERE R1.contract = :contract and R1.oldHousemaid is not null)")
    Housemaid findLastReplacedHousemaidByContract(@Param("contract")Contract contract);

    boolean existsByContractAndIdNotAndNewHousemaidIsNotNullAndCreationDateGreaterThan(
            Contract contract, Long id, Date d);

    @Query("select r from Replacement r " +
            "where r.contract.id = ?1 and r.creationDate >= ?2 and r.newHousemaid is not null")
    List<Replacement> findByContractAndCreationDateGreaterThanEqual(Long contractTd, Date date);
}
