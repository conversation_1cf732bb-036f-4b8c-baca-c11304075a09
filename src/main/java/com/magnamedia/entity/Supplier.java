package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.entity.dto.salesbinder.SupplierDto;
import com.magnamedia.entity.serializer.IdLabelListSerializer;
import com.magnamedia.module.type.ExpensePaymentMethod;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <PERSON> (Jan 25, 2021)
 * NickName SalesBinderSupplier
 */
@Entity
public class Supplier extends BaseEntity {
    public Supplier() {
    }

    public Supplier(SupplierDto dto) {
        this.name = dto.getName();
        this.supplierId = dto.getSupplierId();
        this.webSite = dto.getWebSite();
        this.phoneNumber = dto.getPhoneNumber();
        this.email = dto.getEmail();
    }

    @Label
    private String name;
    @Column(unique = true)
    private String supplierId;
    private String webSite;
    private String phoneNumber;
    private String email;
    private String location;

    @Enumerated(EnumType.STRING)
    private ExpensePaymentMethod paymentMethod;
    private String iban;
    private String accountName;
    private String accountNumber;
    @Column
    @ColumnDefault("0")
    private Boolean hasNoIban = false;
    //    private String eid;
    private String mobileNumber;
    private Boolean vatRegistered;
    private String nameInFinancialStatement;
    private String swift;
    private Boolean international;
    private String address;

    // Jira ACC-4505
    @Column
    private Boolean isTicketNumberRequired;

    public Boolean getIsTicketNumberRequired() {
        return isTicketNumberRequired != null && isTicketNumberRequired;
    }

    public void setIsTicketNumberRequired(Boolean isTicketNumberRequired) {
        this.isTicketNumberRequired = isTicketNumberRequired;
    }

    @Column
    private String bankName;

    @Column
    private String bankCountryName;

    @Column
    private String bankCityName;

    @Column(columnDefinition = "boolean default true")
    private boolean active = true;

    @ElementCollection
    @CollectionTable(name = "SUPPLIER_PAYMENT_DETAILS",
            joinColumns = @JoinColumn(name = "SUPPLIER_ID"))
    List<SupplierPaymentDetail> historyPaymentDetails;

    @BeforeInsert
    public void updatePaymentDetail() {
        if (this.paymentMethod == null) return;

        if (historyPaymentDetails == null) historyPaymentDetails = new ArrayList<>();

        SupplierPaymentDetail paymentDetail = generateSupplierPaymentDetail();
        if (historyPaymentDetails.size() != 0) {
            SupplierPaymentDetail lastDetail = historyPaymentDetails.get(historyPaymentDetails.size() - 1);
            if (lastDetail.equals(paymentDetail))
                return;
        }
        historyPaymentDetails.add(paymentDetail);

    }

    private SupplierPaymentDetail generateSupplierPaymentDetail() {
        SupplierPaymentDetail paymentDetail = new SupplierPaymentDetail();
        paymentDetail.setChangeDate(new Date());
        paymentDetail.setPaymentMethod(this.paymentMethod);
        switch (paymentMethod) {
            case CASH:

                break;
            case BANK_TRANSFER:
                paymentDetail.setAccountName(this.accountName);
                paymentDetail.setIban(this.iban);
                paymentDetail.setSwift(this.swift);
                paymentDetail.setAddress(this.address);
                //Jirra ACC-3522
                paymentDetail.setAccountNumber(this.accountNumber);
                paymentDetail.setHasNoIban(this.hasNoIban);

                break;
            case MONEY_TRANSFER:
                paymentDetail.setIban(this.iban);
                paymentDetail.setMobileNumber(this.mobileNumber);

                break;
            case INVOICED:
                break;
            case CREDIT_CARD:
                break;
            case SALARY:
                break;
            case CHEQUE:
                break;

        }
        return paymentDetail;
    }

    public List<SupplierPaymentDetail> getHistoryPaymentDetails() {
        return historyPaymentDetails;
    }

    public void setHistoryPaymentDetails(List<SupplierPaymentDetail> historyPaymentDetails) {
        this.historyPaymentDetails = historyPaymentDetails;
    }


    @ManyToMany(mappedBy = "suppliers", fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelListSerializer.class)
    List<Expense> expenses = new ArrayList<>();

    public List<Expense> getExpenses() {
        return expenses;
    }

    public void setExpenses(List<Expense> expenses) {
        this.expenses = expenses;
    }

    public String getNameInFinancialStatement() {
        return nameInFinancialStatement;
    }

    public void setNameInFinancialStatement(String nameInFinancialStatement) {
        this.nameInFinancialStatement = nameInFinancialStatement;
    }

    public Boolean getVatRegistered() {
        return vatRegistered != null && vatRegistered;
    }

    public void setVatRegistered(Boolean vatRegistered) {
        this.vatRegistered = vatRegistered;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId;
    }

    public String getWebSite() {
        return webSite;
    }

    public void setWebSite(String webSite) {
        this.webSite = webSite;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public ExpensePaymentMethod getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(ExpensePaymentMethod paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getIban() {
        return iban;
    }

    public void setIban(String iban) {
        this.iban = iban;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

//    public String getEid() {
//        return eid;
//    }
//
//    public void setEid(String eid) {
//        this.eid = eid;
//    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public Boolean getHasNoIban() {
        return hasNoIban;
    }

    public void setHasNoIban(Boolean hasNoIban) {
        this.hasNoIban = hasNoIban;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getSwift() {
        return swift;
    }

    public void setSwift(String swift) {
        this.swift = swift;
    }

    public Boolean getInternational() {
        return international != null && international;
    }

    public void setInternational(Boolean international) {
        this.international = international;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getBankName() {return bankName;}

    public void setBankName(String bankName) {this.bankName = bankName;}

    public String getBankCountryName() {return bankCountryName;}

    public void setBankCountryName(String bankCountryName) {this.bankCountryName = bankCountryName;}

    public String getBankCityName() {return bankCityName;}

    public void setBankCityName(String bankCityName) {this.bankCityName = bankCityName;}

    public boolean isActive() { return active; }

    public void setActive(boolean active) { this.active = active; }
}