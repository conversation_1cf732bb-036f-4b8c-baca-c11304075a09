package com.magnamedia.entity.dto;

import com.magnamedia.core.entity.Attachment;
import com.magnamedia.module.type.ExpensePaymentMethod;

import java.math.BigDecimal;
import java.util.List;

/**
 * <PERSON> (Feb 09, 2021)
 */
public class PurchaseBillInfoDto {
    private Long id;
    private ExpensePaymentMethod paymentMethod;
    private Boolean taxable;
    private BigDecimal vatAmount;
    private BigDecimal totalItemsCost;
    private BigDecimal totalBillAmount;
    private BigDecimal deliveryService;
    private List<Attachment> attachments;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public ExpensePaymentMethod getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(ExpensePaymentMethod paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public Boolean getTaxable() {
        return taxable;
    }

    public void setTaxable(Boolean taxable) {
        this.taxable = taxable;
    }

    public BigDecimal getVatAmount() {
        return vatAmount;
    }

    public void setVatAmount(BigDecimal vatAmount) {
        this.vatAmount = vatAmount;
    }

    public BigDecimal getTotalItemsCost() {
        return totalItemsCost;
    }

    public void setTotalItemsCost(BigDecimal totalItemsCost) {
        this.totalItemsCost = totalItemsCost;
    }

    public BigDecimal getTotalBillAmount() {
        return totalBillAmount;
    }

    public void setTotalBillAmount(BigDecimal totalBillAmount) {
        this.totalBillAmount = totalBillAmount;
    }

    public BigDecimal getDeliveryService() {
        return deliveryService;
    }

    public void setDeliveryService(BigDecimal deliveryService) {
        this.deliveryService = deliveryService;
    }

    public List<Attachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<Attachment> attachments) {
        this.attachments = attachments;
    }
}
