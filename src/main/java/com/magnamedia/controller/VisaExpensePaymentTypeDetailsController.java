/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.VisaExpenseConfiguration;
import com.magnamedia.entity.VisaExpensePaymentTypeDetails;
import com.magnamedia.module.type.PaymentType;
import com.magnamedia.repository.VisaExpensePaymentTypeDetailsRepository;
import com.magnamedia.service.VisaExpenseService;
import com.magnamedia.workflow.visa.ExpensePurpose;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on July 16, 2020
 * Jirra ACC-1970
 */
@RequestMapping("/visaExpensePaymentTypeDetails")
@RestController
public class VisaExpensePaymentTypeDetailsController extends BaseRepositoryController<VisaExpensePaymentTypeDetails> {

    @Autowired
    private VisaExpensePaymentTypeDetailsRepository visaExpensePaymentTypeDetailsRepository;

    @Override
    public BaseRepository<VisaExpensePaymentTypeDetails> getRepository() {
        return visaExpensePaymentTypeDetailsRepository;
    }

    @Override
    protected ResponseEntity<?> createEntity(VisaExpensePaymentTypeDetails entity) {

        validateEntity(entity);

        return super.createEntity(entity);
    }

    @Override
    protected ResponseEntity<?> updateEntity(VisaExpensePaymentTypeDetails entity) {

        validateEntity(entity);

        return super.updateEntity(entity);
    }


    @Override
    public ResponseEntity<?> list(String searchQuery) {
        List<VisaExpensePaymentTypeDetails> all = visaExpensePaymentTypeDetailsRepository.findAll();
        HashMap<String, Object> response = new HashMap<>();
        Date lastUpdateDate = all.stream().map(BaseEntity::getLastModificationDate).max(Date::compareTo).get();
        response.put("last_updated", lastUpdateDate);
        response.put("data", all);

        Map<String, String> m = new HashMap<>();
        VisaExpenseConfiguration.EmployeeType[] array = VisaExpenseConfiguration.EmployeeType.values();
        for (VisaExpenseConfiguration.EmployeeType employeeType: array) {
            m.put(employeeType.getValue(), employeeType.getLabel());
        }
        response.put("employeeTypes", m);

        m = new HashMap<>();
        ExpensePurpose[] array1 = ExpensePurpose.values();
        for (ExpensePurpose expensePurpose: array1) {
            m.put(expensePurpose.name(), expensePurpose.getLabel());
        }
        response.put("expensePurposes", m);

        return ResponseEntity.ok(response);
    }


    @PreAuthorize("hasPermission('bddactivationfiles','updateAllRecords')")
    @RequestMapping(value = "/updateAllRecords", method = RequestMethod.POST)
    public ResponseEntity<?> updateAllRecords(@RequestBody List<VisaExpensePaymentTypeDetails> list) {

        // using for each not save all in order to make @before update method called from entity
        for (VisaExpensePaymentTypeDetails visaExpensePaymentTypeDetails : list) {
            VisaExpensePaymentTypeDetails one = visaExpensePaymentTypeDetailsRepository.findOne(visaExpensePaymentTypeDetails.getId());

            validateEntity(visaExpensePaymentTypeDetails);

            one.setCharge(visaExpensePaymentTypeDetails.getCharge());
            one.setVatChargePercentage(visaExpensePaymentTypeDetails.getVatChargePercentage());
            one.setServiceChargeOfExpense(visaExpensePaymentTypeDetails.getServiceChargeOfExpense());
            one.setVatChargeOfExpense(visaExpensePaymentTypeDetails.getVatChargeOfExpense());
            visaExpensePaymentTypeDetailsRepository.save(one);
        }


        return okResponse();
    }

    private void validateEntity(VisaExpensePaymentTypeDetails entity) {

        if (visaExpensePaymentTypeDetailsRepository.existsByPaymentTypeAndExpensePurposeAndIdNot(
                entity.getPaymentType(), entity.getExpensePurpose(), entity.getId() == null ? -1L : entity.getId()))
            throw new BusinessException("There is already a payment type charge with the same expense type and payment type");

        if (entity.getCharge() < 0 ||
                entity.getVatChargePercentage() < 0 ||
                entity.getServiceChargeOfExpense() < 0 ||
                entity.getVatChargeOfExpense() < 0) {
            throw new BusinessException("Records can't be set with negative values");
        }
    }

    @PreAuthorize("hasPermission('visaExpensePaymentTypeDetails','initializePaymentTypeCharges')")
    @PostMapping(value = "/initializePaymentTypeCharges")
    @Transactional
    public ResponseEntity<?> initializeVisaExpenseConfigurations(
            MultipartFile file,
            @RequestParam(name = "deleteOld", required = false, defaultValue = "false") boolean deleteOld) throws IOException {


        if (deleteOld) {
            visaExpensePaymentTypeDetailsRepository.deleteAll();
        }

        initializePaymentTypeChargesReadSheetData(file);

        return ResponseEntity.ok("Done");
    }

    private void initializePaymentTypeChargesReadSheetData(MultipartFile file) throws IOException {

        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        XSSFSheet sheet = workbook.getSheetAt(0);

        for (Row row : sheet) {
            if (row.getRowNum() == 0) continue;
            logger.info("Row Num: " + row.getRowNum());
            String type = row.getCell(0).getStringCellValue();

            if (type == null || type.isEmpty()) break;
            ExpensePurpose expensePurpose = ExpensePurpose.valueOf(row.getCell(1).getStringCellValue());
            double ServiceChargeOfExpense = row.getCell(2).getNumericCellValue();
            double vatChargeOfExpense = row.getCell(3).getNumericCellValue();

            PaymentType paymentType = PaymentType.valueOf(row.getCell(5).getStringCellValue());
            Double ServiceChargeOfPayment = row.getCell(6).getNumericCellValue();
            Double vatChargeOfPayment = row.getCell(7).getNumericCellValue();

            VisaExpensePaymentTypeDetails v = new VisaExpensePaymentTypeDetails();
            v.setPaymentType(paymentType);
            v.setCharge(ServiceChargeOfPayment);
            v.setVatChargePercentage(vatChargeOfPayment);

            v.setExpensePurpose(expensePurpose);
            v.setServiceChargeOfExpense(ServiceChargeOfExpense);
            v.setVatChargeOfExpense(vatChargeOfExpense);

            createEntity(v);
        }
    }

    @PreAuthorize("hasPermission('visaExpensePaymentTypeDetails','getVisaExpenseVatAmount')")
    @GetMapping(value = "/getVisaExpenseVatAmount")
    public ResponseEntity<?> getVisaExpenseVatAmount(PaymentType paymentType, String expensePurpose) {

        ExpensePurpose purpose = Arrays.stream(ExpensePurpose.values())
                .filter(e -> e.getLabel().equalsIgnoreCase(expensePurpose))
                .findFirst()
                .orElse(null);

        VisaExpensePaymentTypeDetails v = visaExpensePaymentTypeDetailsRepository
                .findFirstByPaymentTypeAndExpensePurpose(paymentType, purpose);
        Double amount = VisaExpenseService.calculateVatViaVisaExpensePaymentTypeDetails(v);

        return ResponseEntity.ok(amount);
    }
}
