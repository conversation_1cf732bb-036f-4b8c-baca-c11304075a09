package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.Payment;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.service.PaymentService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */

// From CM ACC-6103
@BusinessRule(moduleCode = "", entity = DirectDebit.class, events = {BusinessEvent.AfterUpdate}, fields = {"id", "status", "bank.id"})
public class DirectDebitPaymentsBankBusinessRule implements BusinessAction<DirectDebit> {
    private static final Logger logger = Logger.getLogger(DirectDebitPaymentsBankBusinessRule.class.getName());

    @Override
    public boolean validate(DirectDebit directDebit, BusinessEvent be) {
        logger.info("Validation dd id: " + (directDebit.getId() == null ? "NULL" : directDebit.getId()));

        List<Payment> payments =  Setup.getRepository(PaymentRepository.class).findByDirectDebitId(directDebit.getId());
        if(directDebit.getBank() == null || directDebit.getBank().getId() == null) return false;
        return !payments.stream()
                .filter(p-> p.getBankName() == null || !p.getBankName().getId().equals(directDebit.getBank().getId()))
                .collect(Collectors.toList())
                .isEmpty() ;
    }

    @Override
    public Map<String, Object> execute(DirectDebit entity, BusinessEvent be) {
        logger.info("execute dd id: " + (entity.getId() == null ? "NULL" : entity.getId()));

        PaymentRepository paymentRepository = Setup.getRepository(PaymentRepository.class);
        DirectDebit directDebit = Setup.getRepository(DirectDebitRepository.class).findOne(entity.getId());
        PicklistItem bank = Setup.getRepository(PicklistItemRepository.class).findOne(entity.getBank().getId());
        PaymentService paymentService = Setup.getApplicationContext().getBean(PaymentService.class);
        List<Payment> paymentsToUpdate = new ArrayList<>();
        List<Payment> payments =  paymentRepository.findByDirectDebitId(directDebit.getId());
        for (Payment payment : payments) {
            payment.setBankName(bank);
            paymentsToUpdate.add(payment);
        }
        paymentService.forceUpdatePayment(paymentsToUpdate);
        return null;
    }
}