package com.magnamedia.extra;

import com.aspose.words.ConvertUtil;
import com.magnamedia.controller.ContractPaymentTermController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.WordTemplate;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.entity.*;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.ContractPaymentTypeRepository;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.service.CalculateDiscountsWithVatService;
import com.magnamedia.service.PaymentService;
import com.magnamedia.service.WordImage;
import com.magnamedia.service.WordTemplateService;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.joda.time.DateTime;
import org.joda.time.Months;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on Jan 20, 2021
 *         Jirra ACC-2897
 */

public class PaymentReceiptHelper {
    protected static final Logger logger = Logger.getLogger(PaymentReceiptHelper.class.getName());

    public static Attachment generatePaymentsReceipt(
            ContractPaymentTerm cpt,
            Client client,
            Housemaid housemaid,
            InputStream signature,
            WordTemplate wordTemplate) {

        InputStream in = null;
        try {
            in = getPaymentsReceiptStream(cpt, client, housemaid, signature, wordTemplate);
            if(in == null) {
                logger.info("stream is null -> exiting");
                return null;
            }

            Attachment attachment = Storage.storeTemporary("Payment Terms Form.pdf",
                    in, ContractPaymentTermController.FILE_TAG_PAYMENTS_RECEIPT, true);

            return attachment;
        } finally {
            StreamsUtil.closeStream(in);
        }
    }

    // SAL-2843
    public static InputStream getPaymentsReceiptStream(
            ContractPaymentTerm cpt,
            Client client, Housemaid housemaid,
            InputStream signature,
            WordTemplate template) {

        if (housemaid == null) housemaid = cpt.getContract().getHousemaid();
        if (client == null) client = cpt.getContract().getClient();
        if (housemaid == null || client == null) return null;
        if (template == null) throw new RuntimeException("There is no suitable word template");

        List<HashMap> payments = getPaymentsReceiptTermForms(cpt);

        logger.info("payments size: " + payments);

        return generatePaymentsReceiptFile(payments, cpt, client, housemaid, signature, template);
    }

    // ACC-6207
    public static InputStream getFuturePaymentsReceiptStream(
            ContractPaymentTerm cpt,
            WordTemplate template)  {

        Housemaid housemaid = cpt.getContract().getHousemaid();
        Client client = cpt.getContract().getClient();
        if (housemaid == null || client == null) return null;

        if (template == null) {
            throw new RuntimeException("There is no suitable word template");
        }

        List<HashMap> payments = getPaymentsReceiptTermForms(cpt);
        payments.forEach(p -> {
            logger.info("before: " + p.entrySet());

            Map<String, Object> paymentInfo = (Map<String, Object>) p.get("paymentInfo");
            SelectQuery<Payment> q = new SelectQuery<>(Payment.class);
            q.filterBy("contract", "=", cpt.getContract());
            q.filterBy("typeOfPayment.code", "=", paymentInfo.get("typeCode"));
            q.filterBy("status", "=", PaymentStatus.RECEIVED);
            q.sortBy("dateOfPayment", false, true);
            q.setLimit(1);

            List<Payment> l = q.execute();
            if (l.isEmpty()) return;

            DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
            DateTime startsOn = formatter.parseDateTime((String) paymentInfo.get("startsOn"));
            if (startsOn.isAfter(new DateTime(l.get(0).getDateOfPayment()))) return;


            if (paymentInfo.get("frequency").equals("0")) {
                p.put("skipped", true);
                return;
            }
            DateTime commencesOn = new DateTime(l.get(0).getDateOfPayment())
                    .plusMonths((Integer) paymentInfo.get("frequency"));
            p.put("commencesOn", DateUtil.formatMonthDayYear(commencesOn.toDate()));
            p.put("startsOn", commencesOn);

            String frequencyStatement = "";
            ContractPaymentType type = cpt.getContractPaymentTypes()
                    .stream()
                    .filter(t -> t.getType().equals(l.get(0).getTypeOfPayment()))
                    .findFirst()
                    .orElse(null);

            if (type == null) return;

            if (paymentInfo.get("endsOn") != null) {
                DateTime endsOn = formatter.parseDateTime((String) paymentInfo.get("endsOn"));
                if (endsOn.isBefore(commencesOn)) {
                    p.put("skipped", true);
                    return;
                }
                int numOfMonth = Months.monthsBetween(commencesOn, endsOn).getMonths() + 1;
                frequencyStatement = DDUtils.getPaymentFrequencyStatement(type, numOfMonth);
                if (numOfMonth > 1) {
                    frequencyStatement += " (for " + numOfMonth + " months)";
                }
                p.put("frequency", frequencyStatement);
            }
        });

        payments.forEach(p -> logger.info("after: " + p.entrySet()));
        return generatePaymentsReceiptFile(
                payments.stream()
                        .filter(p -> !p.containsKey("skipped"))
                        .collect(Collectors.toList()),
                cpt, client, housemaid, null, template);

    }

    public static List<HashMap> getPaymentsReceiptTermForms(ContractPaymentTerm cpt)  {
        List<AbstractPaymentTypeConfig> contractPaymentTypes = getPaymentTermTypes(cpt);

        Map<String, Object> map = new HashMap<>();
        map.put("contractStartDate", cpt.getContract().getStartOfContract() != null
                ? new DateTime(cpt.getContract().getStartOfContract()) : DateTime.now());
        map.put("isWeeklyPkg", cpt.getWeeklyAmount() > 0.0);
        AbstractPaymentTypeConfig monthlyPaymentType = contractPaymentTypes.stream().filter(
                        item -> item.getType().getCode().equals(PaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE))
                .findFirst().orElse(null);
        if (monthlyPaymentType == null) return new ArrayList<>();

        map.put("monthlyPaymentType", monthlyPaymentType);
        map.put("isProrated", BooleanUtils.toBoolean(cpt.getContract().getIsProRated())
                && monthlyPaymentType != null && monthlyPaymentType.getStartsOn().equals(0) && cpt.getFirstMonthPayment() > 0);
        map.put("isProratedPlusMonth", BooleanUtils.toBoolean(cpt.getContract().getProRatedPlusMonth()));
        map.put("firstMonthPayment", cpt.getFirstMonthPayment());
        map.put("additionalDiscountMonths", cpt.getAdditionalDiscountMonths() != null && cpt.getAdditionalDiscountMonths() > 0 ?
                cpt.getAdditionalDiscountMonths() : 0);
        map.put("paymentsDuration", cpt.getContract().getPaymentsDuration());
        map.put("additionalDiscount", cpt.getAdditionalDiscount());
        map.put("workerSalary", cpt.getContract().getWorkerSalaryWithoutVat());
        map.put("dailyRateAmount", cpt.getPaymentTermConfig() != null ? cpt.getPaymentTermConfig().getDailyRateAmount() : 0.0);
        map.put("isMaidVisa", cpt.getContract().isMaidVisa());
        map.put("ptc", cpt.getPaymentTermConfig());
        map.put("contractPaymentTypes", contractPaymentTypes);
        map.put("periodicalAdditionalDiscount", cpt.getPeriodicalAdditionalDiscount());
        map.put("affectedByAdditionalDiscount", monthlyPaymentType.getAffectedByAdditionalDiscount());
        map.put("monthlyPaymentDiscount", monthlyPaymentType.getDiscount());
        map.put("monthlyPaymentDiscountEffectiveAfter", monthlyPaymentType.getDiscountEffectiveAfter());
        map.put("monthlyPaymentAmount", monthlyPaymentType.getAmount());
        map.put("isOneMonthAgreement", cpt.getContract().isOneMonthAgreement());
        map.put("waivedMonths", cpt.getContract().getWaivedMonths());

        // ACC-7151
        map.put("creditNoteAmount", cpt.getCreditNote() != null && cpt.getCreditNote() > 0.0 ?
                cpt.getCreditNote() : 0.0);
        map.put("creditNoteMonths", cpt.getCreditNoteMonths() != null && cpt.getCreditNoteMonths() > 0 ?
                cpt.getCreditNoteMonths() : 0);

        // ACC-8721
        map.put("isWorkerSalaryVatted", cpt.getContract().isWorkerSalaryVatted());

        return generatePaymentTermForms(map);
    }

    public static InputStream generatePaymentsReceiptFile(
            List<HashMap> payments,
            ContractPaymentTerm cpt,
            Client client,
            Housemaid housemaid,
            InputStream signature,
            WordTemplate template) {

        List<StandardEvaluationContext> paymentsContext = payments.stream()
                .map(o -> new StandardEvaluationContext(o))
                .collect(Collectors.toList());

        InputStream in = null;
        try {
            Map parameters = getPaymentReceiptParameters(cpt, client, housemaid, signature);
            XWPFDocument document = WordDocumentHelper.parseTables(
                    Storage.getStream(template.getAttachment("template")),
                    paymentsContext, parameters);

            ByteArrayOutputStream out = new ByteArrayOutputStream();
            InputStream inputStream = null;
            try {
                document.write(out);
                inputStream = new ByteArrayInputStream(out.toByteArray());
                in = Setup.getApplicationContext().getBean(WordTemplateService.class)
                        .generateDocument(inputStream, parameters);
            } finally {
                StreamsUtil.closeStream(inputStream);
                StreamsUtil.closeStream(out);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return in;
    }

    private static Map<String, Object> getPaymentReceiptParameters(
            ContractPaymentTerm term,
            Client client, Housemaid housemaid,
            InputStream signature) {

        Utils utils = Setup.getApplicationContext().getBean(Utils.class);

//        double vatValue = Double.parseDouble(Setup.getParameter(Setup.getModule("sales"), AccountingModule.PARAMETER_CONTRACT_VAT_PERCENT));
        Contract contract = term.getContract();
        boolean isMaidVisa = contract.isMaidVisa();

        // ACC-1558
        Double higherPriceBound = Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_CONTRACT_PAYMENTS_RECEIPT_HIGHER_PRICE_BOUND));

        Double discount = isMaidVisa ? higherPriceBound - term.getDiscountedMonthlyPaymentWithoutVat() : term.getDiscount();
        Double generalDiscount = higherPriceBound - term.getMonthlyPaymentWithoutVat();

        PicklistItemRepository picklistItemRepository = Setup.getApplicationContext().getBean(PicklistItemRepository.class);
        Map<String, Object> parameters = new HashMap();
        PicklistItem nationality = picklistItemRepository.findOne(housemaid != null ? housemaid.getNationality().getId() : term.getNationality().getId());

        parameters.put("client_name", utils.getWordTemplateParamValue(client.getName()));
        parameters.put("client_mobile",
                utils.getWordTemplateParamValue(client.getMobileNumber()));
        parameters.put("today_date", utils.getWordTemplateParamValue(DateUtil.formatNotDashedFullDate(new Date())));
        parameters.put("maid_name", utils.getWordTemplateParamValue(housemaid != null ? housemaid.getName() : ""));
        parameters.put("housemaid_nationality_english",
                utils.getWordTemplateParamValue(nationality != null ? nationality.getName() : ""));
        parameters.put("housemaid_nationality_arabic", utils.getWordTemplateParamValue(nationality == null ? ""
                : nationality.hasTag("arabic_label") ? nationality.getTagValue("arabic_label").getValue() : ""));

        parameters.put("payment_term_discount", utils.getWordTemplateParamValue(discount));
        parameters.put("pre_discount_month", utils.getWordTemplateParamValue(term.getDiscountEffectiveAfter()));

        // ACC-1677
//        if (term.getContract().getContractProspectType().getCode().equals(PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE))) {
//            parameters.put("discount_effected_after_arabic", utils.getMonthOrder(term.getDiscountEffectiveAfter(), "ar"));
//            parameters.put("discount_effected_after_english", utils.getMonthOrder(term.getDiscountEffectiveAfter(), "en"));
//        }

        logger.log(Level.INFO, "MMM discount effected after: " + term.getDiscountEffectiveAfter());

        parameters.put("signature", signature != null
                ? new WordImage(signature, ConvertUtil.pixelToPoint(160), ConvertUtil.pixelToPoint(47)) : "");
//        parameters.put("vat_prcnt", vatValue);

        // ACC-2897
        parameters.put("general_discount", utils.getWordTemplateParamValue(generalDiscount));
        parameters.put("higher_price_bound", utils.getWordTemplateParamValue(higherPriceBound));
        parameters.put("maid_type_en", nationality.getCode().equals("philippines") ? "Nanny" : "Maid");
        parameters.put("maid_type_ar", nationality.getCode().equals("philippines") ? "مربية أطفال" : "خادمة");

        boolean hasDiscount = isMaidVisa;
        parameters.put("hasDiscount", hasDiscount);

        try {
            Map<String, Double> amounts =  getPaymentsBreakdownWeeklyMonthlyAmount(term.getPaymentTermConfig());

            Double weeklyAmount = amounts.get("weeklyAmount");
            Double totalAmount = amounts.get("totalAmount");

            if (weeklyAmount != null) {
                parameters.put("payments_breakdown_weekly_amount", utils.getWordTemplateParamValue(weeklyAmount));
            }
            if (totalAmount != null) {
                parameters.put("payments_breakdown_monthly_amount", utils.getWordTemplateParamValue(totalAmount));
            }

        } catch (Exception ex) {
            logger.log(Level.SEVERE, "ERROR: " + ex.getMessage(), ex);
        }
        // ACC-6977
        parameters.put("live_out_amount", utils.getWordTemplateParamValue(Optional.ofNullable(term.getLiveOutAmount()).orElse(0.0), true));

        // ACC-5759
        if (isMaidVisa) {
            parameters.put("package_ar", contract.getMvContractPackage() == null ||
                    contract.getMvContractPackage().equals(MVPackageType.FLEXIBLE) ? "المرنة" : "المؤقتة");
            parameters.put("package_en", contract.getMvContractPackage() == null ||
                    contract.getMvContractPackage().equals(MVPackageType.FLEXIBLE) ? "Flexible" : "Temporary");
        }
        return parameters;
    }

    private static Double getPaymentsBreakdownWeeklyAmount(AbstractPaymentTerm ptc) {
        String url = "/sales/paymenttermconfig/getpaymentbreakdownbynationality?nationality=" + ptc.getNationality().getId() + "&weeklyPlan=true";
        logger.log(Level.INFO, "MMM URL: " + url);
        LinkedHashMap responseEntity = Setup.getApplicationContext().getBean(InterModuleConnector.class).get(url, LinkedHashMap.class);
        Object amount = responseEntity.get("weeklyAmount");

        return amount == null ? null : Double.parseDouble(responseEntity.get("weeklyAmount").toString());
    }


    public static Map<String, Double> getPaymentsBreakdownWeeklyMonthlyAmount(PaymentTermConfig ptc) {
        String url = "/sales/paymenttermconfig/getpaymentbreakdownbynationality?nationality=" + ptc.getNationality().getId() + "&weeklyPlan=true";
        logger.log(Level.INFO, "MMM URL: " + url);
        LinkedHashMap responseEntity = Setup.getApplicationContext().getBean(InterModuleConnector.class).get(url, LinkedHashMap.class);
        Object weeklyAmount = responseEntity.containsKey("weeklyAmount") ? responseEntity.get("weeklyAmount") : null;
        Object totalAmount = responseEntity.containsKey("totalAmount") ? responseEntity.get("totalAmount") : null;

        Map<String, Double> amounts = new HashMap<>();

        amounts.put("weeklyAmount", weeklyAmount == null ? null : Double.parseDouble(responseEntity.get("weeklyAmount").toString()));
        amounts.put("totalAmount", totalAmount == null ? null : Double.parseDouble(responseEntity.get("totalAmount").toString()));

        return amounts;
    }

    private static List<AbstractPaymentTypeConfig> getPaymentTermTypes(ContractPaymentTerm cpt) {
        return cpt.getContractPaymentTypes().stream().sorted(
                Comparator.comparingInt(ContractPaymentType::getStartsOn).thenComparingInt(
                        item -> item.getType().getCode().equals(PaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE) ? 1 : 0))
                .collect(Collectors.toList());
    }

    private static Map<Date, HashMap> getPaymentTermsConfigsForWeeklyPkg(Map<String, Object> map) {

        logger.log(Level.INFO, "getPaymentTermsConfigsForWeeklyPkg start");
        AbstractPaymentTerm ptc = (AbstractPaymentTerm) map.get("ptc");
        AbstractPaymentTypeConfig monthlyPaymentType = (AbstractPaymentTypeConfig) map.get("monthlyPaymentType");
        Integer additionalDiscountedPaymentsCount = (Integer) map.get("additionalDiscountedPaymentsCount");
        Double additionalDiscountAmountPerPayment = (Double) map.get("additionalDiscountAmountPerPayment");
        Integer discountEffectiveAfter = (Integer) map.get("monthlyPaymentDiscountEffectiveAfter");
        DateTime contractStartDate = (DateTime) map.get("contractStartDate");
        boolean isProrated = (boolean) map.get("isProrated");
        boolean isProratedPlusMonth = (boolean) map.get("isProratedPlusMonth");
        Map<Date, HashMap> paymentInfoList = new TreeMap<>();

        String description = monthlyPaymentType.getDescription();
        String frequencyStatement = "Weekly";

        int shiftMonths = isProratedPlusMonth ? 2 : isProrated ? 1 : 0;

        // ACC-7050
        Integer waivedMonths = (Integer) map.getOrDefault("waivedMonths", 0);
        if ((boolean) map.get("isMaidVisa") && waivedMonths > 0 && shiftMonths == 0) {
            shiftMonths += waivedMonths;
        }

        DateTime commencesOnDate = new DateTime(contractStartDate).plusMonths(monthlyPaymentType.getStartsOn());
        commencesOnDate = commencesOnDate.plusMonths(shiftMonths);
        Double amount = getWeeklyAmount(ptc); // fixed value // updated http://jira.maids.cc/browse/SAL-3517

        if (monthlyPaymentType.getAffectedByAdditionalDiscount() && additionalDiscountAmountPerPayment != 0
                && additionalDiscountedPaymentsCount > 0) {

            logger.log(Level.INFO, "additionalDiscount payment");

            int numOfMonths = discountEffectiveAfter == null || discountEffectiveAfter.equals(0) ?
                    additionalDiscountedPaymentsCount :
                    Math.min(additionalDiscountedPaymentsCount, discountEffectiveAfter);

            String commencesOn = DDUtils.getCommencesOn(commencesOnDate, contractStartDate) + " to ";
            commencesOn += DateUtil.formatMonthDayYear(commencesOnDate.plusMonths(numOfMonths - 1).dayOfMonth().withMaximumValue().toDate());

            double DWA = Math.round((amount) - ((DiscountsWithVatHelper.getAmountPlusVat(additionalDiscountAmountPerPayment)  / 4)));
            DateTime startsOn = DDUtils.getCommencesOnDate(commencesOnDate, contractStartDate);
            HashMap paymentInfo = getPaymentInfo(null, description, frequencyStatement,
                    commencesOn, DWA, DiscountsWithVatHelper.getAmountWithoutVat(DWA), monthlyPaymentType.getType().getCode(), startsOn,
                    commencesOnDate.plusMonths(numOfMonths), false, "FULL",
                    Double.valueOf(Math.round(additionalDiscountAmountPerPayment / 4)), monthlyPaymentType.getRecurrence());
            logger.log(Level.INFO, "additionalDiscount payment paymentInfo " + paymentInfo.entrySet());
            paymentInfoList.put(startsOn.withMillisOfDay(new DateTime().getMillisOfDay()).toDate(), paymentInfo);
            commencesOnDate = commencesOnDate.plusMonths(numOfMonths);
        }

        String commencesOn = DDUtils.getCommencesOn(commencesOnDate, contractStartDate) + " onwards";
        Double weeklyAmountFee = getPaymentsBreakdownWeeklyAmount(ptc);
        Double amountWithoutVat = DiscountsWithVatHelper.getAmountWithoutVat(ptc.getWeeklyAmount()) + (weeklyAmountFee != null ? weeklyAmountFee : 0.0);
        DateTime startsOn = DDUtils.getCommencesOnDate(commencesOnDate, contractStartDate);
        HashMap paymentInfo = getPaymentInfo(null, description, frequencyStatement, commencesOn,
                amount, amountWithoutVat, monthlyPaymentType.getType().getCode(), startsOn,
                null, true, "FULL", null, monthlyPaymentType.getRecurrence());
        logger.log(Level.INFO, "getPaymentTermsConfigsForWeeklyPkg paymentInfo " + paymentInfo.entrySet());
        paymentInfoList.put(startsOn.withMillisOfDay(new DateTime().getMillisOfDay()).toDate(), paymentInfo);

        return paymentInfoList;
    }

    private static Double getWeeklyAmount(AbstractPaymentTerm ptc) {
        String url = "/sales/paymenttermconfig/getpaymentbreakdownbynationality?nationality=" + ptc.getNationality().getId() + "&weeklyPlan=true";

        logger.log(Level.INFO, "MMM URL: " + url);
        LinkedHashMap responseEntity = Setup.getApplicationContext().getBean(InterModuleConnector.class).get(url, LinkedHashMap.class);
        Object amount = responseEntity.get("weeklyAmount");
        if (amount == null) {
            throw new RuntimeException("Weekly amount is null for PTC: " + ptc.getId());
        }
        double breakDownWeeklyAmount = Double.parseDouble(responseEntity.get("weeklyAmount").toString());

        return ptc.getWeeklyAmount() + Math.round(DiscountsWithVatHelper.getAmountPlusVat(breakDownWeeklyAmount));
    }

    public static LinkedHashMap getPaymentBreakdownsFromSales(PaymentTermConfig ptc, Boolean isWeekly, Boolean isLiveOut) {
        String url = "/sales/paymenttermconfig/getpaymentbreakdownbynationality?nationality="
                + ptc.getNationality().getId() + "&weeklyPlan=" + isWeekly + "&isLiveOut=" + isLiveOut;

        logger.log(Level.INFO, "MMM URL: " + url);
        LinkedHashMap responseEntity = Setup.getApplicationContext().getBean(InterModuleConnector.class).get(url, LinkedHashMap.class);
//        Object amount = responseEntity.get("weeklyAmount");
//        if (amount == null) {
//            throw new RuntimeException("Weekly amount is null for PTC: " + ptc.getId());
//        }

        return responseEntity;
    }

    public static List<HashMap> generatePaymentTermForms(Map<String, Object> map) {
        logger.log(Level.INFO, "Start");
        List<AbstractPaymentTypeConfig> contractPaymentTypes = (List<AbstractPaymentTypeConfig>) map.get("contractPaymentTypes");

        //Monthly payment
        Map<Date, HashMap> payments = new TreeMap<>(generateMonthlyPaymentTermForms(map));

        //Non-monthly payment
        map.put("isMonthlyPayment", false);
        int startDayPeriod  = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_DD_GEN_POSTPONE_START_AFTER_X_DAY));
        contractPaymentTypes.stream().filter(item ->
                !Arrays.asList(PaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE,
                                PaymentTypeConfig.MONTHLY_PAYMENT_ADD_ON_TYPE_CODE)
                        .contains(item.getType().getCode()))
                .forEach(contractPaymentType -> {
            map.put("contractPaymentType", contractPaymentType);
            map.put("discountEffectiveAfter", DDUtils.getDiscountAffectedAfter(
                    contractPaymentType, (Boolean) map.get("isProratedPlusMonth")));
            map.put("affectedByAdditionalDiscount", contractPaymentType.getAffectedByAdditionalDiscount());
            map.putAll(getAdditionalDiscountPaymentsCount(map));
            Map<Date, HashMap> nonMonthlyPayments = generateNonMonthlyPaymentTermForms(map);

            nonMonthlyPayments.forEach((date, term) -> {
                Map<String, Object> paymentInfo = (Map<String, Object>) term.get("paymentInfo");
                DateTime startDate = DateTimeFormat.forPattern("yyyy-MM-dd 00:00:00")
                        .parseDateTime((String) paymentInfo.get("startsOn"));
                Integer frequency = (Integer) paymentInfo.getOrDefault("frequency", 0);
                if (!startDate.toString("yyyy-MM-dd")
                        .equals(((DateTime) map.get("contractStartDate")).toString("yyyy-MM-dd"))) {
                    startDate = frequency != 0 ? startDate.plusDays(startDayPeriod) : startDate.dayOfMonth().withMinimumValue();
                    paymentInfo.put("startsOn", startDate.toString("yyyy-MM-dd 00:00:00"));
                    term.put("commencesOn", DDUtils.getCommencesOnForNonMonthly(startDate));
                    term.put("paymentInfo", paymentInfo);
                }
                payments.put(date, term);
            });
        });

        Map<Date, HashMap> finalPayments = new TreeMap<>(updatePaymentWithCreditNote(payments, map));

        Map<Date, HashMap> finalList = new TreeMap<>(finalPayments);

        if (map.containsKey("consumePaidPayments") && (boolean) map.get("consumePaidPayments") && map.containsKey("contractId")) {
            Contract c = Setup.getRepository(ContractRepository.class).findOne((Long) map.get("contractId"));
            if (c != null) finalList = generatePaymentTermFormsConsumePaidPayments(finalList, c, map);

        }

        int count = 1;
        for (Map<String, Object> p : finalList.values()) {
            p.put("paymentMethod", "Bank Debit Form " + count);
            p.put("num", count);
            count++;
        }
        logger.log(Level.INFO, "End");
        return new ArrayList<>(finalList.values());
    }

    // ACC-7151
    public static Map<Date, HashMap> updatePaymentWithCreditNote(Map<Date, HashMap> payments, Map<String,Object> map) {

        List<AbstractPaymentTypeConfig> contractPaymentTypes = (List<AbstractPaymentTypeConfig>) map.get("contractPaymentTypes");

        AbstractPaymentTypeConfig t =
                contractPaymentTypes.stream().filter(AbstractPaymentTypeConfig::getAffectedByAdditionalDiscount)
                        .findFirst().orElse(null);

        if (t == null) return payments;

        logger.log(Level.INFO, "Start");
        Double creditNoteAmount = (Double) map.getOrDefault("creditNoteAmount", 0.0D);
        Integer creditNoteMonths = (Integer) map.getOrDefault("creditNoteMonths", 0);
        if (creditNoteAmount == 0.0 || creditNoteMonths == 0) return payments;
        logger.info("creditNoteAmount: " + creditNoteAmount + "; creditNoteMonths: " + creditNoteMonths);

        Map<String, Object> discountPeriod = getDiscountPeriod(
                creditNoteMonths, (int) map.getOrDefault("waivedMonths", 0),
                new DateTime(map.get("contractStartDate")), t,
                (boolean) map.get("isOneMonthAgreement"),
                (boolean) map.get("isProrated"), (boolean) map.get("isProratedPlusMonth"));
        DateTime creditNoteStartDate = (DateTime) discountPeriod.get("discountStartDate");
        DateTime creditNoteEndDate = (DateTime) discountPeriod.get("discountEndDate");

        Double creditNoteAmountPerPayment = creditNoteAmount / creditNoteMonths /
                (t.getType().getCode().equals("monthly_payment") && (boolean) map.get("isWeeklyPkg") ? 4 : 1);
        Double creditNoteAmountPerMonth = creditNoteAmount / creditNoteMonths;

        Map<Date, HashMap> finalPayments = new HashMap<>();
        logger.info("payments: " + payments.entrySet());

        payments.forEach((startsOn, payment) -> {
            Map<String, Object> paymentInfo = new HashMap<>((Map<String, Object>) payment.get("paymentInfo"));
            DateTime startDate = new DateTime(startsOn).withTimeAtStartOfDay();

            if (!paymentInfo.get("typeCode").equals((t.getType().getCode()))){
                finalPayments.put(startsOn, payment);
                return;
            }

            if (startDate.withTimeAtStartOfDay().isAfter(creditNoteEndDate)) {
                finalPayments.put(startsOn, payment);
                return;
            }

            if (paymentInfo.containsKey("monthlyPaymentType") &&
                    paymentInfo.get("monthlyPaymentType").equals("PRO_RATED")) {
                finalPayments.put(startsOn, payment);
                return;
            }

            if (paymentInfo.get("endsOn") != null &&
                    startDate.isAfter(creditNoteStartDate.minusMillis(1)) &&
                    DateTimeFormat.forPattern("yyyy-MM-dd 00:00:00")
                            .parseDateTime((String) paymentInfo.get("endsOn"))
                            .isBefore(creditNoteEndDate.plusMillis(1))) {

                addAmountAndAmountWithoutVatForCreditNote(map, payment, creditNoteAmountPerPayment);
                paymentInfo.put("creditNoteAmount", creditNoteAmountPerMonth);
                payment.put("paymentInfo", paymentInfo);
                finalPayments.put(startsOn, payment);
            } else if (paymentInfo.get("endsOn") != null &&
                    startDate.isAfter(creditNoteStartDate.minusMillis(1)) &&
                    !startDate.isAfter(creditNoteEndDate) &&
                    DateTimeFormat.forPattern("yyyy-MM-dd 00:00:00")
                            .parseDateTime((String) paymentInfo.get("endsOn"))
                            .isAfter(creditNoteEndDate)) {

                HashMap newPayment = new HashMap(payment);
                Map<String, Object> newPaymentInfo = new HashMap<>((Map<String, Object>) newPayment.get("paymentInfo"));
                newPayment.put("commencesOn", DateUtil.formatMonthDayYear(creditNoteEndDate.plusDays(1).toDate()));
                newPaymentInfo.put("startsOn", creditNoteEndDate.plusDays(1).toString("yyyy-MM-dd 00:00:00"));
                newPayment.put("paymentInfo", newPaymentInfo);
                if (!(boolean) map.get("isWeeklyPkg")) {
                    newPayment.put("frequency", getFrequencyStatement(t, creditNoteEndDate.plusDays(1),
                            DateTimeFormat.forPattern("yyyy-MM-dd 00:00:00")
                                    .parseDateTime((String) paymentInfo.get("endsOn"))));
                }

                finalPayments.put(creditNoteEndDate.plusDays(1).toDate(), newPayment);

                paymentInfo.put("endsOn", creditNoteEndDate.toString("yyyy-MM-dd 00:00:00"));
                paymentInfo.put("creditNoteAmount", creditNoteAmountPerMonth);
                payment.put("paymentInfo", paymentInfo);
                addAmountAndAmountWithoutVatForCreditNote(map, payment, creditNoteAmountPerPayment);
                if ((boolean) map.get("isWeeklyPkg")) {
                    payment.put("commencesOn", getWeeklyPackageCommencesOn(startDate, new DateTime(creditNoteEndDate)));
                } else {
                    payment.put("frequency", getFrequencyStatement(t, new DateTime(startsOn), creditNoteEndDate));
                }
                finalPayments.put(startsOn, payment);
            } else if (startDate.isAfter(creditNoteStartDate.minusMillis(1)) &&
                    startDate.isBefore(creditNoteEndDate)) {

                if (t.getRecurrence() == null || t.getRecurrence() == 0) {
                    addAmountAndAmountWithoutVatForCreditNote(map, payment, creditNoteAmountPerPayment);
                    paymentInfo.put("creditNoteAmount", creditNoteAmountPerMonth);
                    payment.put("paymentInfo", paymentInfo);
                    finalPayments.put(startsOn, payment);
                    return;
                }

                HashMap newPayment = new HashMap(payment);
                Map<String, Object> newPaymentInfo = new HashMap<>((Map<String, Object>) newPayment.get("paymentInfo"));
                newPayment.put("commencesOn", DateUtil.formatMonthDayYear(creditNoteEndDate.plusDays(1).toDate()) +
                        ((boolean) map.get("isWeeklyPkg") ? " onwards" : "") );
                newPaymentInfo.put("startsOn", creditNoteEndDate.plusDays(1).toString("yyyy-MM-dd 00:00:00"));
                newPayment.put("paymentInfo", newPaymentInfo);
                if (!(boolean) map.get("isWeeklyPkg")) {
                    newPayment.put("frequency", getFrequencyStatement(t,
                            creditNoteEndDate.plusDays(1),
                            paymentInfo.get("endsOn") != null ?
                                    DateTimeFormat.forPattern("yyyy-MM-dd 00:00:00")
                                            .parseDateTime((String) paymentInfo.get("endsOn")) : null));
                }

                finalPayments.put(creditNoteEndDate.plusDays(1).toDate(), newPayment);

                paymentInfo.put("endsOn", creditNoteEndDate.toString("yyyy-MM-dd 00:00:00"));
                paymentInfo.put("indefinite", false);
                paymentInfo.put("creditNoteAmount", creditNoteAmountPerMonth);
                payment.put("paymentInfo", paymentInfo);
                addAmountAndAmountWithoutVatForCreditNote(map, payment, creditNoteAmountPerPayment);
                if ((boolean) map.get("isWeeklyPkg")) {
                    payment.put("commencesOn", getWeeklyPackageCommencesOn(startDate, new DateTime(creditNoteEndDate)));
                } else {
                    payment.put("frequency", getFrequencyStatement(t, new DateTime(startsOn), creditNoteEndDate));
                }

                finalPayments.put(startsOn, payment);
            }
        });

        logger.info("finalPayments: " + finalPayments.entrySet());

        return finalPayments;
    }

    public static Map<String, Object> getDiscountPeriod(
            int discountMonth, int waivedMonths, DateTime contractStartDate, AbstractPaymentTypeConfig t,
            boolean isOneMonthAgreement, boolean isProrated, boolean isProratedPlusMonth) {

        Map<String, Object> m = new HashMap<>();
        int startOn =  0;
        int endsAfter = discountMonth;
        DateTime contractStartDateTime = contractStartDate.dayOfMonth().withMinimumValue();

        if (t.getType().getCode().equals("monthly_payment") && waivedMonths > 0) {
            startOn = startOn + waivedMonths;
        } else if (t.getType().getCode().equals("monthly_payment")) {
            if (isOneMonthAgreement) {
                endsAfter = endsAfter + 1;
                contractStartDateTime =contractStartDate;
            } else if (isProrated && !isProratedPlusMonth) {
                startOn = startOn + 1;
            }
        } else {
            endsAfter = endsAfter * (t.getRecurrence() == 0 ? 1 : t.getRecurrence());
        }

        m.put("discountStartDate", contractStartDateTime.plusMonths(startOn).withTimeAtStartOfDay());
        m.put("discountEndDate", ((DateTime) m.get("discountStartDate")).plusMonths(endsAfter - 1).dayOfMonth().withMaximumValue());

        return m;
    }

    private static String getWeeklyPackageCommencesOn(DateTime startDate, DateTime endDate) {
        int numOfMonths = Months.monthsBetween(startDate, endDate).getMonths() + 1;

        String commencesOn =  DateUtil.formatMonthDayYear(startDate.toDate());
        if (numOfMonths <=  1) return commencesOn;

        return commencesOn + " to " + DateUtil.formatMonthDayYear(endDate.toDate());
    }

    private static void addAmountAndAmountWithoutVatForCreditNote(Map<String, Object> map, HashMap payment, Double creditNoteAmountPerPayment) {

        Double amount = DiscountsWithVatHelper.getFinalAmountAfterAddDiscount(
                ((Long) payment.get("amount")).doubleValue(), creditNoteAmountPerPayment);

        payment.put("amount", Math.round(Math.floor(amount)));

        Double amountWithoutVat = DiscountsWithVatHelper.getAmountWithoutVat(
                (Boolean) map.get("isMaidVisa"), (Boolean) map.get("isWorkerSalaryVatted"),
                ((boolean) payment.getOrDefault("includedWorkerSalary", false)),
                (Double) map.get("workerSalary"), amount);

        // TODO ACC-8721 check with Sales
        payment.put("amountWithoutVat", Integer.parseInt(String.valueOf(Math.round(Math.floor(amountWithoutVat)))));
    }

    public static String getFrequencyStatement(AbstractPaymentTypeConfig paymentType, DateTime startDate, DateTime endDate) {
        String frequencyStatement;

        if (endDate != null) {
            int numOfMonths = Months.monthsBetween(startDate, endDate).getMonths() + 1;

            frequencyStatement = DDUtils.getPaymentFrequencyStatement(paymentType, numOfMonths);
            if (numOfMonths > 1) {
                frequencyStatement += " (for " + numOfMonths + " months)";
            }
        } else frequencyStatement = DDUtils.getPaymentFrequencyStatement(paymentType);
        return frequencyStatement;
    }

    private static Map<Date, HashMap> generatePaymentTermFormsConsumePaidPayments(Map<Date, HashMap> l, Contract c, Map<String, Object> map) {
        Map<Date, HashMap> finalList = new TreeMap<>();

        l.forEach((date, term) -> {
            Map<String, Object> paymentInfo = (Map<String, Object>) term.get("paymentInfo");
            HashMap payment = paymentInfo.get("typeCode").equals("monthly_payment") ?
                    generatePaymentTermFormsConsumePaidPayments_monthlyPayment(term, c, map) :
                    generatePaymentTermFormsConsumePaidPayments_nonMonthlyPayment(term, c, map);

            if (payment != null) {
                paymentInfo = (Map<String, Object>) payment.get("paymentInfo");
                DateTime startDate = DateTimeFormat.forPattern("yyyy-MM-dd 00:00:00")
                        .parseDateTime((String) paymentInfo.get("startsOn"));
                finalList.put(startDate.withMillisOfDay(new DateTime().getMillisOfDay()).toDate(), payment);
            }
        });

        return finalList;
    }

    private static HashMap generatePaymentTermFormsConsumePaidPayments_monthlyPayment(HashMap p, Contract c, Map<String, Object> map) {

        Map<String, Object> paymentInfo = (Map<String, Object>) p.get("paymentInfo");
        DateTime contractStartDate = new DateTime(c.getStartOfContract());
        DateTime startDate = DateTimeFormat.forPattern("yyyy-MM-dd 00:00:00")
                .parseDateTime((String) paymentInfo.get("startsOn"));

        if (contractStartDate.toString("yyyy-MM-dd")
                .equals(new DateTime(c.getPaidEndDate()).toString("yyyy-MM-dd")) ||
                startDate.isAfter(new DateTime(c.getPaidEndDate()))) return p;

        DateTime endDate = paymentInfo.get("endsOn") == null ? null : DateTimeFormat.forPattern("yyyy-MM-dd 00:00:00")
                .parseDateTime((String) paymentInfo.get("endsOn"));
        DateTime paidEndDatePlusOneDay = new DateTime(c.getPaidEndDate()).plusDays(1);
        AbstractPaymentTypeConfig monthlyPaymentType = (AbstractPaymentTypeConfig) map.get("monthlyPaymentType");


        if ((boolean) map.get("isOneMonthAgreement")) {
            if (startDate.toString("yyyy-MM").equals(contractStartDate.toString("yyyy-MM"))) return null;

            boolean proratedIsReceived = Setup.getRepository(PaymentRepository.class).existsMonthlyPaymentReceived(c.getId(), true);
            if (endDate != null && paymentInfo.getOrDefault("monthlyPaymentType", "").equals("PRO_RATED")) {
                if (proratedIsReceived) return null;

                startDate = paidEndDatePlusOneDay;
                endDate =  startDate.dayOfMonth().withMaximumValue();
                paymentInfo.put("endsOn", endDate.toString("yyyy-MM-dd 00:00:00"));
                map.put("proRatedDate", startDate.toLocalDate());
                p.put("amount", Setup.getApplicationContext()
                        .getBean(CalculateDiscountsWithVatService.class)
                        .getProRatedAmount(map));
                p.put("amountWithoutVat", Double.valueOf(DiscountsWithVatHelper.getAmountWithoutVat((Double) p.get("amount"))).intValue());
            } else {
                startDate =  proratedIsReceived ? paidEndDatePlusOneDay :
                        new DateTime(c.getPaidEndDate()).plusMonths(1).dayOfMonth().withMinimumValue();
                p.put("frequency", getFrequencyStatement(monthlyPaymentType, startDate, endDate));
            }
            if (endDate != null && endDate.isBefore(startDate)) return null;

            paymentInfo.put("startsOn", startDate.toString("yyyy-MM-dd 00:00:00"));
            p.put("commencesOn", DateUtil.formatMonthDayYear(startDate.toDate()));
            p.put("paymentInfo", paymentInfo);
            return p;
        } else {
            int frequency = (int) paymentInfo.getOrDefault("frequency", 0);
            if (startDate.isBefore(paidEndDatePlusOneDay) &&
                    (frequency == 0 || (endDate != null && endDate.isBefore(paidEndDatePlusOneDay)))) return null;

            if ((boolean) map.get("isWeeklyPkg")) {
                String commencesOn;
                if (endDate != null) {
                    int numOfMonths = Months.monthsBetween(paidEndDatePlusOneDay, endDate).getMonths() + 1;
                    commencesOn = DDUtils.getCommencesOn(paidEndDatePlusOneDay, contractStartDate) + " to ";
                    commencesOn += DateUtil.formatMonthDayYear(paidEndDatePlusOneDay.plusMonths(numOfMonths - 1).dayOfMonth().withMaximumValue().toDate());

                } else {
                    commencesOn = DDUtils.getCommencesOn(paidEndDatePlusOneDay, contractStartDate) + " onwards";
                }
                paymentInfo.put("startsOn", paidEndDatePlusOneDay.toString("yyyy-MM-dd 00:00:00"));
                p.put("commencesOn", commencesOn);
                p.put("paymentInfo", paymentInfo);

                return p;
            }

            startDate = paidEndDatePlusOneDay;
            p.put("frequency", getFrequencyStatement(monthlyPaymentType, startDate, endDate));

            paymentInfo.put("startsOn", startDate.toString("yyyy-MM-dd 00:00:00"));
            String commencesOn = DDUtils.getCommencesOn(startDate, contractStartDate);
            p.put("commencesOn", commencesOn);
            p.put("paymentInfo", paymentInfo);

            return p;
        }
    }


    private static HashMap generatePaymentTermFormsConsumePaidPayments_nonMonthlyPayment(HashMap p, Contract c, Map<String, Object> map) {

        Map<String, Object> paymentInfo = (Map<String, Object>) p.get("paymentInfo");
        DateTime startDate = DateTimeFormat.forPattern("yyyy-MM-dd 00:00:00")
                .parseDateTime((String) paymentInfo.get("startsOn"));
        DateTime contractStartDate = new DateTime(c.getStartOfContract());
        DateTime endDate = paymentInfo.get("endsOn") == null ? null : DateTimeFormat.forPattern("yyyy-MM-dd 00:00:00")
                .parseDateTime((String) paymentInfo.get("endsOn"));

        DateTime lastPaymentDate = Setup.getApplicationContext()
                .getBean(PaymentService.class)
                .getLastReceivedPaymentDateByType(c, (String) paymentInfo.get("typeCode"));

        // ACC-8118 #2
        if (lastPaymentDate != null && lastPaymentDate.isBefore(contractStartDate)) {
            lastPaymentDate = contractStartDate.withTimeAtStartOfDay();
        }

        if (lastPaymentDate == null || (startDate.isAfter(lastPaymentDate)
                && !startDate.toString("yyyy-MM").equals(lastPaymentDate.toString("yyyy-MM")))) {
            return p;
        }

        Integer frequency = (Integer) paymentInfo.getOrDefault("frequency", 0);
        if ((startDate.isBefore(lastPaymentDate) ||
                (startDate.toString("yyyy-MM").equals(lastPaymentDate.toString("yyyy-MM")))) &&
                (frequency == 0 || (endDate != null &&
                        endDate.isBefore(lastPaymentDate.plusMonths(frequency).dayOfMonth().withMaximumValue().plusDays(1))))) {
            return null;
        }

        AbstractPaymentTypeConfig contractPaymentType = ((List<AbstractPaymentTypeConfig>) map.get("contractPaymentTypes"))
                .stream().filter(t -> t.getType().getCode().equals(paymentInfo.get("typeCode")))
                .findFirst().orElse(null);

        int directDebitCount = c.getIsProRated() || c.isOneMonthAgreement() ? c.getPaymentsDuration() : c.getPaymentsDuration() - 1;
        DateTime contractEndDate = contractStartDate.plusMonths(directDebitCount).dayOfMonth().withMaximumValue();

        // ACC-8118 #1
        if (lastPaymentDate.toString("yyyy-MM-dd")
                .equals(contractStartDate.toString("yyyy-MM-dd")) &&
                frequency != 0) {
            lastPaymentDate = lastPaymentDate.plusDays(Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                    AccountingModule.PARAMETER_DD_GEN_POSTPONE_START_AFTER_X_DAY)));
        }

        if (startDate.toString("yyyy-MM-dd")
                .equals(contractStartDate.toString("yyyy-MM-dd")) &&
                frequency != 0) {
            startDate = startDate.plusDays(Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                    AccountingModule.PARAMETER_DD_GEN_POSTPONE_START_AFTER_X_DAY)));
        }

        while (startDate.isBefore(lastPaymentDate) ||
                startDate.toString("yyyy-MM")
                        .equals(lastPaymentDate.toString("yyyy-MM"))) {
            startDate = startDate.plusMonths(frequency);
        }

        if (startDate.isBefore(contractEndDate) && (contractPaymentType.getEndsAfter() == null ||
                (startDate.isBefore(new DateTime(c.getStartOfContract()).plusMonths(contractPaymentType.getEndsAfter()))))) {

            paymentInfo.put("startsOn", startDate.toString("yyyy-MM-dd 00:00:00"));
            p.put("paymentInfo", paymentInfo);
            p.put("commencesOn", DDUtils.getCommencesOnForNonMonthly(startDate));
            return p;
        }

        return null;
    }

    public static Map<Date, HashMap> generateMonthlyPaymentTermForms(Map<String, Object> map) {
        logger.log(Level.INFO, "start");
        Map<Date, HashMap> payments = new TreeMap<>();
        AbstractPaymentTypeConfig monthlyPaymentType = (AbstractPaymentTypeConfig) map.get("monthlyPaymentType");
        if (monthlyPaymentType == null) return payments;
        
        logger.log(Level.INFO, "building treemap");
        TreeMap<Integer, HashMap<String, Object>> monthlyPayments = new TreeMap<>();
        boolean isWeeklyPkg = (boolean) map.get("isWeeklyPkg");
        boolean isProrated = (boolean) map.get("isProrated");
        boolean isProratedPlusMonth = (boolean) map.get("isProratedPlusMonth");
        DateTime contractStartDate = (DateTime) map.get("contractStartDate");
        Double workerSalary = (Double) map.get("workerSalary");
        boolean isMaidVisa = (boolean) map.get("isMaidVisa");
        boolean isOneMonthAgreement = (boolean) map.get("isOneMonthAgreement");

        map.put("contractPaymentType", monthlyPaymentType);
        map.put("discountEffectiveAfter", DDUtils.getDiscountAffectedAfter(
                monthlyPaymentType, isProratedPlusMonth));
        map.put("isMonthlyPayment", true);
        map.putAll(getAdditionalDiscountPaymentsCount(map));
        
        Integer startOn = monthlyPaymentType.getStartsOn();;
        Double additionalDiscountAmountPerPayment = (Double) map.get("additionalDiscountAmountPerPayment");
        Integer additionalDiscountedPaymentsCount = (Integer) map.get("additionalDiscountedPaymentsCount");
        Integer discountEffectiveAfter = (Integer) map.get("discountEffectiveAfter");

        //ACC-4905
        map.put("proRatedDate", isOneMonthAgreement ? contractStartDate.plusMonths(1).toLocalDate() :
            contractStartDate.toLocalDate());
        double proRatedAmount = Setup.getApplicationContext()
                .getBean(CalculateDiscountsWithVatService.class)
                .getProRatedAmount(map);

        if (isWeeklyPkg && !isOneMonthAgreement) {
            logger.log(Level.INFO, "isWeeklyPkg true");
            
            if (isProrated || isProratedPlusMonth) {
                logger.log(Level.INFO, "getPaymentTermsConfigs isProrated " + isProrated);
                String description = monthlyPaymentType.getDescription();
                String commencesOn = DDUtils.getCommencesOn(contractStartDate, contractStartDate);
                DateTime endsOn;
                Map<String, Double> proPlusAmountInfo = new HashMap<>();

                if (isProratedPlusMonth) {
                    proPlusAmountInfo = getProPlusMonthPaymentAmount(map);
                    proRatedAmount = proPlusAmountInfo.get("amount");
                    endsOn = contractStartDate.plusMonths(startOn + 2);
                } else {
                    endsOn = contractStartDate.plusMonths(startOn + 1);
                }

                Double amountWithoutVat = DiscountsWithVatHelper.getAmountWithoutVat(proRatedAmount);
                HashMap paymentInfo = getPaymentInfo(monthlyPaymentType, description, "One Time Payment", commencesOn,
                    proRatedAmount, amountWithoutVat, monthlyPaymentType.getType().getCode(),
                    contractStartDate, endsOn, false,"PRO_RATED",
                    proPlusAmountInfo.get("additionalDiscountAmountPerPayment"), monthlyPaymentType.getRecurrence());
                logger.info("getPaymentTermsConfigs Prorated paymentInfo " + paymentInfo.entrySet());
                payments.put(contractStartDate.withMillisOfDay(new DateTime().getMillisOfDay()).toDate(), paymentInfo);
            }
           payments.putAll(getPaymentTermsConfigsForWeeklyPkg(map));
           return payments;
        }
        logger.log(Level.INFO, "isWeeklyPkg false");

        //ACC-4905
        if (isOneMonthAgreement) {
            logger.log(Level.INFO, "isOneMonthAgreement true");

            DateTime endsOn = contractStartDate.plusMonths(startOn).plusMonths(1).minusDays(1);
            String commencesOn = DDUtils.getCommencesOn(contractStartDate, contractStartDate);
            Double additionalDiscount = null;
            double amount =  monthlyPaymentType.getAmount();

            if (monthlyPaymentType.getAffectedByAdditionalDiscount()
                && additionalDiscountAmountPerPayment != 0
                && additionalDiscountedPaymentsCount > 0) {

                amount = DiscountsWithVatHelper.getFinalAmountAfterAddDiscount(amount, additionalDiscountAmountPerPayment);
                additionalDiscount = additionalDiscountAmountPerPayment;
                additionalDiscountedPaymentsCount--;
                map.put("additionalDiscountedPaymentsCount", additionalDiscountedPaymentsCount);
            }

            HashMap paymentInfo = getPaymentInfo(monthlyPaymentType, monthlyPaymentType.getDescription(), "One Time Payment", commencesOn,
                amount, DiscountsWithVatHelper.getAmountWithoutVat(amount), monthlyPaymentType.getType().getCode(),
                contractStartDate.plusMonths(startOn), endsOn, false,"ONE_MONTH_AGREEMENT",
                additionalDiscount, monthlyPaymentType.getRecurrence());
            logger.log(Level.INFO, "One month agreement paymentInfo " + paymentInfo.entrySet());
            payments.put(contractStartDate.withMillisOfDay(new DateTime().getMillisOfDay()).toDate(), paymentInfo);

            startOn ++;
        }

        // prorated payment
        if (isProrated || isProratedPlusMonth || isOneMonthAgreement) {
            logger.log(Level.INFO, "isProrated {0}; isProratedPlusMonth : {1}", new Object[]{isProrated, isProratedPlusMonth});
            HashMap<String, Object> paymentInfo = new HashMap<>();

            if (isProratedPlusMonth) {
                Map<String, Double> proPlusAmountInfo = getProPlusMonthPaymentAmount(map);
                proRatedAmount = proPlusAmountInfo.get("amount");
                paymentInfo.put("additionalDiscountAmountPerPayment", proPlusAmountInfo.get("additionalDiscountAmountPerPayment"));
            }

            paymentInfo.put("amount", proRatedAmount);
            paymentInfo.put("monthlyPaymentType", "PRO_RATED");
            monthlyPayments.put(startOn, paymentInfo);
            logger.log(Level.INFO, "Prorated paymentInfo : {0}", paymentInfo.entrySet());
        }

        logger.log(Level.INFO, "checking monthly payments");
        startOn += isProratedPlusMonth ? 2 : (isProrated || isOneMonthAgreement) ? 1 : 0;

        // ACC-7050
        Integer waivedMonths = (Integer) map.getOrDefault("waivedMonths", 0);
        boolean waivedPayment = false;
        if (isMaidVisa && waivedMonths > 0 &&
                startOn == 0) { // this only for MV without pro-rated payment
            waivedPayment = true;
            startOn += waivedMonths; // shift to the first payment will calculate and consider the additional discount
            discountEffectiveAfter = discountEffectiveAfter != null ?
                    // the discount on the payment should be applied at it's time without effective by the waived payments
                    Math.max((discountEffectiveAfter - waivedMonths), 0) : null;
        }

        if (monthlyPaymentType.getAffectedByAdditionalDiscount()
                && additionalDiscountAmountPerPayment != 0 && additionalDiscountedPaymentsCount > 0) {
            
            logger.log(Level.INFO, "affectedByAdditionalDiscount");
            startOn = isOneMonthAgreement ?
                generateMonthlyPaymentWithAdditionalDiscountWithOneAgreement(monthlyPayments, map, startOn) :
                generateMonthlyPaymentWithAdditionalDiscountWithoutOneAgreement(monthlyPayments, map, startOn, discountEffectiveAfter);

        } else if ((!waivedPayment && (!isOneMonthAgreement || (discountEffectiveAfter == null || !discountEffectiveAfter.equals(1)))) ||
                (waivedPayment && (discountEffectiveAfter == null || discountEffectiveAfter >= 1))) {
            logger.log(Level.INFO, "not affectedByAdditionalDiscount");

            HashMap<String, Object> paymentInfo = new HashMap<>();
            paymentInfo.put("amount", monthlyPaymentType.getAmount());
            paymentInfo.put("monthlyPaymentType", "FULL");
            if (discountEffectiveAfter != null && discountEffectiveAfter > 0) {
                paymentInfo.put("endsAfter", discountEffectiveAfter);
            }
            monthlyPayments.put(startOn, paymentInfo);
            if (isOneMonthAgreement) {
                startOn = startOn + (discountEffectiveAfter == null || discountEffectiveAfter.equals(0) ?
                    1 : (discountEffectiveAfter -1));
            } else if (waivedPayment) {
                // since we subtract the waived payment count we need to fix the value to meet
                // the condition to add the discount below
                startOn = startOn + (discountEffectiveAfter == null ? 1 : discountEffectiveAfter);
            } else {
                startOn += discountEffectiveAfter;
            }

            logger.log(Level.INFO, "Base paymentInfo : {0}", paymentInfo.entrySet());
        }

        if (discountEffectiveAfter != null && (discountEffectiveAfter > 0 ||
                monthlyPaymentType.getAffectedByAdditionalDiscount() && additionalDiscountAmountPerPayment != 0 &&
                        additionalDiscountedPaymentsCount > 0 || waivedPayment)) {
            logger.log(Level.INFO, "getPaymentTermsConfigs getDiscountedMonthlyPaymentInfo");

            String description = isMaidVisa ? monthlyPaymentType.getDescription()
                     + " + Maid Salary" + ((boolean) map.get("isWorkerSalaryVatted") ? "" :" (VAT FREE)") :
                    monthlyPaymentType.getDescription();
            Double discountedAmount = monthlyPaymentType.getAmount() - monthlyPaymentType.getDiscount();

            Double discountedAmountWithoutVAT = DiscountsWithVatHelper.getAmountWithoutVat(
                    isMaidVisa, (boolean) map.get("isWorkerSalaryVatted"),
                    isMaidVisa, workerSalary, discountedAmount);

            HashMap<String, Object> paymentInfo = new HashMap<>();
            paymentInfo.put("amount", discountedAmount);
            paymentInfo.put("monthlyPaymentType", discountEffectiveAfter != null &&
                    discountEffectiveAfter > 0 ? "DISCOUNTED" : "FULL");
            paymentInfo.put("description", description);
            paymentInfo.put("discountedAmountWithoutVAT", discountedAmountWithoutVAT);
            monthlyPayments.put(startOn, paymentInfo);
            logger.log(Level.INFO, "Discounted paymentInfo : {0}", paymentInfo.entrySet());
        }

        //Generate Add on Payments
        updateMonthlyPaymentWithAddOn(monthlyPayments, map);

        logger.log(Level.INFO, "Final monthlyPayments : {0}", monthlyPayments.entrySet());
        monthlyPayments.forEach((paymentStartOn, info) -> {
            String description = info.get("description") == null ? monthlyPaymentType.getDescription() : (String) info.get("description");
            AtomicReference<Integer> endsAfter = new AtomicReference<>();
            monthlyPayments.keySet().stream().filter(startsOn ->
                     startsOn > paymentStartOn).min(Comparator.naturalOrder()).ifPresent(endsAfter::set);
            String frequencyStatement;
            if (endsAfter.get() != null) {
                Integer numOfMonth = endsAfter.get() - paymentStartOn;
                frequencyStatement = DDUtils.getPaymentFrequencyStatement(monthlyPaymentType, numOfMonth);

                if (numOfMonth > 1) {
                    frequencyStatement += " (for " + numOfMonth + " months)";
                }
            } else frequencyStatement = DDUtils.getPaymentFrequencyStatement(monthlyPaymentType);

            DateTime startsOn = DDUtils.getCommencesOnDate(contractStartDate.plusMonths(paymentStartOn), contractStartDate);
            String commencesOn = DDUtils.getCommencesOn(startsOn, contractStartDate);
            if (isOneMonthAgreement && paymentStartOn == 1) {
                    startsOn = contractStartDate.plusMonths(paymentStartOn);
                    commencesOn = DateUtil.formatMonthDayYear(startsOn.toDate());
            }

            Double amount = (Double) info.get("amount");
            Double additionalDiscount = (Double) info.get("additionalDiscountAmountPerPayment");
            Double amountWithoutVat = DiscountsWithVatHelper.getAmountWithoutVat(amount);

            boolean includedWorkerSalary = false;
            if (isMaidVisa && (
                    info.get("discountedAmountWithoutVAT") != null
                    || info.get("additionalDiscountedAmountWithoutVAT") != null)) {
                includedWorkerSalary = true;
                amountWithoutVat = DiscountsWithVatHelper.getAmountWithoutVat(
                        true, (Boolean) map.get("isWorkerSalaryVatted"),
                        true, workerSalary, amount);
            }

            HashMap paymentInfo = getPaymentInfo(monthlyPaymentType, description, frequencyStatement,
                    commencesOn, amount, amountWithoutVat, monthlyPaymentType.getType().getCode(),
                    startsOn, endsAfter.get() != null ? contractStartDate.plusMonths(endsAfter.get()) : null,
                    paymentStartOn.equals(monthlyPayments.lastKey()), (String) info.get("monthlyPaymentType"),
                    additionalDiscount, monthlyPaymentType.getRecurrence(), includedWorkerSalary);
            logger.log(Level.INFO, "getAdditionalDiscountedPaymentInfo monthly payment paymentInfo " + paymentInfo.entrySet());
            payments.put(startsOn.withMillisOfDay(new DateTime().getMillisOfDay()).toDate(), paymentInfo);
        });

        return payments;
    }

    public static int generateMonthlyPaymentWithAdditionalDiscountWithoutOneAgreement(
        TreeMap<Integer, HashMap<String, Object>> monthlyPayments,
        Map<String, Object> map,
        int startOn,
        Integer discountEffectiveAfter)  {

        Integer additionalDiscountedPaymentsCount = (Integer) map.get("additionalDiscountedPaymentsCount");
        Double additionalDiscountAmountPerPayment = (Double) map.get("additionalDiscountAmountPerPayment");
        AbstractPaymentTypeConfig monthlyPaymentType = (AbstractPaymentTypeConfig) map.get("monthlyPaymentType");
        boolean isMaidVisa = (Boolean) map.get("isMaidVisa");
        Double workerSalary = (Double) map.get("workerSalary");

        int numOfMonths = discountEffectiveAfter == null || discountEffectiveAfter.equals(0) ?
            additionalDiscountedPaymentsCount :
            Math.min(additionalDiscountedPaymentsCount, discountEffectiveAfter);
        Double amount = DiscountsWithVatHelper.getFinalAmountAfterAddDiscount(monthlyPaymentType.getAmount(), additionalDiscountAmountPerPayment);

        HashMap<String, Object> paymentInfo = new HashMap<>();
        paymentInfo.put("amount", amount);
        paymentInfo.put("monthlyPaymentType", "FULL");
        paymentInfo.put("additionalDiscountAmountPerPayment", additionalDiscountAmountPerPayment);
        monthlyPayments.put(startOn, paymentInfo);
        startOn += numOfMonths;
        logger.log(Level.INFO, "First additional discount paymentInfo : {0}", paymentInfo.entrySet());

        if (discountEffectiveAfter != null && !discountEffectiveAfter.equals(0)
            && additionalDiscountedPaymentsCount < discountEffectiveAfter) {
            logger.log(Level.INFO, "with discount");

            int fullMonthlyPaymentsCount = discountEffectiveAfter - additionalDiscountedPaymentsCount;
            paymentInfo = new HashMap<>();
            paymentInfo.put("amount", monthlyPaymentType.getAmount());
            paymentInfo.put("monthlyPaymentType", "FULL");
            monthlyPayments.put(startOn, paymentInfo);
            startOn += fullMonthlyPaymentsCount;
            logger.info( "Second additional discount paymentInfo : " + paymentInfo.entrySet());
        } else if (discountEffectiveAfter != null && discountEffectiveAfter > 0
            && additionalDiscountedPaymentsCount > discountEffectiveAfter) {

            String description = isMaidVisa ? monthlyPaymentType.getDescription()
                    + " + Maid Salary" + ((boolean) map.get("isWorkerSalaryVatted") ? "" :" (VAT FREE)") :
                    monthlyPaymentType.getDescription();

            Double discountedAmount = monthlyPaymentType.getAmount() - monthlyPaymentType.getDiscount();
            double additionalDiscountedAmount = DiscountsWithVatHelper.getFinalAmountAfterAddDiscount(discountedAmount, additionalDiscountAmountPerPayment);;

            double additionalDiscountedAmountWithoutVAT = DiscountsWithVatHelper.getAmountWithoutVat(
                    isMaidVisa, (boolean) map.get("isWorkerSalaryVatted"),
                    isMaidVisa, workerSalary, additionalDiscountedAmount);

            paymentInfo = new HashMap<>();
            paymentInfo.put("amount", additionalDiscountedAmount);
            paymentInfo.put("monthlyPaymentType", "DISCOUNTED");
            paymentInfo.put("description", description);
            paymentInfo.put("additionalDiscountedAmountWithoutVAT", additionalDiscountedAmountWithoutVAT);
            paymentInfo.put("additionalDiscountAmountPerPayment", additionalDiscountAmountPerPayment);

            monthlyPayments.put(startOn, paymentInfo);
            startOn += additionalDiscountedPaymentsCount - discountEffectiveAfter;
            logger.log(Level.INFO, "Third additional discount paymentInfo : {0}", paymentInfo.entrySet());
        }
        return startOn;
    }
    //ACC-4905
    public static int generateMonthlyPaymentWithAdditionalDiscountWithOneAgreement(
        TreeMap<Integer, HashMap<String, Object>> monthlyPayments,
        Map<String, Object> map,
        int startOn)  {

        Integer discountEffectiveAfter = (Integer) map.get("discountEffectiveAfter");
        Integer additionalDiscountedPaymentsCount = (Integer) map.get("additionalDiscountedPaymentsCount");
        Double additionalDiscountAmountPerPayment = (Double) map.get("additionalDiscountAmountPerPayment");
        AbstractPaymentTypeConfig monthlyPaymentType = (AbstractPaymentTypeConfig) map.get("monthlyPaymentType");

        int numOfMonths = discountEffectiveAfter == null || discountEffectiveAfter.equals(0) ?
            additionalDiscountedPaymentsCount :
            Math.min(additionalDiscountedPaymentsCount, discountEffectiveAfter);
        Double amount = DiscountsWithVatHelper.getFinalAmountAfterAddDiscount(monthlyPaymentType.getAmount(), additionalDiscountAmountPerPayment);
        HashMap<String, Object> paymentInfo = new HashMap<>();

        if (discountEffectiveAfter == null || discountEffectiveAfter.equals(0)) {
            paymentInfo.put("amount", amount);
            paymentInfo.put("monthlyPaymentType", "FULL");
            paymentInfo.put("additionalDiscountAmountPerPayment", additionalDiscountAmountPerPayment);
            monthlyPayments.put(startOn, paymentInfo);
            startOn += numOfMonths;
            logger.log(Level.INFO, "First additional discount paymentInfo : {0}", paymentInfo.entrySet());

        } else if (!discountEffectiveAfter.equals(0)
            && additionalDiscountedPaymentsCount < discountEffectiveAfter) {
            paymentInfo.put("amount", amount);
            paymentInfo.put("monthlyPaymentType", "FULL");
            paymentInfo.put("additionalDiscountAmountPerPayment", additionalDiscountAmountPerPayment);
            monthlyPayments.put(startOn, paymentInfo);
            startOn += numOfMonths;
            logger.log(Level.INFO, "First additional discount paymentInfo : {0}", paymentInfo.entrySet());
            if (discountEffectiveAfter > (additionalDiscountedPaymentsCount + 1)) {
                int fullMonthlyPaymentsCount = discountEffectiveAfter - additionalDiscountedPaymentsCount - 1;
                paymentInfo = new HashMap<>();
                paymentInfo.put("amount", monthlyPaymentType.getAmount());
                paymentInfo.put("monthlyPaymentType", "FULL");
                monthlyPayments.put(startOn, paymentInfo);
                startOn += fullMonthlyPaymentsCount;
                logger.log(Level.INFO, "Second additional discount paymentInfo : {0}", paymentInfo.entrySet());
            }

        } else if (discountEffectiveAfter > 0) {
            String description = monthlyPaymentType.getDescription();

            Double discountedAmount = monthlyPaymentType.getAmount() - monthlyPaymentType.getDiscount();
            double additionalDiscountedAmount = DiscountsWithVatHelper.getFinalAmountAfterAddDiscount(discountedAmount, additionalDiscountAmountPerPayment);
            double additionalDiscountedAmountWithoutVAT = DiscountsWithVatHelper.getAmountWithoutVat(additionalDiscountedAmount);

            paymentInfo.put("amount", additionalDiscountedAmount);
            paymentInfo.put("monthlyPaymentType", "DISCOUNTED");
            paymentInfo.put("description", description);
            paymentInfo.put("additionalDiscountedAmountWithoutVAT", additionalDiscountedAmountWithoutVAT);
            paymentInfo.put("additionalDiscountAmountPerPayment", additionalDiscountAmountPerPayment);
            monthlyPayments.put(startOn, paymentInfo);
            startOn += additionalDiscountedPaymentsCount;
            logger.log(Level.INFO, "Third additional discount paymentInfo : {0}", paymentInfo.entrySet());
        }
        return startOn;

    }
    //ACC-4820
    public static void updateMonthlyPaymentWithAddOn(
        TreeMap<Integer, HashMap<String, Object>> monthlyPayments,
        Map<String,Object> map) {

        logger.log(Level.INFO, "add ons");
        List<AbstractPaymentTypeConfig> contractPaymentTypes = (List<AbstractPaymentTypeConfig>) map.get("contractPaymentTypes");
        contractPaymentTypes.stream().filter(paymentType -> paymentType.getType().getCode()
                .equals(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_ADD_ON_TYPE_CODE) && paymentType.getStartsOn() != null)
            .forEach(addOnPayment -> {
                if (monthlyPayments.containsKey(addOnPayment.getStartsOn())) {
                    HashMap<String, Object> payment = monthlyPayments.get(addOnPayment.getStartsOn());
                    payment.put("amount", (Double) payment.get("amount") + addOnPayment.getAmount());
                    monthlyPayments.replace(addOnPayment.getStartsOn(), payment);
                } else {
                    monthlyPayments.keySet().stream().filter(startsOn ->
                        startsOn < addOnPayment.getStartsOn()).max(Comparator.naturalOrder()).ifPresent(startsOn -> {
                        HashMap<String, Object> payment = monthlyPayments.get(startsOn);
                        HashMap<String, Object> newPayment = new HashMap<>();
                        newPayment.put("amount", (Double) payment.get("amount") + addOnPayment.getAmount());
                        newPayment.put("additionalDiscountAmountPerPayment", payment.get("additionalDiscountAmountPerPayment"));
                        newPayment.put("discountedAmountWithoutVAT", payment.get("discountedAmountWithoutVAT"));
                        newPayment.put("additionalDiscountedAmountWithoutVAT", payment.get("additionalDiscountedAmountWithoutVAT"));
                        newPayment.put("description", payment.get("description"));
                        newPayment.put("monthlyPaymentType", payment.get("monthlyPaymentType"));
                        monthlyPayments.put(addOnPayment.getStartsOn(), newPayment);
                    });
                }

                if (addOnPayment.getEndsAfter() == null) {
                    monthlyPayments.forEach((startsOn, stringObjectHashMap) -> {
                        if (startsOn > addOnPayment.getStartsOn()) {
                            HashMap<String, Object> payment = monthlyPayments.get(startsOn);
                            payment.put("amount", (Double) payment.get("amount") + addOnPayment.getAmount());
                            monthlyPayments.replace(startsOn, payment);
                        }
                    });
                } else {
                    monthlyPayments.forEach((startsOn, stringObjectHashMap) -> {
                        if (startsOn > addOnPayment.getStartsOn() && startsOn < addOnPayment.getEndsAfter()) {
                            HashMap<String, Object> payment = monthlyPayments.get(startsOn);
                            payment.put("amount", (Double) payment.get("amount") + addOnPayment.getAmount());
                            monthlyPayments.replace(startsOn, payment);
                        }
                    });
                    monthlyPayments.forEach((startsOn, stringObjectHashMap) -> {
                        if (startsOn > addOnPayment.getStartsOn() && startsOn.equals(addOnPayment.getEndsAfter())) {
                            HashMap<String, Object> payment = monthlyPayments.get(startsOn);
                            HashMap<String, Object> newPayment = monthlyPayments.get(startsOn);
                            newPayment.put("amount", (Double) payment.get("amount") + addOnPayment.getAmount());
                            newPayment.put("additionalDiscountAmountPerPayment", payment.get("additionalDiscountAmountPerPayment"));
                            newPayment.put("discountedAmountWithoutVAT", payment.get("discountedAmountWithoutVAT"));
                            newPayment.put("additionalDiscountedAmountWithoutVAT", payment.get("additionalDiscountedAmountWithoutVAT"));
                            newPayment.put("description", payment.get("description"));
                            newPayment.put("monthlyPaymentType", payment.get("monthlyPaymentType"));
                            monthlyPayments.replace(startsOn, newPayment);
                            monthlyPayments.put(startsOn + 1, payment);
                        }
                    });
                    if (!monthlyPayments.containsKey(addOnPayment.getEndsAfter() + 1)) {
                        HashMap<String, Object> payment = monthlyPayments.get(addOnPayment.getStartsOn());
                        HashMap<String, Object> oldPayment = new HashMap<>();
                        oldPayment.put("amount", (Double) payment.get("amount") - addOnPayment.getAmount());
                        oldPayment.put("additionalDiscountAmountPerPayment", payment.get("additionalDiscountAmountPerPayment"));
                        oldPayment.put("discountedAmountWithoutVAT", payment.get("discountedAmountWithoutVAT"));
                        oldPayment.put("additionalDiscountedAmountWithoutVAT", payment.get("additionalDiscountedAmountWithoutVAT"));
                        oldPayment.put("description", payment.get("description"));
                        oldPayment.put("monthlyPaymentType", payment.get("monthlyPaymentType"));
                        monthlyPayments.put(addOnPayment.getEndsAfter() + 1, oldPayment);
                    }
                }
                logger.log(Level.INFO, "monthlyPayments : {0}", monthlyPayments.entrySet());
            });
    }

    public static Map<Date, HashMap> generateNonMonthlyPaymentTermForms(Map<String, Object> map) {
        Map<Date, HashMap> payments = new TreeMap<>();
        logger.log(Level.INFO, "start");
        ContractPaymentTypeRepository contractPaymentTypeRepository =
                Setup.getRepository(ContractPaymentTypeRepository.class);
        AbstractPaymentTypeConfig contractPaymentType = (AbstractPaymentTypeConfig) map.get("contractPaymentType");
        Double additionalDiscountAmountPerPayment = (Double) map.get("additionalDiscountAmountPerPayment");
        Integer additionalDiscountedPaymentsCount = (Integer) map.get("additionalDiscountedPaymentsCount");
        Integer discountEffectiveAfter = (Integer) map.get("discountEffectiveAfter");
        DateTime contractStartDate = (DateTime) map.get("contractStartDate");
        DateTime commencesOnDate = new DateTime(contractStartDate).plusMonths(contractPaymentType.getStartsOn());

        if (contractPaymentType.getAffectedByAdditionalDiscount() && additionalDiscountAmountPerPayment != 0
                && additionalDiscountedPaymentsCount > 0) {
            map.put("commencesOnDate", commencesOnDate);
            payments.putAll(getAdditionalDiscountedNonMonthlyPaymentInfo(map));
            logger.log(Level.INFO, "getPaymentTermsConfigs getAdditionalDiscountedPaymentInfo " + contractStartDate);
            return payments;
        }

        String description = contractPaymentType.getDescription();
        DateTime endsOn = null;
        String frequencyStatement = DDUtils.getPaymentFrequencyStatement(contractPaymentType);

        if (discountEffectiveAfter != null && discountEffectiveAfter > 0) {
            frequencyStatement = DDUtils.getPaymentFrequencyStatement(contractPaymentType, discountEffectiveAfter);
            if (discountEffectiveAfter > 1) {
                frequencyStatement += " (for " + discountEffectiveAfter + " months)";
            }
            endsOn = commencesOnDate.plusMonths(discountEffectiveAfter);
        }


        String commencesOn = DDUtils.getCommencesOnForNonMonthly(commencesOnDate);
        logger.log(Level.INFO, "getPaymentTermsConfigs & commencesOn " + commencesOn);
        HashMap paymentInfo = getPaymentInfo(contractPaymentType, description, frequencyStatement, commencesOn,
                contractPaymentType.getAmount(),
                contractPaymentType.getType() != null && contractPaymentType.getType().getId() != null &&
                        contractPaymentTypeRepository.paymentHasNoVat(contractPaymentType.getType().getId()) ?
                        contractPaymentType.getAmount() :
                        DiscountsWithVatHelper.getAmountWithoutVat(contractPaymentType.getAmount()),
                contractPaymentType.getType().getCode(),
                commencesOnDate, endsOn, false, null,
                null, contractPaymentType.getRecurrence());
        logger.log(Level.INFO, "getPaymentTermsConfigs paymentInfo " + paymentInfo.entrySet());

        payments.put(commencesOnDate.withMillisOfDay(new DateTime().getMillisOfDay()).toDate(), paymentInfo);
        if (discountEffectiveAfter != null && discountEffectiveAfter > 0) {
            logger.log(Level.INFO, "getPaymentTermsConfigs getDiscountedMonthlyPaymentInfo");
            map.put("commencesOnDate", commencesOnDate.plusMonths(discountEffectiveAfter));
            payments.putAll(getDiscountedNonMonthlyPaymentInfo(map));

        }

        return payments;
    }

    public static Map<Date, HashMap> getAdditionalDiscountedNonMonthlyPaymentInfo(Map<String, Object> map) {
        logger.log(Level.INFO, "getAdditionalDiscountedPaymentInfo start");
        AbstractPaymentTypeConfig contractPaymentType = (AbstractPaymentTypeConfig) map.get("contractPaymentType");
        DateTime commencesOnDate = (DateTime) map.get("commencesOnDate");
        Integer discountEffectiveAfter = (Integer) map.get("discountEffectiveAfter");
        Integer additionalDiscountedPaymentsCount = (Integer) map.get("additionalDiscountedPaymentsCount");
        Double additionalDiscountAmountPerPayment = (Double) map.get("additionalDiscountAmountPerPayment");

        Map<Date, HashMap> paymentInfoList = new TreeMap<>();

        String description = contractPaymentType.getDescription();

        boolean paymentContinuesAfterAdditionalDiscount = true;
        logger.log(Level.INFO, "getAdditionalDiscountedPaymentInfo isMonthlyPayment false");

        String frequencyStatement;

        String commencesOn = DDUtils.getCommencesOnForNonMonthly(commencesOnDate);
        Double amount = DiscountsWithVatHelper.getFinalAmountAfterAddDiscount(contractPaymentType.getAmount(), additionalDiscountAmountPerPayment);
        logger.log(Level.INFO, "getAdditionalDiscountedPaymentInfo amount " + amount);

        int numOfAdditionalDiscountedPayments = 1;
        if (additionalDiscountedPaymentsCount == 1) {
            frequencyStatement = "One Time Payment";
        } else {
            frequencyStatement = DDUtils.getPaymentFrequencyStatement(contractPaymentType);
            numOfAdditionalDiscountedPayments = additionalDiscountedPaymentsCount;
        }
        DateTime endsOn = null;
        if (contractPaymentType.getEndsAfter() != null && contractPaymentType.getEndsAfter() <= (additionalDiscountedPaymentsCount * contractPaymentType.getRecurrence())) {
            numOfAdditionalDiscountedPayments = contractPaymentType.getEndsAfter() / contractPaymentType.getRecurrence();
            paymentContinuesAfterAdditionalDiscount = false;
            logger.log(Level.INFO, "getAdditionalDiscountedPaymentInfo numOfAdditionalDiscountedPayments " + numOfAdditionalDiscountedPayments);

        } else {
            if (contractPaymentType.getRecurrence() == 0) paymentContinuesAfterAdditionalDiscount = false;
            endsOn = commencesOnDate.plusMonths(numOfAdditionalDiscountedPayments * contractPaymentType.getRecurrence());
        }

        HashMap paymentInfo = getPaymentInfo(contractPaymentType, description, frequencyStatement, commencesOn,
                amount, DiscountsWithVatHelper.getAmountWithoutVat(amount), contractPaymentType.getType().getCode(), commencesOnDate, endsOn, false,
                null, additionalDiscountAmountPerPayment, contractPaymentType.getRecurrence());
        logger.log(Level.INFO, "getAdditionalDiscountedPaymentInfo not monthly payment paymentInfo " + paymentInfo.entrySet());

        paymentInfoList.put(commencesOnDate.withMillisOfDay(new DateTime().getMillisOfDay()).toDate(), paymentInfo);
        commencesOnDate = commencesOnDate.plusMonths(numOfAdditionalDiscountedPayments * contractPaymentType.getRecurrence());

        if (!paymentContinuesAfterAdditionalDiscount) return paymentInfoList;
        logger.log(Level.INFO, "getAdditionalDiscountedPaymentInfo paymentContinuesAfterAdditionalDiscount true");


        logger.log(Level.INFO, "getAdditionalDiscountedPaymentInfo isMonthlyPayment false or & discountEffectiveAfter != 0");
        frequencyStatement = DDUtils.getPaymentFrequencyStatement(contractPaymentType);
        commencesOn = DDUtils.getCommencesOnForNonMonthly(commencesOnDate);
        endsOn = null;

        paymentInfo = getPaymentInfo(contractPaymentType, description, frequencyStatement, commencesOn,
                contractPaymentType.getAmount(), DiscountsWithVatHelper.getAmountWithoutVat(contractPaymentType.getAmount()),
                contractPaymentType.getType().getCode(), commencesOnDate, endsOn, false,null,
                null, contractPaymentType.getRecurrence());
        logger.log(Level.INFO, "getAdditionalDiscountedPaymentInfo not monthly payment & discountEffectiveAfter != 0 paymentInfo " + paymentInfo.entrySet());
        paymentInfoList.put(commencesOnDate.withMillisOfDay(new DateTime().getMillisOfDay()).toDate(), paymentInfo);


        if (discountEffectiveAfter != null) {
            logger.log(Level.INFO, "getAdditionalDiscountedPaymentInfo getDiscountedMonthlyPaymentInfo");
            map.put("commencesOnDate", commencesOnDate);
            paymentInfoList.putAll(getDiscountedNonMonthlyPaymentInfo(map));
        }
        logger.log(Level.INFO, "getAdditionalDiscountedPaymentInfo end");

        return paymentInfoList;
    }

    private static Map<Date, HashMap> getDiscountedNonMonthlyPaymentInfo(Map<String, Object> map) {
        Double workerSalary = (Double) map.get("workerSalary");
        DateTime contractStartDate = (DateTime) map.get("contractStartDate");
        Double additionalDiscountAmountPerPayment = (Double) map.get("additionalDiscountAmountPerPayment");
        Integer additionalDiscountedPaymentsCount = (Integer) map.get("additionalDiscountedPaymentsCount");
        Integer discountEffectiveAfter = (Integer) map.get("discountEffectiveAfter");
        DateTime commencesOnDate = (DateTime) map.get("commencesOnDate");
        boolean isMaidVisa = (boolean) map.get("isMaidVisa");
        AbstractPaymentTypeConfig contractPaymentType = (AbstractPaymentTypeConfig) map.get("contractPaymentType");

        logger.log(Level.INFO, "getDiscountedMonthlyPaymentInfo start");

        Map<Date, HashMap> paymentInfoList = new TreeMap<>();

        String description = isMaidVisa ? contractPaymentType.getDescription()
                + " + Maid Salary" + ((boolean) map.get("isWorkerSalaryVatted") ? "" :" (VAT FREE)") :
                contractPaymentType.getDescription();

        Double discountedAmount = contractPaymentType.getAmount() - contractPaymentType.getDiscount();
        //Double discountedAmountWithoutVat = Utils.getAmountWithoutVat(contractPaymentType.getAmount()) - contractPaymentType.getDiscount();
        if (discountEffectiveAfter != null && !discountEffectiveAfter.equals(0) &&
                additionalDiscountedPaymentsCount > discountEffectiveAfter) {
            logger.log(Level.INFO, "getDiscountedMonthlyPaymentInfo & discountEffectiveAfter != 0 ");
            int numOfMonths = additionalDiscountedPaymentsCount - discountEffectiveAfter;
            DateTime endAdditionalDiscountDate = commencesOnDate.plusMonths(numOfMonths);
//            String description = isMaidVisa ? contractPaymentType.getDescription() + " + Maid Salary" :
//                    isMaidCC ? String.format("Months (from %s to %s)",
//                            DateUtil.formatSimpleMonthYear(commencesOnDate.toDate()),
//                            DateUtil.formatSimpleMonthYear(endAdditionalDiscountDate.toDate())) :
//                            contractPaymentType.getDescription();
            String frequencyStatement = DDUtils.getPaymentFrequencyStatement(contractPaymentType, numOfMonths);
            if (numOfMonths > 1) {
                frequencyStatement += " (for " + numOfMonths + " months)";
            }
            String commencesOn = DDUtils.getCommencesOnForNonMonthly(commencesOnDate);
            double additionalDiscountedAmount = DiscountsWithVatHelper.getFinalAmountAfterAddDiscount(discountedAmount, additionalDiscountAmountPerPayment);
            double additionalDiscountedAmountWithoutVAT = DiscountsWithVatHelper.getAmountWithoutVat(
                    isMaidVisa, (Boolean) map.get("isWorkerSalaryVatted"),
                    isMaidVisa, workerSalary, additionalDiscountedAmount);

            HashMap paymentInfo = getPaymentInfo(contractPaymentType, description, frequencyStatement, commencesOn,
                    Math.floor(additionalDiscountedAmount), additionalDiscountedAmountWithoutVAT,
                    contractPaymentType.getType().getCode(), commencesOnDate, endAdditionalDiscountDate, false, "DISCOUNTED", additionalDiscountAmountPerPayment, contractPaymentType.getRecurrence());
            logger.log(Level.INFO, "getDiscountedMonthlyPaymentInfo & discountEffectiveAfter != 0 paymentInfo" + paymentInfo.entrySet());

            paymentInfoList.put(commencesOnDate.withMillisOfDay(new DateTime().getMillisOfDay()).toDate(), paymentInfo);

            commencesOnDate = endAdditionalDiscountDate;
        }

        double discountedAmountWithoutVAT = DiscountsWithVatHelper.getAmountWithoutVat(
                isMaidVisa, (Boolean) map.get("isWorkerSalaryVatted"),
                isMaidVisa, workerSalary, discountedAmount);

//        String description = isMaidVisa ? contractPaymentType.getDescription() + " + Maid Salary" :
//                isMaidCC ? "Months (from "
//                        + DateUtil.formatSimpleMonthYear(commencesOnDate.toDate())
//                        + " until termination notice)" : "";

        String frequencyStatement = DDUtils.getPaymentFrequencyStatement(contractPaymentType);
        String commencesOn = DDUtils.getCommencesOnForNonMonthly(commencesOnDate);
        logger.log(Level.INFO, "getDiscountedMonthlyPaymentInfo & commencesOn " + commencesOn);

        HashMap paymentInfo = getPaymentInfo(contractPaymentType, description, frequencyStatement, commencesOn, discountedAmount,
                discountedAmountWithoutVAT, contractPaymentType.getType().getCode(), commencesOnDate, null, true, "DISCOUNTED", null, contractPaymentType.getRecurrence());
        logger.log(Level.INFO, "getDiscountedMonthlyPaymentInfo " + paymentInfo.entrySet());

        paymentInfoList.put(commencesOnDate.withMillisOfDay(new DateTime().getMillisOfDay()).toDate(), paymentInfo);

        return paymentInfoList;
    }

    private static HashMap getPaymentInfo(AbstractPaymentTypeConfig type, String description, String frequency,
                                          String commencesOn, Double amount, Double amountWithoutVat,String typeCode,
                                          DateTime startsOn, DateTime endsOn, Boolean  indefinite, String monthlyPaymentType,
                                          Double additionalDiscountAmount, Integer recurrence) {
        return getPaymentInfo(type, description, frequency,
                commencesOn, amount, amountWithoutVat, typeCode,
                startsOn, endsOn, indefinite, monthlyPaymentType,
                additionalDiscountAmount, recurrence, false);
    }

    private static HashMap getPaymentInfo(
            AbstractPaymentTypeConfig type,
            String description, String frequency, String commencesOn,
            Double amount, Double amountWithoutVat,
            String typeCode, DateTime startsOn, DateTime endsOn,
            Boolean  indefinite,
            String monthlyPaymentType,
            Double additionalDiscountAmount,
            Integer recurrence,
            boolean includedWorkerSalary) {

        HashMap paymentInfo = new HashMap();
        paymentInfo.put("description", description);
        paymentInfo.put("frequency", frequency);
        paymentInfo.put("commencesOn", commencesOn);
        paymentInfo.put("amount", Math.round(Math.floor(amount)));
        paymentInfo.put("amountWithoutVat", Math.round(Math.floor(amountWithoutVat)));
        paymentInfo.put("includedWorkerSalary", includedWorkerSalary);

        HashMap <String, Object> additionalInfo = new HashMap<>();
        additionalInfo.put("paymentTypeConfigId", type == null ? null : type.getId());
        additionalInfo.put("typeCode", typeCode);
        additionalInfo.put("startsOn", startsOn.toString("yyyy-MM-dd 00:00:00"));
        additionalInfo.put("endsOn", endsOn == null ? null : DDUtils.getPaymentEndsOn(startsOn, endsOn).toString("yyyy-MM-dd 00:00:00"));
        additionalInfo.put("indefinite", indefinite);
        additionalInfo.put("additionalDiscountAmount", additionalDiscountAmount);
        if (monthlyPaymentType != null) {
            if (monthlyPaymentType.equals("ONE_MONTH_AGREEMENT") && endsOn != null) {
                additionalInfo.put("endsOn", endsOn.toString("yyyy-MM-dd 00:00:00"));
                monthlyPaymentType = "FULL";
            }
            additionalInfo.put("monthlyPaymentType", monthlyPaymentType);
        }
        if (recurrence != null) {
            additionalInfo.put("frequency", recurrence);
        }

        paymentInfo.put("paymentInfo", additionalInfo);
        return paymentInfo;
    }

    public static Map <String, Object> getAdditionalDiscountPaymentsCount(Map <String, Object> map) {
        Map <String, Object> response = new HashMap<>();
        Double additionalDiscount = (Double) map.get("additionalDiscount");

        Integer additionalDiscountedPaymentsCount = getAdditionalDiscountMonthsCount(map);

        Double additionalDiscountAmountPerPayment = additionalDiscount != null && additionalDiscountedPaymentsCount != null && additionalDiscountedPaymentsCount != 0 ?
                additionalDiscount / additionalDiscountedPaymentsCount : 0D;

        if ((boolean) map.get("isMonthlyPayment") && (boolean) map.get("isProratedPlusMonth") && additionalDiscountedPaymentsCount != null) {
            additionalDiscountedPaymentsCount--;
        }
        response.put("additionalDiscountedPaymentsCount", additionalDiscountedPaymentsCount);
        response.put("additionalDiscountAmountPerPayment", additionalDiscountAmountPerPayment);


        return response;
    }



    public static Integer getAdditionalDiscountMonthsCount(Map<String, Object> map) {
        Boolean periodicalAdditionalDiscount = (Boolean) map.get("periodicalAdditionalDiscount");
        Boolean affectedByAdditionalDiscount = (Boolean) map.get("affectedByAdditionalDiscount");
        Integer additionalDiscountMonths = (Integer) map.get("additionalDiscountMonths");
        Integer paymentsDuration = (Integer) map.get("paymentsDuration");
        Double monthlyPaymentDiscount  = (Double) map.get("monthlyPaymentDiscount");
        Integer monthlyPaymentDiscountEffectiveAfter  = (Integer) map.get("monthlyPaymentDiscountEffectiveAfter");

        //ACC-3272
        if (!affectedByAdditionalDiscount) {
            return 0;
        }

        //Jirra ACC-3272
        if (!periodicalAdditionalDiscount) {
            return 1;
        }

        if (additionalDiscountMonths != null) return additionalDiscountMonths;

        if (monthlyPaymentDiscount != 0) return monthlyPaymentDiscountEffectiveAfter;

        return paymentsDuration;
    }

    public static Map<String, Double> getProPlusMonthPaymentAmount(Map<String, Object> map) {
        Map<String, Double> response = new HashMap<>();
        Double firstMonthPayment = (Double) map.get("firstMonthPayment");
        Double additionalDiscount = (Double) map.get("additionalDiscount");
        Double monthlyPaymentDiscount = (Double) map.get("monthlyPaymentDiscount");
        Integer monthlyPaymentDiscountEffectiveAfter = (Integer) map.get("monthlyPaymentDiscountEffectiveAfter");
        Boolean affectedByAdditionalDiscount = (Boolean) map.get("affectedByAdditionalDiscount");
        Double amount = firstMonthPayment + (Double) map.get("monthlyPaymentAmount");;
        if (monthlyPaymentDiscountEffectiveAfter != null && monthlyPaymentDiscountEffectiveAfter < 2) {
            amount -= monthlyPaymentDiscount;
        }

        Integer additionalDiscountedPaymentsCount = getAdditionalDiscountMonthsCount(map);
        double additionalDiscountAmountPerPayment = additionalDiscount != null && additionalDiscountedPaymentsCount != null && additionalDiscountedPaymentsCount != 0 ?
                additionalDiscount / additionalDiscountedPaymentsCount : 0D;

        if (BooleanUtils.toBoolean(affectedByAdditionalDiscount) && additionalDiscountAmountPerPayment != 0 && additionalDiscountedPaymentsCount > 0) {
            amount = DiscountsWithVatHelper.getFinalAmountAfterAddDiscount(amount, additionalDiscountAmountPerPayment);
        }
        response.put("additionalDiscountAmountPerPayment", additionalDiscountAmountPerPayment);
        response.put("amount", amount);
        return response;
    }

    public static Map<String, Object> getPaymentsPlanAsInDdcPage(ContractPaymentTerm cpt) {
        List<String> paymentsInfo = new ArrayList<>();
        Map<String, Object> r = new HashMap<>();

        if(!cpt.getContract().isOneMonthAgreement() && cpt.getWeeklyAmount() > 0.0){
            paymentsInfo.add("Weekly paid monthly");
        }

        for(Map<String, Object> p : getPaymentsReceiptTermForms(cpt)){
            StringBuilder payments = new StringBuilder();
            Map<String, Object> paymentInfo = (Map<String, Object>) p.get("paymentInfo");
            Double amountWithoutVat = p.get("amountWithoutVat") instanceof Long ?
                    ((Long) p.get("amountWithoutVat")).doubleValue() :
                    p.get("amountWithoutVat") instanceof Integer ?
                            ((Integer) p.get("amountWithoutVat")) : (Double) p.get("amountWithoutVat");
            Double amount = p.get("amount") instanceof Long ?
                    ((Long) p.get("amount")).doubleValue() :
                    p.get("amount") instanceof Integer ?
                            (Integer) p.get("amount") : (Double) p.get("amount");
            boolean setFrequencyAsMonthly = false;

            if (!cpt.getContract().isOneMonthAgreement() && cpt.getWeeklyAmount() > 0.0 &&
                    paymentInfo.get("typeCode").equals(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE) &&
                    !paymentInfo.get("monthlyPaymentType").equals("PRO_RATED")){

                amountWithoutVat = amountWithoutVat * 4;
                amount = DiscountsWithVatHelper.getAmountPlusVat(amountWithoutVat);
                setFrequencyAsMonthly = true;
            }

            payments.append(p.get("description")).append(": ");

            double vatValue = amount - amountWithoutVat;
            if (!cpt.getContract().isMaidCc() &&
                    paymentInfo.get("typeCode").equals(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE) &&
                    paymentInfo.containsKey("monthlyPaymentType") &&
                    paymentInfo.get("monthlyPaymentType").equals("DISCOUNTED")) {

                Double workerSalary = cpt.getContract().getWorkerSalaryWithoutVat();
                amountWithoutVat = amountWithoutVat - workerSalary;

                payments.append(workerSalary.intValue()).append(" Maid's Salary + ");
            }

            payments.append(amountWithoutVat.intValue()).append(" + ");
            payments.append((int) vatValue).append(" VAT, ");
            payments.append("on ").append(p.get("commencesOn")).append(" ");
            payments.append("(").append(setFrequencyAsMonthly ? "Monthly" : p.get("frequency")).append(")");

            paymentsInfo.add(payments.toString());
        }

        r.put("paymentsInfo", paymentsInfo);
        r.put("additionalDiscount", addAdditionalDiscountDetails(cpt.getAdditionalDiscount(), cpt.getAdditionalDiscountMonths(), cpt));
        r.put("creditNoteDiscount", addCreditNoteDetails(cpt.getCreditNote(), cpt.getCreditNoteMonths(), cpt));
        return r;
    }

    private static String addAdditionalDiscountDetails(Double discountValue, Integer discountMonths, ContractPaymentTerm cpt) {
        StringBuilder discountPhrase = new StringBuilder();

        if(discountValue != null){
            discountPhrase.append("Discount Amount: ");
            discountPhrase.append(discountValue.intValue());
            cpt.getContractPaymentTypes()
                    .stream()
                    .filter(AbstractPaymentTypeConfig::getAffectedByAdditionalDiscount)
                    .findFirst()
                    .ifPresent(p -> {
                        discountPhrase.append(" applied on ");
                        discountPhrase.append(p.getDescription());
                    });

            if (discountMonths != null && discountMonths > 1) {
                discountPhrase.append(" over ").append(discountMonths).append(" months");
            }
        }

        return discountPhrase.toString();
    }

    private static String addCreditNoteDetails(Double discountValue, Integer discountMonths, ContractPaymentTerm cpt) {
        StringBuilder discountPhrase = new StringBuilder();

        if(discountValue != null){
            discountPhrase.append("Credit Note Amount: ");
            discountPhrase.append(discountValue.intValue());
            cpt.getContractPaymentTypes()
                    .stream()
                    .filter(AbstractPaymentTypeConfig::getAffectedByAdditionalDiscount)
                    .findFirst()
                    .ifPresent(p -> {
                        discountPhrase.append(" applied on ");
                        discountPhrase.append(p.getDescription());
                    });

            if (discountMonths != null && discountMonths > 1) {
                discountPhrase.append(" over ").append(discountMonths).append(" months");
            }
        }

        return discountPhrase.toString();
    }
}
