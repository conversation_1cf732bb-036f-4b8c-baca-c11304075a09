package com.magnamedia.entity.dto;

import com.magnamedia.entity.ExpenseRelatedTo;
import com.magnamedia.module.type.ExpenseBeneficiaryType;
import com.magnamedia.module.type.ExpensePaymentMethod;
import com.magnamedia.workflow.type.ExpenseRequestStatus;

import java.util.Date;
import java.util.List;

public class ExpenseRequestSearchDto {
    private Long id;
    private Long expenseId;
    private String expenseCaption;
    private ExpenseRequestStatus status;
    private List<ExpenseRequestStatus> multipleStatus;
    private String amountOperator;
    private Double amount;
    private ExpensePaymentMethod paymentMethod;
    private ExpenseBeneficiaryType beneficiaryType;
    private Long relatedToId;
    private List<Long> relatedToIds;
    private String relatedToName;
    private ExpenseRelatedTo.ExpenseRelatedToType relatedToType;
    private Long beneficiaryId;
    private Date date1;
    private Date date2;
    private String dateOperator;
    private Long requesterId;
    private String approvedBy;
    private Long approvedById;

    private Boolean confirmed;
    
    public Long getId() {
        return id;
    }
    private Boolean isRefunded;
    private Boolean refundConfirmed;

    private Boolean onlyUnconfirmed;

    private String bucketName;

    private String pendingForApproval;

    public String getBucketName() {return bucketName;}

    public void setBucketName(String bucketName) {this.bucketName = bucketName;}

    public String getPendingForApproval() {return pendingForApproval;}

    public void setPendingForApproval(String pendingForApproval) {this.pendingForApproval = pendingForApproval;}

    public void setId(Long id) {
        this.id = id;
    }
    
    public Boolean getConfirmed() {
        return confirmed;
    }

    public void setConfirmed(Boolean confirmed) {
        this.confirmed = confirmed;
    }

    public Boolean getRefunded() {
        return isRefunded;
    }

    public void setIsRefunded(Boolean refunded) {
        isRefunded = refunded;
    }

    public Boolean getRefundConfirmed() {
        return refundConfirmed;
    }

    public void setRefundConfirmed(Boolean refundConfirmed) {
        this.refundConfirmed = refundConfirmed;
    }

    public ExpenseRequestSearchDto() {
    }

    public Long getExpenseId() {
        return expenseId;
    }

    public void setExpenseId(Long expenseId) {
        this.expenseId = expenseId;
    }

    public String getExpenseCaption() {
        return expenseCaption;
    }

    public void setExpenseCaption(String expenseCaption) {
        this.expenseCaption = expenseCaption;
    }

    public ExpenseRequestStatus getStatus() {
        return status;
    }

    public void setStatus(ExpenseRequestStatus status) {
        this.status = status;
    }

    public List<ExpenseRequestStatus> getMultipleStatus() {
        return multipleStatus;
    }

    public void setMultipleStatus(List<ExpenseRequestStatus> multipleStatus) {
        this.multipleStatus = multipleStatus;
    }

    public String getAmountOperator() {
        return amountOperator;
    }

    public void setAmountOperator(String amountOperator) {
        this.amountOperator = amountOperator;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public ExpensePaymentMethod getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(ExpensePaymentMethod paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public ExpenseBeneficiaryType getBeneficiaryType() {
        return beneficiaryType;
    }

    public void setBeneficiaryType(ExpenseBeneficiaryType beneficiaryType) {
        this.beneficiaryType = beneficiaryType;
    }

    public Long getRelatedToId() {
        return relatedToId;
    }

    public void setRelatedToId(Long relatedToId) {
        this.relatedToId = relatedToId;
    }

    public List<Long> getRelatedToIds() {
        return relatedToIds;
    }

    public void setRelatedToIds(List<Long> relatedToIds) {
        this.relatedToIds = relatedToIds;
    }

    public String getRelatedToName() {
        return relatedToName;
    }

    public void setRelatedToName(String relatedToName) {
        this.relatedToName = relatedToName;
    }

    public ExpenseRelatedTo.ExpenseRelatedToType getRelatedToType() {
        return relatedToType;
    }

    public void setRelatedToType(ExpenseRelatedTo.ExpenseRelatedToType relatedToType) {
        this.relatedToType = relatedToType;
    }

    public Long getBeneficiaryId() {
        return beneficiaryId;
    }

    public void setBeneficiaryId(Long beneficiaryId) {
        this.beneficiaryId = beneficiaryId;
    }

    public Date getDate1() {
        return date1;
    }

    public void setDate1(Date date1) {
        this.date1 = date1;
    }

    public Date getDate2() {
        return date2;
    }

    public void setDate2(Date date2) {
        this.date2 = date2;
    }

    public String getDateOperator() {
        return dateOperator;
    }

    public void setDateOperator(String dateOperator) {
        this.dateOperator = dateOperator;
    }

    public Long getRequesterId() {
        return requesterId;
    }

    public void setRequesterId(Long requesterId) {
        this.requesterId = requesterId;
    }

    public Long getApprovedById() {
        return approvedById;
    }

    public void setApprovedById(Long approvedById) {
        this.approvedById = approvedById;
    }

    public String getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(String approvedBy) {
        this.approvedBy = approvedBy;
    }

    public Boolean getOnlyUnconfirmed() {
        return onlyUnconfirmed;
    }

    public void setOnlyUnconfirmed(Boolean onlyUnconfirmed) {
        this.onlyUnconfirmed = onlyUnconfirmed;
    }
}
