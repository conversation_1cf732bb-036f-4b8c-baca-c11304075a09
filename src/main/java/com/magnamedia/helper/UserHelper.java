package com.magnamedia.helper;

import com.magnamedia.core.entity.Position;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.module.AccountingModule;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Mar 20, 2019
 */
public class UserHelper {

	public static boolean hasPosition(Set<Position> positions, String position) {
		return positions != null
                        && (positions.size() > 0)
			&& (positions.stream().filter(x -> x.getCode().equalsIgnoreCase(position)).count() > 0);
	}

	public static List<User> getCooUser() {
		SelectQuery<User> query = new SelectQuery<>(User.class);
		query.join("positions");
		query.filterBy("positions.code", "in", Arrays.asList(AccountingModule.CLIENT_REFUND_COO_USER_POSITION,
				AccountingModule.COO_PAGE_TABS_FULL_CONTROL));
		query.filterBy("positions.deleted", "=", false);
		return query.execute();
	}

	public static User getCeoUser() {
		SelectQuery<OfficeStaff> query = new SelectQuery<>(OfficeStaff.class);
		query.filterBy("jobTitle.code", "=", "ceo");
		List<OfficeStaff> staffs = query.execute();
		return staffs != null && !staffs.isEmpty() ? staffs.get(0).getRelatedUser() : null;
	}

	public static boolean hasFamilyRefundCooPosition() {
		return hasFamilyRefundCooPosition(CurrentRequest.getUser());
	}

	public static boolean hasFamilyRefundCooPosition(User u) {
		return u != null &&
				(u.hasPosition(AccountingModule.CLIENT_REFUND_COO_USER_POSITION) ||
						u.hasPosition(AccountingModule.COO_PAGE_TABS_FULL_CONTROL));
	}

	public static boolean hasBankTransferCooPosition() {
		return CurrentRequest.getUser() != null &&
				(CurrentRequest.getUser().hasPosition(AccountingModule.BANK_TRANSFER_COO_USER_POSITION) ||
						CurrentRequest.getUser().hasPosition(AccountingModule.COO_PAGE_TABS_FULL_CONTROL));
	}

	public static boolean hasBankTransferReviewerPosition() {
		return CurrentRequest.getUser() != null &&
				(CurrentRequest.getUser().hasPosition(AccountingModule.BANK_TRANSFER_REVIEWER_USER_POSITION) ||
						CurrentRequest.getUser().hasPosition(AccountingModule.COO_PAGE_TABS_FULL_CONTROL));
	}

	public static boolean hasNightReviewCooPosition() {
		return CurrentRequest.getUser() != null &&
				(CurrentRequest.getUser().hasPosition(AccountingModule.NIGHT_REVIEW_COO_USER_POSITION) ||
						CurrentRequest.getUser().hasPosition(AccountingModule.COO_PAGE_TABS_FULL_CONTROL));
	}

	//ACC-9018
	public static boolean hasAddRefundToClientPosition() {
		return CurrentRequest.getUser() != null &&
				(CurrentRequest.getUser().hasPosition(AccountingModule.ALLOW_ADD_REFUND_TO_MAID_VISA_CONTRACT_POSITION));
	}
}
