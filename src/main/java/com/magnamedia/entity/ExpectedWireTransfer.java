package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.entity.serializer.IdNameMobileSerializer;
import com.magnamedia.entity.serializer.IdOnlySerializer;
import com.magnamedia.module.type.ExpectedWireTransferStatus;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.service.PaymentService;

import java.sql.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.Transient;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Jan 23, 2019
 *         ACC-373
 */
@Entity
public class ExpectedWireTransfer extends BaseEntity {

    @ManyToOne
    @JsonSerialize(using = IdNameMobileSerializer.class)
    private Client client;

    @Column
    private Double amount;

    @Column
    private Date expectedDate;

    @Enumerated(EnumType.STRING)
    private ExpectedWireTransferStatus status = ExpectedWireTransferStatus.Not_Matched;

    @ManyToOne
    private Transaction transaction;

    @Column
    private Long paymentId;

    @Transient
    @JsonSerialize(using = IdOnlySerializer.class)
    private Payment payment;

    //CMA-154
    @ManyToOne
    @JsonSerialize(using = IdOnlySerializer.class)
    private Payment replacementFor;

    // ACC-461 3)
    @Column
    private String transactionNumber;

    // ACC-592
    @Lob
    @Column
    private String notes;

    // CM-442
    @Transient
    private Long clienttransferDetailsID;

    public Client getClient() {
        return client;
    }

    public void setClient(Client client) {
        this.client = client;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Date getExpectedDate() {
        return expectedDate;
    }

    public void setExpectedDate(Date expectedDate) {
        this.expectedDate = expectedDate;
    }

    public ExpectedWireTransferStatus getStatus() {
        return status;
    }

    public void setStatus(ExpectedWireTransferStatus status) {
        this.status = status;
    }

    public Transaction getTransaction() {
        return transaction;
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
    }

    public Long getPaymentId() {
        return paymentId;
    }

    public void setPaymentId(Long paymentId) {
        this.paymentId = paymentId;
    }

    public Payment getPayment() {
        if (this.paymentId != null) {
            PaymentRepository paymentRepository =
                    Setup.getRepository(PaymentRepository.class);
            this.payment = paymentRepository.findOne(paymentId);
        }

        return this.payment;
    }

    public void setPayment(Payment payment) {
        this.payment = payment;
    }

    public Payment getReplacementFor() {
        return replacementFor;
    }

    public void setReplacementFor(Payment replacementFor) {
        this.replacementFor = replacementFor;
    }

    public String getTransactionNumber() {
        return transactionNumber;
    }

    public void setTransactionNumber(String transactionNumber) {
        this.transactionNumber = transactionNumber;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public Long getClienttransferDetailsID() {
        return clienttransferDetailsID;
    }

    public void setClienttransferDetailsID(Long clienttransferDetailsID) {
        this.clienttransferDetailsID = clienttransferDetailsID;
    }

    @BeforeInsert
    @BeforeUpdate
    public void validate() {

        if (this.client == null)
            throw new RuntimeException("Client is mandatory.");
        if (this.amount == null)
            throw new RuntimeException("Amount is mandatory.");
        if (this.expectedDate == null)
            throw new RuntimeException("Date is mandatory.");
        if (this.status == null)
            this.status = ExpectedWireTransferStatus.Not_Matched;
//        if (this.status == ExpectedWireTransferStatus.Matched)
//            throw new RuntimeException("Status is not insertable or updatable.");

    }

    @BeforeInsert
    public void processReplacementPayment() {
        if (this.replacementFor != null && this.replacementFor.getId() != null) {
            PaymentRepository paymentRepository = Setup.getRepository(PaymentRepository.class);
            Payment p = paymentRepository.findOne(this.replacementFor.getId());
            if (p == null)
                throw new RuntimeException("Payment not Found");
            if (p.isSwitchingAccountActionTaken() || (p.getReplaced() != null && p.getReplaced())) {
                throw new RuntimeException("You’re seeing this message because you’ve clicked on a button that no longer works. <REFRESH>");
            }

            p.setSwitchingAccountActionTaken(Boolean.TRUE);
            p.setBouncedFlowPausedForReplacement(Boolean.TRUE);
            p.setBouncedFlowPausedFromDate(new java.util.Date());
            Setup.getApplicationContext()
                    .getBean(PaymentService.class)
                    .forceUpdatePayment(p);
        }
    }
}
