package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseAdditionalInfo;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.NewRequest;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.NewVisaRequestRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> Created on Dec 28 , 2024
 */
@Service
public class HousemaidService {

    @Autowired
    private NewVisaRequestRepository newVisaRequestRepository;

    public static boolean hasBaseAdditionalInfoByKey(Long housemaidId, String key, String value) {
        return QueryService.existsEntity(BaseAdditionalInfo.class,
                "e.ownerId = :p0 and e.ownerType = 'Housemaid' and e.infoKey = :p1 and e.infoValue = :p2",
                new Object[]{ housemaidId, key, value });
    }

    public List<Housemaid> getHousemaids(
            String mobileNumber, String eidNumber, String firstName,
            String middleName, String lastName, Long contractId) {

        validateParameters(contractId, mobileNumber, eidNumber, firstName, middleName, lastName);
        validateNameFiltration(firstName, middleName, lastName);
        return fetchHousemaids(mobileNumber, eidNumber, firstName, middleName, lastName, contractId);
    }

    private List<Housemaid> fetchHousemaids(
            String mobileNumber, String eidNumber, String firstName,
            String middleName, String lastName, Long contractId) {

        List<Housemaid> housemaids = new ArrayList<>();

        if (StringUtils.isNotBlank(mobileNumber)) {
            housemaids = findMaidByMobileNumber(mobileNumber);
        }

        if (housemaids.isEmpty() && StringUtils.isNotBlank(eidNumber)) {
            housemaids = findHousemaidByEid(eidNumber);
        }

        if (housemaids.isEmpty() && !StringUtils.isAllBlank(firstName, middleName, lastName)) {
            housemaids = findByName(firstName, middleName, lastName);
        }

        if (housemaids.isEmpty() && contractId != null) {
            Contract contract = Setup.getRepository(ContractRepository.class).findOne(contractId);
            if (contract != null && contract.getHousemaid() != null) {
                housemaids.add(contract.getHousemaid());
            }
        }

        return housemaids;
    }

    public List<Housemaid> findMaidByMobileNumber(String mobileNumber) {
        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        String normalizedPhoneNumber = com.magnamedia.extra.StringUtils.NormalizePhoneNumber(mobileNumber);
        query.filterBy("phoneNumber", "=", mobileNumber)
                .or("whatsAppPhoneNumber", "=", mobileNumber)
                .or("normalizedPhoneNumber", "=", normalizedPhoneNumber)
                .or("normalizedWhatsAppPhoneNumber", "=", normalizedPhoneNumber);

        query.sortBy("creationDate", false);
        return query.execute();
    }

    public List<Housemaid> findHousemaidByEid(String eidNumber) {
        List<NewRequest> newRequests = newVisaRequestRepository.findVisaNewRequestByNewEidNumberOrderByCreationDate(eidNumber);

        return newRequests.isEmpty() || newRequests.get(0).getHousemaid() == null ? new ArrayList<>()
                : Collections.singletonList(newRequests.get(0).getHousemaid());
    }

    public List<Housemaid> findByName(String firstName, String middleName, String lastName) {
        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        query.filterBy("name", "like", Stream.of(firstName, middleName, lastName)
                .filter(StringUtils::isNotBlank).collect(Collectors.joining("%")) + "%");
        query.sortBy("creationDate", false);
        return query.execute();
    }

    private void validateParameters(Long contractId, String mobileNumber, String eidNumber, String firstName, String middleName, String lastName) {
        if (contractId == null && StringUtils.isAllBlank(mobileNumber, eidNumber, firstName, middleName, lastName)) {
            throw new BusinessException("At least one of the parameters must be provided.");
        }
    }

    private void validateNameFiltration(String firstName, String middleName, String lastName) {
        if (StringUtils.isBlank(firstName) &&
                (StringUtils.isBlank(firstName) && !StringUtils.isAllBlank(middleName, lastName)) ||
                (StringUtils.isBlank(lastName) && StringUtils.isNotBlank(middleName))) {
            throw new BusinessException("Name based filtering should be passed as combination like either (firstName), (firstName, lastName) or (firstName, middleName, lastName).");
        }
    }
}
