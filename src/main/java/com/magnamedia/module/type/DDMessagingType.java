package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR>
 * Created on Apr 13, 2020
 * ACC-1611
 */

public enum DDMessagingType implements LabelValueEnum {

    DirectDebitRejected("Direct Debit Rejected", false),
    ClientPaidCashAndNoSignatureProvided("Client paid cash and no signature provided", false),
    IncompleteDDRejectedByDataEntry("Incomplete flow / Data entry rejection", false),
    BouncedPayment("Bounced Payment", false),
    IncompleteDDClientHasNoApprovedSignature("Incomplete flow / Missing bank info", false),
    ExpiryPayment("Expiry Payment", false),
    OnlineCreditCardPaymentReminders("Online Credit Card Payment Reminders", false),
    ClientsPayingViaCreditCard("Clients Paying Via Credit Card", false),
    Termination("Termination", false), // ACC-6795
    ExtensionFlow("Extension Flow", false), // ACC-6795

    // DEPRECATED
    OneMonthAgreement("One Month Agreement", true), // ACC-6647
    SendSmsNextDay("send Sms Next Day", true);

    private final String label;
    private final boolean deprecated;

    DDMessagingType(String label, boolean deprecated) {
        this.label = label; this.deprecated = deprecated;
    }

    public String getLabel() {
        return label;
    }

    public boolean isDeprecated() {
        return deprecated;
    }
}