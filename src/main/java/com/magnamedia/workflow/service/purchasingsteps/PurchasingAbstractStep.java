package com.magnamedia.workflow.service.purchasingsteps;

import com.magnamedia.core.repository.workflow.WorkflowRepository;
import com.magnamedia.core.workflow.ManualTask;
import com.magnamedia.entity.PurchasingToDo;
import com.magnamedia.repository.PurchasingToDoRepository;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <PERSON> (Jan 31, 2021)
 */
public abstract class PurchasingAbstractStep <T extends PurchasingToDo> extends ManualTask<T> {

    @Autowired
    WorkflowRepository<T> purchasingToDoRepository;

    @Override
    public WorkflowRepository<T> getRepository() {
        return purchasingToDoRepository;
    }

    @Override
    public void onSave(PurchasingToDo purchasingToDo) {
    }
    @Override
    public List<String> getTaskHeader() {
        return new ArrayList<>();
    }

}
