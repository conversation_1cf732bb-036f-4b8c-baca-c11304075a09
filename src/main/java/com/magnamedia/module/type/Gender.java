package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Mar 08, 2020
 *         Jirra ACC-1435
 */
public enum Gender implements LabelValueEnum {
    Female("Female"),
    Male("Male");

    private final String label;

    Gender(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
