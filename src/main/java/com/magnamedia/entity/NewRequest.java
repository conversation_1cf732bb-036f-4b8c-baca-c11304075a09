package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.serialize.IdSerializer;
import com.magnamedia.core.type.Nationality;
import com.magnamedia.core.workflow.FormField;
import com.magnamedia.extra.HousemaidReligion;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.type.HousemaidType;
import com.magnamedia.module.type.PaymentType;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.workflow.visa.ChangeStatusExpensesService;
import com.magnamedia.workflow.visa.CreateOfferLetterExpensesService;
import com.magnamedia.workflow.visa.EntryVisaExpensesService;
import com.magnamedia.workflow.visa.ImmigrationCancellationExpensesService;
import com.magnamedia.workflow.visa.PayLaborExpensesService;
import com.magnamedia.workflow.visa.PrepareAfterEntryVisaExpensesService;
import com.magnamedia.workflow.visa.RVisaExpensesService;
import com.magnamedia.workflow.visa.UploadingToTasheelExpensesService;
import com.magnamedia.workflow.visa.WorkPermitExpensesService;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import org.hibernate.envers.NotAudited;

/**
 * <AUTHOR> Jabr <<EMAIL>>
 * Created on Sep 16, 2017
 */
@Entity
@Table(
        indexes = {
                @Index(columnList = "HOUSEMAID_ID", unique = false)
        })
public class NewRequest extends VisaRequest<NewRequest, NewRequestNote, NewRequestExpense> implements Serializable {

    private static final int ENTRY_VISA_COST_INSIDE_UAE = 900;
    private static final int ENTRY_VISA_COST_OUTSIDE_UAE = 400;


    public NewRequest() {
        super("Collect Documents");
    }

    @Override
    public String getFinishedTaskName() {
        return "Visa processing complete";
    }

    //Jirra ACC-832
    @OneToMany(mappedBy = "newRequest", fetch = FetchType.LAZY)
    @JsonIgnore
    private List<CancelRequest> cancelRequests;

    public List<CancelRequest> getCancelRequests() {
        return cancelRequests;
    }

    public void setCancelRequests(List<CancelRequest> cancelRequests) {
        this.cancelRequests = cancelRequests;
    }

    @JsonSerialize(using = IdSerializer.class)
    public CancelRequest getCancelRequest() {
        if (getCancelRequests().isEmpty()) {
            return null;
        }
        return cancelRequests.get(cancelRequests.size() - 1);
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem location;

    @Enumerated(EnumType.STRING)
    @Column
    private HousemaidReligion religion;

    @Column
    private String facebookUser;

    @Column
    private java.sql.Date birthdate;

    @Column
    private String passportId;

    @Column
    private java.sql.Date passportExpiryDate;

    @Column
    private String offerLetterNumber;

    @Column
    private java.sql.Date offerLetterDate;

    @Column
    private Double offerLetterExpenses;

    @Enumerated
    @Column
    private PaymentType offerLetterPaymentType;

    @Column
    private String mb;

    @Column
    private java.sql.Date workPermitSubmitionDate;

    @Enumerated
    @Column
    private PaymentType workPermitPaymentType;

    @Column
    private Double workPermitExpenses;

    @Column
    private String workPermitMinistryApproval;

    @Column
    private String employeeUniqueId;

    @Column
    private String molCardNumber;

    @Column
    private java.sql.Date workPermitExpiryDate;

    @Column
    private java.sql.Date payLaborCardFeesDate;

    @Enumerated
    @Column
    private PaymentType payLaborPaymentType;

    @Column
    private Double payLaborExpenses;

    @Column
    private java.sql.Date entryVisaApplicationDate;

    @Enumerated
    @Column
    private PaymentType entryVisaPaymentType;

    @Column
    private Double entryVisaExpenses;

    @Column
    private java.sql.Date entryVisaExpiryDate;

    @Column
    private String entryVisaPermitNumber;

    @Column
    private String uid;

    @Column
    private String immigrationApplicationNumber;

    @Enumerated
    @Column
    private PaymentType immigrationCancellationPaymentType;

    @Column
    private Double immigrationCancellationExpenses;

    @Enumerated
    @Column
    private PaymentType changeOfStatusPaymentType;

    @Column
    private Double changeOfStatusExpenses;

    @Column
    private java.sql.Date medicalDocumentIssueDate;

    @Enumerated
    @Column
    private PaymentType medicalPaymentType;

    @Column
    private Double medicalExpenses;

    @Column
    private Double contractExpenses;

    @Enumerated
    @Column
    private PaymentType eidPaymentType;

    @Column
    private Double eidExpenses;

    @Column
    private String agentId;

    @Column
    @NotAudited
    private Boolean eidAndInsuranceCardsPrepared;

    @Column
    @NotAudited
    private Boolean packageHasBeenHanded;

    @Column
    @NotAudited
    private Boolean aramexPickedUpTheEidAndInsuranceCardFromTheOffice;

    @Column
    @NotAudited
    private String eidApplicationNumber;

    @Column
    @NotAudited
    private String personNameEnglish;

    @Column
    @NotAudited
    private String personNameArabic;

    @Column
    @NotAudited
    private String companyCode;

    // ACC-1633
    @Column
    @NotAudited
    private Boolean fitToWorkInUAE;

    @Column
    private String employeeAccountWithAgent;

    @Column
    private java.sql.Date newPassportExpiryDate;

    @Column
    private java.sql.Date entryDate;

    @Column
    private java.sql.Date medicalCertificateExpiryDate;

    @Enumerated
    @Column
    private PaymentType tasheelPaymentType;

    @Column
    private Double tasheelExpenses;

    @Column
    private java.sql.Date laborCardExpiryDate;

    @Column
    private java.sql.Date insurancePolicyExpiryDate;

    @Enumerated
    @Column
    private PaymentType rVisaPaymentType;

    @Column
    private Double rVisaExpenses;

    @Column
    private String residencyNumber;

    @Column
    private java.sql.Date rVisaExpiryDate;

    @Column
    private String newEidNumber;

    @Column
    private String insuranceCardNumber;

    //Jirra ACC-1208
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn
    @JsonSerialize(using = IdLabelSerializer.class)
    @NotAudited
    private PicklistItem workerType;

    @Column
    private Double overstayFee;

    public PicklistItem getLocation() {
        return location;
    }

    public void setLocation(PicklistItem location) {
        this.location = location;
    }

    public HousemaidReligion getReligion() {
        if (religion == null
                && getHousemaid() != null) {
            return getHousemaid()
                    .getReligion();
        }
        return religion;
    }

    public void setReligion(HousemaidReligion religion) {
        this.religion = religion;
        if (religion != null) {
            if (getHousemaid() != null) {
                getHousemaid()
                        .setReligion(religion);
            }
        }
    }

    public String getFacebookUser() {
        if (facebookUser == null) {
            facebookUser = getHousemaid()
                    .getFacebookAcc();
        }
        return facebookUser;
    }

    public void setFacebookUser(String facebookUser) {
        this.facebookUser = facebookUser;
    }

    public java.sql.Date getBirthdate() {
        return birthdate;
    }

    public void setBirthdate(java.sql.Date birthdate) {
        this.birthdate = birthdate;
    }

    public String getPassportId() {
        return passportId;
    }

    public void setPassportId(String passportId) {
        this.passportId = passportId;
    }

    public java.sql.Date getPassportExpiryDate() {
        return passportExpiryDate;
    }

    public void setPassportExpiryDate(java.sql.Date passportExpiryDate) {
        this.passportExpiryDate = passportExpiryDate;
    }

    public String getOfferLetterNumber() {
        return offerLetterNumber;
    }

    public void setOfferLetterNumber(String offerLetterNumber) {
        this.offerLetterNumber = offerLetterNumber;
    }

    public java.sql.Date getOfferLetterDate() {
        return offerLetterDate;
    }

    public void setOfferLetterDate(java.sql.Date offerLetterDate) {
        this.offerLetterDate = offerLetterDate;
    }

    public PaymentType getOfferLetterPaymentType() {
        return offerLetterPaymentType;
    }

    public void setOfferLetterPaymentType(PaymentType offerLetterPaymentType) {
        this.offerLetterPaymentType = offerLetterPaymentType;
    }

    public String getMb() {
        return mb;
    }

    public void setMb(String mb) {
        this.mb = mb;
    }

    public java.sql.Date getWorkPermitSubmitionDate() {
        return workPermitSubmitionDate;
    }

    public void setWorkPermitSubmitionDate(java.sql.Date workPermitSubmitionDate) {
        this.workPermitSubmitionDate = workPermitSubmitionDate;
    }

    public Boolean getFitToWorkInUAE() {
        return fitToWorkInUAE;
    }

    public void setFitToWorkInUAE(Boolean fitToWorkInUAE) {
        this.fitToWorkInUAE = fitToWorkInUAE;
    }

    public PaymentType getWorkPermitPaymentType() {
        return workPermitPaymentType;
    }

    public void setWorkPermitPaymentType(PaymentType workPermitPaymentType) {
        this.workPermitPaymentType = workPermitPaymentType;
    }

    public String getWorkPermitMinistryApproval() {
        return workPermitMinistryApproval;
    }

    public void setWorkPermitMinistryApproval(String workPermitMinistryApproval) {
        this.workPermitMinistryApproval = workPermitMinistryApproval;
    }

    public String getEmployeeUniqueId() {
        return employeeUniqueId;
    }

    public void setEmployeeUniqueId(String employeeUniqueId) {
        this.employeeUniqueId = employeeUniqueId;
    }

    public String getMolCardNumber() {
        return molCardNumber;
    }

    public void setMolCardNumber(String molCardNumber) {
        this.molCardNumber = molCardNumber;
    }

    public java.sql.Date getPayLaborCardFeesDate() {
        return payLaborCardFeesDate;
    }

    public void setPayLaborCardFeesDate(java.sql.Date payLaborCardFeesDate) {
        this.payLaborCardFeesDate = payLaborCardFeesDate;
    }

    public PaymentType getPayLaborPaymentType() {
        return payLaborPaymentType;
    }

    public void setPayLaborPaymentType(PaymentType payLaborPaymentType) {
        this.payLaborPaymentType = payLaborPaymentType;
    }

    public Double getOfferLetterExpenses() {
        if (offerLetterExpenses == null) {
            offerLetterExpenses = CreateOfferLetterExpensesService.DEFAULT_EXPENSES;
        }
        return offerLetterExpenses;
    }

    public void setOfferLetterExpenses(Double offerLetterExpenses) {
        this.offerLetterExpenses = offerLetterExpenses;
    }

    public Double getWorkPermitExpenses() {
        if (workPermitExpenses == null) {
            workPermitExpenses = WorkPermitExpensesService.DEFAULT_EXPENSES;
        }
        return workPermitExpenses;
    }

    public void setWorkPermitExpenses(Double workPermitExpenses) {
        this.workPermitExpenses = workPermitExpenses;
    }

    public Double getPayLaborExpenses() {
        if (payLaborExpenses == null) {
            payLaborExpenses = PayLaborExpensesService.DEFAULT_EXPENSES;
        }
        return payLaborExpenses;
    }

    public void setPayLaborExpenses(Double payLaborExpenses) {
        this.payLaborExpenses = payLaborExpenses;
    }

    public java.sql.Date getEntryVisaApplicationDate() {
        return entryVisaApplicationDate;
    }

    public void setEntryVisaApplicationDate(java.sql.Date entryVisaApplicationDate) {
        this.entryVisaApplicationDate = entryVisaApplicationDate;
    }

    public PaymentType getEntryVisaPaymentType() {
        return entryVisaPaymentType;
    }

    public void setEntryVisaPaymentType(PaymentType entryVisaPaymentType) {
        this.entryVisaPaymentType = entryVisaPaymentType;
    }

    public Double getEntryVisaExpenses() {
        if (entryVisaExpenses == null) {
            entryVisaExpenses = EntryVisaExpensesService
                    .getDefaultEntryVisaExpenses(this);
        }
        return entryVisaExpenses;
    }

    public void setEntryVisaExpenses(Double entryVisaExpenses) {
        this.entryVisaExpenses = entryVisaExpenses;
    }

    public java.sql.Date getEntryVisaExpiryDate() {
        return entryVisaExpiryDate;
    }

    public void setEntryVisaExpiryDate(java.sql.Date entryVisaExpiryDate) {
        this.entryVisaExpiryDate = entryVisaExpiryDate;
    }

    public String getEntryVisaPermitNumber() {
        return entryVisaPermitNumber;
    }

    public void setEntryVisaPermitNumber(String entryVisaPermitNumber) {
        this.entryVisaPermitNumber = entryVisaPermitNumber;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getImmigrationApplicationNumber() {
        return immigrationApplicationNumber;
    }

    public void setImmigrationApplicationNumber(String immigrationApplicationNumber) {
        this.immigrationApplicationNumber = immigrationApplicationNumber;
    }

    public PaymentType getImmigrationCancellationPaymentType() {
        return immigrationCancellationPaymentType;
    }

    public void setImmigrationCancellationPaymentType(PaymentType immigrationCancellationPaymentType) {
        this.immigrationCancellationPaymentType = immigrationCancellationPaymentType;
    }

    public Double getImmigrationCancellationExpenses() {
        if (immigrationCancellationExpenses == null) {
            immigrationCancellationExpenses = ImmigrationCancellationExpensesService.DEFAULT_EXPENSES;
        }
        return immigrationCancellationExpenses;
    }

    public void setImmigrationCancellationExpenses(Double immigrationCancellationExpenses) {
        this.immigrationCancellationExpenses = immigrationCancellationExpenses;
    }

    public PaymentType getChangeOfStatusPaymentType() {
        return changeOfStatusPaymentType;
    }

    public void setChangeOfStatusPaymentType(PaymentType changeOfStatusPaymentType) {
        this.changeOfStatusPaymentType = changeOfStatusPaymentType;
    }

    public Double getChangeOfStatusExpenses() {
        if (changeOfStatusExpenses == null) {
            changeOfStatusExpenses = ChangeStatusExpensesService.DEFAULT_EXPENSES;
        }
        return changeOfStatusExpenses;
    }

    public void setChangeOfStatusExpenses(Double changeOfStatusExpenses) {
        this.changeOfStatusExpenses = changeOfStatusExpenses;
    }

    public java.sql.Date getMedicalDocumentIssueDate() {
        return medicalDocumentIssueDate;
    }

    public void setMedicalDocumentIssueDate(java.sql.Date medicalDocumentIssueDate) {
        this.medicalDocumentIssueDate = medicalDocumentIssueDate;
    }

    public PaymentType getMedicalPaymentType() {
        return medicalPaymentType;
    }

    public void setMedicalPaymentType(PaymentType medicalPaymentType) {
        this.medicalPaymentType = medicalPaymentType;
    }

    public Double getMedicalExpenses() {
        if (medicalExpenses == null) {
            medicalExpenses = PrepareAfterEntryVisaExpensesService.DEFAULT_MEDICAL_EXPENSES;
        }
        return medicalExpenses;
    }

    public void setMedicalExpenses(Double medicalExpenses) {
        this.medicalExpenses = medicalExpenses;
    }

    public Double getContractExpenses() {
        if (contractExpenses == null) {
            contractExpenses = PrepareAfterEntryVisaExpensesService.DEFAULT_CONTRACT_EXPENSES;
        }
        return contractExpenses;
    }

    public void setContractExpenses(Double contractExpenses) {
        this.contractExpenses = contractExpenses;
    }

    public PaymentType getEidPaymentType() {
        return eidPaymentType;
    }

    public void setEidPaymentType(PaymentType eidPaymentType) {
        this.eidPaymentType = eidPaymentType;
    }

    public Double getEidExpenses() {
        if (eidExpenses == null) {
            eidExpenses = PrepareAfterEntryVisaExpensesService.DEFAULT_EID_EXPENSES;
        }
        return eidExpenses;
    }

    public void setEidExpenses(Double eidExpenses) {
        this.eidExpenses = eidExpenses;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public Boolean getEidAndInsuranceCardsPrepared() {
        return eidAndInsuranceCardsPrepared;
    }

    public void setEidAndInsuranceCardsPrepared(Boolean eidAndInsuranceCardsPrepared) {
        this.eidAndInsuranceCardsPrepared = eidAndInsuranceCardsPrepared;
    }

    public Boolean getPackageHasBeenHanded() {
        return packageHasBeenHanded;
    }

    public void setPackageHasBeenHanded(Boolean packageHasBeenHanded) {
        this.packageHasBeenHanded = packageHasBeenHanded;
    }

    public Boolean getAramexPickedUpTheEidAndInsuranceCardFromTheOffice() {
        return aramexPickedUpTheEidAndInsuranceCardFromTheOffice;
    }

    public void setAramexPickedUpTheEidAndInsuranceCardFromTheOffice(Boolean aramexPickedUpTheEidAndInsuranceCardFromTheOffice) {
        this.aramexPickedUpTheEidAndInsuranceCardFromTheOffice = aramexPickedUpTheEidAndInsuranceCardFromTheOffice;
    }

    public String getEidApplicationNumber() {
        return eidApplicationNumber;
    }

    public void setEidApplicationNumber(String eidApplicationNumber) {
        this.eidApplicationNumber = eidApplicationNumber;
    }

    public String getPersonNameEnglish() {
        return personNameEnglish;
    }

    public void setPersonNameEnglish(String personNameEnglish) {
        this.personNameEnglish = personNameEnglish;
    }

    public String getPersonNameArabic() {
        return personNameArabic;
    }

    public void setPersonNameArabic(String personNameArabic) {
        this.personNameArabic = personNameArabic;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getEmployeeAccountWithAgent() {
        return employeeAccountWithAgent;
    }

    public void setEmployeeAccountWithAgent(String employeeAccountWithAgent) {
        this.employeeAccountWithAgent = employeeAccountWithAgent;
    }

    public java.sql.Date getNewPassportExpiryDate() {
        return newPassportExpiryDate;
    }

    public void setNewPassportExpiryDate(java.sql.Date newPassportExpiryDate) {
        this.newPassportExpiryDate = newPassportExpiryDate;
    }

    public java.sql.Date getEntryDate() {
        if (entryDate == null) {
            entryDate = new java.sql.Date(new Date().getTime());
        }
        return entryDate;
    }

    public void setEntryDate(java.sql.Date entryDate) {
        this.entryDate = entryDate;
    }

    public java.sql.Date getMedicalCertificateExpiryDate() {
        return medicalCertificateExpiryDate;
    }

    public void setMedicalCertificateExpiryDate(java.sql.Date medicalCertificateExpiryDate) {
        this.medicalCertificateExpiryDate = medicalCertificateExpiryDate;
    }

    public PaymentType getTasheelPaymentType() {
        return tasheelPaymentType;
    }

    public void setTasheelPaymentType(PaymentType tasheelPaymentType) {
        this.tasheelPaymentType = tasheelPaymentType;
    }

    public Double getTasheelExpenses() {
        if (tasheelExpenses == null) {
            tasheelExpenses = UploadingToTasheelExpensesService.DEFAULT_EXPENSES;
        }
        return tasheelExpenses;
    }

    public void setTasheelExpenses(Double tasheelExpenses) {
        this.tasheelExpenses = tasheelExpenses;
    }

    public java.sql.Date getLaborCardExpiryDate() {
        return laborCardExpiryDate;
    }

    public void setLaborCardExpiryDate(java.sql.Date laborCardExpiryDate) {
        this.laborCardExpiryDate = laborCardExpiryDate;
    }

    public java.sql.Date getInsurancePolicyExpiryDate() {
        return insurancePolicyExpiryDate;
    }

    public void setInsurancePolicyExpiryDate(java.sql.Date insurancePolicyExpiryDate) {
        this.insurancePolicyExpiryDate = insurancePolicyExpiryDate;
    }

    public PaymentType getrVisaPaymentType() {
        return rVisaPaymentType;
    }

    public void setrVisaPaymentType(PaymentType rVisaPaymentType) {
        this.rVisaPaymentType = rVisaPaymentType;
    }

    public Double getrVisaExpenses() {
        if (rVisaExpenses == null) {
            rVisaExpenses = RVisaExpensesService.DEFAULT_EXPENSES;
        }
        return rVisaExpenses;
    }

    public void setrVisaExpenses(Double rVisaExpenses) {
        this.rVisaExpenses = rVisaExpenses;
    }

    public String getResidencyNumber() {
        return residencyNumber;
    }

    public void setResidencyNumber(String residencyNumber) {
        this.residencyNumber = residencyNumber;
    }

    public java.sql.Date getrVisaExpiryDate() {
        return rVisaExpiryDate;
    }

    public void setrVisaExpiryDate(java.sql.Date rVisaExpiryDate) {
        this.rVisaExpiryDate = rVisaExpiryDate;
    }

    public String getNewEidNumber() {
        return newEidNumber;
    }

    public void setNewEidNumber(String newEidNumber) {
        this.newEidNumber = newEidNumber;
    }

    public String getInsuranceCardNumber() {
        return insuranceCardNumber;
    }

    public void setInsuranceCardNumber(String insuranceCardNumber) {
        this.insuranceCardNumber = insuranceCardNumber;
    }

    public boolean isInsideUae() {
        if (location != null) {
            switch (location.getName()) {
                case "Abu Dhabi":
                case "Ajman":
                case "Al Ain":
                case "Dubai":
                case "Fujairah":
                case "Ras Al Kaimah":
                case "Sharjah":
                case "Umm Al Quwain":
                    return true;
            }
        }
        return false;
    }

    public boolean isShowTicket() {
        return !isInsideUae();
    }

    public java.sql.Date getWorkPermitExpiryDate() {
        return workPermitExpiryDate;
    }

    public void setWorkPermitExpiryDate(java.sql.Date workPermitExpiryDate) {
        this.workPermitExpiryDate = workPermitExpiryDate;
    }

    @JsonIgnore
    public int getEntryVisaCost() {
        if (isInsideUae()) {
            return ENTRY_VISA_COST_INSIDE_UAE;
        } else {
            return ENTRY_VISA_COST_OUTSIDE_UAE;
        }
    }

    @JsonIgnore
    public boolean isIndonesian() {
        return getHousemaid()
                .getNationality()
                .getName()
                .equalsIgnoreCase(Nationality.INDONESIAN);
    }

    @JsonIgnore
    public boolean isPhilippines() {
        return getHousemaid()
                .getNationality()
                .getName()
                .equalsIgnoreCase(Nationality.PHILIPPINES);
    }

    public PicklistItem getWorkerType() {
        if (workerType == null) {
            PicklistItem cleaner = PicklistHelper.getItem("worker_types", "cleaner");
            workerType = cleaner;
            if (getHousemaid() != null && getHousemaid().getHousemaidType().equals(HousemaidType.MAID_VISA)) {
                ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
                Contract contract = contractRepository.findFirstOneByHousemaidOrderByCreationDateDesc(this.getHousemaid());
                if (contract != null) {
                    workerType = contract.getWorkerType();
                }
            }
        }
        return workerType;
    }

    public void setWorkerType(PicklistItem workerType) {
        this.workerType = workerType;
    }

    @Override
    public boolean equals(Object object) {
        if (!(object instanceof NewRequest)) {
            return false;
        }
        return super.equals(object);
    }

    @Override
    public List<FormField> getForm(String taskName) {
        return null;
    }

    public Double getOverstayFee() { return overstayFee; }

    public void setOverstayFee(Double overstayFee) { this.overstayFee = overstayFee; }
}
