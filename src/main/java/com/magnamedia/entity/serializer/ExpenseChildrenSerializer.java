package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.entity.Expense;
import com.magnamedia.module.type.ExpenseApprovalMethod;
import com.magnamedia.module.type.ExpenseApproveHolderType;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 1/29/2021
 */
public class ExpenseChildrenSerializer extends JsonSerializer<List<Expense>> {
    @Override
    public void serialize(
            List<Expense> children,
            JsonGenerator gen,
            SerializerProvider serializerProvider) throws IOException, JsonProcessingException {

        gen.writeStartArray();

        for (Expense child : children) {
            gen.writeStartObject();

            gen.writeNumberField("id", child.getId());
            gen.writeStringField("name", child.getName());
            gen.writeStringField("code", child.getCode());
            gen.writeStringField("label", child.getLabel());
            gen.writeStringField("caption", child.getCaption());
            gen.writeStringField("manager", child.getManager()==null? "null" : child.getManager().getName());
            gen.writeBooleanField("disabled", child.getDisabled());
            gen.writeBooleanField("deleted", child.getDeleted());
            // ACC-7989
            gen.writeBooleanField("isMatchedSearchFilter", child.getMatchedSearchFilter());
            // ACC-7613
            gen.writeStringField("requestedFrom", child.getRequestedFrom() != null ? child.getRequestedFrom().getName() : "");
            gen.writeStringField("approvalMethod", child.getApprovalMethod() != null ? child.getApprovalMethod().getLabel() : "");
            gen.writeStringField("limitForApproval", child.getLimitForApproval() != null ? String.valueOf(child.getLimitForApproval().intValue()) : null);
            gen.writeStringField("approveHolder", child.getApproveHolderType() != null ?
                    child.getApproveHolderType().equals(ExpenseApproveHolderType.USER) ?
                        (child.getApproveHolder() != null ? child.getApproveHolder().getFullName()  : "") :
                        child.getApproveHolderType().equals(ExpenseApproveHolderType.FINAL_MANAGER) ?
                                (child.getManager() != null ? child.getManager().getFullName()  : "") :
                                child.getApproveHolderType().equals(ExpenseApproveHolderType.EMAIL) ?
                                    (child.getApproveHolderEmail() != null ? child.getApproveHolderEmail() : "") :
                                "" :
                    "");
            gen.writeStringField("LimitCOO", child.getLimitCOO() != null ? String.valueOf(child.getLimitCOO().intValue()) : null);
            gen.writeStringField("parentCode", child.getParent().getCode());

            gen.writeEndObject();
        }

        gen.writeEndArray();
    }
}