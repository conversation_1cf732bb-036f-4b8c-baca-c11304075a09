package com.magnamedia.entity.projection;

import java.util.Date;
import org.springframework.beans.factory.annotation.Value;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Dec 27, 2018
 */
public interface BankPaymentActivationFileProjection {
    
    public Long getId();
    
    public String getBankName();

    public Date getDate();
    
    @Value("#{target.getAttachments().size()>0?target.getAttachments().get(0).getName():null}")
    public String getFileName();
    
    @Value("#{target.getAttachments().size()>0?target.getAttachments().get(0).getUuid():null}")
    public String getUuid();

}
