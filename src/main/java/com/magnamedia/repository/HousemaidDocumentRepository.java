package com.magnamedia.repository;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.HousemaidDocument;
import java.util.List;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on May 14, 2019
 */
@Repository
public interface HousemaidDocumentRepository extends BaseRepository<HousemaidDocument> {
    
    List<HousemaidDocument> findByHousemaidAndType(
            Housemaid housemaid, PicklistItem docType);
    
    List<HousemaidDocument> findByHousemaidAndTypeOrderByCreationDateDesc(
            Housemaid housemaid, PicklistItem docType);
}
