package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.PaymentStatus;

import java.sql.Date;

/**
 * Created by Mamon.Masod on 2/28/2021.
 */
public interface PaymentSearchProjection {

    Long getId();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    PicklistItem getTypeOfPayment();

    PaymentMethod getMethodOfPayment();

    Double getAmountOfPayment();

    Date getDateOfPayment();

    PaymentStatus getStatus();
}
