package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.entity.Bucket;
import com.magnamedia.entity.CooQuestion;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.serializer.HousemaidSerilizer;
import com.magnamedia.module.type.ExpensePaymentMethod;
import com.magnamedia.workflow.type.ExpenseRequestStatus;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface ExpenseRequestProjection {
    String getBenefeciaryInfo();

    String getBeneficiaryDetails();

    String getRelatedToInfo();

    Double getAmount();

    Double getAmountInLocalCurrency();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    PicklistItem getCurrency();

    ExpensePaymentMethod getPaymentMethod();

    ExpenseRequestStatus getStatus();

    @Value("#{{name: target.getRequestedBy()?.getName(), id: target.getRequestedBy()?.getId()}}")
    Map<?, ?> getRequestedBy();

//    @Value("#{{name: target.getApproveHolder()?.getName(), id: target.getApproveHolder()?.getId()}}")
//    Map<?, ?> getApproveHolder();

    String getApproveHolderEmail();

    String getApprovedBy();

    @Value("#{{name: target.getExpense()?.getName(), id: target.getExpense()?.getId(), approvalMethod: target.getExpense()?.getApprovalMethod()}}")
    Map<?, ?> getExpense();

    @Value("#{target.getExpense()?.getCaption()}")
    String getExpenseCaption();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    PicklistItem getPurposeAdditionalDescription();

    List<Attachment> getAttachments();

    @Value("#{{id: target.getExpensePayment()?.getId(), transaction: target.getExpensePayment()?.getTransaction()}}")
    Map<?, ?> getPayment();

    Date getCreationDate();

    Boolean getCanBeRefunded();

    Long getId();

    String getToken();

    Long getRelatedToId();

    Long getBeneficiaryId();

    String getBeneficiaryMobileNumber();

    String getBeneficiaryAccountName();

    String getBeneficiaryIban();

    String getSwift();

    Double getLoanAmount();

    @JsonSerialize(using = HousemaidSerilizer.class)
    Housemaid getHousemaid();

    String getDescription();

    String getCooRequestType();

    String getNotes();

    Boolean getIsRefunded();

    String getCooAllNotes();

    String getEntityType();

    List<CooQuestion> getCooQuestions();

    boolean isOneQuestionAnswered();

    boolean isNoneQuestionAnswered();

    boolean isAllQuestionsAnswered();

    // ACC-5341
    String getRejectionNotes();

    @Value("#{ target.getExpensePayment() == null ? null : target.getExpensePayment().getFromBucket()}")
    Bucket getBucket(); // ACC-4505
}