package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.Position;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.repository.PositionRepository;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.entity.Category;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.OrderCycle;
import com.magnamedia.repository.CategoryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <PERSON> (Feb 02, 2021)
 */
@Service
public class PurchasingReminderService {

    static Logger logger = Logger.getLogger(PurchasingReminderService.class.getName());

    @Autowired
    private CategoryRepository categoryRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private EmailTemplateService emailTemplateService;
    @Autowired
    private MessagingService messagingService;

    public List<Category> getMonthlyCategories() {
        PicklistItem monthlyItem = Setup.getItem(AccountingModule.PICKLIST_CATEGORY_ORDER_CYCLE, OrderCycle.MONTHLY.getName());
        return categoryRepository.findByOrderCycle(monthlyItem);
    }

    public List<Category> getWeeklyCategories() {
        PicklistItem weeklyItem = Setup.getItem(AccountingModule.PICKLIST_CATEGORY_ORDER_CYCLE, OrderCycle.WEEKLY.getName());
        return categoryRepository.findByOrderCycle(weeklyItem);
    }


    public void sendMailToStockKeeper(
            List<Category> categories) {

        String categoriesParameter = categories.stream().map(c -> "\u2022" + c.getName())
                .collect(Collectors.joining("\n"));
        logger.info("categories " + categoriesParameter);

        Position position = Setup.getRepository(PositionRepository.class).findByCode(AccountingModule.STOCK_KEEPER);
        List<User> stockKeepers = userRepository.findByPositionOrderByFullNameAsc(position);
        //ACC-8776
        List<Long> excludeUsersIds = Arrays.stream(Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_EXCLUDE_USERS_IDS_FROM_SENDING_PURCHASE_ORDER_REMINDER_EMAIL).split(","))
                .filter(e -> !e.isEmpty())
                .map(Long::parseLong).collect(Collectors.toList());

        for (User keeper : stockKeepers) {
            if (excludeUsersIds.contains(keeper.getId())) continue;

            logger.info("stock_keeper_name " + keeper.getName());
            Map<String, String> parameters = new HashMap<String, String>() {{
                put("stock_keeper_name", keeper.getName());
                put("categories", categoriesParameter);
            }};
            emailTemplateService.sendExpenseRequestTodoEmail(keeper.getEmail(), "Purchase Order Reminder", "PURCHASING_STOCK_KEEPER_REMINDER", parameters);

            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    public void sendMailToStockKeeperManager(List<Category> categories) {
        String categoriesParameter = categories.stream().map(c -> "\u2022" + c.getName())
                .collect(Collectors.joining("\n"));
        logger.info("categories " + categoriesParameter);

        Position managerPosition = Setup.getRepository(PositionRepository.class).findByCode(AccountingModule.STOCK_KEEPER_MANAGER);
        List<User> stockKeeperManagers = userRepository.findByPositionOrderByFullNameAsc(managerPosition);

        String keeperName = getKeeperNameVariable();
        //ACC-8776
        List<Long> excludeUsersIds = Arrays.stream(Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_EXCLUDE_USERS_IDS_FROM_SENDING_PURCHASE_ORDER_REMINDER_EMAIL).split(","))
                .filter(e -> !e.isEmpty())
                .map(Long::parseLong).collect(Collectors.toList());

        for (User manager : stockKeeperManagers) {
            if (excludeUsersIds.contains(manager.getId())) continue;

            Map<String, String> parameters = new HashMap<String, String>();
            logger.info("stock_keeper_manager_name " + manager.getName());
            parameters.put("stock_keeper_manager_name", manager.getName());
            parameters.put("stock_keeper_name", keeperName);
            parameters.put("categories", categoriesParameter);

            emailTemplateService.sendExpenseRequestTodoEmail(manager.getEmail(), "Purchase Order Reminder", "PURCHASING_STOCK_KEEPER_MANAGER_REMINDER", parameters);

            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    private String getKeeperNameVariable() {
        Position keeperPosition = Setup.getRepository(PositionRepository.class).findByCode(AccountingModule.STOCK_KEEPER);
        List<User> stockKeepers = userRepository.findByPositionOrderByFullNameAsc(keeperPosition);
        Optional<User> keeper = stockKeepers.stream().findFirst();
        String keeperName = "";
        if (keeper.isPresent())
            keeperName = keeper.get().getName();

        logger.info("KeeperNameVariable " + keeperName);
        return keeperName;
    }

}
