package com.magnamedia.repository;

import com.magnamedia.core.repository.workflow.WorkflowRepository;
import com.magnamedia.entity.PurchasingToDo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <PERSON> (Jan 31, 2021)
 */
@Repository
public interface PurchasingToDoRepository extends WorkflowRepository<PurchasingToDo> {
    List<PurchasingToDo> findByCompletedFalseAndStoppedFalseAndTaskNameIgnoreCaseIn(List<String> taskNames);
}
