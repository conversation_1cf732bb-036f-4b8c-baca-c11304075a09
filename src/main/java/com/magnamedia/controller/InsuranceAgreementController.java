package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.entity.InsuranceAgreement;
import com.magnamedia.entity.projection.InsuranceNoteCSV;
import com.magnamedia.extra.InsuranceNote;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.repository.InsuranceAgreementRepository;
import org.joda.time.DateTime;
import org.joda.time.Interval;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.stream.Collectors;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Sep 20, 2018
 */
@RequestMapping("/insuranceagreement")
@RestController
public class InsuranceAgreementController extends BaseRepositoryController<InsuranceAgreement> {

    @Autowired
    InsuranceAgreementRepository insuranceAgreementRepository;

    @Autowired
    PicklistItemRepository picklistItemRepository;

    @Override
    public BaseRepository<InsuranceAgreement> getRepository() {
        return insuranceAgreementRepository;
    }

    private Boolean overlapedAagreementDuration(InsuranceAgreement entity) {
        List<InsuranceAgreement> agreements =
                insuranceAgreementRepository.findAll();

        Interval entityInterval =
                new Interval(entity.getStartDate().getTime(),
                        entity.getEndDate().getTime());

        for (InsuranceAgreement agreement : agreements) {
            if (entity.getId() != null && entity.getId() == agreement.getId())
                continue;

            Interval agreementInterval =
                    new Interval(agreement.getStartDate().getTime(),
                            agreement.getEndDate().getTime());

            if (agreementInterval.overlaps(entityInterval))
                return true;
        }

        return false;
    }

    @Override
    protected ResponseEntity<?> createEntity(InsuranceAgreement entity) {

        if (overlapedAagreementDuration(entity)) {
            return new ResponseEntity<>("the dates you entered intersect with prevoius entries.", HttpStatus.BAD_REQUEST);
        } else
            return super.createEntity(entity);
    }

    @Override
    protected ResponseEntity<?> updateEntity(InsuranceAgreement entity) {

        if (overlapedAagreementDuration(entity)) {
            return new ResponseEntity<>("the dates you entered intersect with prevoius entries.", HttpStatus.BAD_REQUEST);
        } else
            return super.updateEntity(entity);
    }

    @PreAuthorize("hasPermission('insuranceagreement','debitnotelist')")
    @RequestMapping(value = "/debitnotelist", method = RequestMethod.GET)
    public ResponseEntity<?> debitNoteList(
            @RequestParam(value = "from", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") java.util.Date from,
            @RequestParam(value = "to", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") java.util.Date to,
            @RequestParam(value = "maidName", required = false) String maidName) {


        Map<String, Object> map = fetchInsuranceAgreementsByFilterDates(from, to);


        if (map.get("insuranceAgreement") == null) return ResponseEntity.ok(new ArrayList<>());

        Timestamp fromTimestamp = (Timestamp) map.get("fromTimestamp");
        Timestamp toTimestamp = (Timestamp) map.get("toTimestamp");

        // get maid list
        List<Object[]> objectList = insuranceAgreementRepository
                .findMaidDebitNoteList(fromTimestamp, toTimestamp,
                   maidName == null ? null : "%" + maidName + "%");

        // Jirra ACC-1208
        // get office staff list
        objectList.addAll(insuranceAgreementRepository
                .findOfficeStaffDebitNoteList(fromTimestamp, toTimestamp, maidName == null ? null : "%" + maidName + "%"));

        List<InsuranceNote> debitNoteList = objectList.stream().map(x -> InsuranceNote.getDebitNote(
                        (InsuranceAgreement) map.get("insuranceAgreement"),
                        x[0] != null ? ((BigInteger) x[0]).longValue() : 0L,
                        x[1] != null ? x[1].toString() : "",
                        x[2] != null ? new Date(((java.util.Date) x[2]).getTime()) : null,
                        (x[3] != null && (x[3].toString().equalsIgnoreCase("Office Staff"))) ? "Office Staff" : "Housemaid",
                        x[4] != null ? x[4].toString() : "",
                        x[5] != null ? x[5].toString() : "",
                        // ACC-4655
                        (x[6] != null) ? new Date(((java.util.Date) x[6]).getTime()): null
                ))
                .collect(Collectors.toList());

        return new ResponseEntity<>(debitNoteList, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('insuranceagreement','debitnotelist')")
    @RequestMapping(value = "/creditnotelist", method = RequestMethod.GET)
    public ResponseEntity<?> creditNoteList(
            @RequestParam(value = "from",required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") java.util.Date from,
            @RequestParam(value = "to", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") java.util.Date to,
            @RequestParam(value = "maidName", required = false) String maidName) {

        Map<String, Object> map = fetchInsuranceAgreementsByFilterDates(from, to);


        if (map.get("insuranceAgreement") == null) return ResponseEntity.ok(new ArrayList<>());

        Timestamp fromTimestamp = (Timestamp) map.get("fromTimestamp");
        Timestamp toTimestamp = (Timestamp) map.get("toTimestamp");
            // get maid list
        List<Object[]> objectList = insuranceAgreementRepository
                .findMaidCreditNoteList(fromTimestamp, toTimestamp,
                        maidName == null ? null : "%" + maidName + "%");

        // Jirra ACC-1208
        // get office staff list
        objectList.addAll(insuranceAgreementRepository
                .findOfficeStaffCreditNoteList(fromTimestamp, toTimestamp, maidName == null ? null : "%" + maidName + "%"));

        List<InsuranceNote> creditNoteList = objectList.stream().map(x -> InsuranceNote.getCreditNote(
                            (InsuranceAgreement) map.get("insuranceAgreement"),
                            x[0] != null ? ((BigInteger) x[0]).longValue() : 0L,
                            x[1] != null ? x[1].toString() : "",
                            x[2] != null ? new Date(((java.util.Date) x[2]).getTime()) : null,
                            (x[3] != null && (x[3].toString().equalsIgnoreCase("Office Staff"))) ? "Office Staff" : "Housemaid",
                            x[4] != null ? x[4].toString() : "",
                            x[5] != null ? x[5].toString() : "",
                            // ACC-4655
                            (x[6] != null) ? new Date(((java.util.Date) x[6]).getTime()): null
                    ))
                    .collect(Collectors.toList());

        return new ResponseEntity<>(creditNoteList, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('insuranceagreement','debitnotelist')")
    @RequestMapping(value = "/debitnotelist/csv", method = RequestMethod.GET)
    public void debitNoteListCSV(
            @RequestParam(value = "from", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") java.util.Date from,
            @RequestParam(value = "to", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") java.util.Date to,
            @RequestParam(value = "maidName", required = false) String maidName,
            HttpServletResponse response) {

        Map<String, Object> map = fetchInsuranceAgreementsByFilterDates(from, to);


        if (map.get("insuranceAgreement") == null) return;

        Timestamp fromTimestamp = (Timestamp) map.get("fromTimestamp");
        Timestamp toTimestamp = (Timestamp) map.get("toTimestamp");
        // get maid list
        List<Object[]> objectList = insuranceAgreementRepository
                .findMaidDebitNoteList(fromTimestamp, toTimestamp,
                        maidName == null ? null : "%" + maidName + "%");

        // Jirra ACC-1208
        // get office staff list
        objectList.addAll(insuranceAgreementRepository
                .findOfficeStaffDebitNoteList(fromTimestamp, toTimestamp, maidName == null ? null : "%" + maidName + "%"));

        List<InsuranceNote> debitNoteList = objectList.stream().map(x -> InsuranceNote.getDebitNote(
                        (InsuranceAgreement) map.get("insuranceAgreement"),
                        x[0] != null ? ((BigInteger) x[0]).longValue() : 0L,
                        x[1] != null ? x[1].toString() : "",
                        x[2] != null ? new Date(((java.util.Date) x[2]).getTime()) : null,
                        (x[3] != null && (x[3].toString().equalsIgnoreCase("Office Staff"))) ? "Office Staff" : "Housemaid",
                        x[4] != null ? x[4].toString() : "",
                        x[5] != null ? x[5].toString() : "",
                        // ACC-4655
                        (x[6] != null) ? new Date(((java.util.Date) x[6]).getTime()): null
                ))
                .collect(Collectors.toList());


        InputStream is = null;
        try {
            String[] namesOrdered = {"maidName", "joiningDate", "insuranceStartDate", "numberOfDays", "workerType", "emiratesId", "passportNumber", "basmaCharge", "premium", "totalAmount"};
            is = generateCsv(debitNoteList, InsuranceNoteCSV.class, namesOrdered);
            createDownloadResponse(response,
                    "debitnotelist.csv",
                    is);
        } catch (IOException ex) {
            logger.log(Level.SEVERE,
                    ex.getMessage(),
                    ex);
        } finally {
            StreamsUtil.closeStream(is);
        }
    }

    @PreAuthorize("hasPermission('insuranceagreement','debitnotelist')")
    @RequestMapping(value = "/creditnotelist/csv", method = RequestMethod.GET)
    public void creditNoteListCSV(
            @RequestParam(value = "from", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") java.util.Date from,
            @RequestParam(value = "to", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") java.util.Date to,
            @RequestParam(value = "maidName", required = false) String maidName,
            HttpServletResponse response) {


        Map<String, Object> map = fetchInsuranceAgreementsByFilterDates(from, to);


        if (map.get("insuranceAgreement") == null) return;

        Timestamp fromTimestamp = (Timestamp) map.get("fromTimestamp");
        Timestamp toTimestamp = (Timestamp) map.get("toTimestamp");
        // get maid list
        List<Object[]> objectList = insuranceAgreementRepository
                .findMaidCreditNoteList(fromTimestamp, toTimestamp,
                        maidName == null ? null : "%" + maidName + "%");

        // Jirra ACC-1208
        // get office staff list
        objectList.addAll(insuranceAgreementRepository
                .findOfficeStaffCreditNoteList(fromTimestamp, toTimestamp, maidName == null ? null : "%" + maidName + "%"));

        List<InsuranceNote> creditNoteList = objectList.stream().map(x -> InsuranceNote.getCreditNote(
                        (InsuranceAgreement) map.get("insuranceAgreement"),
                        x[0] != null ? ((BigInteger) x[0]).longValue() : 0L,
                        x[1] != null ? x[1].toString() : "",
                        x[2] != null ? new Date(((java.util.Date) x[2]).getTime()) : null,
                        (x[3] != null && (x[3].toString().equalsIgnoreCase("Office Staff"))) ? "Office Staff" : "Housemaid",
                        x[4] != null ? x[4].toString() : "",
                        x[5] != null ? x[5].toString() : "",
                        // ACC-4655
                        (x[6] != null) ? new Date(((java.util.Date) x[6]).getTime()): null
                ))
                .collect(Collectors.toList());


        InputStream is = null;
        try {
            String[] namesOrdered = {"maidName", "terminationDate", "insuranceEndDate", "numberOfDays", "workerType", "emiratesId", "passportNumber", "premium"};
            is = generateCsv(creditNoteList, InsuranceNoteCSV.class, namesOrdered);
            createDownloadResponse(response,
                    "creditnotelist.csv",
                    is);
        } catch (IOException ex) {
            logger.log(Level.SEVERE,
                    ex.getMessage(),
                    ex);
        } finally {
            StreamsUtil.closeStream(is);
        }
    }

    private Map<String, Object> fetchInsuranceAgreementsByFilterDates(java.util.Date from, java.util.Date to) {
        Map<String, Object> map = new HashMap<>();
        if (from == null || to == null) {
            InsuranceAgreement a = insuranceAgreementRepository.findTopByOrderByEndDateDesc();
            map.put("insuranceAgreement", a);
            if (a != null) {
                from = a.getStartDate();
                to = a.getEndDate();
            }
        } else {
            List<InsuranceAgreement> l = insuranceAgreementRepository.findTopByStartDateLessThanEqualAndEndDateGreaterThanEqual(
                    new Date(from.getTime()),
                    new Date(to.getTime()),
                    PageRequest.of(0, 1));
            map.put("insuranceAgreement", l.isEmpty() ? null : l.get(0));
        }

        Timestamp fromTimestamp = from == null ? null :
                new Timestamp(new DateTime(from).withTimeAtStartOfDay().toDate().getTime());
        Timestamp toTimestamp = to == null ? null :
                new Timestamp(new DateTime(to).plusDays(1).withTimeAtStartOfDay().toDate().getTime());

        map.put("fromTimestamp", fromTimestamp);
        map.put("toTimestamp", toTimestamp);
        return map;
    }
}