package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;

import javax.persistence.*;


@Entity
public class DirectDebitSignatureApprovalLink extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Contract contract;

    @Column
    private String link;

    @Column
    private boolean expired;

    @Column
    private boolean clientProvidedSignatures;

    public Contract getContract() {
        return contract;
    }

    public void setContract(Contract contract) {
        this.contract = contract;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public boolean isExpired() {
        return expired;
    }

    public void setExpired(boolean expired) {
        this.expired = expired;
    }

    public boolean isClientProvidedSignatures() {
        return clientProvidedSignatures;
    }

    public void setClientProvidedSignatures(boolean clientProvidedSignatures) {
        this.clientProvidedSignatures = clientProvidedSignatures;
    }
}