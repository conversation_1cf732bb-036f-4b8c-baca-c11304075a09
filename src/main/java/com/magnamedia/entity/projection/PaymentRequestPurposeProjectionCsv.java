package com.magnamedia.entity.projection;

import com.magnamedia.module.type.LoanType;
import org.springframework.beans.factory.annotation.Value;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Sep 2, 2019
 * ACC-837
 */
public interface PaymentRequestPurposeProjectionCsv {
    
    String getName();
    Integer getThreshold();
    @Value("#{(target.getUser()!=null)?(target.getUser().getLabel()):null}")
    String getUser();
    String getCategory();
    Boolean getActive();
    LoanType getLoanType();
}
