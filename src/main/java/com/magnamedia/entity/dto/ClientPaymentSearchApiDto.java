package com.magnamedia.entity.dto;

import com.magnamedia.core.Setup;
import com.magnamedia.entity.ContractPaymentConfirmationToDo;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.service.AttachmentService;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class ClientPaymentSearchApiDto {
    private Long id;
    private String typeOfPaymentLabel;
    private Boolean isReplacement;
    private Long contractId;
    private Long clientId;
    private Long replacementForId;
    private PaymentMethod methodOfPayment;
    private Date dateOfPayment;
    private Boolean replaced;
    private PaymentStatus status;
    private String reasonOfBouncingChequeName;
    private Date dateOfBouncing;
    private Double amountOfPayment;
    private Double discount;
    private Boolean isInitial;
    private String oldApplicationId;
    private String directDebitFileApplicationId;
    private String chequeNumber;
    private Date creationDate;
    private Date dateChangedToPDP;
    private Date dateChangedToReceived;
    private Double vat;
    private Long bouncingTrials;
    private Date lastLogDate;
    private String chequeName;
    private Boolean prepareToRefund;
    private String bankNameLabel;
    private String note;
    private Boolean vatPaidByClient;
    private List<Map> attachments;
    private String creatorFullName;
    private String lastModifierFullName;
    private Date statusLastModificationDate;
    private Long todoId;
    private ContractPaymentConfirmationToDo todo;

    public ClientPaymentSearchApiDto(
            Long id, String typeOfPaymentLabel, Boolean isReplacement, Long contractId, Long clientId, Long replacementForId,
            PaymentMethod methodOfPayment, Date dateOfPayment, Boolean replaced, PaymentStatus status, String reasonOfBouncingChequeName,
            Date dateOfBouncing, Double amountOfPayment, Double discount, Boolean isInitial, String oldApplicationId,
            String directDebitFileApplicationId, String chequeNumber, Date creationDate, Date dateChangedToPDP, Date dateChangedToReceived,
            Double vat, Long bouncingTrials, Date lastLogDate, String chequeName, Boolean prepareToRefund, String bankNameLabel,
            String note, Boolean vatPaidByClient, String creatorFullName, String lastModifierFullName, Date statusLastModificationDate ,Long todoId) {

        this.id = id;
        this.typeOfPaymentLabel = typeOfPaymentLabel;
        this.isReplacement = isReplacement;
        this.contractId = contractId;
        this.clientId = clientId;
        this.replacementForId = replacementForId;
        this.methodOfPayment = methodOfPayment;
        this.dateOfPayment = dateOfPayment;
        this.replaced = replaced;
        this.status = status;
        this.reasonOfBouncingChequeName = reasonOfBouncingChequeName;
        this.dateOfBouncing = dateOfBouncing;
        this.amountOfPayment = amountOfPayment;
        this.discount = discount;
        this.isInitial = isInitial;
        this.oldApplicationId = oldApplicationId;
        this.directDebitFileApplicationId = directDebitFileApplicationId;
        this.chequeNumber = chequeNumber;
        this.creationDate = creationDate;
        this.dateChangedToPDP = dateChangedToPDP;
        this.dateChangedToReceived = dateChangedToReceived;
        this.vat = vat;
        this.bouncingTrials = bouncingTrials;
        this.lastLogDate = lastLogDate;
        this.chequeName = chequeName;
        this.prepareToRefund = prepareToRefund;
        this.bankNameLabel = bankNameLabel;
        this.note = note;
        this.vatPaidByClient = vatPaidByClient;
        this.creatorFullName = creatorFullName;
        this.lastModifierFullName = lastModifierFullName;
        this.statusLastModificationDate = statusLastModificationDate;
        this.todoId = todoId;
    }

    // used in ClientPaymentsSearchProjection
    public List<Map> getFilteredAttachments() {
        return Setup.getApplicationContext()
                .getBean(AttachmentService.class)
                .getFilteredAttachments(getAttachments());
    }
}