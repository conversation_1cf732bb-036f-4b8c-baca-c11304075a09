package com.magnamedia.extra.ExpenseFlow;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;

import java.util.Date;

public class TaxiOrderDto {
    private String bookingId;
    private String passenger;
    private String purpose;
    private Date date;
    private Double erpAmount;
    private Attachment attachment;
    private String pickupLocation;
    private String dropOffLocation;

    public TaxiOrderDto(String bookingId, String passenger, String purpose, Date date, Double erpAmount, Attachment attachment, String pickupLocation, String dropOffLocation) {
        this.bookingId = bookingId;
        this.passenger = passenger;
        this.purpose = purpose;
        this.date = date;
        this.erpAmount = erpAmount;
        this.attachment = attachment;
        this.pickupLocation = pickupLocation;
        this.dropOffLocation = dropOffLocation;

    }

    public Double getErpAmount() { return erpAmount; }

    public void setErpAmount(Double erpAmount) {
        this.erpAmount = erpAmount;
    }

    public Attachment getAttachment() { return attachment; }

    public void setAttachment(Attachment attachment) {
        this.attachment = attachment;
    }

    public String getPickupLocation() { return pickupLocation; }

    public void setPickupLocation(String pickupLocation) {
        this.pickupLocation = pickupLocation;
    }

    public String getDropOffLocation() { return dropOffLocation; }

    public void setDropOffLocation(String dropOffLocation) {
        this.dropOffLocation = dropOffLocation;
    }
    public String getBookingId() {
        return bookingId;
    }

    public void setBookingId(String bookingId) {
        this.bookingId = bookingId;
    }

    public String getPassenger() {
        return passenger;
    }

    public void setPassenger(String passenger) {
        this.passenger = passenger;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getAttachmentLink() {
        if (attachment != null) {
            return Setup.getParameter(Setup.getModule("accounting"), "backend_base_url") +
                    "/public/download/" + attachment.getUuid();
        }
        return "No Attachments";
    }
}
