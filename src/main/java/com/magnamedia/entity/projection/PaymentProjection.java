package com.magnamedia.entity.projection;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.PaymentStatus;
import org.springframework.beans.factory.annotation.Value;

import java.sql.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Dec 27, 2017
 */
public interface PaymentProjection {
    
    Long getId();
    @Value("#{(target.getContract() != null ? "
                    + "{id: target.getContract().getId(), "
                    + "contractType: target.getContract().getContractType(), "
                    + "contractProspectType: target.getContract().getContractProspectType(), "
                    + "status: target.getContract().getStatus(), "
                    //Jirra ACC-1305
                    + "startOfContract: target.getContract().getStartOfContractCSV(), "
                    + "client: (target.getContract().getClient() != null ? "
                        + " {id: target.getContract().getClient().getId(), "
                        + "name: target.getContract().getClient().getName()}"
                    + ": null), "
                    + "housemaid: (target.getContract().getHousemaid() != null ? "
                        + " {id: target.getContract().getHousemaid().getId(), "
                            + "name: target.getContract().getHousemaid().getName(), "
                            + "nationality: (target.getContract().getHousemaid().getNationality() != null ? "
                            + "target.getContract().getHousemaid().getNationality().getName() "
                                + ": null)}"
                    + ": null)}"
                + ": null)}")
    Map<?, ?> getContract();
    PicklistItem getTypeOfPayment();
    PaymentMethod getMethodOfPayment();
    Double getAmountOfPayment();
    Date getDateOfPayment();
    String getChequeName();
    String getChequeNumber();
    PaymentStatus getStatus();
    PicklistItem getBankName();
    java.util.Date getCreationDate();
    String getNote();
    Boolean getChequeWithTheBank();
    Boolean getReplaced();
    //Jirra ACC-797
    Boolean getVatPaidByClient();
    //Jirra ACC-594
    @Value("#{target.getDirectDebit() != null ?"
                + "{id:target.getDirectDebit().getId()}"
            + ": null}")
    Map<?, ?> getDirectDebit();

    //Jirra ACC-1588
    @Value("#{target.getDirectDebitFile() != null ?"
            + "{id:target.getDirectDebitFile().getId(),"
            + "applicationId:target.getDirectDebitFile().getApplicationId()}"
            + ": null}")
    Map<?, ?> getDirectDebitFile();
    //ACC-1036
    Boolean getIsInitial();
    //ACC-1056
    Double getVat();

    java.util.Date getDateChangedToPDP();

    //ACC-1544
    java.util.Date getDateChangedToReceived();

}
