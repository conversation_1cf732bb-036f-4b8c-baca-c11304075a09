package com.magnamedia.repository.OnlineCardStatement;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.OnlineCardStatement.OnlineCardStatementTransaction;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface OnlineCardStatementTransactionRepository extends BaseRepository<OnlineCardStatementTransaction> {

    @Query("select new map(" +
                "r.id as rId, p.id as paymentId, p.typeOfPayment.name as paymentType, " +
                    "ot.transaction.id as transactionId) " +
            "from OnlineCardStatementTransaction ot " +
            "join ot.onlineCardStatementRecord as r " +
            "left join ot.payment as p " +
            "where r.id in ?1")
    List<Map> findGridInfoByRecordsIds(
            List<Long> ids);
}
