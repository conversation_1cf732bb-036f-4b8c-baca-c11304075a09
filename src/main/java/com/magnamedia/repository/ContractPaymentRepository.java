package com.magnamedia.repository;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.*;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.module.type.PaymentMethod;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Dec 30, 2018
 */
public interface ContractPaymentRepository extends BaseRepository<ContractPayment> {

    // ACC-391 ACC-430
    @Query("SELECT MIN(date) FROM ContractPayment cp "
            + "JOIN cp.directDebit dd "
            + "LEFT JOIN cp.contractPaymentTerm cpt "
            + "LEFT JOIN cp.contractPaymentTerm.contract c "
            + "LEFT JOIN cp.contractPaymentTerm.contract.client cl "
            + "LEFT JOIN cp.contractPaymentTerm.contract.housemaid h "
            + "LEFT JOIN cp.contractPaymentTerm.contract.housemaid.nationality n "
            + "LEFT JOIN cp.paymentType pt "
            + "WHERE cl = ?1 AND c = ?2 AND n = ?3 AND cp.paymentType = ?4 AND dd = ?5")
    Date getFirstDate(Client client, Contract contract, PicklistItem nationality,
            PicklistItem paymentType, DirectDebit directDebit);

    // ACC-825
    List<ContractPayment> findByDirectDebit(DirectDebit directDebit);

    // MC-28
    ContractPayment findFirstByDirectDebitAndPaymentType_Code(DirectDebit directDebit, String code);

    // ACC-1092
    @Query("Select CP from ContractPayment CP " +
            "where CP.contractPaymentTerm.isActive = true and CP.contractPaymentTerm.contract = ?1 and " +
                "CP.paymentMethod = ?2 and CP.paymentType = ?3 and " +
                "CP.date >= ?4 and CP.date <= ?5 and CP.amount = ?6")
    List<ContractPayment> findMatchedContractPayment(
            Contract contract, PaymentMethod method, PicklistItem type,
            Date fromDate, Date toDate, Double amount);

    @Query("Select c from ContractPayment c " +
            "join c.contractPaymentTerm cpt " +
            "where cpt.contract = ?1 and c.directDebit.id = ?2 and " +
                "c.paymentMethod = ?3 and c.paymentType = ?4 and " +
                "c.date >= ?5 and c.date <= ?6 and c.amount = ?7 " +
            "order by cpt.isActive desc")
    List<ContractPayment> findMatchedContractPaymentViaDd(
            Contract contract, Long ddId, PaymentMethod method, PicklistItem type,
            Date fromDate, Date toDate, Double amount);

    @Query("select c " +
            "from Payment p " +
            "inner join ContractPaymentWrapper cpw on cpw.generatedPaymentId = p.id " +
            "join cpw.contractPayment c " +
            "where p.id = ?1 and cpw.contractPaymentConfirmationToDo.source <> 'CLIENT_REFUND'")
    List<ContractPayment> findMatchedContractPaymentViaWrapper(Long paymentId);


    // ACC-1435 from here
    List<ContractPayment> findByContractPaymentTerm(ContractPaymentTerm contractPaymentTerm);

    List<ContractPayment> findByContractPaymentTerm_Contract(Contract contract);

    @Query("Select CP from ContractPayment CP " +
            "where CP.contractPaymentTerm = :term and " +
                "(:paymentType is null or CP.paymentType = :paymentType) " +
                "and CP.paymentMethod <> 'DIRECT_DEBIT' ")
    List<ContractPayment> findCashByContractPaymentTermAndPaymentType(
            @Param("term") ContractPaymentTerm cpt, @Param("paymentType") PicklistItem type);

    @Query("Select cp from ContractPayment cp " +
            "join cp.directDebit d  " +
            "where d.contractPaymentTerm = ?1 " +
            "and d.status not in ?2 and d.MStatus not in ?2 ")
    List<ContractPayment> findByContractPaymentTermAndAndDirectDebitStatusNotIn(
            ContractPaymentTerm contractPaymentTerm, List<DirectDebitStatus> notAllowedStatus);

    Boolean existsByContractPaymentTerm(ContractPaymentTerm contractPaymentTerm);

    @Query("Select CP from ContractPayment CP left join CP.directDebit DD " +
            "where CP.contractPaymentTerm.isActive = true and CP.contractPaymentTerm.contract.id = ?1 and " +
                "CP.paymentMethod = ?2 and CP.paymentType.id = ?3 and " +
                "CP.date >= ?4 and CP.date <= ?5 and CP.amount = ?6 and CP.confirmed = 0 and " +
                "(DD is null or (DD.status <> 'CANCELED' and DD.status <> 'REJECTED'))")
    List<ContractPayment> findMatchedContractPayment(
            long contractId, PaymentMethod method, long typeId,
            Date fromDate, Date toDate, Double amount);

    @Query("Select CP from ContractPayment CP " +
            "where CP.contractPaymentTerm.isActive = true and CP.contractPaymentTerm.contract.id = ?1 and " +
                "CP.paymentMethod = ?2 and CP.paymentType.id = ?3 and " +
                "CP.date >= ?4 and CP.date <= ?5 and CP.amount = ?6")
    List<ContractPayment> findAllMatchedContractPayment(
            long contractId, PaymentMethod method, long typeId,
            Date fromDate, Date toDate, Double amount);

    @Override
    default void delete(ContractPayment contractPayment) {
        if (contractPayment != null && !contractPayment.getIsDeleted()) {
            contractPayment.setIsDeleted(true);
            save(contractPayment);
        }
    }

    @Query("select count(cp)>0 from ContractPayment cp where cp.replaceOf = :payment and (cp.directDebit is null or cp.directDebit.status not in :statuses)")
    boolean existsOldReplaceOfPayment(@Param("payment") Payment payment, @Param("statuses") List<DirectDebitStatus> statuses);

    @Query("SELECT cp FROM ContractPayment cp " +
            "WHERE cp.contractPaymentTerm.contract = ?1 AND cp.contractPaymentTerm.isActive = 1 AND " +
                "cp.paymentType.code = ?2 AND cp.date >= ?3 and cp.date <= ?4 " +
            "ORDER BY cp.id DESC")
    List<ContractPayment> findMatchedContractPayment(
            Contract contract, String type,
            Date fromDate, Date toDate);

    @Query("SELECT cp FROM ContractPayment cp " +
            "WHERE cp.contractPaymentTerm.contract = :ct AND " +
                "(:t is null or cp.paymentType = :t) AND cp.paymentMethod <> 'DIRECT_DEBIT' AND " +
                "(cp.online = 0 OR cp.paid = 1) " +
            "ORDER BY cp.date DESC")
    List<ContractPayment> findNonDDContractPayment(
            @Param("ct") Contract contract, @Param("t") PicklistItem type);

    @Query("SELECT count(cp.id) > 0 FROM ContractPayment cp " +
            "WHERE cp.contractPaymentTerm.contract = :ct AND " +
            "(:c is null or cp.paymentType.code = :c) AND cp.paymentMethod <> 'DIRECT_DEBIT' ")
    boolean existsNonDDContractPayment(
            @Param("ct") Contract contract, @Param("c") String code);

    @Query("SELECT new map(n.housemaid.nationality.name as newMaid, o.housemaid.nationality.name as oldMaid) " +
            "FROM ContractPaymentTerm n JOIN ContractPaymentTerm o " +
                "ON o.id = (SELECT MAX(m.id) FROM ContractPaymentTerm m WHERE m.contract.id = n.contract.id AND m.id < ?1) " +
            "WHERE n.id = ?1")
    List<?> getSwitchingOldNewMaids(Long switchingCptId);

    @Query("select cp.date from DirectDebit dd " +
            "join dd.contractPayments cp " +
            "join dd.contractPaymentTerm cpt " +
            "where cpt.contract = ?1 and dd.id <> ?2 and cp.paymentType.code = 'monthly_payment' and " +
                "((dd.category = 'A' and dd.MStatus not in ('CANCELED', 'EXPIRED', 'PENDING_FOR_CANCELLATION')) or " +
                "(dd.category = 'B' and dd.status not in ('CANCELED', 'EXPIRED', 'PENDING_FOR_CANCELLATION'))) and " +
                "cp.date between ?3 and ?4")
    List<Date> getAllContractPaymentDateBetweenTwoDate(Contract c, Long ddbId, Date d, Date ddbStartDate);

    @Query("SELECT distinct(cp.directDebit) FROM ContractPayment cp WHERE cp.id in ?1")
    List<DirectDebit> getDirectDebitsOfPayments(List<Long> ids);

    @Query("select count(cp.id) > 0 " +
            "from ContractPayment cp " +
            "join cp.directDebit d " +
            "where cp.replaceOf = ?1 and d.MStatus = ?2 and d.addedManuallyFromClientProfile = true")
    boolean existsConfirmedDdaCoveredPaymentAddFromErp(Payment payment, DirectDebitStatus status);

    @Query("select count(distinct(subStr(cp.date, 1, 7))) from ContractPayment cp " +
            "join cp.contractPaymentTerm.contract c " +
            "inner join Payment p on p.contract.id = c.id and p.dateOfPayment = cp.date and p.typeOfPayment.id = cp.paymentType.id and p.status = 'RECEIVED' " +
            "where c = ?1 and cp.moreAdditionalDiscount is not null and cp.moreAdditionalDiscount > 0  and cp.paymentType = ?2")
    int countContractPaymentTermDetailsDiscountedPayment(Contract c, PicklistItem type);

    @Query("select count(distinct(subStr(date, 1, 7))) from ContractPayment cp " +
            "join cp.contractPaymentTerm.contract c " +
            "inner join Payment p on p.contract.id = c.id and p.dateOfPayment = cp.date and p.typeOfPayment.id = cp.paymentType.id and p.status = 'RECEIVED' " +
            "where c = ?1 and cp.paymentType = ?2 and (cp.isProRated = false or cp.isProRatedPlusMonth = true)")
    int countReceivedPaymentWithoutProrated(Contract c, PicklistItem type);

    @Query("select count(cp.id) > 0 from ContractPayment cp " +
            "left join DirectDebit dd on dd = cp.directDebit "+
            "where cp.contractPaymentTerm.contract.id = ?1 and " +
                "cp.paymentType.code = ?2 and cp.date >= ?3 and cp.date <= ?4 and " +
                "((cp.paymentMethod <> 'DIRECT_DEBIT' and (cp.online = false or cp.paid = true)) or " +
                "(cp.paymentMethod = 'DIRECT_DEBIT' and dd.MStatus not in ?5))")
    boolean existsContractPaymentGeneratedByTypeAndDateAndStatus(Long contractId, String type, Date startDate, Date endDate, List<DirectDebitStatus> notAllowedStatuses);

    @Query("select count(cp.id) > 0 " +
            "from ContractPayment cp " +
            "where cp.replaceOf = ?1 and (cp.directDebit is null or cp.directDebit.status not in ?2)")
    boolean existsByReplaceOfAndDirectDebitStatusNotIn(Payment payment, List<DirectDebitStatus> statuses);

    @Query("select distinct cp from DirectDebitFile ddf " +
            "join ddf.directDebit dd " +
            "inner join ContractPayment cp on cp.directDebit = dd " +
            "join dd.contractPaymentTerm cpt " +
            "where cpt.contract.id = ?1 and cpt.isActive = true and cp.paymentType.code = 'monthly_payment' and " +
            "((dd.category = 'A' and dd.MStatus not in ?2) or (dd.category = 'B' and dd.status not in ?2 and ddf.ddMethod = 'AUTOMATIC')) and " +
            "(ddf.ddStatus not in ('PENDING', 'CONFIRMED') or (ddf.status = 'APPROVED' and " +
                "not exists (select 1 from DirectDebitCancelationToDo todo " +
                    "where todo.directDebitFile.id = ddf.id and todo.stopped = false and todo.completed = false)))")
    List<ContractPayment> findAllContractPaymentOfActiveMonthlyDdByContract(Long contractId, List<DirectDebitStatus> statuses);

    @Query("select cp from ContractPayment cp  " +
            "inner join cp.directDebit dd " +
            "left join DirectDebitFile ddf on ddf.directDebit = dd " +
            "where dd.contractPaymentTerm.contract = ?1 and dd.contractPaymentTerm.isActive = true and " +
                "cp.paymentType.code = ?2 and " +
                    "((dd.status not in ?5 AND dd.category = 'B') or (dd.MStatus not in ?5 AND dd.category = 'A')) and " +
                    "(ddf is null or (ddf.ddMethod = 'AUTOMATIC' AND dd.category = 'B') or " +
                        "(ddf.ddMethod = 'MANUAL' AND dd.category = 'A')) and " +
                "(ddf is null or not exists(select 1 from DirectDebitCancelationToDo toDo " +
                            "where toDo.directDebitFile = ddf and toDo.stopped = false and toDo.completed = false)) and " +
                "(?3 is null or cp.date >= ?3) and (?4 is null or cp.date <= ?4) and " +
                "cp.date = (select min(cp2.date) from ContractPayment cp2 " +
                                "where cp2.directDebit = cp.directDebit and cp2.paymentType.code = cp.paymentType.code " +
                                    "and (?3 is null or cp2.date >= ?3) and (?4 is null or cp2.date <= ?4)) " +
            "order by dd.expiryDate desc, dd.creationDate desc")
    List<ContractPayment> findDDContractPaymentByContractAndTypeAndDate(
            Contract contract, String typeOfPayment, Date startDate, Date endDate, List<DirectDebitStatus> s);

    @Query("select cp from ContractPayment cp  " +
            "inner join cp.directDebit dd " +
            "left join DirectDebitFile ddf on ddf.directDebit = dd " +
            "where dd.contractPaymentTerm.contract = ?1 and dd.contractPaymentTerm.isActive = true and " +
                "dd.MStatus not in ?2 and cp.paymentType.code NOT IN ('monthly_payment', 'insurance', 'same_day_recruitment_fee') and " +
                "(ddf is null or not exists(select 1 from DirectDebitCancelationToDo toDo " +
                            "where toDo.directDebitFile = ddf  and toDo.stopped = false and toDo.completed = false))")
    List<ContractPayment> findDDNonMonthlyContractPayments(Contract contract, List<DirectDebitStatus> s);

    @Query("SELECT COUNT(cp) > 0 FROM ContractPayment cp " +
            "INNER JOIN cp.directDebit dd " +
            "left join DirectDebitFile ddf on ddf.directDebit = dd " +
            "WHERE dd.contractPaymentTerm.contract = ?1 AND cp.paymentType.code = 'monthly_payment' AND " +
                "((dd.category = 'B' AND dd.status NOT IN ?2) OR (dd.category = 'A' AND dd.MStatus NOT IN ?2)) AND " +
                "(ddf is null or (ddf.ddMethod = 'AUTOMATIC' AND dd.category = 'B') or " +
                    "(ddf.ddMethod = 'MANUAL' AND dd.category = 'A')) and " +
                "(ddf is null or not exists (select 1 from DirectDebitCancelationToDo todo " +
                            "where todo.directDebitFile = ddf and todo.stopped = false and todo.completed = false))")
    boolean existsActiveMonthlyDd(Contract ct, List<DirectDebitStatus> l);

    @Query("select count(cp.id) > 0 from ContractPayment cp " +
            "where cp.contractPaymentTerm.contract.id = ?1 and cp.paymentType.code = ?2 and " +
            "cp.date >= ?3 and cp.date <= ?4 and cp.paymentMethod <> 'DIRECT_DEBIT' and (cp.online = false or cp.paid = true)")
    boolean existsCardContractPaymentPaidByTypeAndDateAndStatus(Long contractId, String type, Date startDate, Date endDate);

    @Query("select count(cp.id) > 0 from DirectDebit dd " +
            "inner join ContractPayment cp on cp.directDebit = dd " +
            "left join DirectDebitFile ddf on ddf.directDebit = dd " +
            "where cp.contractPaymentTerm.contract.id = ?1 and cp.paymentType.code = ?2 and cp.date >= ?3 and cp.date <= ?4 and " +
            "((dd.category = 'A' and dd.MStatus not in ?5) or (dd.category = 'B' and dd.status not in ?5 and ddf.ddMethod = 'AUTOMATIC')) and " +
            "(ddf.id is null or not exists (select 1 from DirectDebitCancelationToDo todo " +
            "where todo.directDebitFile.id = ddf.id and todo.stopped = false and todo.completed = false))")
    boolean existsActiveDdContractPaymentByTypeAndDateAndStatus(Long contractId, String type, Date startDate, Date endDate, List<DirectDebitStatus> notAllowedStatuses);
}