package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 *
 * <AUTHOR>
 */
public enum ExpenseApprovalMethod implements LabelValueEnum {
    AUTO_APPROVED("Auto Approved"),
    APPROVAL_REQUIRED("Approval Required"),
    APPROVAL_REQUIRED_ON_LIMIT("Approval Required On Limit");

    private final String label;

    ExpenseApprovalMethod(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
