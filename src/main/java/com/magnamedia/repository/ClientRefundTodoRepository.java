package com.magnamedia.repository;

import com.magnamedia.core.repository.workflow.WorkflowRepository;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.entity.workflow.PaymentRequestPurpose;
import com.magnamedia.workflow.type.ClientRefundStatus;
import com.magnamedia.workflow.type.ClientRefundTodoManagerAction;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 *         Created on Dec 06, 2020
 *         ACC-2847
 */

@Repository
public interface ClientRefundTodoRepository extends WorkflowRepository<ClientRefundToDo> {

    @Query("Select cr from ClientRefundToDo cr " +
           "where cr.client = ?1 and cr.contract = ?2 and cr.status <> 'STOPPED'")
    Page<ClientRefundToDo> findByClientAndContract(Client client, Contract contract, Pageable pageable);

    @Query("select new map(cr.amount as amount, cr.methodOfPayment as methodOfPayment, cr.status as status, " +
                "cr.statusChangeDate as date, cr.notes as notes) " +
            "from ClientRefundToDo cr " +
            "where cr.contract = ?1 and cr.status <> 'STOPPED' " +
            "order by cr.statusChangeDate")
    List<Map<String, Object>> findRefundInfoByContract(Contract contract);

    List<ClientRefundToDo> findByTaskNameAndCeoActionAndStatus(String taskName, ClientRefundTodoManagerAction action, ClientRefundStatus status);

    Page<ClientRefundToDo> findByTaskNameAndCeoActionAndStatus(String taskName, ClientRefundTodoManagerAction action, ClientRefundStatus status, Pageable pageable);

    List<ClientRefundToDo> findByParent(ClientRefundToDo parent);

    List<ClientRefundToDo> findByParentAndStatus(ClientRefundToDo parent, ClientRefundStatus status);

    @Query("select coalesce(sum(cr.amount), 0) from ClientRefundToDo cr where cr.contract = ?1 and cr.status = ?2")
    Double findPendingRefundAmountByContract(Contract contract, ClientRefundStatus status);

    @Query("select c from ClientRefundToDo c where c.taskName in :taskNames and c.status = :status " +
            "and c.stopped = false  and c.completed = false and c.numberOfMonthlyPayments is not null and c.parent is null ")
    Page<ClientRefundToDo> findPendingForCreationAccountantTodo
            (Pageable pageable, @Param("taskNames") List<String> taskNames,
             @Param("status") ClientRefundStatus status);

    @Query("select c from ClientRefundToDo c where c.status = ?1 and c.creationDate > ?2 and c.creationDate <= ?3 and c.completed = false and c.stopped = false and c.taskName is not null and c.taskName <> ''")
    List<ClientRefundToDo> findByStatusAndCreationDate(ClientRefundStatus status, Date from, Date to);

    List<ClientRefundToDo> findByCreationDateGreaterThanEqualAndManagerActionIsNotNull(Date creationDate);


    @Query("SELECT cr FROM ClientRefundToDo cr WHERE cr.conditionalRefund = true and ?1 MEMBER OF cr.requiredPayments")
    List<ClientRefundToDo> findByConditionalRefundAndRequiredPayment(Payment payment);

    @Query("select c from ClientRefundToDo c where c.contract = ?1 and c.purpose.name = ?1")
    ClientRefundToDo findFirstByContractAndPurpose(Contract contract, String purposeName);

    Boolean existsByRelatedPaymentId(Long paymentId);

    Boolean existsByContractAndStatus(Contract contract, ClientRefundStatus status);

    ClientRefundToDo findFirstByContractAndStatus(Contract contract, ClientRefundStatus status);

    @Query("SELECT cr FROM ClientRefundToDo cr " +
           "INNER JOIN ContractPaymentTerm c on cr.contract = c.contract " +
           "WHERE cr.contract.payingViaCreditCard = true and c.isActive = true and cr.automaticRefund = true " +
                "and cr.status = 'STOPPED' and cr.stopped = false")
    List<ClientRefundToDo> findByConditionalRefundAndRequiredPayment();


    @Query("select cr.conditionalRefund, cr.amount, cr.taskName " +
            "from ClientRefundToDo cr " +
            "where cr.contract = ?1 and cr.status = 'PENDING' and cr.stopped = 0 and cr.completed = 0 and " +
                "cr.taskName is not null and cr.taskName <> ''")
    List<Object[]> findPendingRefundByContract(Contract c);

    @Query("select sum(cr.amount) " +
            "from ClientRefundToDo cr " +
            "where cr.id <> ?1 and cr.contract = ?2 and cr.transferReference = ?3 and cr.status in ('PAID', 'PENDING')")
    Double getTotalAmountRefundedByTransferReference(Long id, Contract contract, String transferReference);

    @Query("select cr " +
            "from ClientRefundToDo cr " +
            "where cr.contract = ?1 and cr.transferReference = ?2 and cr.amount = ?3 and cr.status = 'PENDING' and " +
            "cr.contractPaymentConfirmationToDo is null and cr.taskName = 'CREDIT_CARD_TRANSFER_CREATED' ")
    List<ClientRefundToDo> findByContractAndTransferReferenceAndAmountAndPending(Contract c, String transferReference, Double amount);

    @Query("select cr from ClientRefundToDo cr where cr.contractPaymentConfirmationToDo.id = ?1 ")
    List<ClientRefundToDo> findRefundByConfirmationToDo(Long todoId);

    @Query("select cr from ClientRefundToDo cr " +
            "where cr.contract = ?1 and cr.conditionalRefund = true and cr.status = 'PENDING' " +
            "and cr.stopped = 0 and cr.completed = 0 and " +
            "cr.taskName is not null and cr.taskName <> ''")
    List<ClientRefundToDo> findPendingRefundWithRequiredPaymentsByContract(Contract c);

    @Query("select count(c.id) > 0 from ClientRefundToDo c " +
            "where c.contract.id = :contractId and ((:paymentId is not null and c.relatedPaymentId = :paymentId) or " +
                    "c.housemaidPayrollLogId = :housemaidPayrollLogId or c.pendingSalaryRecord between :startDate and :endDate) and " +
                "c.purpose.id = :purposeId and c.status in (:statuses)")
    boolean existsForAcc7120(
            @Param("contractId") Long contractId, @Param("paymentId") Long paymentId, @Param("housemaidPayrollLogId") Long housemaidPayrollLogId,
            @Param("startDate") Date startDate, @Param("endDate") Date endDate,
            @Param("purposeId") Long purposeId, @Param("statuses") List<ClientRefundStatus> statuses);

    @Query("select count(c.id) > 0 " +
            "from ClientRefundToDo c " +
            "where c.id = ?1 and c.status in ?2")
    boolean existsByIdAndStatusIn(Long paymentId, List<ClientRefundStatus> l);

    ClientRefundToDo findFirstByContractAndStatusAndCreationDateGreaterThanEqualOrderByCreationDateAsc(
            Contract contract, ClientRefundStatus status, Date fromDate);

    ClientRefundToDo findFirstByContractAndStatusNotOrderByCreationDateDesc(Contract contract, ClientRefundStatus status);

    @Query("select count(cr) > 0 from ClientRefundToDo cr " +
            "where ?1 member of cr.requiredPayments")
    boolean existsByRequiredPayment(Payment payment);

    @Query("select sum(c.amount) " +
            "from ClientRefundToDo c " +
            "where c.contract = ?1 and c.status = ?2 and c.creationDate >= ?3")
    Double findTheAmountByContractAndStatusAndCreationDateGreaterThanOrEqual(Contract contract, ClientRefundStatus status, Date date);

    // Check if contract has refunds with specified purpose (regardless of status)
    @Query("select count(cr.id) > 0 from ClientRefundToDo cr " +
            "where cr.contract.id = :contractId and cr.purpose.id = :purposeId")
    boolean existsByContractAndPurpose(@Param("contractId") Long contractId,  @Param("purposeId") Long purposeId);
}
