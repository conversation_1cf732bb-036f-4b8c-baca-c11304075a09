package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.mail.TextEmail;
import com.magnamedia.core.type.EmailReceiverType;
import com.magnamedia.entity.BankStatementTransaction;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.Transaction;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.repository.BankStatementTransactionRepository;
import com.magnamedia.repository.ClientRefundTodoRepository;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.repository.TransactionRepository;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.core.repository.PicklistItemRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
public class TransactionService {
    private static final Logger logger = Logger.getLogger(TransactionService.class.getName());
    @Autowired
    private PicklistItemRepository picklistItemRepository;
    @Autowired
    private AttachementRepository attachmentRepository;
    @Autowired
    private TransactionRepository transactionRepository;
    @Autowired
    private BankStatementTransactionRepository bankStatementTransactionRepository;
    @Autowired
    private ClientRefundTodoRepository clientRefundTodoRepository;

    public void setMissingTaxInvoice(Transaction transaction) {
        if(transaction.getLicense() == null) {
            transaction.setMissingTaxInvoice(false);
            return;
        }

        PicklistItem license = picklistItemRepository.findOne(transaction.getLicense().getId());

        if(license.getCode().equals("no_vat")) {
            transaction.setMissingTaxInvoice(false);
            return;
        }

        boolean noTaxInvoice = transaction.getAttachments() == null ||
                transaction.getAttachments().stream().noneMatch(a -> {
                        a = attachmentRepository.findOne(a.getId());
                        return a.getTag().toUpperCase().startsWith("VAT_") ||
                                a.getTag().toUpperCase().startsWith("PAYMENT_TAX_INVOICE") ||
                                a.getTag().toUpperCase().startsWith("PAYMENT_TAX_CREDIT_NOTE") ||
                                a.getTag().toUpperCase().startsWith("TR_PAYMENT_TAX_INVOICE") ||
                                a.getTag().toUpperCase().startsWith("TR_PAYMENT_TAX_CREDIT_NOTE") ||
                                a.getTag().toUpperCase().startsWith("RECEIPT -");
                });

        transaction.setMissingTaxInvoice(noTaxInvoice);
    }

    // ACC-3189 ACC-5425
    public void updateTransaction(
            Transaction t,
            Long bankStatementTransactionId) {
        try {
            logger.info("Transaction ID " + t.getId() + "; bankStatementTransactionId: " + bankStatementTransactionId);

            BankStatementTransaction bt = bankStatementTransactionRepository.findOne(bankStatementTransactionId);
            bt.setTransaction(t);
            bankStatementTransactionRepository.save(bt);

            if (bt.getClientRefundToDo() != null) {
                ClientRefundToDo r = clientRefundTodoRepository.findOne(
                        bt.getClientRefundToDo().getId());
                r.setTransaction(t);
                clientRefundTodoRepository.save(r);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Setup.getMailService()
                    .sendEmail(
                            EmailHelper.getRecipients("<EMAIL>"),
                            new TextEmail("An error happened on updateTransaction", e.getMessage()),
                            EmailReceiverType.Office_Staff);
        }
    }


    public void addAttachmentFromPayment(Attachment entity) {
        logger.log(Level.INFO, "execute.");

        Payment payment = Setup.getRepository(PaymentRepository.class).findOne(entity.getOwnerId());
        logger.log(Level.INFO, "payment id: " + (payment != null ? payment.getId() : null));

        if (payment == null) return;

        Transaction transaction = transactionRepository.findFirstByPaymentId(payment.getId());
        logger.log(Level.INFO, "transaction id: " + (transaction != null ? transaction.getId() : null));

        if (transaction == null) return;

        if (transaction.getAttachment(entity.getTag()) != null) {
            logger.log(Level.INFO, "Attachment with tag: " + entity.getTag() + " is already exist on transaction");
            return;
        }

        Attachment copied = Storage.cloneTemporary(entity, "tr_" + entity.getTag());

        logger.log(Level.INFO, "Attachment entity copy: " + copied.getId());
        List<Attachment> attachments = transaction.getAttachments();
        attachments.add(copied);
        transaction.setAttachments(attachments);

        if (entity.getTag().toUpperCase().startsWith("VAT_")
                || entity.getTag().toUpperCase().startsWith("PAYMENT_TAX_INVOICE")
                || entity.getTag().toUpperCase().startsWith("PAYMENT_TAX_CREDIT_NOTE")
                || entity.getTag().toUpperCase().startsWith("TR_PAYMENT_TAX_INVOICE")
                || entity.getTag().toUpperCase().startsWith("TR_PAYMENT_TAX_CREDIT_NOTE")) {

            transaction.setMissingTaxInvoice(false);
        }

        transactionRepository.save(transaction);
    }

}
