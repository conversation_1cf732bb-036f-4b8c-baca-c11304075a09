package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.workflow.DirectDebitCancelationToDo;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DirectDebitCancellationToDoReason;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.*;
import com.magnamedia.service.MessagingService;
import com.magnamedia.service.PaymentService;
import org.apache.commons.lang3.BooleanUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static com.magnamedia.helper.PicklistHelper.getItem;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on Jan 7, 2020
 *         Jirra ACC-1135
 *         final flow
 * 
 */
@BusinessRule(moduleCode = "clientmgmt", entity = Contract.class,
        events = {BusinessEvent.BeforeUpdate},
        fields = {"id", "scheduledDateOfTermination", "ccAppRetractedCancellation"})
public class ContractUnDoTerminationBusinessRule implements BusinessAction<Contract> {
    private static final Logger logger =
            Logger.getLogger(ContractUnDoTerminationBusinessRule.class.getName());
    private static final String prefix = "MMM ";

    @Override
    public boolean validate(Contract entity, BusinessEvent event) {
        logger.log(Level.SEVERE, "ContractUnDoTerminationBusinessRule Validation.");
        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);

        Contract old = contractRepository.findOne(entity.getId());

        logger.log(Level.SEVERE, prefix + "contract code: " + old.getContractProspectType().getCode());
        logger.log(Level.SEVERE, prefix + "old scheduled date of termination: " + old.getScheduledDateOfTermination());
        logger.log(Level.SEVERE, prefix + "new scheduled date of termination: " + entity.getScheduledDateOfTermination());
        logger.log(Level.SEVERE, prefix + "ccAppRetractedCancellation: " + entity.getCcAppRetractedCancellation());

        return old.getContractProspectType().getCode().equals(
                PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE))
                && entity.getScheduledDateOfTermination() == null
                && old.getScheduledDateOfTermination() != null;
    }

    @Override
    public Map<String, Object> execute(Contract entity, BusinessEvent even) {
        Map<String, Object> map = new HashMap();
        logger.log(Level.SEVERE, "ContractUnDoTerminationBusinessRule execute.");
        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
        PaymentService paymentService = Setup.getApplicationContext().getBean(PaymentService.class);
        DirectDebitRepository directDebitRepository = Setup.getRepository(DirectDebitRepository.class);
        DirectDebitCancelationToDoRepository debitCancelationToDoRepository = Setup.getRepository(DirectDebitCancelationToDoRepository.class);

        Contract old = contractRepository.findOne(entity.getId());

        // we owes refund to the client
        PicklistItem monthlyPayment = getItem(AccountingModule.PICKLIST_PAYMETN_TYPE_OF_PAYMENT_CODE,
                AccountingModule.PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_MONTHLY_PAYMENT);

        SelectQuery<Payment> selectQuery = new SelectQuery(Payment.class);
        selectQuery.filterBy("contract", "=", old)
                .and("bouncedInCancellationWaitingPeriod", "=", true)
                .and("status", "=", PaymentStatus.DELETED)
                .and("typeOfPayment", "=", monthlyPayment);

        List<Payment> deletedPayments = selectQuery.execute();

        // Change any DELETED payment flagged as AUTO_DELETED to BOUNCED and Delete the flag
        deletedPayments.forEach(payment -> {
            logger.info("bouncing payment with id: " + payment.getId());
            payment.setStatus(PaymentStatus.BOUNCED);
            payment.setBouncedInCancellationWaitingPeriod(false);
            paymentService.forceUpdatePayment(payment);
        });

        // Close any to-do to Cancel Future DDs
        List<DirectDebit> dds = directDebitRepository.findByContractPaymentTerm_Contract(old);
        if (dds != null) {
            for (DirectDebit dd : dds) {
                List<DirectDebitCancelationToDo> cancelationToDos = debitCancelationToDoRepository.
                        findByDirectDebitFile_DirectDebitAndCompletedFalseAndStoppedFalseAndReason(dd, DirectDebitCancellationToDoReason.CONTRACT_CANCELLATION);
                if (cancelationToDos != null) {
                    for (DirectDebitCancelationToDo cancelationToDo : cancelationToDos) {
                        logger.log(Level.SEVERE, prefix + "closing DDCTODO with id: " + cancelationToDo.getId());
                        cancelationToDo.setStopped(true);
                        debitCancelationToDoRepository.save(cancelationToDo);
                        logger.log(Level.SEVERE, prefix + "undo termination rule: ddCToDo with id " + cancelationToDo.getId() + " cancelled successfully");
                    }
                }
            }
        }

        if (BooleanUtils.toBoolean(entity.getCcAppRetractedCancellation())) {
            //Jirra ACC-2764
            sendEmailAndSMS(old);
        }

        logger.log(Level.SEVERE, "ContractUnDoTerminationBusinessRule execute end.");
        return map;
    }

    private void sendEmailAndSMS(Contract contract) {

        HashMap<String, String> para = new HashMap<>();
        para.put("Client_Name", contract.getClient().getName());
        para.put("ERP_ID", contract.getClient().getId().toString());
        para.put("Contract_Id", contract.getId().toString());

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff("MAID.cc_CANCELLATION_RETRACTION",
                        para, Setup.getParameter(Setup.getCurrentModule(), AccountingModule.CC_APP_RETRACT_CANCELLATION_MAIL_RECIPIENTS),
                        "Contract Cancellation Retraction");
        // jaber
        String phoneNumber = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_MARIO_MOBILE_NUMBER);
        if (phoneNumber != null && !phoneNumber.isEmpty()) {
            OfficeStaff officeStaff = Setup.getRepository(OfficeStaffRepository.class)
                    .findFirstByPhoneNumber(phoneNumber);
            if (officeStaff == null) return;

            Setup.getApplicationContext()
                    .getBean(MessagingService.class)
                    .sendMessageToOfficeStaff(contract,
                            officeStaff,
                            TemplateUtil.getTemplate("MAID.cc_CANCELLATION_RETRACTION"),
                            para,
                            officeStaff.getId(),
                            officeStaff.getEntityType(),
                            phoneNumber);
        }
    }
}
