package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.workflow.WorkflowEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.workflow.FormField;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.util.List;


/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jan 25, 2021
 *         Jirra ACC-2956
 */

@Entity
public class HousemaidAttendanceLog extends WorkflowEntity {

    public HousemaidAttendanceLog() {
        super("");
    }

    public HousemaidAttendanceLog(String startTaskName) {
        super(startTaskName);
    }

    @Override
    public String getFinishedTaskName() {
        return "";
    }

    @Override
    public List<FormField> getForm(String taskName) {
        return null;
    }

    @ManyToOne
    @JsonSerialize(using = IdLabelSerializer.class)
    private Housemaid housemaid;

    @Column
    @Audited
    @Enumerated(EnumType.STRING)
    private AttendanceStatus attendanceStatus;

    @Column
    private String logType;

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public AttendanceStatus getAttendanceStatus() {
        return attendanceStatus;
    }

    public void setAttendanceStatus(AttendanceStatus attendanceStatus) {
        this.attendanceStatus = attendanceStatus;
    }

    public String getLogType() {
        return logType;
    }

    public void setLogType(String logType) {
        this.logType = logType;
    }

    public enum AttendanceStatus {
        PRESENT, ABSENT, BAGGAGE_CHECKED, CONFIRMED_LANDED_IN_DUBAI;
    }
}
