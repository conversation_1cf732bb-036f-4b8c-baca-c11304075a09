package com.magnamedia.entity.projection;

import com.magnamedia.core.entity.Attachment;
import org.springframework.beans.factory.annotation.Value;

import java.sql.Date;
import java.util.List;

/**
 *
 * <AUTHOR> <<EMAIL>> 
 * Created at May 31, 2019
 */
public interface PaymentAttachmentsProjection {
    Long getId();
    Date getDateOfPayment();
//    PicklistItem getTypeOfPayment();
//    PaymentMethod getMethodOfPayment();
    @Value("#{target.getFilteredAttachments()}")
    List<Attachment> getAttachments();
    
}