package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.CooQuestion;
import org.springframework.beans.factory.annotation.Value;

import java.sql.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON>.Ma<PERSON>d on 4/10/2021.
 */
public interface TransactionNightReviewProjection {
    Long getId();

    Double getAmount();

    Double getVatAmount();

    java.util.Date getPnlValueDate();

    Date getDate();

    @JsonSerialize(using = IdLabelSerializer.class)
    User getCreator();

    @Value("#{{id: target.getFromBucket()?.getId(), name: target.getFromBucket()?.getName(), code: target.getFromBucket()?.getCode(), " +
            "cardNumber: target.getFromBucket()?.getCardNumber()}}")
    Map<?, ?> getFromBucket();

    @Value("#{{id: target.getRevenue()?.getId(), name: target.getRevenue()?.getName(), code: target.getRevenue()?.getCode()}}")
    Map<?, ?> getRevenue();

    @Value("#{{id: target.getExpense()?.getId(), name: target.getExpense()?.getNameLabel(), code: target.getExpense()?.getCodeLabel()}}")
    Map<?, ?> getExpense();

    @Value("#{{id: target.getToBucket()?.getId(), name: target.getToBucket()?.getName(), code: target.getToBucket()?.getCode(), " +
            "cardNumber: target.getToBucket()?.getCardNumber()}}")
    Map<?, ?> getToBucket();

    String getDescription();

    List<Attachment> getAttachments();

    String getEntityType();

    List<CooQuestion> getCooQuestions();

    boolean isOneQuestionAnswered();

    boolean isNoneQuestionAnswered();

    boolean isAllQuestionsAnswered();
}
