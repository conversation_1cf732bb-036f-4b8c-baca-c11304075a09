package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
public class BedAssignmentHistory extends BaseEntity implements Serializable {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class  )
    private Housemaid housemaid;

    @Column
    @Temporal(javax.persistence.TemporalType.DATE)
    private Date assignmentDate;

    @Column
    @Temporal(javax.persistence.TemporalType.DATE)
    private Date origenAssignmentDate;

    @Transient
    private String housemaidName;

    @Transient
    private String housemaidStatus;

    @Transient
    private String housemaidPendingStatus;

    @Transient
    private String housemaidNationality;

    @Column
    private String oldHousemaidStatus;

    @Column
    private String oldHousemaidPendingStatus;

    @Column
    private String roomNumber;

    @Column
    private Integer roomPriority;

    @Column
    private String bedNumber;


    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public Date getAssignmentDate() {
        return assignmentDate;
    }

    public void setAssignmentDate(Date assignmentDate) {
        this.assignmentDate = assignmentDate;
    }

    public String getHousemaidName() {
        if (this.housemaidName == null) {
            if (housemaid != null) {
                this.housemaidName = housemaid.getName();
            }
        }
        return this.housemaidName;
    }

    public void setHousemaidName(String housemaidName) {
        this.housemaidName = housemaidName;
    }

    public String getHousemaidStatus() {
        if (this.housemaidStatus == null) {
            if (housemaid != null) {
                this.housemaidStatus = housemaid.getStatus().name();
            }
        }
        return this.housemaidStatus;
    }

    public void setHousemaidStatus(String housemaidStatus) {
        this.housemaidStatus = housemaidStatus;
    }

    public String getHousemaidPendingStatus() {
        if (this.housemaidPendingStatus == null) {
            if (housemaid != null && housemaid.getPendingStatus() != null) {
                this.housemaidPendingStatus = housemaid.getPendingStatus().name();
            }
        }
        return this.housemaidPendingStatus;
    }

    public void setHousemaidPendingStatus(String housemaidPendingStatus) {
        this.housemaidPendingStatus = housemaidPendingStatus;
    }

    public String getHousemaidNationality() {
        if (this.housemaidNationality == null && housemaid != null) {
            return housemaid.getNationality().getName();
        }
        return this.housemaidNationality;
    }

    public void setHousemaidNationality(String housemaidNationality) {
        this.housemaidNationality = housemaidNationality;
    }

    public String getRoomNumber() {
        return this.roomNumber;
    }

    public void setRoomNumber(String roomNumber) {
        this.roomNumber = roomNumber;
    }

    public Integer getRoomPriority() {
        return this.roomPriority;
    }

    public void setRoomPriority(Integer roomPriority) {
        this.roomPriority = roomPriority;
    }

    public String getBedNumber() {
        return this.bedNumber;
    }

    public void setBedNumber(String bedNumber) {
        this.bedNumber = bedNumber;
    }

    public String getApartmentNumber() {
        return getRoomNumber();
    }

    public Integer getApartmentPriority() {
        return getRoomPriority();
    }


    public String getOldHousemaidStatus() {
        return oldHousemaidStatus;
    }

    public void setOldHousemaidStatus(String oldHousemaidStatus) {
        this.oldHousemaidStatus = oldHousemaidStatus;
    }

    public String getOldHousemaidPendingStatus() {
        return oldHousemaidPendingStatus;
    }

    public void setOldHousemaidPendingStatus(String oldHousemaidPendingStatus) {
        this.oldHousemaidPendingStatus = oldHousemaidPendingStatus;
    }

    public Date getOrigenAssignmentDate() {
        return origenAssignmentDate;
    }

    public void setOrigenAssignmentDate(Date origenAssignmentDate) {
        this.origenAssignmentDate = origenAssignmentDate;
    }
}
