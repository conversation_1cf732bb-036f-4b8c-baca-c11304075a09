package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.Position;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.serialize.IdJsonSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.CustomIdLabelCodeSerializer;
import com.magnamedia.extra.OfficeStaffLastSalary;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.OfficeStaffStatus;
import com.magnamedia.module.type.OfficeStaffType;
import com.magnamedia.module.type.SalaryCurrency;
import com.magnamedia.repository.OfficeStaffRepository;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import javax.validation.constraints.Pattern;
import java.time.DayOfWeek;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> Al-Habash Created on 2017-08-16
 */
@Entity
public class OfficeStaff extends BaseEntity {

    //    public final static Set<OfficeStaffStatus> rejectedStatuses
//            = new HashSet<>(Arrays.asList(OfficeStaffStatus.TERMINATED, OfficeStaffStatus.TERMINATED_FROM_MOL, OfficeStaffStatus.INACTIVE));
    @Column(updatable = false,
            insertable = false)
    @Label
    private String name;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem nationality;

    @Column(updatable = false,
            insertable = false)
    private Boolean isActive;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem manager;

    @Enumerated(EnumType.STRING)
    private OfficeStaffStatus status;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem city;

    @Column
    private Double basicSalary;

    //Jirra ACC-785
    @Transient
    private Double basicSalarySearch;

    @Column(columnDefinition = "double default 0")
    private Double primarySalary = 0.0;

    @Column(columnDefinition = "double default 0")
    private Double monthlyLoan = 0.0;

    @Column
    private Date startingDate;

    @Column
    private String pnl;

    @Column
    private Double housingAllowance;

    @Column
    private Double internetAllowance;

    @NotAudited
    @Column
    private String phoneNumber;

    @NotAudited
    @Column
    private String fullNameInArabic;

    @Column
    private Date terminationDate;

    @Column
    private Double trasnportation;

    @Column
    private Boolean excludedFromPayroll;

    @NotAudited
    @Enumerated(EnumType.STRING)
    private OfficeStaffType employeeType;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem salaryPayementMethod;

    @Enumerated(EnumType.STRING)
    private SalaryCurrency salaryCurrency;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = CustomIdLabelCodeSerializer.class)
    private PicklistItem salaryTransfereDestination;

    @NotAudited
    @Lob
    @Column
    private String salaryTransferDestinationOther;

    @Column
    private Double defaulMonthlyRepayment;

    //Jirra ACC-1227
    @Enumerated(EnumType.STRING)
    private DayOfWeek weeklyOffDay;

    @Column(columnDefinition = "double default 0")
    private Double consumedOffDaysBalance = 0D;

    @Column(columnDefinition = "bigint(20) default 0")
    private Long paidOffDaysBalance = 0L;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdJsonSerializer.class)
    private NewRequest visaNewRequest;
    @NotAudited
    @Column
    private String localReceiverName;

    @NotAudited
    @Column
    private String localReceiverArabicName;

    @NotAudited
    @Column
    private String localReceiverPhoneNumber;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem team;

    @Column
    private String insuranceNumber;

    @Column
    private OfficeStaffLastSalary lastSalary;

    //pay roll new fields
    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem updatedSalaryTransfereDestination;

    @NotAudited
    @Lob
    private String updatedOtherSalaryTransfereDestination;

    @NotAudited
    @Column
    private String updatedArabicName;

    @NotAudited
    @Column
    private String updatedEnglishName;

    @NotAudited
    @Column
    private String updatedMobileNumber;

    @Column
    private String qid;

    @Column
    private String employeeAccountQnb;

    //Jirra ACC-1258
    @Column
    private String accountName;

    // jira acc-1472
    @Transient
    private boolean canAddTicketType;

    @NotAudited
    @Pattern(regexp = "^([_a-zA-Z0-9-]+(\\.[_a-zA-Z0-9-]+)*@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*(\\.[a-zA-Z]{1,6}))?$")
    private String email;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private OfficeStaff employeeManager;

    @Column
    private String passportId;

    public OfficeStaff getEmployeeManager() {
        return employeeManager;
    }

    public void setEmployeeManager(OfficeStaff employeeManager) {
        this.employeeManager = employeeManager;
    }

    @OneToOne(fetch = FetchType.LAZY)
    private TransferDestination selectedTransferDestination;
    
    //Jirra ACC-3473
    @NotAudited
    @Lob
    @Column
    private String fullAddress;

    @NotAudited
    @Column
    private String updatedFullAddress;

    @Column
    private String iban;

    @Column
    private String accountHolderName;

    @Column
    private String accountNumber;

    @Column
    private String eidNumber;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem jobTitle;

    @JsonSerialize(using = IdLabelSerializer.class)
    @OneToOne
    private User user;

    public PicklistItem getJobTitle() {return jobTitle;}

    public void setJobTitle(PicklistItem jobTitle) {this.jobTitle = jobTitle;}

    public User getRelatedUser(){return user;}

    public void setUser(User user) {this.user = user;}

    public PicklistItem getTeam() {
        return team;
    }

    public void setTeam(PicklistItem team) {
        this.team = team;
    }

    public String getName() {
        return name;
    }
    
    public String getNameDestination() {
        if (selectedTransferDestination != null
                && (selectedTransferDestination.getSelfReceiver() == null
                || !selectedTransferDestination.getSelfReceiver())){
            return selectedTransferDestination.getName();
        }
        return name;
//        return selectedTransferDestination.getName();
    }

    public void setName(String name) {
        this.name = name;
    }

    public PicklistItem getNationality() {
        return nationality;
    }

    public void setNationality(PicklistItem nationality) {
        this.nationality = nationality;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public PicklistItem getManager() {
        return manager;
    }

    public void setManager(PicklistItem manager) {
        this.manager = manager;
    }

    public OfficeStaffStatus getStatus() {
        return status;
    }

    public void setStatus(OfficeStaffStatus status) {
        this.status = status;
    }

    public PicklistItem getCity() {
        return city;
    }

    public void setCity(PicklistItem city) {
        this.city = city;
    }

    public Double getBasicSalary() {
        if (this.basicSalary == null)
            this.basicSalary =
                    (this.monthlyLoan != null ? this.monthlyLoan : 0.0)
                            + (this.primarySalary != null ? this.primarySalary : 0.0);
        return basicSalary;
    }

    public Double getRosterSalary() {
        //Jirra ACC-279
        return (this.basicSalary != null ? this.basicSalary : 0.0)
                + (this.housingAllowance != null ? this.housingAllowance : 0.0)
                + (this.trasnportation != null ? this.trasnportation : 0.0)
                + (this.internetAllowance != null ? this.internetAllowance : 0.0);
    }

    public Double getBasicSalarySearch() {
        return basicSalarySearch;
    }

    public void setBasicSalarySearch(Double basicSalarySearch) {
        this.basicSalarySearch = basicSalarySearch;
    }

    public void setBasicSalary(Double basicSalary) {
        this.basicSalary = basicSalary;
    }

    public Date getStartingDate() {
        return startingDate;
    }

    public void setStartingDate(Date startingDate) {
        this.startingDate = startingDate;
    }

    public String getPnl() {
        return pnl;
    }

    public void setPnl(String pnl) {
        this.pnl = pnl;
    }

    public Double getHousingAllowance() {
        return housingAllowance;
    }

    public void setHousingAllowance(Double housingAllowance) {
        this.housingAllowance = housingAllowance;
    }

    public Double getInternetAllowance() {
        return internetAllowance;
    }

    public void setInternetAllowance(Double internetAllowance) {
        this.internetAllowance = internetAllowance;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }
    
    public String getPhoneNumberDestination() {
//        if (selectedTransferDestination != null
//                && (selectedTransferDestination.getSelfReceiver() == null
//                || !selectedTransferDestination.getSelfReceiver())){
//            return selectedTransferDestination.getPhoneNumber();
//        }
//        return phoneNumber;
        return selectedTransferDestination == null ? null : selectedTransferDestination.getPhoneNumber();
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getFullNameInArabic() {
        return fullNameInArabic;
    }

    public void setFullNameInArabic(String fullNameInArabic) {
        this.fullNameInArabic = fullNameInArabic;
    }

    public Date getTerminationDate() {
        return terminationDate;
    }

    public void setTerminationDate(Date terminationDate) {
        this.terminationDate = terminationDate;
    }

    public Double getTrasnportation() {
        return trasnportation;
    }

    public void setTrasnportation(Double trasnportation) {
        this.trasnportation = trasnportation;
    }

    public OfficeStaffType getEmployeeType() {
        return employeeType;
    }

    public void setEmployeeType(OfficeStaffType employeeType) {
        this.employeeType = employeeType;
    }

    public PicklistItem getSalaryPayementMethod() {
        return salaryPayementMethod;
    }

    public void setSalaryPayementMethod(PicklistItem salaryPayementMethod) {
        this.salaryPayementMethod = salaryPayementMethod;
    }

    public SalaryCurrency getSalaryCurrency() {
        return salaryCurrency;
    }

    public void setSalaryCurrency(SalaryCurrency salaryCurrency) {
        this.salaryCurrency = salaryCurrency;
    }

    public PicklistItem getSalaryTransfereDestination() {
        return salaryTransfereDestination;
    }

    public void setSalaryTransfereDestination(
            PicklistItem salaryTransfereDestination) {
        this.salaryTransfereDestination = salaryTransfereDestination;
    }

    public Boolean getExcludedFromPayroll() {
        return excludedFromPayroll;
    }

    public void setExcludedFromPayroll(Boolean excludedFromPayroll) {
        this.excludedFromPayroll = excludedFromPayroll;
    }

    public Double getDefaulMonthlyRepayment() {
        return defaulMonthlyRepayment;
    }

    public void setDefaulMonthlyRepayment(Double defaulMonthlyRepayment) {
        this.defaulMonthlyRepayment = defaulMonthlyRepayment;
    }

    public NewRequest getVisaNewRequest() {
        return visaNewRequest;
    }

    public void setVisaNewRequest(NewRequest visaNewRequest) {
        this.visaNewRequest = visaNewRequest;
    }

    public String getLocalReceiverName() {
        return localReceiverName;
    }

    public void setLocalReceiverName(String localReceiverName) {
        this.localReceiverName = localReceiverName;
    }

    public String getLocalReceiverArabicName() {
        return localReceiverArabicName;
    }

    public void setLocalReceiverArabicName(String localReceiverArabicName) {
        this.localReceiverArabicName = localReceiverArabicName;
    }

    public String getLocalReceiverPhoneNumber() {
        return localReceiverPhoneNumber;
    }

    public void setLocalReceiverPhoneNumber(String localReceiverPhoneNumber) {
        this.localReceiverPhoneNumber = localReceiverPhoneNumber;
    }

    public String getInsuranceNumber() {
        return insuranceNumber;
    }

    public void setInsuranceNumber(String insuranceNumber) {
        this.insuranceNumber = insuranceNumber;
    }

    public OfficeStaffLastSalary getLastSalary() {
        return lastSalary;
    }

    public void setLastSalary(OfficeStaffLastSalary lastSalary) {
        this.lastSalary = lastSalary;
    }

    public String getSalaryTransferDestinationOther() {
        return salaryTransferDestinationOther;
    }

    public void setSalaryTransferDestinationOther(String salaryTransferDestinationOther) {
        this.salaryTransferDestinationOther = salaryTransferDestinationOther;
    }

    public PicklistItem getUpdatedSalaryTransfereDestination() {
        return updatedSalaryTransfereDestination;
    }

    public void setUpdatedSalaryTransfereDestination(PicklistItem updatedSalaryTransfereDestination) {
        this.updatedSalaryTransfereDestination = updatedSalaryTransfereDestination;
    }

    public String getUpdatedOtherSalaryTransfereDestination() {
        return updatedOtherSalaryTransfereDestination;
    }

    public void setUpdatedOtherSalaryTransfereDestination(String updatedOtherSalaryTransfereDestination) {
        this.updatedOtherSalaryTransfereDestination = updatedOtherSalaryTransfereDestination;
    }

    public String getUpdatedArabicName() {
        return updatedArabicName;
    }

    public void setUpdatedArabicName(String updatedArabicName) {
        this.updatedArabicName = updatedArabicName;
    }

    public String getUpdatedEnglishName() {
        return updatedEnglishName;
    }

    public void setUpdatedEnglishName(String updatedEnglishName) {
        this.updatedEnglishName = updatedEnglishName;
    }

    public String getUpdatedMobileNumber() {
        return updatedMobileNumber;
    }

    public void setUpdatedMobileNumber(String updatedMobileNumber) {
        this.updatedMobileNumber = updatedMobileNumber;
    }

    public String getQid() {
        return qid;
    }

    public void setQid(String qid) {
        this.qid = qid;
    }

    public String getEmployeeAccountQnb() {
        return employeeAccountQnb;
    }

    public void setEmployeeAccountQnb(String employeeAccountQnb) {
        this.employeeAccountQnb = employeeAccountQnb;
    }

    public Double getPrimarySalary() {
        return primarySalary;
    }

    public void setPrimarySalary(Double primarySalary) {
        this.primarySalary = primarySalary;
    }

    public Double getMonthlyLoan() {
        return monthlyLoan;
    }

    public void setMonthlyLoan(Double monthlyLoan) {
        this.monthlyLoan = monthlyLoan;
    }

    public String getAccountName() {
        return accountName;
    }
    
    public String getAccountNameDestination() {
//        if (selectedTransferDestination != null
//                && (selectedTransferDestination.getSelfReceiver() == null
//                || !selectedTransferDestination.getSelfReceiver())){
//            return selectedTransferDestination.getAccountHolderName();
//        }
//        return accountName;
        return selectedTransferDestination == null ? null : selectedTransferDestination.getAccountHolderName();
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public TransferDestination getSelectedTransferDestination() {
        return selectedTransferDestination;
    }

    public void setSelectedTransferDestination(TransferDestination selectedTransferDestination) {
        this.selectedTransferDestination = selectedTransferDestination;
    }
    
    public String getFullAddress() {
        return fullAddress;
    }
    
    public String getFullAddressDestination() {
        if (selectedTransferDestination != null
                && (selectedTransferDestination.getSelfReceiver() == null
                || !selectedTransferDestination.getSelfReceiver())){
            return selectedTransferDestination.getFullAddress();
        }
        return updatedFullAddress != null ? updatedFullAddress : fullAddress;
//        return selectedTransferDestination.getFullAddress();
    }

    public void setFullAddress(String fullAddress) {
        this.fullAddress = fullAddress;
    }

    public String getUpdatedFullAddress() {
        return updatedFullAddress;
    }

    public void setUpdatedFullAddress(String updatedFullAddress) {
        this.updatedFullAddress = updatedFullAddress;
    }

    public String getIban() {
        return iban;
    }
    
    public String getIbanDestination() {
//        if (selectedTransferDestination != null
//                && (selectedTransferDestination.getSelfReceiver() == null
//                || !selectedTransferDestination.getSelfReceiver())){
//            return selectedTransferDestination.getIban();
//        }
//        return iban;
        return selectedTransferDestination == null ? null : selectedTransferDestination.getIban();
    }

    public void setIban(String iban) {
        this.iban = iban;
    }

    public String getAccountHolderName() {
        return accountHolderName;
    }
    
    public String getAccountHolderNameDestination() {
//        if (selectedTransferDestination != null
//                && (selectedTransferDestination.getSelfReceiver() == null
//                || !selectedTransferDestination.getSelfReceiver())){
//            return selectedTransferDestination.getAccountHolderName();
//        }
//        return accountHolderName;
        return selectedTransferDestination == null ? null : selectedTransferDestination.getAccountHolderName();
    }

    public void setAccountHolderName(String accountHolderName) {
        this.accountHolderName = accountHolderName;
    }

    public String getAccountNumber() {
        return accountNumber;
    }
    
    public String getAccountNumberDestination() {
//        if (selectedTransferDestination != null
//                && (selectedTransferDestination.getSelfReceiver() == null
//                || !selectedTransferDestination.getSelfReceiver())){
//            return selectedTransferDestination.getAccountNumber();
//        }
//        return accountNumber;
        return selectedTransferDestination == null ? null : selectedTransferDestination.getAccountNumber();
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getEidNumber() {
        return eidNumber;
    }

    public void setEidNumber(String eidNumber) {
        this.eidNumber = eidNumber;
    }
    public String getSwiftDestination() {
//        if (selectedTransferDestination != null
//                && (selectedTransferDestination.getSelfReceiver() == null
//                || !selectedTransferDestination.getSelfReceiver())){
//            return selectedTransferDestination.getSwiftCode();
//        }
//        return "";
        return selectedTransferDestination == null ? null : selectedTransferDestination.getSwiftCode();
    }
    
    public DayOfWeek getWeeklyOffDay() {
        return weeklyOffDay;
    }

    public void setWeeklyOffDay(DayOfWeek weeklyOffDay) {
        this.weeklyOffDay = weeklyOffDay;
    }

    public Double getConsumedOffDaysBalance() {
        return consumedOffDaysBalance;
    }

    public void setConsumedOffDaysBalance(Double consumedOffDaysBalance) {
        this.consumedOffDaysBalance = consumedOffDaysBalance;
    }

    public Long getPaidOffDaysBalance() {
        return paidOffDaysBalance;
    }

    public void setPaidOffDaysBalance(Long paidOffDaysBalance) {
        this.paidOffDaysBalance = paidOffDaysBalance;
    }

    @BeforeUpdate
    private void preSave() {
        OfficeStaff old = Setup.getApplicationContext()
                .getBean(OfficeStaffRepository.class)
                .getOne(this.getId());
        //Jirra ACC-270
        if (this.employeeType == OfficeStaffType.FT_OFFICE_STAFF ||
                this.employeeType == OfficeStaffType.PT_OFFICE_STAFF)
            throw new RuntimeException("Emplouee Type could not be " +
                    this.employeeType.toString() + " .");

        if (this.monthlyLoan == null)
            this.monthlyLoan = old.getMonthlyLoan();
        if (this.primarySalary == null)
            this.primarySalary = old.getPrimarySalary();
        this.basicSalary =
                (this.monthlyLoan != null ? this.monthlyLoan : 0D)
                        + (this.primarySalary != null ? this.primarySalary : 0D);
    }

    @BeforeInsert
    private void preInsert() {
        //Jirra ACC-270
        if (this.employeeType == OfficeStaffType.FT_OFFICE_STAFF ||
                this.employeeType == OfficeStaffType.PT_OFFICE_STAFF)
            throw new RuntimeException("Emplouee Type could not be " +
                    this.employeeType.toString() + " .");

        if (this.monthlyLoan == null)
            this.monthlyLoan = 0.0;
        if (this.primarySalary == null)
            this.primarySalary = 0.0;
        this.basicSalary = this.monthlyLoan + this.primarySalary;
    }
    
    public void setCanAddTicketType(boolean canAddTicketType) {
        this.canAddTicketType = canAddTicketType;
    }
    
    public boolean getCanAddTicketType() {
        return getCanAddTicketType(employeeType);
    }

    public boolean getCanAddTicketType(OfficeStaffType employeeType) {
        
        String localStaffTagName = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_lOCAL_STAFF_NATIONALITY_TAG);
        return employeeType == OfficeStaffType.UAE_NON_H_C
                && (localStaffTagName == null
                || localStaffTagName.isEmpty()
                || !nationality.hasTag(localStaffTagName));
    }

    @JsonIgnore
    private User getUser(){
        OfficeStaffRepository officeStaffRepository = Setup.getRepository(OfficeStaffRepository.class);
        List<User> users = officeStaffRepository.getUsers(getId());
        if (users!=null && !users.isEmpty())
            return users.get(0);
        return null;
    }

    @JsonIgnore
    private OfficeStaff getFinalManagerOfficeStaff(){
        if(getEmployeeManager()==null)
            return this;
        else {
            User user = getEmployeeManager().getUser();
            if(user !=null){
                Set<Position>  positions = user.getPositions();
                if(!positions.isEmpty() && positions.stream().anyMatch(pos->pos.getCode().equals(AccountingModule.EXPENSE_COO_USER_POSITION)))
                    return this;
                else
                    return getEmployeeManager().getFinalManagerOfficeStaff();
            }
            else
                return getEmployeeManager().getFinalManagerOfficeStaff();

        }
    }

    @JsonIgnore
    public User getFinalManagerUser(){
        return getFinalManagerOfficeStaff().getUser();
    }

    public String getPassportId() {return passportId;}

    public void setPassportId(String passportId) {this.passportId = passportId;}
}
