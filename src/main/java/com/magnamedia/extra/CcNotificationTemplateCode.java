package com.magnamedia.extra;

public enum CcNotificationTemplateCode {
    CC_PAYMENT_EXPIRY_4_1_1_NOTIFICATION,
    CC_PAYMENT_EXPIRY_4_1_2_NOTIFICATION,
    
    CC_ACCOUNTING_PAY_VIA_CREDIT_CARD_NOTIFICATION,
    CC_DD_PENDING_INFO_NOTIFICATION,
    CC_CLIENT_REFUND_BANK_TRANSFER_DETAILS_NOTIFICATION,

    CC_ACCOUNTING_NOT_OWED_MONEY_FROM_CLIENT_8_1_2_NOTIFICATION, //row 8 "If we receive an amount from the client when he doesn't owe us any amount (IF client has DD) if he already cancelled"
    CC_ACCOUNTING_WRONGLY_CHARGED_MONEY_ON_CLIENT_8_1_3_NOTIFICATION, //row 9 "If we receive an amount from the client when he doesn't owe us any amount (IF client has DD),  IF his contract is “scheduled for termination” AND we don’t need that amount"
    
    CC_PAYTAB_THANKS_MESSAGE_NOTIFICATION,
    
    CC_PAYMENT_NOT_RECEIVED_OR_BOUNCED_8_1_5_NOTIFICATION,
    CC_PAYMENT_RECEIVED_8_1_5_NOTIFICATION,
    CC_PAYMENT_UNDER_PROCESSING_8_1_5_NOTIFICATION,
    
    CC_PAYMENT_NOT_RECEIVED_OR_BOUNCED_8_1_6_NOTIFICATION,
    CC_PAYMENT_RECEIVED_8_1_6_NOTIFICATION,
    CC_PAYMENT_UNDER_PROCESSING_8_1_6_NOTIFICATION,
    
    CC_ACCOUNTING_OWE_MONEY_TO_CLIENT_8_1_1_NOTIFICATION,

    //ACC-4905
    CC_PAYMENT_REMINDER_THANKS_MESSAGE_NOTIFICATION,
    CC_SIGNING_OFFER_PAYMENT_RECEIVED_THANKS_MESSAGE_NOTIFICATION,

    // ACC-4591
    CC_ACCOUNTING_PAY_ACCOMMODATION_FEE_NOTIFICATION,
    CC_ACCOUNTING_PAY_CC_TO_MV_NOTIFICATION,
    CC_ACCOUNTING_PAY_MONTHLY_PAYMENT_NOTIFICATION,
    CC_ACCOUNTING_PAY_OVERSTAY_FEES_NOTIFICATION,
    CC_ACCOUNTING_PAY_PCR_TEST_NOTIFICATION,
    CC_ACCOUNTING_PAY_URGENT_VISA_CHARGES_NOTIFICATION,
    CC_ACCOUNTING_PAY_INSURANCE_NOTIFICATION,
    CC_ACCOUNTING_PAY_OTHER_PAYMENTS_TYPES_NOTIFICATION,

    // ACC-5533 CMA-3750
    CC_CLIENT_PAYING_VIA_CREDIT_CARD_REPLACEMENT_SUCCESS_WITH_PAY_LINK_NOTIFICATION,
    CC_CLIENT_PAYING_VIA_CREDIT_CARD_REPLACEMENT_SUCCESS_NOTIFICATION,

    CC_ACCOUNTING_REFUND_REJECTED_NOTIFICATION,
    CC_ACCOUNTING_CONDITIONAL_REFUND_REQUESTED_NOTIFICATION,
    CC_ACCOUNTING_NON_CONDITIONAL_REFUND_REQUESTED_NOTIFICATION,
    CC_ACCOUNTING_REFUND_REJECTED_CREDIT_CARD_NOTIFICATION,
    CC_ACCOUNTING_CONDITIONAL_REFUND_REQUESTED_CREDIT_CARD_NOTIFICATION,
    CC_ACCOUNTING_NON_CONDITIONAL_REFUND_REQUESTED_CREDIT_CARD_NOTIFICATION,

    // ACC-6943
    CC_PAYMENT_FOR_APPROVAL_REQUEST_FROM_ERP,

    CC_PAYMENT_RECEIVED_FROM_ONE_TO_X_MINUS_ONE_MONTHS_NOTIFICATION,
    CC_PAYMENT_RECEIVED_ON_X_MONTH_NOTIFICATION,
    CC_PAYMENT_RECEIVED_FROM_X_PLUS_ONE_ONWARDS_MONTHS_NOTIFICATION,
}