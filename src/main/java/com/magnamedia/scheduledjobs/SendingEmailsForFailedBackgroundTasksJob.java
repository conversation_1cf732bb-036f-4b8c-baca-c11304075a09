package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.mail.TextEmail;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.core.type.EmailReceiverType;
import com.magnamedia.entity.AccountingEntityProperty;
import com.magnamedia.entity.AccountingReportSetting;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.extra.FailedBGTsCSV;
import com.magnamedia.extra.FailedBGTsProjection;
import com.magnamedia.extra.JobUtils;
import com.magnamedia.helper.CsvHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.AccountingEntityPropertyRepository;
import com.magnamedia.repository.AccountingReportSettingRepository;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.io.File;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

// ACC-8397
public class SendingEmailsForFailedBackgroundTasksJob implements MagnamediaJob {
    private static final Logger logger = Logger.getLogger(SendingEmailsForFailedBackgroundTasksJob.class.getName());

    private AccountingEntityProperty accountingEntityProperty;

    private static final String ACCOUNTING_PROPERTY_FAILED_BGT_JOB_TIME = "accounting_property_failed_bgt_job_time";

    @Override
    public void run(Map<String, ?> map) {
        accountingEntityProperty = Setup.getRepository(AccountingEntityPropertyRepository.class)
                .findByKeyAndIsDeletedFalse(ACCOUNTING_PROPERTY_FAILED_BGT_JOB_TIME);

        sendEmail(getData());
    }

    public static void sendEmail(List<FailedBGTsCSV> data) {
        try {

            logger.info("failed background tasks array size: " + data.size());

            if (data.isEmpty()) return;

            List<EmailRecipient> recipients = EmailHelper.getRecipients(
                    Setup.getParameter(Setup.getCurrentModule(),
                            AccountingModule.PARAMETER_FAILED_BACKGROUND_TASKS_WITHIN_DAY_EMAILS));

            if (recipients.isEmpty()) return;

            String[] headers = {"ID", "Name", "Error", "Target Bean", "Target Method", "Parameters", "Creation Date", "Start Date", "PAGE_CODE"};
            String[] names = {"id", "name", "error", "targetBean", "targetMethod", "parameters", "creationDate", "startDate", "pageCode"};

            File file = CsvHelper.generateCsv(data, FailedBGTsProjection.class, headers, names,
                    "Failed Background Tasks_" + new DateTime().toString("yyyy-MM-dd HH:mm:ss"), ".csv");
            TextEmail mail = new TextEmail("Failed Background Tasks for " + new DateTime().toString("yyyy-MM-dd HH:mm:ss"), "");
            mail.addAttachement(file);
            Setup.getMailService().sendEmail(recipients, mail, EmailReceiverType.Office_Staff);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private List<FailedBGTsCSV> getData() {
        try {
            AccountingReportSetting failedBackgroundTasksQuery = Setup.getRepository(AccountingReportSettingRepository.class)
                    .findFirstByReportCategoryAndQueryCategory(
                            AccountingReportSetting.ReportCategory.FAILED_BACKGROUND_TASKS,
                            AccountingReportSetting.QueryCategory.FAILED_BACKGROUND_TASKS);

            if (failedBackgroundTasksQuery == null) {
                return new ArrayList<>();
            }

            String query = failedBackgroundTasksQuery.getQuery();

            Date lastRunDate = JobUtils.getJobLastRunDate(accountingEntityProperty, DateUtil.now());
            JobUtils.setJobLastRunDate(accountingEntityProperty, ACCOUNTING_PROPERTY_FAILED_BGT_JOB_TIME, new LocalDateTime());

            List<Object[]> backgroundTasks = executeNativeQuery(query, lastRunDate);
            logger.info("returned backgroundTasks size is :" + backgroundTasks.size());
            List<FailedBGTsCSV> data = new ArrayList<>();

            for (Object[] bgt : backgroundTasks) {
                data.add(toFailedBGTCSV(bgt));
            }

            return data;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return new ArrayList<>();
    }

    public static FailedBGTsCSV toFailedBGTCSV(Object[] obj) {
        try {
            return new FailedBGTsCSV(
                    obj[0] == null ? "N\\A" : ((BigInteger) obj[0]).toString(),
                    obj[1] == null ? "N\\A" : (String) obj[1],
                    obj[2] == null ? "N\\A" : (String) obj[2],
                    obj[3] == null ? "N\\A" : (String) obj[3],
                    obj[4] == null ? "N\\A" : (String) obj[4],
                    obj[5] == null ? "N\\A" : (String) obj[5],
                    obj[6] == null ? "N\\A" : new LocalDateTime((Date) obj[6]).toString("yyyy-MM-dd HH:mm:ss"),
                    obj[7] == null ? "N\\A" : new LocalDateTime((Date) obj[7]).toString("yyyy-MM-dd HH:mm:ss"),
                    obj[8] == null ? "N\\A" : (String) obj[8]
            );

        } catch (Exception e) {
            e.printStackTrace();
            return new FailedBGTsCSV("N\\A", "N\\A", "N\\A", "N\\A", "N\\A", "N\\A", "N\\A", "N\\A", "N\\A");
        }
    }


    private List<Object[]> executeNativeQuery(String q, Date lastRunDate) {
        EntityManager em = Setup.getEntityManagerFactory().createEntityManager();
        try {
            Query resultQuery = em.createNativeQuery(q);
            resultQuery.setParameter("lastRunDate", lastRunDate);
            return resultQuery.getResultList();
        } catch (Exception e) {
            e.printStackTrace();
            Setup.getMailService()
                    .sendEmail(
                            EmailHelper.getRecipients(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_FAILED_BACKGROUND_TASKS_WITHIN_DAY_EMAILS)),
                            new TextEmail("An error happened in  Sending Failed Background tasks in email Exceptions",
                                    e.getMessage() + ": " + q),
                            EmailReceiverType.Office_Staff);
        } finally {
            em.close();
        }
        return new ArrayList<>();
    }
}
