package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.AfterInsert;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.module.type.PaymentMatchingRecordStatus;
import com.magnamedia.repository.PaymentMatchingRecordRepository;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.repository.PaymentsMatchingFileRepository;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.envers.NotAudited;
import org.joda.time.DateTime;

import javax.persistence.*;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Mar 12, 2019
 *         Jirra ACC-469
 */
@Entity
public class PaymentsMatchingFile extends BaseEntity {

    private static final Logger logger =
            Logger.getLogger(BaseRepositoryController.class.getName());
    private static final String prefix = "MMM ";

    @Label
    private String fileName;

    @Column
    private boolean hidden = false;

    @JsonIgnore
    @NotAudited
    @OneToMany(mappedBy = "paymentsMatchingFile",
            fetch = FetchType.LAZY,
            cascade = CascadeType.ALL)
    private List<PaymentMatchingRecord> records;

    @JsonIgnore
    @Transient
    private List<PaymentMatchingRecord> matchedRecords;

    @JsonIgnore
    @Transient
    private List<PaymentMatchingRecord> notMatchedRecords;

    // Jirra ACC-599
    @JsonIgnore
    @Transient
    private List<PaymentMatchingRecord> confirmedRecords;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public boolean isHidden() {
        return hidden;
    }

    public void setHidden(boolean hidden) {
        this.hidden = hidden;
    }

    public List<PaymentMatchingRecord> getRecords() {
        return records;
    }

    public void setRecords(List<PaymentMatchingRecord> records) {
        this.records = records;
    }

    public List<PaymentMatchingRecord> getMatchedRecords() {
        if (this.matchedRecords == null || this.matchedRecords.isEmpty()) {
            logger.log(Level.SEVERE, prefix + "started getMatchedRecords: " + new DateTime());
            PaymentMatchingRecordRepository repo = Setup.getRepository(PaymentMatchingRecordRepository.class);
            logger.log(Level.SEVERE, prefix + "finished getMatchedRecords: " + new DateTime());
            this.matchedRecords = repo.findByPaymentsMatchingFileAndRecordStatus(this, PaymentMatchingRecordStatus.MATCHED,
                    new DateTime().withYear(2019).withMonthOfYear(5).withDayOfMonth(19).toDate());
        }
        return matchedRecords;
    }

    public Integer getMatchedRecordsCount() {
        logger.log(Level.SEVERE, prefix + "started getMatchedRecordsCount: " + new DateTime());
        PaymentMatchingRecordRepository repo = Setup.getRepository(PaymentMatchingRecordRepository.class);
        logger.log(Level.SEVERE, prefix + "finished getMatchedRecordsCount: " + new DateTime());
        return repo.countByPaymentsMatchingFileAndRecordStatus(this, PaymentMatchingRecordStatus.MATCHED);
    }

    public Double getMatchedRecordsAmount() {
        Double amount = 0.0D;
        for (PaymentMatchingRecord r : getMatchedRecords())
            if (r.getPayment() != null
                    && r.getPayment().getAmountOfPayment() != null)
                amount += r.getPayment().getAmountOfPayment();
        return amount;
    }

    public List<PaymentMatchingRecord> getConfirmedRecords() {
        if (this.confirmedRecords == null || this.confirmedRecords.isEmpty()) {
            logger.log(Level.SEVERE, prefix + "started getConfirmedRecords: " + new DateTime());
            PaymentMatchingRecordRepository repo = Setup.getRepository(PaymentMatchingRecordRepository.class);
            logger.log(Level.SEVERE, prefix + "finished getConfirmedRecords: " + new DateTime());
            this.confirmedRecords = repo.findByPaymentsMatchingFileAndRecordStatus(this, PaymentMatchingRecordStatus.CONFIRMED,
                    new DateTime().withYear(2019).withMonthOfYear(5).withDayOfMonth(19).toDate());
        }
        return confirmedRecords;
    }

    public Integer getConfirmedRecordsCount() {
        logger.log(Level.SEVERE, prefix + "started getConfirmedRecordsCount: " + new DateTime());
        PaymentMatchingRecordRepository repo = Setup.getRepository(PaymentMatchingRecordRepository.class);
        logger.log(Level.SEVERE, prefix + "finished getConfirmedRecordsCount: " + new DateTime());
        return repo.countByPaymentsMatchingFileAndRecordStatus(this, PaymentMatchingRecordStatus.CONFIRMED);
    }

    public Double getConfirmedRecordsAmount() {
        Double amount = 0.0D;
        for (PaymentMatchingRecord r : getConfirmedRecords())
            if (r.getPayment() != null
                    && r.getPayment().getAmountOfPayment() != null)
                amount += r.getPayment().getAmountOfPayment();
        return amount;
    }

    public List<PaymentMatchingRecord> getNotMatchedRecords() {
        if (this.notMatchedRecords == null || this.notMatchedRecords.isEmpty()) {
            logger.log(Level.SEVERE, prefix + "started getNotMatchedRecords: " + new DateTime());
            PaymentMatchingRecordRepository repo = Setup.getRepository(PaymentMatchingRecordRepository.class);
            logger.log(Level.SEVERE, prefix + "finished getNotMatchedRecords: " + new DateTime());
            this.notMatchedRecords = repo.findByPaymentsMatchingFileAndRecordStatus(this, PaymentMatchingRecordStatus.NOT_MATCHED,
                    new DateTime().withYear(2019).withMonthOfYear(5).withDayOfMonth(19).toDate());
        }
        return notMatchedRecords;
    }

    public Integer getNotMatchedRecordsCount() {
        logger.log(Level.SEVERE, prefix + "started getNotMatchedRecordsCount: " + new DateTime());
        PaymentMatchingRecordRepository repo = Setup.getRepository(PaymentMatchingRecordRepository.class);
        logger.log(Level.SEVERE, prefix + "finished getNotMatchedRecordsCount: " + new DateTime());
        return repo.countByPaymentsMatchingFileAndRecordStatus(this, PaymentMatchingRecordStatus.NOT_MATCHED);
    }

    public Double getNotMatchedRecordsAmount() {
        Double amount = 0.0D;
        List<Long> ids = new ArrayList<>();
        for (PaymentMatchingRecord r : getNotMatchedRecords())
            try {
                if (r.getPaymentIdStr() != null)
                    ids.add(Long.parseLong(r.getPaymentIdStr()));
            } catch (Exception e) {

            }
        PaymentRepository paymentRepository =
                Setup.getApplicationContext().getBean(PaymentRepository.class);
        if (!ids.isEmpty())
            amount = paymentRepository.sumByPaymentIdsList(ids);
        return amount == null ? 0D : amount;
    }

    @BeforeInsert
    public void validateInsert() {
        if (getAttachments() == null || getAttachments().isEmpty())
            throw new RuntimeException("Attachment file is required.");
    }

    @AfterInsert
    public void parseRecords() throws IOException {

        PaymentsMatchingFileRepository paymentsMatchingFileRepository =
                Setup.getApplicationContext().getBean(PaymentsMatchingFileRepository.class);
        PaymentMatchingRecordRepository repo =
                Setup.getRepository(PaymentMatchingRecordRepository.class);

        Storage.updateAttachements(this);
        Attachment att = getAttachments().get(0);

        PaymentsMatchingFile file =
                paymentsMatchingFileRepository.findOne(this.getId());
        file.fileName = att.getName();
        paymentsMatchingFileRepository.save(file);
        this.fileName = file.fileName;

        this.records = new ArrayList<>();

        Workbook workbook = null;
        if (att.getExtension().equals("xlsx"))
            workbook = new XSSFWorkbook(Storage.getStream(att));
        else if (att.getExtension().equals("xls"))
            workbook = new HSSFWorkbook(Storage.getStream(att));

        if (workbook != null) {
            Sheet sheet = workbook.getSheetAt(0);

            Iterator<Row> rowIterator = sheet.iterator();
            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                if (row.getRowNum() < 1)
                    continue;
                try {
                    PaymentMatchingRecord matchingRecord = new PaymentMatchingRecord();
                    Cell cell0 = row.getCell(0);
                    if (cell0 != null) {
                        cell0.setCellType(Cell.CELL_TYPE_STRING);
                        matchingRecord.setPaymentIdStr(row.getCell(0).getStringCellValue());
                    }
                    Cell cell1 = row.getCell(1);
                    if (cell1 != null) {
                        cell1.setCellType(Cell.CELL_TYPE_STRING);
                        matchingRecord.setStatus(row.getCell(1).getStringCellValue());
                    }
                    Cell cell2 = row.getCell(2);
                    if (cell2 != null) {
                        cell2.setCellType(Cell.CELL_TYPE_STRING);
                        matchingRecord.setReasonOfBouncedCheque(row.getCell(2).getStringCellValue());
                    }
                    matchingRecord.setPaymentsMatchingFile(this);

                    matchingRecord.validateInsert();
                    records.add(matchingRecord);
                } catch (Exception ex) {
                    Logger.getLogger(BankDirectDebitActivationFile.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
            repo.save(records);
        }

    }
}
