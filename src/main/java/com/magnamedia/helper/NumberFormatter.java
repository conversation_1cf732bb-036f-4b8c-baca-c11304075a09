package com.magnamedia.helper;

import java.text.NumberFormat;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 6/16/2020
 **/
public class NumberFormatter {

    public static NumberFormat formatter = NumberFormat.getInstance();

    public static String formatNumber(Double number){
        if(number == null)
            return null;

        formatter.setGroupingUsed(true);
        formatter.setMaximumFractionDigits(2);
        return formatter.format(number);
    }

    public static Double twoDecimalPoints(Double number) {
        if(number == null) return 0d;
        return Math.round(number * 100.0) / 100.0;
    }
    

    public static Double threeDecimalPoints(Double number) {
        if(number == null) return 0d;
        return Math.round(number * 1000.0) / 1000.0;
    }
}
