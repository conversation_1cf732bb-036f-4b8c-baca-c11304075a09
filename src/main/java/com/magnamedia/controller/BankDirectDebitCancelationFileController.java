package com.magnamedia.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.JwtSecured;
import com.magnamedia.core.annotation.Searchable;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.entity.BackgroundTaskStatus;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.security.ViewScope;
import com.magnamedia.entity.AccountingEntityProperty;
import com.magnamedia.entity.BankDirectDebitCancelationFile;
import com.magnamedia.entity.BankDirectDebitCancelationRecord;
import com.magnamedia.entity.projection.BankDirectDebitCancelationFileProjection;
import com.magnamedia.entity.projection.BankDirectDebitCancelationRecordCsvProjection;
import com.magnamedia.entity.projection.BankDirectDebitCancelationRecordProjection;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.extra.UploadStatementEntityType;
import com.magnamedia.extra.annotations.UsedBy;
import com.magnamedia.helper.BackgroundTaskHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.repository.AccountingEntityPropertyRepository;
import com.magnamedia.repository.BankDirectDebitCancelationFileRepository;
import com.magnamedia.repository.BankDirectDebitCancelationRecordRepository;
import com.magnamedia.service.BankDirectDebitCancellationRecordService;
import com.magnamedia.service.DirectDebitCancellationService;
import com.magnamedia.service.MessagingService;
import com.magnamedia.service.QueryService;
import org.joda.time.LocalDateTime;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Nov 20, 2019
 *         Jirra ACC-1134
 */
@RequestMapping("/bddcancelationfiles")
@RestController
public class BankDirectDebitCancelationFileController extends BaseRepositoryController<BankDirectDebitCancelationFile> {

    @Autowired
    private BankDirectDebitCancelationFileRepository bankDirectDebitCancelationFileRepository;
    @Autowired
    private BankDirectDebitCancelationRecordRepository bankDirectDebitCancelationRecordRepository;
    @Autowired
    private ProjectionFactory projectionFactory;
    @Autowired
    private BackgroundTaskService backgroundTaskService;
    @Autowired
    private DirectDebitCancellationService directDebitCancellationService;
    @Autowired
    private BankDirectDebitCancellationRecordService bankDirectDebitCancellationRecordService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private AccountingEntityPropertyRepository accountingEntityPropertyRepository;
    @Autowired
    private BackgroundTaskHelper backgroundTaskHelper;

    @Override
    public BaseRepository<BankDirectDebitCancelationFile> getRepository() {
        return bankDirectDebitCancelationFileRepository;
    }

    // ACC-2224
    @Override
    protected ResponseEntity<?> createEntity(BankDirectDebitCancelationFile entity) {

        if (entity.getAttachments() == null || entity.getAttachments().isEmpty())
            throw new RuntimeException("Attachment file is required.");

        if (QueryService.existsEntity(BackgroundTask.class, "e.name = :p0 and e.status not in :p1",
                new Object[]{UploadStatementEntityType.BankDirectDebitCancelationFileByRPA.toString(),
                        Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed)}) ||
                QueryService.existsEntity(AccountingEntityProperty.class, "e.key = :p0 and e.purpose = :p1",
                new Object[]{AccountingModule.RUN_BACKGROUND_TASK_IN_SEQUENTIAL,
                        UploadStatementEntityType.BankDirectDebitCancelationFileByRPA.toString()}) ) {
            throw new BusinessException("There’s another file under parsing, please wait …");
        }

        entity = getRepository().save(entity);

        Map<String, Object> payload = new HashMap<>();
        payload.put("entityId", entity.getId());

        BackgroundTaskHelper.createBGTParsingStatementUploaded(UploadStatementEntityType.BankDirectDebitCancelationFile,
                UploadStatementEntityType.BankDirectDebitCancelationFile.toString(),
                payload);
        return new ResponseEntity("start processing, this process may take up to 15 minutes before the file is shown in the grid.", HttpStatus.OK);
    }

    @Deprecated
    @Transactional
    public void saveNewFile(Map<String, Object> payload) throws IOException {
        BankDirectDebitCancelationFile file = saveNewEntity(payload);
        payload.put("entityId", file.getId());
        bankDirectDebitCancellationRecordService.processFile(payload);
    }

    //ACC-9005
    @UsedBy(others = UsedBy.Others.Rpa)
    @PreAuthorize("hasPermission('bddcancelationfiles', 'createByRPA')")
    @PostMapping("/createByRPA")
    public ResponseEntity<?> createByRPA(@RequestBody BankDirectDebitCancelationFile entity) {
        if (entity.getAttachments() == null || entity.getAttachments().isEmpty())
            throw new BusinessException("Attachment file is required.");

        try {
            entity.setAddedByRPA(true);
            entity.setConfirmedByRPA(true);
            entity = getRepository().save(entity);

            Map<String, Object> payload = new HashMap<>();
            payload.put("entityId", entity.getId());

            if (QueryService.existsEntity(BackgroundTask.class, "e.name = :p0 and e.status not in :p1",
                    new Object[]{"BankDirectDebitCancelationFileByRPA",
                            Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed)})) {
                logger.info("existing bgt running --> create new RUN_BGT_IN_SEQUENTIAL property for : " + entity.getId());
                AccountingEntityProperty a = new AccountingEntityProperty();
                a.setKey(AccountingModule.RUN_BACKGROUND_TASK_IN_SEQUENTIAL);
                // BGT name
                a.setOrigin(entity);
                a.setPurpose(UploadStatementEntityType.BankDirectDebitCancelationFileByRPA.toString());
                a.setValue(objectMapper.writeValueAsString(payload));
                accountingEntityPropertyRepository.save(a);
            } else {
                BackgroundTaskHelper.createBGTParsingStatementUploaded(
                        UploadStatementEntityType.BankDirectDebitCancelationFileByRPA,
                        UploadStatementEntityType.BankDirectDebitCancelationFileByRPA.toString(),
                        payload);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }

        return ResponseEntity.ok("Your request has been added to the queue and will be processed shortly");
    }

    //ACC-9005
    @Deprecated
    public void saveNewFileByRPA(Map<String, Object> payload) throws IOException {
        BankDirectDebitCancelationFile file = saveNewEntity(payload);
        logger.info("file ID : " + file.getId());

        payload.put("entityId", file.getId());
        bankDirectDebitCancellationRecordService.processFileByRPA(payload);
    }

    //ACC-9005
    @Deprecated
    private BankDirectDebitCancelationFile saveNewEntity(Map<String, Object> payload) {
        Long attachmentId = Long.parseLong(payload.get("attachmentId").toString());
        Date date = new LocalDateTime(DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss")
                .parseDateTime((String) payload.get("date"))).toDate();

        BankDirectDebitCancelationFile cancellationFile = new BankDirectDebitCancelationFile(attachmentId, date);

        cancellationFile.setAddedByRPA((Boolean) payload.getOrDefault("addedByRPA", false));
        cancellationFile.setConfirmedByRPA((Boolean) payload.getOrDefault("confirmedByRPA", false));

        BankDirectDebitCancelationFile savedFile = bankDirectDebitCancelationFileRepository.save(cancellationFile);
        bankDirectDebitCancelationFileRepository.flush();
        return savedFile;
        //processUnsuccessfulDDsCancellationRecords(cancelationFile);
    }

    // ACC-2885
    /*private void processUnsuccessfulDDsCancellationRecords(BankDirectDebitCancelationFile cancelationFile) {
        List<BankDirectDebitCancelationRecord> records = bankDirectDebitCancelationRecordRepository.findByBankDirectDebitCancelationFile(cancelationFile);

        List<UnsuccessfulDDsCancellationReport.NAK_DDCancellationRecords> nakRecords = records.
                stream().filter(record -> record.getDirectDebitFile() != null && record.getDirectDebitFile().getDdStatus().equals(DirectDebitStatus.PENDING_FOR_CANCELLATION)
                && record.getCbStatus() != null && record.getCbStatus().equals(BankDirectDebitCancelationRecord.CB_STATUS_NAK))
                .map(record -> new UnsuccessfulDDsCancellationReport.NAK_DDCancellationRecords(record))
                .collect(Collectors.toList());

        List<UnsuccessfulDDsCancellationReport.NAK_DDCancellationRecords> filteredNakRecords = new ArrayList();

        for (UnsuccessfulDDsCancellationReport.NAK_DDCancellationRecords nak_ddCancellationRecord : nakRecords) {
            boolean addNakRecord = true;
            for (BankDirectDebitCancelationRecord record : records) {
                if (record.getDdaRefNo() != null && record.getDdaRefNo().equals(nak_ddCancellationRecord.getDdRefNumber())
                        && record.getDirectDebitFile() != null && record.getDirectDebitFile().getDdStatus().equals(DirectDebitStatus.PENDING_FOR_CANCELLATION)
                        && record.getCbStatus() != null && record.getCbStatus().equals(BankDirectDebitCancelationRecord.CB_STATUS_ACK)) {
                    addNakRecord = false;
                    break;
                }
            }

            if (addNakRecord) {
                filteredNakRecords.add(nak_ddCancellationRecord);
            }
        }

        UnsuccessfulDDsCancellationReport.sendMail(filteredNakRecords);

    }*/

    @PreAuthorize("hasPermission('bddcancelationfiles','list')")
    @RequestMapping(value = "/projectedlist", method = RequestMethod.GET)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    public ResponseEntity<?> projectedList(Pageable pageable) {

        SelectQuery<BankDirectDebitCancelationFile> query =
                new SelectQuery<>(BankDirectDebitCancelationFile.class);
        query.filterBy("hidden", "=", false);
        //Jirra ACC-404
        query.sortBy("creationDate", false, false);

        Page page = query.execute(pageable);

        List<BankDirectDebitCancelationFileProjection> projectedList =  ((List<BankDirectDebitCancelationFile>) page.getContent())
                .stream().map(
                        obj -> projectionFactory.createProjection(
                                BankDirectDebitCancelationFileProjection.class, obj))
                .collect(Collectors.toList());

        return new ResponseEntity(new PageImpl(projectedList, pageable, page.getTotalElements()), HttpStatus.OK);
    }

    // ACC-2909
    @PreAuthorize("hasPermission('bddcancelationfiles','rpa/confirmdd')")
    @RequestMapping(value = "/rpa/confirmdd/{fileId}", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity confirmDD_RPA(@PathVariable("fileId") BankDirectDebitCancelationFile file) {
        List<BankDirectDebitCancelationRecord> matchedRecords =
                directDebitCancellationService.getBankCancellationRecords(file, RecordMatched.MATCHED, null)
                        .getContent();
        List<BankDirectDebitCancelationRecord> unMatchedRecords =
                directDebitCancellationService.getBankCancellationRecords(file, RecordMatched.NOT_MATCHED, null)
                        .getContent();

        String mail = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_DDS_CANCELLATION_RPA_MAIL);

        if (matchedRecords != null) {
            confirmDDs(matchedRecords.stream()
                    .map(record -> record.getId())
                    .collect(Collectors.toList()), true);
        }

        file.setConfirmedByRPA(true);
        getRepository().save(file);

        Integer matchedCount = matchedRecords.size();
        Integer unMatchedCount = unMatchedRecords.size();

        Map<String, String> parameters = new HashMap();
        String fileLink = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_FRONT_END_URL) + "#!/" +
                "accounting/importing-bank-dd-cancellation-file/" + file.getId();

        parameters.put("file_link", fileLink);
        parameters.put("matched", matchedCount.toString());
        parameters.put("matched_needs_action", matchedCount > 0 ? " (Already confirmed by RPA no action required)" : "");
        parameters.put("unmatched", unMatchedCount.toString());
        parameters.put("unmatched_needs_action", unMatchedCount > 0 ? " (Needs your action)" : "");

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff("dd_cancellation_rpa",
                        parameters, mail,
                        "New (DD 400) file has been uploaded and proceed by RPA on " + DateUtil.formatDateDashedV2(new Date()));

        return okResponse();
    }

    @PreAuthorize("hasPermission('bddcancelationfiles','confirmdd')")
    @PostMapping(value = "/confirmdd")
    @ResponseBody
    @Transactional
    @JsonView(ViewScope.Normal.class)
    public ResponseEntity<?> confirmDDs(
            @RequestBody List<Long> ids,
            @RequestParam(value = "fromRPA", defaultValue = "false") boolean fromRPA) {

        if (ids == null || ids.isEmpty()) return okResponse();

        List<BankDirectDebitCancelationRecord> records = bankDirectDebitCancelationRecordRepository.findAll(ids);
        logger.log(Level.SEVERE, "Enter confirmAllDd API for " + records.size() + " records");

        SelectQuery<BackgroundTask> query = new SelectQuery<>(BackgroundTask.class);
        query.filterBy("name", "=", "BankDirectDebitCancellationFile_Confirm_DD_" + records.get(0).getBankDirectDebitCancelationFile().getId());
        query.filterBy("status", "NOT IN", Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed));
        List<BackgroundTask> result = query.execute();

        if (result != null && !result.isEmpty())
            throw new RuntimeException("This feature is requested before and still processing.");

        Long userId = CurrentRequest.getUser().getId();
        String error = "";
        for (BankDirectDebitCancelationRecord r : records) {
            // ACC-2885
            if (r.getCbStatus().equals(BankDirectDebitCancelationRecord.CB_STATUS_NAK)) continue;

            // ACC-5520
            boolean proceed = r.getDirectDebitFile() == null || directDebitCancellationService
                    .validateDirectDebitCancellation(r.getDirectDebitFile());
            if(!proceed) {
                error += r.getDirectDebitFile().getApplicationId() + ", ";
                continue;
            }
            backgroundTaskHelper.createBGTForConfirmOneDD(
                    new HashMap<String, Object>() {{
                        put("id", r.getId());
                        put("status", r.getStatus());
                        put("fileId", r.getBankDirectDebitCancelationFile().getId());
                    }},
                    fromRPA,
                    userId);
        }

        if(!error.isEmpty()) {
            throw new RuntimeException("DD '" + error + "' are under RPA process, they cannot be cancelled");
        }

        return new ResponseEntity("confirmation requested, dd will be updated.", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('bddcancelationfiles','confirmAlldd')")
    @PostMapping(value = "/confirmAlldd")
    @Transactional
    @JsonView(ViewScope.Normal.class)
    public ResponseEntity<?> confirmAlldd(
            @RequestBody BankDirectDebitCancelationFile file) {

        SelectQuery<BackgroundTask> query = new SelectQuery<>(BackgroundTask.class);
        query.filterBy("name", "=", "BankDirectDebitCancellationFile_Confirm_DD_" + file.getId());
        query.filterBy("status", "NOT IN",
                Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed));
        List<BackgroundTask> result = query.execute();
        if (result != null && !result.isEmpty())
            throw new RuntimeException("This feature is requested before and still processing.");

        List<BankDirectDebitCancelationRecord> records = bankDirectDebitCancelationRecordRepository
                .findByBankDirectDebitCancelationFile(file);
        logger.info("confirmAlldd for " + records.size() + " records");

        Long userId = CurrentRequest.getUser().getId();
        String error = "";
        for (BankDirectDebitCancelationRecord r : records) {
            // ACC-5520
            boolean proceed = r.getDirectDebitFile() == null || directDebitCancellationService
                    .validateDirectDebitCancellation(r.getDirectDebitFile());
            if(!proceed) {
                error += r.getDirectDebitFile().getApplicationId() + ", ";
                continue;
            }
            backgroundTaskHelper.createBGTForConfirmOneDD(
                    new HashMap<String, Object>() {{
                        put("id", r.getId());
                        put("status", r.getStatus());
                        put("fileId", r.getBankDirectDebitCancelationFile().getId());
                    }},
                    false,
                    userId);
        }

        if(!error.isEmpty()) {
            throw new RuntimeException("DD '" + error + "' are under RPA process, they cannot be cancelled");
        }

        try {
            Thread.sleep(5 * 1000);
        } catch (Exception e) {

        }

        return new ResponseEntity("confirmation requested, dd will be updated.", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('bddcancelationfiles','dismissAlldd')")
    @PostMapping(value = "/dismissAlldd")
    @Transactional
    @JsonView(ViewScope.Normal.class)
    public ResponseEntity<?> dismissAlldd(
            @RequestBody BankDirectDebitCancelationFile bankDirectDebitCancelationFile) {

        List<BankDirectDebitCancelationRecord> toDelete = new ArrayList<>();
        List<BankDirectDebitCancelationRecord> records = bankDirectDebitCancelationRecordRepository
                .findByBankDirectDebitCancelationFile(bankDirectDebitCancelationFile);

        for (BankDirectDebitCancelationRecord r : records) {
            if (r.getDirectDebitFile() == null ||
                    r.getDirectDebitFile().getDdStatus().equals(DirectDebitStatus.CONFIRMED) ||
                    r.getDirectDebitFile().getDdStatus().equals(DirectDebitStatus.REJECTED)) {

                toDelete.add(r);
            }
        }
        bankDirectDebitCancelationRecordRepository.delete(toDelete);

        return new ResponseEntity<>("", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('bddcancelationfiles','get')")
    @GetMapping(value = "/getrecords/{id}")
    @Searchable(fieldName = "directDebitFile.directDebit.contractPaymentTerm.contract.client.name",
            label = "Family Name",
            entity = BankDirectDebitCancelationRecord.class,
            apiKey = "importing_bank_direct_debit_cancelation_file")
    public ResponseEntity<?> getRecordsAPI(
            @PathVariable("id") BankDirectDebitCancelationFile file,
            @RequestParam(required = false, value = "matched") RecordMatched matched,
            Pageable pageable) {

        return ResponseEntity.ok(directDebitCancellationService.getBankCancellationRecords(file, matched, pageable)
                .map(obj -> projectionFactory.createProjection(
                        BankDirectDebitCancelationRecordProjection.class, obj)));
    }

    @PreAuthorize("hasPermission('bddcancelationfiles','get')")
    @GetMapping(value = "/getrecords/csv/{id}")
    public void getRecordsCSV(
            HttpServletResponse response,
            @PathVariable("id") BankDirectDebitCancelationFile file,
            @RequestParam(required = false, value = "matched") RecordMatched matched,
            Sort sort,
            @RequestParam(name = "limit", required = false) Integer limit) {

        SelectQuery<BankDirectDebitCancelationRecord> query =
                new SelectQuery<>(BankDirectDebitCancelationRecord.class);
        query.leftJoin("directDebitFile");
        query.filterBy(CurrentRequest.getSearchFilter());
        query.filterBy("bankDirectDebitCancelationFile", "=", file);
        List<String> namesOrdared = Arrays.asList(
                "clientName", "contract", "amount", "presentmentDate", "startDate", "expiryDate", "status");
        if (matched == null)
            matched = RecordMatched.MATCHED;
        switch (matched) {
            case MATCHED:
                query.filterBy("directDebitFile", "IS NOT NULL", null);
                query.filterBy("directDebitFile.ddStatus", "=", DirectDebitStatus.PENDING_FOR_CANCELLATION);
                query.filterBy("status", "=", BankDirectDebitCancelationRecord.status.CONFIRMED);
                break;
            case NOT_MATCHED:
                namesOrdared = Arrays.asList("ddaRefNoCsv", "cancelReason", "status");
                query.filterBy(
                        new SelectFilter()
                                .or("directDebitFile", "IS NULL", null)
                                .or("directDebitFile.ddStatus", "=", DirectDebitStatus.CONFIRMED)
                                .or("directDebitFile.ddStatus", "=", DirectDebitStatus.REJECTED));
                break;
            case PREV_MATCHED:
                query.filterBy("directDebitFile", "IS NOT NULL", null);
                query.filterBy(new SelectFilter("directDebitFile.ddStatus", "=", DirectDebitStatus.CANCELED).or(
                        new SelectFilter("confirmed", "=", true)
                                .and("status", "=", BankDirectDebitCancelationRecord.status.REJECTED)));
                break;
            case MATCHED_AND_REJECTED:
                query.filterBy("directDebitFile", "IS NOT NULL", null);
                query.filterBy("directDebitFile.ddStatus", "=", DirectDebitStatus.PENDING_FOR_CANCELLATION);
                query.filterBy("status", "=", BankDirectDebitCancelationRecord.status.REJECTED);
                query.filterBy("confirmed", "=", false);
        }

        //Sorting
        if (sort != null) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        }
        
        InputStream is = null;
        try {
            if (limit == null) limit = 1000;
            is = generateCsv(query, BankDirectDebitCancelationRecordCsvProjection.class, (String[]) namesOrdared.toArray(), limit);
            
            createDownloadResponse(response,
                    matched.toString() + "DD_Records.csv",
                    is);
        } catch (IOException ex) {
            logger.log(Level.SEVERE,
                    ex.getMessage(),
                    ex);
        } finally {
            StreamsUtil.closeStream(is);
        }
    }

    public enum RecordMatched {
        MATCHED, NOT_MATCHED, PREV_MATCHED, MATCHED_AND_REJECTED
    }

    @UsedBy(others = UsedBy.Others.Rpa)
    @JwtSecured
    @GetMapping(value = "/allowAddNewCancellationFileFromRpa")
    public boolean allowAddNewCancellationFileFromRpa() {

        return !QueryService.existsEntity(BackgroundTask.class, "e.name in :p0 and e.status not in :p1",
                new Object[]{
                        Arrays.asList(
                                UploadStatementEntityType.BankDirectDebitCancelationFileByRPA.toString(),
                                UploadStatementEntityType.BankDirectDebitCancelationFile.toString()),
                        Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed)}) &&

                !QueryService.existsEntity(BackgroundTask.class, "e.name like :p0 and e.status not in :p1",
                        new Object[]{"BankDirectDebitCancellationFile_Confirm_DD_%",
                                Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed)}) &&

                !QueryService.existsEntity(AccountingEntityProperty.class, "e.key = :p0 and e.purpose = :p1",
                        new Object[]{AccountingModule.RUN_BACKGROUND_TASK_IN_SEQUENTIAL,
                                UploadStatementEntityType.BankDirectDebitCancelationFileByRPA.toString()});
    }
}
