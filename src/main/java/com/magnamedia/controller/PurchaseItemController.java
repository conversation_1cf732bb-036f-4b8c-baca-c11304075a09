package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.PurchaseItem;
import com.magnamedia.repository.PurchaseItemRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <PERSON> (Feb 02, 2021)
 */
@RestController
@RequestMapping("/purchase-item")
public class PurchaseItemController extends BaseRepositoryController<PurchaseItem> {
    @Autowired
    PurchaseItemRepository purchaseItemRepository;

    @Override
    public BaseRepository<PurchaseItem> getRepository() {
        return purchaseItemRepository;
    }
}
