package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.Client;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;

public interface DirectDebitGenerationPlanProjection {

    Long getId();

    Date getDDSendDate();

    Date getDDExpiryDate();

    Date getDDGenerationDate();

    @Value("#{target.getAmount().intValue()}")
    int getAmount();

    @Value("#{target.getPaymentType().getName()}")
    String getDDType();

    @JsonSerialize(using = IdLabelSerializer.class)
    Client getClient();

    @Value("#{target.getContract().getId()}")
    Long getContractId();

}
