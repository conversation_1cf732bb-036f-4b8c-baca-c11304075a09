package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.entity.workflow.DirectDebitCancelationToDo;
import com.magnamedia.module.type.DirectDebitCancellationToDoReason;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.repository.DirectDebitCancelationToDoRepository;
import com.magnamedia.service.SwitchingNationalityService;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Nov 27, 2019
 *         Jirra ACC-1135
 *         revised flow
 *         Jirra ACC-1587 move BR from being on DD to be on DDF
 *         this BR should alaways be BeforeUpdate update because it's related to DirectDebitFileBusinessRule
 */

//ask osamah
@BusinessRule(moduleCode = "", entity = DirectDebitFile.class,
        events = {BusinessEvent.BeforeUpdate},
        fields = {"id", "ddStatus", "applicationId", "directDebit.id", "ignoreDDRejectionFlow"})
public class DirectDebitFileCancelationToDoBusinessRule implements BusinessAction<DirectDebitFile> {

    private static final Logger logger =
            Logger.getLogger(DirectDebitFileCancelationToDoBusinessRule.class.getName());

    @Override
    public boolean validate(DirectDebitFile entity, BusinessEvent event) {
        logger.log(Level.SEVERE, "DirectDebitFileCancelationToDoBusinessRule Validation.");

        DirectDebitCancelationToDoRepository dDCancelationToDoRepository = Setup.getRepository(DirectDebitCancelationToDoRepository.class);
        List<DirectDebitCancelationToDo> dDCancelationToDos = dDCancelationToDoRepository.findByDirectDebitFileAndCompletedFalseAndStoppedFalse(entity);
        SwitchingNationalityService switchingNationalityService = Setup.getApplicationContext().getBean(SwitchingNationalityService.class);

        if (dDCancelationToDos == null || dDCancelationToDos.size() != 1) return false;

        DirectDebitCancelationToDo ddCToDo = dDCancelationToDos.get(0);
        //Jirra ACC-2800
        if ((entity.getIgnoreDDRejectionFlow() == null || !entity.getIgnoreDDRejectionFlow()) &&
                ddCToDo.getHidden() != null && ddCToDo.getHidden() &&
                entity.getDdStatus() != null && entity.getDdStatus().equals(DirectDebitStatus.REJECTED) &&
                ddCToDo.getReason() != null &&
                (Arrays.asList(DirectDebitCancellationToDoReason.SWITCHING_TO_VAT, DirectDebitCancellationToDoReason.SWITCHING_NATIONALITY).contains(ddCToDo.getReason())) &&
                ddCToDo.getDirectDebit() != null &&
                switchingNationalityService.doesDDCoverSwitchingPeriod(ddCToDo.getDirectDebit().getId())) {
            logger.log(Level.SEVERE, "DD Cancellation todo related to Switching Nationality");
            return false;
        }

        HistorySelectQuery<DirectDebitFile> historyQuery = new HistorySelectQuery(DirectDebitFile.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.filterByChanged("ddStatus");
        historyQuery.sortBy("lastModificationDate", false, true);

        List<DirectDebitFile> oldDirectDebitFiles = historyQuery.execute();

        if (oldDirectDebitFiles == null || oldDirectDebitFiles.isEmpty()) return false;

        DirectDebitFile oldDirectDebitFile = oldDirectDebitFiles.get(0);
        return oldDirectDebitFile.getDdStatus() != null && entity.getDdStatus() != null &&
                ((!oldDirectDebitFile.getDdStatus().equals(DirectDebitStatus.REJECTED) &&
                        entity.getDdStatus().equals(DirectDebitStatus.REJECTED))

                        || (!oldDirectDebitFile.getDdStatus().equals(DirectDebitStatus.CANCELED) &&
                        entity.getDdStatus().equals(DirectDebitStatus.CANCELED)));
    }

    @Override
    public Map<String, Object> execute(DirectDebitFile entity, BusinessEvent even) {
        logger.log(Level.SEVERE, "DirectDebitFileCancelationToDoBusinessRule execute.");
        Map<String, Object> map = new HashMap();
        DirectDebitCancelationToDoRepository dDCancelationToDoRepository = Setup.getRepository(DirectDebitCancelationToDoRepository.class);

        DirectDebitCancelationToDo dDCancelationToDo = dDCancelationToDoRepository.findByDirectDebitFileAndCompletedFalseAndStoppedFalse(entity).get(0);
        dDCancelationToDo.setStopped(true);
        dDCancelationToDoRepository.save(dDCancelationToDo);

        if (entity.getDdStatus().equals(DirectDebitStatus.REJECTED)) {
            map.put("ddStatus", DirectDebitStatus.CANCELED);
        }
        
        return map;
    }
}