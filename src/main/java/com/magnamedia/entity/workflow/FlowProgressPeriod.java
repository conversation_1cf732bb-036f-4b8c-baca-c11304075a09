package com.magnamedia.entity.workflow;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

/**
 *
 * <AUTHOR>
 */

@Entity
public class FlowProgressPeriod extends BaseEntity {
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private FlowSubEventConfig flowSubEventConfig;
    
    private int trials;
    private int reminders;
    private int periodInHours;

    public FlowSubEventConfig getFlowSubEventConfig() {
        return flowSubEventConfig;
    }

    public void setFlowSubEventConfig(FlowSubEventConfig flowSubEventConfig) {
        this.flowSubEventConfig = flowSubEventConfig;
    }

    public int getTrials() {
        return trials;
    }

    public void setTrials(int trials) {
        this.trials = trials;
    }

    public int getReminders() {
        return reminders;
    }

    public void setReminders(int reminders) {
        this.reminders = reminders;
    }

    public int getPeriodInHours() {
        return periodInHours;
    }

    public void setPeriodInHours(int periodInHours) {
        this.periodInHours = periodInHours;
    }
}
