package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import java.sql.Date;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Apr 29, 2020
 *         Jirra ACC-1721
 */

@Entity
public class BouncedPaymentLog extends BaseEntity {

    @ManyToOne(optional = false)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Payment payment;

    @ManyToOne
    @JsonSerialize(using = IdLabelSerializer.class)
    private DirectDebitFile directDebitFile;

    @Column
    private Date dateOfBouncing;

    @Column
    private String bouncingReason;

    public Payment getPayment() {
        return payment;
    }

    public void setPayment(Payment payment) {
        this.payment = payment;
    }

    public DirectDebitFile getDirectDebitFile() {
        return directDebitFile;
    }

    public void setDirectDebitFile(DirectDebitFile directDebitFile) {
        this.directDebitFile = directDebitFile;
    }

    public Date getDateOfBouncing() {
        return dateOfBouncing;
    }

    public void setDateOfBouncing(Date dateOfBouncing) {
        this.dateOfBouncing = dateOfBouncing;
    }

    public String getBouncingReason() {
        return bouncingReason;
    }

    public void setBouncingReason(String bouncingReason) {
        this.bouncingReason = bouncingReason;
    }
}
