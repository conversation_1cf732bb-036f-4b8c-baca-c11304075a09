package com.magnamedia.extra;

import com.magnamedia.entity.Contract;
import com.magnamedia.module.type.ContractStatus;

import java.util.Arrays;
import java.util.List;

/**
 * Created by Ma<PERSON>.Masod on 3/4/2021.
 */
public class ContractScheduleForTerminationUtils {

    private static List<String> getDDRejectionFlowTerminationReasons() {
        return Arrays.asList("direct_debit_rejection_type_a_maxbankinfotrials_reached".toLowerCase(),
                "direct_debit_rejection_type_a_maxsignaturetrials_reached".toLowerCase(),
                "direct_debit_rejection_type_b_bank_info_max_trials_reached".toLowerCase(),
                "direct_debit_rejection_type_b_maxsignaturetrialsb_reached".toLowerCase(),
                "client_did_not_provide_new_info_after_rejection".toLowerCase()
        );
    }

    public static boolean isTerminationReasonDueBouncedPayment(String reason) {
        return reason.equalsIgnoreCase("Due_bounced_payment");
    }

    public static boolean isTerminationReasonDueDDRejectionFlow(String reason) {
        return getDDRejectionFlowTerminationReasons().contains(reason.toLowerCase());
    }

    public static boolean isTerminationReasonDueInCompleteDDFlow(String reason) {
        return reason.equalsIgnoreCase("client_did_not_sign_dd_after_x_days");
    }

    public static boolean isTerminationReasonDueDDRejectionOrInCompleteFlows(String reason) {
        return isTerminationReasonDueDDRejectionFlow(reason) || isTerminationReasonDueInCompleteDDFlow(reason);
    }

    // ACC-7272
    public static boolean isTerminationReasonDueClientPayingViaCreditCardFlows(Contract c) {

        return c.getScheduledDateOfTermination() != null &&
                c.getReasonOfTerminationList() != null &&
                c.getReasonOfTerminationList().getCode().equals(Contract.DUE_PAYING_VIA_CC_FLOW_TERMINATION_REASON);
    }
}
