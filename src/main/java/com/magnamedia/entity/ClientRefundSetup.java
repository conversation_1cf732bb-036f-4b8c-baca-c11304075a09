

package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.controller.ClientRefundSetupController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.IdLabelListSerializer;
import com.magnamedia.entity.serializer.PaymentRequestPurposeSerializer;
import com.magnamedia.entity.workflow.PaymentRequestPurpose;
import com.magnamedia.repository.ClientRefundSetupRepository;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.*;
import java.util.List;

/**
 * <AUTHOR>
 *         Created on Dec 05, 2020
 *         ACC-2843
 */

@Entity
@Table(name = "CLIENT_REFUND_SETUP")
public class ClientRefundSetup extends BaseEntity {

    @OneToOne(fetch = FetchType.EAGER)
    @JsonSerialize(using = PaymentRequestPurposeSerializer.class)
    private PaymentRequestPurpose paymentRequestPurpose;

    @JsonSerialize(using = IdLabelListSerializer.class)
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "CLIENT_REFUND_SETUPS_REQUESTED_USERS",
            joinColumns = @JoinColumn(
                    name = "SETUP_ID",
                    referencedColumnName = "ID"),
            inverseJoinColumns = @JoinColumn(
                    name = "USER_ID",
                    referencedColumnName = "ID")
    )
    private List<User> requestedBy;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private User approvedBy;

    @Column(nullable = false)
    private Boolean linkComplaint;

    @Column(nullable = false)
    private Boolean linkWithAllComplaints;

    @JsonSerialize(using = IdLabelListSerializer.class)
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "CLIENT_REFUND_SETUPS_COMPLAINT_TYPES",
            joinColumns = @JoinColumn(
                    name = "SETUP_ID",
                    referencedColumnName = "ID"),
            inverseJoinColumns = @JoinColumn(
                    name = "COMPLAINT_TYPE_ID",
                    referencedColumnName = "ID")
    )
    private List<ComplaintType> complaintTypes;

    @Column(nullable = false)
    private Boolean validateClientBank;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "CLIENT_REFUND_SETUPS_BANKS",
            joinColumns = @JoinColumn(
                    name = "SETUP_ID",
                    referencedColumnName = "ID"),
            inverseJoinColumns = @JoinColumn(
                    name = "BANK_ID",
                    referencedColumnName = "ID")
    )
    private List<PicklistItem> banks;

    @Column
    private Boolean autoApproved = false;

    @Column
    private Boolean allowMonthlyRefunds = false;

    @Column
    private Boolean requireAttachment = false;

    @ColumnDefault("false")
    private boolean checkCeoLimit = Boolean.FALSE;

    @Column
    private Integer limitForCeoApproval;

    @ColumnDefault("false")
    private boolean hidden;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem partialRefundForCancellationPaymentMethod;

    public PaymentRequestPurpose getPaymentRequestPurpose() {
        return paymentRequestPurpose;
    }

    public void setPaymentRequestPurpose(PaymentRequestPurpose paymentRequestPurpose) {
        this.paymentRequestPurpose = paymentRequestPurpose;
    }

    public List<User> getRequestedBy() {
        return requestedBy;
    }

    public void setRequestedBy(List<User> requestedBy) {
        this.requestedBy = requestedBy;
    }

    public User getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(User approvedBy) {
        this.approvedBy = approvedBy;
    }

    public Boolean getLinkComplaint() {
        return linkComplaint;
    }

    public void setLinkComplaint(Boolean linkComplaint) {
        this.linkComplaint = linkComplaint;
    }

    public Boolean getLinkWithAllComplaints() {
        return linkWithAllComplaints;
    }

    public void setLinkWithAllComplaints(Boolean linkWithAllComplaints) {
        this.linkWithAllComplaints = linkWithAllComplaints;
    }

    public List<ComplaintType> getComplaintTypes() {
        if (linkWithAllComplaints != null && linkWithAllComplaints) {
            ClientRefundSetupController clientRefundSetupController = Setup.getApplicationContext().getBean(ClientRefundSetupController.class);
            return clientRefundSetupController.getComplaintTypes();
        }

        return complaintTypes;
    }

    public void setComplaintTypes(List<ComplaintType> complaintTypes) {
        this.complaintTypes = complaintTypes;
    }

    public Boolean getValidateClientBank() {
        return validateClientBank;
    }

    public void setValidateClientBank(Boolean validateClientBank) {
        this.validateClientBank = validateClientBank;
    }

    public List<PicklistItem> getBanks() {
        return banks;
    }

    public void setBanks(List<PicklistItem> banks) {
        this.banks = banks;
    }

    public Boolean getAutoApproved() {
        return autoApproved;
    }

    public void setAutoApproved(Boolean autoApproved) {
        this.autoApproved = autoApproved;
    }

    public Boolean getAllowMonthlyRefunds() {
        return allowMonthlyRefunds;
    }

    public void setAllowMonthlyRefunds(Boolean allowMonthlyRefunds) {
        this.allowMonthlyRefunds = allowMonthlyRefunds;
    }

    public Boolean getRequireAttachment() {
        return requireAttachment != null && requireAttachment;
    }

    public void setRequireAttachment(Boolean requireAttachment) {
        this.requireAttachment = requireAttachment;
    }

    public boolean isCheckCeoLimit() {
        return checkCeoLimit;
    }

    public void setCheckCeoLimit(boolean checkCeoLimit) {
        this.checkCeoLimit = checkCeoLimit;
    }

    public Integer getLimitForCeoApproval() {
        return limitForCeoApproval;
    }

    public void setLimitForCeoApproval(Integer limitForCeoApproval) {
        this.limitForCeoApproval = limitForCeoApproval;
    }

    public boolean isHidden() {
        return hidden;
    }

    public void setHidden(boolean hidden) {
        this.hidden = hidden;
    }

    @BeforeInsert
    @BeforeUpdate
    public void validateEntity() {
        ClientRefundSetupRepository clientRefundSetupRepository =
                Setup.getRepository(ClientRefundSetupRepository.class);
        Boolean existsBefore;

        if (this.getId() != null) {
            existsBefore = clientRefundSetupRepository
                    .existsBeforeByPaymentRequestPurpose(this.getId(),
                            this.getPaymentRequestPurpose(),
                            this.getPartialRefundForCancellationPaymentMethod() == null ? null :
                                    this.getPartialRefundForCancellationPaymentMethod().getId());
        } else {
            existsBefore = clientRefundSetupRepository
                    .existsBeforeByPaymentRequestPurpose(this.getPaymentRequestPurpose(),
                            this.getPartialRefundForCancellationPaymentMethod() == null ? null :
                                    this.getPartialRefundForCancellationPaymentMethod().getId());
        }

        if (existsBefore) {
            throw new RuntimeException("This purpose has setup before.");
        }

        if (this.getLinkComplaint() && (this.getComplaintTypes() == null || this.getComplaintTypes().size() == 0)) {
            throw new RuntimeException("Please choose complaint type.");
        }

        if (this.getValidateClientBank() && (this.getBanks() == null || this.getBanks().size() == 0)) {
            throw new RuntimeException("Please choose bank.");
        }

        if (!this.getLinkComplaint() && (this.getComplaintTypes() != null && this.getComplaintTypes().size() > 0)) {
            this.setComplaintTypes(null);
        }

        if (!this.getValidateClientBank() && (this.getBanks() != null && this.getBanks().size() > 0)) {
            this.setBanks(null);
        }

        if (this.getAutoApproved() && this.getApprovedBy() != null) {
            this.setApprovedBy(null);
        }
    }

    public boolean needsCeoApproval(Double amount){
        return this.checkCeoLimit && this.limitForCeoApproval != null && this.limitForCeoApproval >= amount;
    }

    public boolean doesNeedCeoApproval(Double amount) {
        return this.checkCeoLimit && this.limitForCeoApproval != null && amount >= this.limitForCeoApproval;
    }

    public PicklistItem getPartialRefundForCancellationPaymentMethod() {
        return partialRefundForCancellationPaymentMethod;
    }

    public void setPartialRefundForCancellationPaymentMethod(PicklistItem partialRefundForCancellationPaymentMethod) {
        this.partialRefundForCancellationPaymentMethod = partialRefundForCancellationPaymentMethod;
    }
}
