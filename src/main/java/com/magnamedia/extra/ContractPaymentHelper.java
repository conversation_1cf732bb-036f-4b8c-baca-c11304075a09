package com.magnamedia.extra;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.entity.ContractPayment;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.repository.PaymentRepository;
import org.joda.time.DateTime;

import java.util.List;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Dec 28, 2020
 */
public class ContractPaymentHelper {
    private final static Logger logger = Logger.getLogger(ContractPaymentHelper.class.getName());

    public static boolean doesMonthlyContractPaymentCoverDate(
            ContractPayment contractPayment,
            DateTime date,
            List<DirectDebitStatus> excludeStatuses) {

        if (contractPayment == null) return false;
        PicklistItem monthlyType = Setup.getItem("TypeOfPayment", "monthly_payment");

        if (!contractPayment.getPaymentType().equals(monthlyType) ||
                contractPayment.getDirectDebit() == null) {

            logger.info("CP: " + contractPayment.getId() + " IS not MONTHLY or has no DD");
            return false;
        }

        DirectDebit directDebit = contractPayment.getDirectDebit();

        boolean paymentReceived = Setup.getRepository(PaymentRepository.class)
                .paymentReceived(contractPayment.getContractPaymentTerm().getContract(),
                        monthlyType, date.toDate(), date.plusMonths(1).toDate());
        logger.info("paymentReceived: " + paymentReceived);

        if (!paymentReceived && directDebit.isStatusIn(excludeStatuses)) {
            logger.info("CP: " + contractPayment.getId() +
                    "; Related DD: " + directDebit.getId() + " IS " + directDebit.getMStatus());
            return false;
        }

        DateTime cpDate = new DateTime(contractPayment.getDate());
        if (cpDate.getYear() == date.getYear() && cpDate.getMonthOfYear() == date.getMonthOfYear()) {
            return true;
        }

        // if payment is Pro+1
        Boolean isProPlusMonth = contractPayment.getIsProRated() &&
                contractPayment.getContractPaymentTerm().getContract().getProRatedPlusMonth();
        if (isProPlusMonth &&
                ((cpDate.getYear() == date.getYear() && cpDate.getMonthOfYear() == (date.getMonthOfYear() - 1)) ||
                        (cpDate.getYear() == (date.getYear() - 1) && cpDate.getMonthOfYear() == 12 && date.getMonthOfYear() == 1))) {
            return true;
        }

        return false;
    }
}