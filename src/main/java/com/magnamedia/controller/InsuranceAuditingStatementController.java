package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.InsuranceAuditingRecord;
import com.magnamedia.entity.InsuranceAuditingSetup;
import com.magnamedia.entity.InsuranceAuditingStatement;
import com.magnamedia.entity.InsuranceBalanceLog;
import com.magnamedia.entity.projection.InsuranceAuditingRecordMatchedCSVProjection;
import com.magnamedia.entity.projection.InsuranceAuditingRecordUnmatchedCSVProjection;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.module.type.InsuranceAuditingStatementStatus;
import com.magnamedia.repository.InsuranceAuditingSetupRepository;
import com.magnamedia.repository.InsuranceAuditingStatementRepository;
import com.magnamedia.repository.InsuranceBalanceLogRepository;
import com.magnamedia.service.InsuranceStatementAuditingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2/11/2021
 */
@RequestMapping("/InsuranceAuditingStatement")
@RestController
public class InsuranceAuditingStatementController extends BaseRepositoryController<InsuranceAuditingStatement> {

    @Autowired
    private InsuranceBalanceLogRepository insuranceBalanceLogRepository;

    @Autowired
    private InsuranceAuditingStatementRepository repository;

    @Autowired
    private InsuranceAuditingSetupRepository setupRepository;

    @Autowired
    private InsuranceStatementAuditingService insuranceStatementAuditingService;

    @Transactional
    @PreAuthorize("hasPermission('InsuranceAuditingStatement','uploadStatement')")
    @RequestMapping(value = "/uploadStatement",
            method = RequestMethod.GET)
    public ResponseEntity<?> uploadStatement(
            @RequestParam("from") @DateTimeFormat(pattern = "yyyy-MM-dd") java.util.Date from,
            @RequestParam("to") @DateTimeFormat(pattern = "yyyy-MM-dd") java.util.Date to,
            @RequestParam Long attachmentId) {
        InsuranceAuditingStatement statement = new InsuranceAuditingStatement();
        statement.setFromDate(from);
        statement.setToDate(to);
        statement = repository.save(statement);
        statement = insuranceStatementAuditingService.audit(attachmentId, statement);
        return new ResponseEntity<>(statement, HttpStatus.OK);
    }
    @PreAuthorize("hasPermission('InsuranceAuditingStatement','updateBalance')")
    @RequestMapping(value = "/updateBalance",
            method = RequestMethod.GET)
    public ResponseEntity<?> updateBalance(
            @RequestParam Long statementId) {
        InsuranceAuditingStatement statement = repository.findOne(statementId);
        if(statement==null)
            throw new RuntimeException("statement is not exist");
        InsuranceAuditingSetup  setup = setupRepository.findAll().get(0);
        if(setup == null)
            throw new RuntimeException("insurance setup is not exist");
        InsuranceBalanceLog lastLog = insuranceBalanceLogRepository.findTopByOrderByLogDateDesc();
        if(lastLog == null)
            throw new RuntimeException("insurance log is empty");
        if(setup.getMin_tot()<= statement.getDifference() && setup.getMax_tot() >=statement.getDifference()){
            SimpleDateFormat dateFormat=new SimpleDateFormat("dd/MM/yyyy");

            InsuranceBalanceLog log=new InsuranceBalanceLog();
            log.setLogDate(new Date());
            log.setDescription("statement "+
                    dateFormat.format(statement.getFromDate())+
                    " to "+
                    dateFormat.format(statement.getToDate())
            );

            log.setAmount(statement.getInvoiceTotalAmount());
            log.setBalance(lastLog.getBalance()+statement.getInvoiceTotalAmount());
            log.setRefId(statementId);
            insuranceBalanceLogRepository.save(log);
            //update statement
            statement.setAmount(statement.getInvoiceTotalAmount());
            statement.setBalance(lastLog.getBalance()+statement.getInvoiceTotalAmount());
            // Jira ACC-4655
            statement.setStatus(InsuranceAuditingStatementStatus.UPDATED);
            repository.save(statement);
            return new ResponseEntity<>("balance updated",HttpStatus.OK);
        }else{
            throw new RuntimeException("statement difference should be between minTotal and maxTotal in setup");
        }
    }

    // ACC-4655
    @GetMapping(path = "/{id}/matchedRecords/csv")
    public void exportMatchedRecordsCSV(
            @PathVariable("id") InsuranceAuditingStatement insuranceAuditingStatement,
            @RequestParam("recordType") InsuranceAuditingRecord.recordType recordType,
            HttpServletResponse response) throws IOException {

        SelectQuery<InsuranceAuditingRecord> query = new SelectQuery(InsuranceAuditingRecord.class);
        query.filterBy("statement","=", insuranceAuditingStatement);
        query.filterBy("matchType", "=", InsuranceAuditingRecord.matchType.MATCHED);
        query.filterBy("type", "=", recordType);
        List<InsuranceAuditingRecord> records = query.execute();

        InputStream inputStream = generateCsv(records, InsuranceAuditingRecordMatchedCSVProjection.class);

        try{
            String filename = recordType.toString() + "-MATCHED-InsuranceAuditingRecords-" + insuranceAuditingStatement.getId() +".csv";
            createDownloadResponse(response, filename, inputStream);
        } finally {
            StreamsUtil.closeStream(inputStream);
        }
    }

    // ACC-4655
    @GetMapping(path = "/{id}/unmatchedRecords/csv")
    public void exportUnmatchedRecordsCSV(
            @PathVariable("id") InsuranceAuditingStatement insuranceAuditingStatement,
            HttpServletResponse response) throws IOException {

        SelectQuery<InsuranceAuditingRecord> query = new SelectQuery(InsuranceAuditingRecord.class);
        query.filterBy("statement","=", insuranceAuditingStatement);
        query.filterBy("matchType", "=", InsuranceAuditingRecord.matchType.UNMATCHED);
        List<InsuranceAuditingRecord> records = query.execute();

        InputStream inputStream = generateCsv(records, InsuranceAuditingRecordUnmatchedCSVProjection.class);

        try{
            String filename = "UNMATCHED-InsuranceAuditingRecords-" + insuranceAuditingStatement.getId() +".csv";
            createDownloadResponse(response, filename, inputStream);
        } finally {
            StreamsUtil.closeStream(inputStream);
        }
    }

    // ACC-4655
    @RequestMapping(path = "/{id}", method = RequestMethod.DELETE)
    public ResponseEntity<?> deleteInsuranceAuditingStatement(
            @PathVariable("id") InsuranceAuditingStatement insuranceAuditingStatement){

        if(!insuranceAuditingStatement.getStatus().equals(InsuranceAuditingStatementStatus.PENDING))
            return this.badRequestResponse();

        this.getRepository().delete(insuranceAuditingStatement);

        return this.okResponse();
    }

    @Override
    public BaseRepository<InsuranceAuditingStatement> getRepository() {
        return repository;
    }
}