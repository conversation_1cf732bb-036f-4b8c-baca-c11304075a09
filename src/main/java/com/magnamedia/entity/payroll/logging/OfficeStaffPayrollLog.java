package com.magnamedia.entity.payroll.logging;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.controller.PayrollAccountantTodoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.CooQuestion;
import com.magnamedia.entity.NewRequest;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.workflow.PayrollAccountantTodo;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.NumberFormatter;
import com.magnamedia.module.type.SalaryCurrency;
import com.magnamedia.repository.CooQuestionRepository;
import com.magnamedia.workflow.type.PayrollAccountantTodoManagerAction;
import com.magnamedia.workflow.type.PayrollAccountantTodoType;
import org.joda.time.LocalDate;

import javax.persistence.*;
import java.util.*;

@Entity
public class OfficeStaffPayrollLog extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private OfficeStaff officeStaff;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    PayrollAccountantTodo payrollAccountantTodo;

    @Column(columnDefinition = "boolean default false")
    private Boolean transferred = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean singleOfficeStaff = false;

    //Jirra ACC-3152
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private User managerActionBy;

    @Enumerated(EnumType.STRING)
    private PayrollAccountantTodoManagerAction managerAction = PayrollAccountantTodoManagerAction.PENDING;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private User ceoActionBy;

    @Enumerated(EnumType.STRING)
    private PayrollAccountantTodoManagerAction ceoAction = PayrollAccountantTodoManagerAction.PENDING;

    //Jirra ACC-3315
    @Column(columnDefinition = "boolean default false")
    private boolean doneByCoo = Boolean.FALSE;

    // Required for WPS file
    private String sn;
    private String recordType;
    private String employeeUniqueId;
    private String employeeName;
    private String agentId;
    private String employeeAccountWithAgent;
    private java.sql.Date payrollMonth;
    private Date payStartDate;
    private Date payEndDate;
    private int daysInPeriod;
    private Double totalSalary;

    // Required for international transfer file
    private String fullNameEnglish;
    private String fullNameArabic;
    private String destinationOfTransfer;
    private String mobileNumber;
    private String currency;
    private String rate;
    private Double amountInAED;
    private Double chargeVAT;
    
    @Column(columnDefinition = "double default 0")
    private Double totalInAED;

    // Required for local transfer file
    private String receiverName;

    // Required for bank transfer
    private String bankName;
    private String iban;
    private String accountHolderName;
    private String accountNumber;
    private String swiftCode;

    // Required for pension authority
    String idNumber;
    String routingCode = "*********";
    String routingCodeId = "AE008510";
    String beneficiaryBank = "GPSSA PENSION CONTR. And DISB.";
    Double contributionAmountPercentage = 17.5d;
    Double contributionAmount;
    Double employeeContributionAmountPercentage = 5d;
    Double employeeContributionAmount;
    Double employerContributionAmountPercentage = 12.5d;
    Double employerContributionAmount;

    @Lob
    private String rejectionNotes;

    public OfficeStaffPayrollLog() {
    }

    // Used for WPS file
    public OfficeStaffPayrollLog(OfficeStaff officeStaff, String recordType, String employeeName, NewRequest newRequest,
                                 Date payStartDate, Date payEndDate, Double totalSalary, java.sql.Date payrollMonth) {
        this.officeStaff = officeStaff;
        this.recordType = recordType;
        this.employeeName = employeeName;
        if (newRequest != null) {
            this.employeeUniqueId = newRequest.getEmployeeUniqueId();
            this.agentId = newRequest.getAgentId();
            this.employeeAccountWithAgent = newRequest.getEmployeeAccountWithAgent();
        }
        this.payStartDate = new LocalDate(payrollMonth).withDayOfMonth(1).toDate();
        this.payEndDate = new LocalDate(payrollMonth).dayOfMonth().withMaximumValue().toDate();
        this.daysInPeriod = DateUtil.getDaysBetween(this.payStartDate, this.payEndDate) + 1;
        this.totalSalary = totalSalary == null ? 0d : totalSalary;
        this.payrollMonth = new java.sql.Date(payrollMonth.getTime());
    }

    // Used for international transfer file
    public OfficeStaffPayrollLog(OfficeStaff officeStaff, String fullNameEnglish, String fullNameArabic, String destinationOfTransfer,
                                 String mobileNumber, Double totalSalary, String currency, java.sql.Date payrollMonth) {
        this.officeStaff = officeStaff;
        this.employeeName = officeStaff.getName();
        this.fullNameEnglish = fullNameEnglish;
        this.fullNameArabic = fullNameArabic;
        this.destinationOfTransfer = destinationOfTransfer;
        this.mobileNumber = mobileNumber;
        this.totalSalary = totalSalary == null ? 0d : totalSalary;
        this.currency = currency;
        this.payrollMonth = new java.sql.Date(payrollMonth.getTime());
    }

    //Used for local transfer file
    public OfficeStaffPayrollLog(OfficeStaff officeStaff, String receiverName, String destinationOfTransfer,
                                 String mobileNumber, Double totalSalary, java.sql.Date payrollMonth) {
        this.officeStaff = officeStaff;
        this.receiverName = receiverName;
        this.destinationOfTransfer = destinationOfTransfer;
        this.mobileNumber = mobileNumber;
        this.totalSalary = totalSalary == null ? 0d : totalSalary;
        this.payrollMonth = new java.sql.Date(payrollMonth.getTime());
    }

    // Used for bank transfer
    public OfficeStaffPayrollLog(OfficeStaff officeStaff, String fullNameEnglish, String fullNameArabic, String destinationOfTransfer, String mobileNumber, Double totalSalary, String currency,
                                 String iban, String accountHolderName, String accountNumber, String bankName, String swiftCode, java.sql.Date payrollMonth) {
        this.officeStaff = officeStaff;
        this.employeeName = officeStaff.getName();
        this.fullNameEnglish = fullNameEnglish;
        this.fullNameArabic = fullNameArabic;
        this.destinationOfTransfer = destinationOfTransfer;
        this.mobileNumber = mobileNumber;
        this.totalSalary = totalSalary == null ? 0d : totalSalary;
        this.currency = currency;
        this.iban = iban;
        this.accountHolderName = accountHolderName;
        this.accountNumber = accountNumber;
        this.bankName = bankName;
        this.swiftCode = swiftCode;
        this.payrollMonth = new java.sql.Date(payrollMonth.getTime());
    }


    public OfficeStaff getOfficeStaff() {
        return officeStaff;
    }

    public void setOfficeStaff(OfficeStaff officeStaff) {
        this.officeStaff = officeStaff;
    }

    public PayrollAccountantTodo getPayrollAccountantTodo() {
        return payrollAccountantTodo;
    }

    public void setPayrollAccountantTodo(PayrollAccountantTodo payrollAccountantTodo) {
        this.payrollAccountantTodo = payrollAccountantTodo;
    }

    public Boolean getTransferred() {
        return transferred;
    }

    public void setTransferred(Boolean transferred) {
        this.transferred = transferred;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getRecordType() {
        return recordType;
    }

    public void setRecordType(String recordType) {
        this.recordType = recordType;
    }

    public String getEmployeeUniqueId() {
        return employeeUniqueId;
    }

    public void setEmployeeUniqueId(String employeeUniqueId) {
        this.employeeUniqueId = employeeUniqueId;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getEmployeeAccountWithAgent() {
        return employeeAccountWithAgent;
    }

    public void setEmployeeAccountWithAgent(String employeeAccountWithAgent) {
        this.employeeAccountWithAgent = employeeAccountWithAgent;
    }

    public Date getPayStartDate() {
        return payStartDate;
    }

    public void setPayStartDate(Date payStartDate) {
        this.payStartDate = payStartDate;
    }

    public Date getPayEndDate() {
        return payEndDate;
    }

    public void setPayEndDate(Date payEndDate) {
        this.payEndDate = payEndDate;
    }

    public int getDaysInPeriod() {
        return daysInPeriod;
    }

    public void setDaysInPeriod(int daysInPeriod) {
        this.daysInPeriod = daysInPeriod;
    }

    public Double getTotalSalary() {
        return NumberFormatter.twoDecimalPoints(totalSalary);
    }

    public void setTotalSalary(Double totalSalary) {
        this.totalSalary = totalSalary;
    }

    public String getFullNameEnglish() {
        return fullNameEnglish;
    }

    public void setFullNameEnglish(String fullNameEnglish) {
        this.fullNameEnglish = fullNameEnglish;
    }

    public String getFullNameArabic() {
        return fullNameArabic;
    }

    public void setFullNameArabic(String fullNameArabic) {
        this.fullNameArabic = fullNameArabic;
    }

    public String getDestinationOfTransfer() {
        return destinationOfTransfer;
    }

    public void setDestinationOfTransfer(String destinationOfTransfer) {
        this.destinationOfTransfer = destinationOfTransfer;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    @JsonIgnore
    public String getForeignCurrency() {
        return this.officeStaff.getSalaryCurrency().toString();
    }

    public String getRate() {
        return rate;
    }

    public void setRate(String rate) {
        this.rate = rate;
    }

    public Double getAmountInAED() {
        return NumberFormatter.twoDecimalPoints(amountInAED);
    }

    public void setAmountInAED(Double amountInAED) {
        this.amountInAED = amountInAED;
    }

    public Double getChargeVAT() {
        return NumberFormatter.twoDecimalPoints(chargeVAT);
    }

    public void setChargeVAT(Double chargeVAT) {
        this.chargeVAT = chargeVAT;
    }

    public Double getTotalInAED() {
        return NumberFormatter.threeDecimalPoints(totalInAED);
    }

    public void setTotalInAED(Double totalInAED) {
        this.totalInAED = totalInAED;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getIban() {
        return iban;
    }

    public void setIban(String iban) {
        this.iban = iban;
    }

    public String getAccountHolderName() {
        return accountHolderName;
    }

    public void setAccountHolderName(String accountHolderName) {
        this.accountHolderName = accountHolderName;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getSwiftCode() {
        return swiftCode;
    }

    public void setSwiftCode(String swiftCode) {
        this.swiftCode = swiftCode;
    }

    public String getIdNumber() {
        return idNumber;
    }

    public void setIdNumber(String idNumber) {
        this.idNumber = idNumber;
    }

    public String getRoutingCode() {
        return routingCode;
    }

    public void setRoutingCode(String routingCode) {
        this.routingCode = routingCode;
    }

    public String getRoutingCodeId() {
        return routingCodeId;
    }

    public void setRoutingCodeId(String routingCodeId) {
        this.routingCodeId = routingCodeId;
    }

    public String getBeneficiaryBank() {
        return beneficiaryBank;
    }

    public void setBeneficiaryBank(String beneficiaryBank) {
        this.beneficiaryBank = beneficiaryBank;
    }

    public Double getContributionAmountPercentage() {
        return NumberFormatter.twoDecimalPoints(contributionAmountPercentage);
    }

    public void setContributionAmountPercentage(Double contributionAmountPercentage) {
        this.contributionAmountPercentage = contributionAmountPercentage;
    }

    public Double getContributionAmount() {
        return NumberFormatter.twoDecimalPoints(contributionAmount);
    }

    public void setContributionAmount(Double contributionAmount) {
        this.contributionAmount = contributionAmount;
    }

    public Double getEmployeeContributionAmountPercentage() {
        return NumberFormatter.twoDecimalPoints(employeeContributionAmountPercentage);
    }

    public void setEmployeeContributionAmountPercentage(Double employeeContributionAmountPercentage) {
        this.employeeContributionAmountPercentage = employeeContributionAmountPercentage;
    }

    public Double getEmployeeContributionAmount() {
        return NumberFormatter.twoDecimalPoints(employeeContributionAmount);
    }

    public void setEmployeeContributionAmount(Double employeeContributionAmount) {
        this.employeeContributionAmount = employeeContributionAmount;
    }

    public Double getEmployerContributionAmountPercentage() {
        return NumberFormatter.twoDecimalPoints(employerContributionAmountPercentage);
    }

    public void setEmployerContributionAmountPercentage(Double employerContributionAmountPercentage) {
        this.employerContributionAmountPercentage = employerContributionAmountPercentage;
    }

    public Double getEmployerContributionAmount() {
        return NumberFormatter.twoDecimalPoints(employerContributionAmount);
    }

    public void setEmployerContributionAmount(Double employerContributionAmount) {
        this.employerContributionAmount = employerContributionAmount;
    }

    public java.sql.Date getPayrollMonth() {
        return payrollMonth != null ? payrollMonth : new java.sql.Date(this.getCreationDate().getTime());
    }

    public void setPayrollMonth(java.sql.Date payrollMonth) {
        this.payrollMonth = payrollMonth;
    }

    public Boolean getSingleOfficeStaff() {
        return singleOfficeStaff;
    }

    public void setSingleOfficeStaff(Boolean singleOfficeStaff) {
        this.singleOfficeStaff = singleOfficeStaff;
    }

    public User getManagerActionBy() {
        return managerActionBy;
    }

    public void setManagerActionBy(User managerActionBy) {
        this.managerActionBy = managerActionBy;
    }

    public PayrollAccountantTodoManagerAction getManagerAction() {
        return managerAction;
    }

    public void setManagerAction(PayrollAccountantTodoManagerAction managerAction) {
        this.managerAction = managerAction;
    }

    public User getCeoActionBy() {
        return ceoActionBy;
    }

    public void setCeoActionBy(User ceoActionBy) {
        this.ceoActionBy = ceoActionBy;
    }

    public PayrollAccountantTodoManagerAction getCeoAction() {
        return ceoAction;
    }

    public void setCeoAction(PayrollAccountantTodoManagerAction ceoAction) {
        this.ceoAction = ceoAction;
    }

    public boolean isDoneByCoo() {
        return doneByCoo;
    }

    public void setDoneByCoo(boolean doneByCoo) {
        this.doneByCoo = doneByCoo;
    }

    public String getRejectionNotes() { return rejectionNotes; }

    public void setRejectionNotes(String rejectionNotes) { this.rejectionNotes = rejectionNotes; }

    @JsonIgnore
    public String getManagerUserName() {
        return getManagerActionBy() == null ? "" : getManagerActionBy().getUsername();
    }

    @JsonIgnore
    public String getAmount() {
        return totalSalary != null ? NumberFormatter.formatNumber(totalSalary) : "0";
    }

    @JsonIgnore
    public String getBeneficiaryName() {
        return this.accountHolderName;
    }

    @JsonIgnore
    public String getPensionAmount() {
        return contributionAmount != null ? NumberFormatter.formatNumber(contributionAmount) : "0.0";
    }

    public String getMonthYear() {
        return DateUtil.formatSimpleMonthYear(getPayrollMonth());
    }

    public String getPaymentMethod() {
        return this.getPayrollAccountantTodo() == null ? "" : getPayrollAccountantTodo().getTaskName();
    }

    //Jirra ACC-3152
    @JsonIgnore
    public String getTodoCategory() {
        return PayrollAccountantTodoController.ToDoCategory.OFFICE_STAFF_PAYROLL_LOG_TODO.toString();
    }

    //Jirra ACC-3152
    @JsonIgnore
    public String getDescription() {
        PayrollAccountantTodoType todoType = PayrollAccountantTodoType.valueOf(this.getPayrollAccountantTodo().getTaskName());
        String result = null;

        switch (todoType) {
            case PENSION_AUTHORITY:
                result = "Pension Authority Transfer for " + this.officeStaff.getName();
                break;

            case BANK_TRANSFER:
                result = this.officeStaff.getName() + " - Office Staff Payroll: " + getPayrollMonth();
                break;
        }

        return result;
    }

    @JsonIgnore
    public String getBeneficiary() {
        PayrollAccountantTodoType todoType = PayrollAccountantTodoType.valueOf(this.getPayrollAccountantTodo().getTaskName());
        String result = null;

        switch (todoType) {
            case PENSION_AUTHORITY:
                result = this.payrollAccountantTodo != null ? this.payrollAccountantTodo.getBeneficiary() : "";
                break;

            case BANK_TRANSFER:
                result = this.officeStaff != null && this.officeStaff.getSelectedTransferDestination() != null ? this.officeStaff.getSelectedTransferDestination().getName() : "";
                break;
        }

        return result;
    }

    @JsonIgnore
    public Double getAmountAED() {
        PayrollAccountantTodoType todoType = PayrollAccountantTodoType.valueOf(this.getPayrollAccountantTodo().getTaskName());

        Double result = null;
        switch (todoType) {
            case PENSION_AUTHORITY:
                result = this.contributionAmount;
                break;

            case BANK_TRANSFER:
                result = this.totalSalary != null && this.officeStaff != null && this.officeStaff.getSalaryCurrency().equals(SalaryCurrency.AED) ? this.totalSalary : null;
                break;
        }

        return result != null ? NumberFormatter.twoDecimalPoints(result) : null;
    }

    @JsonIgnore
    public Double getAmountInForeignCurrency() {
        PayrollAccountantTodoType todoType = PayrollAccountantTodoType.valueOf(this.getPayrollAccountantTodo().getTaskName());

        Double result = null;
        switch (todoType) {
            case BANK_TRANSFER:
                result = this.totalSalary != null && this.officeStaff != null && !this.officeStaff.getSalaryCurrency().equals(SalaryCurrency.AED) ? this.totalSalary : null;
                break;
        }

        return result != null ? NumberFormatter.twoDecimalPoints(result) : null;
    }

    @Transient
    private CooQuestion.QuestionedPage cooQuestionedPage;

    public CooQuestion.QuestionedPage getCooQuestionedPage() {
        return cooQuestionedPage;
    }

    public void setCooQuestionedPage(CooQuestion.QuestionedPage cooQuestionedPage) {
        this.cooQuestionedPage = cooQuestionedPage;
    }

    @JsonIgnore
    public List<CooQuestion> getCooQuestions() {
        if (this.cooQuestionedPage == null) return new ArrayList();

        return Setup.getRepository(CooQuestionRepository.class).findByRelatedEntityAndQuestionedPage(this, this.cooQuestionedPage);
    }

    @JsonIgnore
    public boolean isAllQuestionsAnswered() {
        if (this.cooQuestionedPage == null) return false;

        List<CooQuestion> cooQuestions = getCooQuestions();

        return !cooQuestions.isEmpty() && !cooQuestions.stream().anyMatch(q -> !q.isAnswered());
    }

    @JsonIgnore
    public boolean isOneQuestionAnswered() {
        if (this.cooQuestionedPage == null) return false;

        return getCooQuestions().stream().filter(q -> q.isAnswered()).count() >= 1l;
    }

    @JsonIgnore
    public boolean isNoneQuestionAnswered() {
        if (this.cooQuestionedPage == null) return false;

        return getCooQuestions().stream().filter(q -> q.isAnswered()).count() == 0l;
    }

    @JsonIgnore
    public Map<String, String> getBeneficiaryType() {
        return officeStaff != null ? getMapBeneficiaryType("EXPENSES_OFFICE_STAFF", officeStaff.getName()) :
                getMapBeneficiaryType("", "");
    }

    @JsonIgnore
    public Map<String, String> getMapBeneficiaryType(String beneficiaryType, String beneficiaryName) {
        return new HashMap<String, String>() {{
            put("beneficiaryType", beneficiaryType);
            put("beneficiaryName", beneficiaryName);
        }};
    }

    @JsonIgnore
    public Long getBeneficiaryTypeId() {
        return officeStaff != null ? officeStaff.getId() : null;
    }
}
