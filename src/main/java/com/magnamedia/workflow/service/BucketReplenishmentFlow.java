package com.magnamedia.workflow.service;

import com.magnamedia.core.helper.SearchField;
import com.magnamedia.core.workflow.RoleBasedWorkflow;
import com.magnamedia.entity.workflow.BucketReplenishmentTodo;
import com.magnamedia.workflow.service.bucketreplenishmentsteps.BucketReplenishmentApproveStep;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR> @date 1/21/2021
 */
@Service(value = "bucketReplenishmentFlow")
public class BucketReplenishmentFlow extends RoleBasedWorkflow<BucketReplenishmentTodo> {
    @Autowired
    BucketReplenishmentApproveStep bucketReplenishmentApproveStep;

    public BucketReplenishmentFlow(BucketReplenishmentApproveStep bucketReplenishmentApproveStep) {

        this.setId("bucketReplenishmentFlow");

        this.bucketReplenishmentApproveStep=bucketReplenishmentApproveStep;

        this.getWorkflowSteps().addAll(Arrays.asList(
                this.bucketReplenishmentApproveStep
        ));

        this.getWorkflowSteps().forEach((workflowStep) -> {
            this.idStepMap.put(workflowStep.getId(), workflowStep);
        });

    }


    @Override
    public List<SearchField> fillSearchFields() {
        List<SearchField> fields = new ArrayList();

        return fields;
    }

    @Override
    public Map<String, String> getTableHeader() {
        Map<String, String> tableHeader = new HashMap();
        return tableHeader;
    }

    @Override
    public Map<String, String> getTableColumnTypes() {
        Map<String, String> tableColumnTypes = new HashMap();

        return tableColumnTypes;
    }

    @Override
    public String getWorkflowHeader() {
        return "Bucket Replenishment Flow";
    }
}
