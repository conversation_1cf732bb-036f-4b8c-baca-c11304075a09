package com.magnamedia.workflow.entity.projection;

import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.workflow.Task;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.rest.core.config.Projection;
import java.sql.Date;
import java.util.List;

@Projection(name = "ClientRefundFlowCSVProjection", types = BaseEntity.class)
public interface ClientRefundFlowCSVProjection extends Task {

    @Value("#{target.getClient().getName()}")
    String getName();

    @Value("#{target.getPurpose() != null ? target.getPurpose().getCategoryStr() : ''}")
    String getCategory();

    @Value("#{target.getPurpose() != null ? target.getPurpose().getName() : ''}")
    String getPurpose();

    Double getAmount();

    @Value("#{target.getParent() != null ? target.getParent().getAmount() : 0 }")
    Double getMasterAmount();

    @Value("#{target.getStatus() != null ? target.getStatus().getValue() : ''}")
    String getStatus();
    // ACC-5818
    boolean isLenient();

    @Value("#{target.getLeniencyType() != null ? target.getLeniencyType().getName() : ''}")
    String getLeniencyType();

    @Value("#{target.getContract() != null ? target.getContract().isMaidCc ? 'CC' : 'MV' : ''}")
    String getContractType();

    @Value("#{target.getStatusChangeDate()}")
    Date getDate();

    String getNotes();

    @Value("#{target.getDisplayId()}")
    String getRequestID();

    @Value("#{target.getTransaction() != null ? target.getTransaction().getId() : ''}")
    Long getTransaction();

    @Value("#{(target.getRequesterUserName().equals(\"guest\") || target.getRequesterUserName().equals(\"admin\"))?"
            + " \"erp\" : target.getRequesterUserName()}")
    String getRequestedBy();

    @Value("#{target.getComplaintType() != null ? target.getComplaintType().getLabel() : ''}")
    String getComplaintType();

    @Value("#{ target.getAttachments().size() > 0  ?" +
            " T(com.magnamedia.core.Setup).getParameter(T(com.magnamedia.core.Setup).getCurrentModule(), 'backend_base_url') " +
            "+ \"/public/download/\" + target.getAttachments().get(0).getUuid() : 'No Attachments'}")
    String getAttachments();

}