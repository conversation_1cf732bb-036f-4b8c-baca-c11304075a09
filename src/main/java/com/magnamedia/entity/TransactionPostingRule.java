package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.CustomIdLabelCodeSerializer;
import com.magnamedia.entity.workflow.PaymentRequestPurpose;
import com.magnamedia.extra.TransactionPostingRulePostAs;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.TransactionPostingRuleRepository;
import com.magnamedia.workflow.type.PaymentRequestMethod;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.util.List;

/**
 * <AUTHOR> masod <<EMAIL>>
 *         Created on Dec 21, 2019
 *         ACC-1230
 */
@Entity
public class TransactionPostingRule extends BaseEntity {

    public static final String DUPLICATION_ERR_MSG = "a record with same data(company, type of payment, method of payment, is initial and payment status) already exists";
    private static final String REQUIRES_FIELDS_ERR_MSG = "company, type of payment, method of payment, post as and payment status are all required";
    private static final String DUPLICATION_ERR_MSG_PAYMENT_ORDER = "a record with same data(payment Order Status, purpose Of Payment, method of payment) already exists";
    private static final String REQUIRES_FIELDS_ERR_MSG_PAYMENT_ORDER = "payment Order Status, purpose Of Payment, method of payment and post are all required";

    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    @JsonSerialize(using = CustomIdLabelCodeSerializer.class)
    private PicklistItem company;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem typeOfPayment;

    @Enumerated(EnumType.STRING)
    private PaymentMethod methodOfPayment;

    @Enumerated(EnumType.STRING)
    private PaymentStatus paymentStatus;

    @Enumerated(EnumType.STRING)
    private TransactionPostingRulePostAs postAs;

    @ManyToOne
    @NotAudited
    private Bucket fromBucket;

    @ManyToOne
    @NotAudited
    private Bucket toBucket;

    @ManyToOne
    @NotAudited
    private Expense expense;

    @ManyToOne
    @NotAudited
    private Revenue revenue;

    @Column
    @Enumerated(EnumType.STRING)
    private VatType vatType;

    @Enumerated(EnumType.STRING)
    @Column(length = 32, columnDefinition = "varchar(32) default 'UNKNOWN'")
    private TransactionEntityType transactionFor;

    @Enumerated(EnumType.STRING)
    private PaymentMethod transactionMethodOfPayment;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem license;

    @Lob
    private String description;

    @Column
    private Boolean revenueNegativeAmount;

    @Column
    private Boolean sameAsPaymentNote;

    @Column
    @Enumerated(EnumType.STRING)
    private PostingEngineEntityType postingEngineEntityType;

    @Column
    @Enumerated(EnumType.STRING)
    private PaymentOrderStatus paymentOrderStatus;

    @JsonSerialize(using = IdLabelSerializer.class)
    @ManyToOne(fetch = FetchType.LAZY)
    private PaymentRequestPurpose purposeOfPayment;

    @Column
    @Enumerated(EnumType.STRING)
    private PaymentRequestMethod paymentOrderMethodOfPayment;

    //Jirra ACC-2991
    @Column(columnDefinition = "boolean default true")
    private boolean positiveVat = true;

    //Jirra ACC-3414
    @Column(columnDefinition = "boolean default false")
    private boolean initialPayment = false;

    @Column(columnDefinition = "boolean default true")
    private Boolean active = true;

    @BeforeUpdate
    @BeforeInsert
    public void checkIntegrity() {
        // check required fields
        if (postingEngineEntityType == PostingEngineEntityType.PAYMENT) {
            if (company == null || typeOfPayment == null || methodOfPayment == null || paymentStatus == null || postAs == null)
                throw new RuntimeException(REQUIRES_FIELDS_ERR_MSG);

            TransactionPostingRuleRepository repository = Setup.getRepository(TransactionPostingRuleRepository.class);

            // check duplicated data
            List<TransactionPostingRule> old = repository.findByCompanyAndTypeOfPaymentAndMethodOfPaymentAndInitialPaymentAndPaymentStatusAndActiveTrue
                    (company, typeOfPayment, methodOfPayment, initialPayment, paymentStatus);
            if (old != null && !old.isEmpty()) {
                if (!(this.getId() != null && old.size() == 1 && old.get(0).getId().equals(this.getId())))
                    throw new RuntimeException(DUPLICATION_ERR_MSG);
            }

            if ((postAs.equals(TransactionPostingRulePostAs.EXPENSE) && (expense == null || fromBucket == null)) ||
                    (postAs.equals(TransactionPostingRulePostAs.REVENUE) && (revenue == null || toBucket == null)))
                throw new RuntimeException("Source -> Target must be (revenue -> to bucket) or (from bucket -> expense)");

        } else if (postingEngineEntityType == PostingEngineEntityType.PAYMENT_ORDER) {

            transactionFor = TransactionEntityType.HOUSEMAID;
            paymentOrderMethodOfPayment = PaymentRequestMethod.CASH;

            if (paymentOrderStatus == null || purposeOfPayment == null || paymentOrderMethodOfPayment == null || postAs == null)
                throw new RuntimeException(REQUIRES_FIELDS_ERR_MSG_PAYMENT_ORDER);

            TransactionPostingRuleRepository repository = Setup.getRepository(TransactionPostingRuleRepository.class);

            // check duplicated data
            List<TransactionPostingRule> old = repository.findByPaymentOrderStatusAndPurposeOfPaymentAndPaymentOrderMethodOfPayment
                    (paymentOrderStatus, purposeOfPayment, paymentOrderMethodOfPayment);
            if (old != null && !old.isEmpty()) {
                if (!(this.getId() != null && old.size() == 1 && old.get(0).getId().equals(this.getId())))
                    throw new RuntimeException(DUPLICATION_ERR_MSG_PAYMENT_ORDER);
            }

            if ((postAs.equals(TransactionPostingRulePostAs.EXPENSE) && (expense == null || fromBucket == null)) ||
                    (postAs.equals(TransactionPostingRulePostAs.REVENUE) && (revenue == null || toBucket == null)))
                throw new RuntimeException("Source -> Target must be (revenue -> to bucket) or (from bucket -> expense)");

        } else {
            throw new RuntimeException("postingEngineEntityType is required");
        }
    }

    public PicklistItem getCompany() {
        return company;
    }

    public void setCompany(PicklistItem company) {
        this.company = company;
    }

    public PicklistItem getTypeOfPayment() {
        return typeOfPayment;
    }

    public void setTypeOfPayment(PicklistItem typeOfPayment) {
        this.typeOfPayment = typeOfPayment;
    }

    public PaymentMethod getMethodOfPayment() {
        return methodOfPayment;
    }

    public void setMethodOfPayment(PaymentMethod methodOfPayment) {
        this.methodOfPayment = methodOfPayment;
    }

    public PaymentStatus getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(PaymentStatus paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public TransactionPostingRulePostAs getPostAs() {
        return postAs;
    }

    public void setPostAs(TransactionPostingRulePostAs postAs) {
        this.postAs = postAs;
    }

    public Bucket getFromBucket() {
        return fromBucket;
    }

    public void setFromBucket(Bucket fromBucket) {
        this.fromBucket = fromBucket;
    }

    public Bucket getToBucket() {
        return toBucket;
    }

    public void setToBucket(Bucket toBucket) {
        this.toBucket = toBucket;
    }

    public Expense getExpense() {
        return expense;
    }

    public void setExpense(Expense expense) {
        this.expense = expense;
    }

    public Revenue getRevenue() {
        return revenue;
    }

    public void setRevenue(Revenue revenue) {
        this.revenue = revenue;
    }

    public VatType getVatType() {
        return vatType;
    }

    public void setVatType(VatType vatType) {
        this.vatType = vatType;
    }

    public TransactionEntityType getTransactionFor() {
        return transactionFor;
    }

    public void setTransactionFor(TransactionEntityType transactionFor) {
        this.transactionFor = transactionFor;
    }

    public PaymentMethod getTransactionMethodOfPayment() {
        return transactionMethodOfPayment;
    }

    public void setTransactionMethodOfPayment(PaymentMethod transactionMethodOfPayment) {
        this.transactionMethodOfPayment = transactionMethodOfPayment;
    }

    public PicklistItem getLicense() {
        return license;
    }

    public void setLicense(PicklistItem license) {
        this.license = license;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getRevenueNegativeAmount() {
        return revenueNegativeAmount != null && revenueNegativeAmount;
    }

    public void setRevenueNegativeAmount(Boolean revenueNegativeAmount) {
        this.revenueNegativeAmount = revenueNegativeAmount;
    }

    public Boolean getSameAsPaymentNote() {
        return sameAsPaymentNote;
    }

    public void setSameAsPaymentNote(Boolean sameAsPaymentNote) {
        this.sameAsPaymentNote = sameAsPaymentNote;
    }

    public PostingEngineEntityType getPostingEngineEntityType() {
        return postingEngineEntityType;
    }

    public void setPostingEngineEntityType(PostingEngineEntityType postingEngineEntityType) {
        this.postingEngineEntityType = postingEngineEntityType;
    }

    public PaymentOrderStatus getPaymentOrderStatus() {
        return paymentOrderStatus;
    }

    public void setPaymentOrderStatus(PaymentOrderStatus paymentOrderStatus) {
        this.paymentOrderStatus = paymentOrderStatus;
    }

    public PaymentRequestPurpose getPurposeOfPayment() {
        return purposeOfPayment;
    }

    public void setPurposeOfPayment(PaymentRequestPurpose purposeOfPayment) {
        this.purposeOfPayment = purposeOfPayment;
    }

    public PaymentRequestMethod getPaymentOrderMethodOfPayment() {
        return paymentOrderMethodOfPayment;
    }

    public void setPaymentOrderMethodOfPayment(PaymentRequestMethod paymentOrderMethodOfPayment) {
        this.paymentOrderMethodOfPayment = paymentOrderMethodOfPayment;
    }

    public boolean isPositiveVat() {
        return positiveVat;
    }

    public void setPositiveVat(boolean positiveVat) {
        this.positiveVat = positiveVat;
    }

    public boolean isInitialPayment() {
        return initialPayment;
    }

    public void setInitialPayment(boolean initialPayment) {
        this.initialPayment = initialPayment;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }
}
