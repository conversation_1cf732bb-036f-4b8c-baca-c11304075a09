/**
 *
 */
package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.entity.Housemaid;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public interface HousemaidRepository extends BaseRepository<Housemaid> {

    public List<Housemaid> findByStatus(HousemaidStatus status);

    public List<Housemaid> findByName(String Name);

    public List<Housemaid> findByNameContains(String Name);

    public List<Housemaid> findByStartDateGreaterThanEqual(Date startDate);

    @Query("SELECT h FROM Housemaid h"
            + " where  ("
            + "(h.status<>com.magnamedia.core.type.HousemaidStatus.WITH_CLIENT and (( h.foodAllowance IS NOT NULL and h.foodAllowance >0) or (h.housingAllowance IS NOT NULL and h.housingAllowance>0))) or "
            + "(h.status=com.magnamedia.core.type.HousemaidStatus.WITH_CLIENT and (( h.foodAllowance IS NOT NULL and h.foodAllowance >0) or (h.housingAllowance IS NOT NULL and h.housingAllowance>0)) and h.living=com.magnamedia.module.type.HousemaidLiveplace.IN) or "
            + "(h.status=com.magnamedia.core.type.HousemaidStatus.WITH_CLIENT and (h.foodAllowance IS NULL or h.housingAllowance IS NULL or h.foodAllowance<=0 or h.housingAllowance<=0) and h.living=com.magnamedia.module.type.HousemaidLiveplace.OUT)"
            + ")")
    public List<Housemaid> findInvalidAllowances();

    @Query("SELECT h"
            + " FROM Housemaid h"
            + " WHERE h.status<>com.magnamedia.core.type.HousemaidStatus.WITH_CLIENT AND h.living=com.magnamedia.module.type.HousemaidLiveplace.OUT")
    public List<Housemaid> findHousemaidsLiveOutNotWithClient();

    @Query("SELECT h"
            + " FROM Housemaid h"
            + " WHERE (h.startDate is not null or (h.landedInDubaiDate is not null and h.startDate is null)) and (h.basicSalary is null or h.basicSalary<1)")
    public List<Housemaid> findWorkingHousemaidsWithoutSalary();

    @Query("SELECT h"
            + " FROM Housemaid h"
            + " WHERE (SELECT c FROM Contract c where c.housemaid =h and c.status=com.magnamedia.module.type.ContractStatus.ACTIVE) is not null and (SELECT c.living FROM Contract c where c.housemaid =h and c.status=com.magnamedia.module.type.ContractStatus.ACTIVE)<> h.living")
    public List<Housemaid> findHousemaidDifferentLivingThanContract();

    @Query("SELECT h"
            + " FROM Housemaid h"
            + " WHERE (SELECT c FROM Contract c where c.housemaid =h and c.status=com.magnamedia.module.type.ContractStatus.ACTIVE) is not null and h.status<>com.magnamedia.core.type.HousemaidStatus.WITH_CLIENT")
    public List<Housemaid> findHousemaidsWithActiveContractNotWithClient();

    @Query("SELECT h"
            + " FROM Housemaid h"
            + " WHERE (SELECT count(c) FROM Contract c where c.housemaid =h and c.status=com.magnamedia.module.type.ContractStatus.ACTIVE) "
            + " is null and h.status=com.magnamedia.core.type.HousemaidStatus.WITH_CLIENT")
    public List<Housemaid> findHHousemaidsWithClientWithoutContract();

    @Query("SELECT h"
            + " FROM Housemaid h WHERE "
            + " h.basicSalary - (h.primarySalary + h.overTime+h.monthlyLoan+h.holiday+h.airfareFee) NOT BETWEEN -1 AND 1")
    public List<Housemaid> findHousemaidsWithBasicSalaryDifferentThanBreakdown();

    @Query("SELECT h FROM Housemaid h "
            + " WHERE h.freedomMaid=true AND (h.basicSalary=0 OR h.basicSalary is null) AND h.startDate is not null")
    public List<Housemaid> findFreedomHousemaidsWithStartDateAndNoSalary();

    @Query("SELECT h FROM Housemaid h "
            + " WHERE h.isAgency=true AND (h.basicSalary=0 OR h.basicSalary is null) AND h.startDate is not null")
    public List<Housemaid> findAgencyHousemaidsWithStartDateAndNoSalary();

    @Query("SELECT h FROM Housemaid h "
            + " WHERE h.isAgency=false AND h.freedomMaid=false AND (h.basicSalary=0 OR h.basicSalary is null) AND h.startDate is not null")
    public List<Housemaid> findCleanExitHousemaidsWithStartDateAndNoSalary();

    @Query("SELECT h FROM Housemaid h "
            + " WHERE h.isBeingPaid50PercentSalary=true AND h.startDate<=?1")
    public List<Housemaid> findMaidsPaid50PercentSalaryAndStartDateMoreThan(Date minStartDate);

//    //ACC-275
//    @Query("SELECT h FROM Housemaid h JOIN h.contracts c "
//            + "WHERE (h.basicSalary = 0 OR h.basicSalary is null) AND h.startDate is not null AND "
//            + "c.status = com.magnamedia.module.type.ContractStatus.ACTIVE AND "
//            + "c.contractProspectType = ?1")
//    public List<Housemaid> findVisaHousemaidsWithStartDateAndNoSalary(PicklistItem item);

    //Jirra ACC-462
    @Query(
            nativeQuery = true,
            value =
                    "SELECT h.id as hid, hr.ID as hrid, hr.LAST_MODIFICATION_DATE as LAST_MODIFICATION_DATE, hr.STATUS as status, hr2.ID as hr2id " +
                            "FROM HOUSEMAIDS h " +
                            "INNER JOIN (select * from HOUSEMAIDS_REVISIONS where LAST_MODIFICATION_DATE < ?1) as hr on h.id=hr.ID " +
                            "LEFT JOIN (select * from HOUSEMAIDS_REVISIONS where LAST_MODIFICATION_DATE < ?1) as hr2 on hr.ID = hr2.ID and hr.LAST_MODIFICATION_DATE < hr2.LAST_MODIFICATION_DATE " +
                            "WHERE hr2.ID is null and hr.STATUS not in ?2")
    public List<Object[]> getHousmaidRevisionsNotInStatusesAndBeforeDate(Date date, List<String> statuses);

    //Jirra ACC-462
    @Query(
            nativeQuery = true,
            value =
                    "SELECT distinct h.id as hid, hr.ID as hrid, hr.LAST_MODIFICATION_DATE as LAST_MODIFICATION_DATE, hr.STATUS as status, hr2.ID as hr2id "
                            + "FROM HOUSEMAIDS h "
                            + "INNER JOIN (select * from HOUSEMAIDS_REVISIONS where LAST_MODIFICATION_DATE >= ?1 and LAST_MODIFICATION_DATE < ?2) as hr on h.id=hr.ID "
                            + "LEFT JOIN (select * from HOUSEMAIDS_REVISIONS where LAST_MODIFICATION_DATE >= ?1 and LAST_MODIFICATION_DATE < ?2) as hr2 on hr.ID = hr2.ID and hr.LAST_MODIFICATION_DATE < hr2.LAST_MODIFICATION_DATE "
                            + "WHERE hr2.ID is null and hr.STATUS in ?3")
    public List<Object[]> getHousmaidRevisionsInStatusesAndBetweenDates(Date fromDate, Date toDate, List<String> statuses);


    @Query(value = "SELECT ID FROM HOUSEMAIDS_REVISIONS " +
            "WHERE STATUS = 'WITH_CLIENT' AND STATUS_MODIFIED = 1 " +
            "AND LAST_MODIFICATION_DATE >= ?1 AND LAST_MODIFICATION_DATE <= ?2 " +
            "and ID in (" +
            "      SELECT o.ID FROM HOUSEMAIDS_REVISIONS o WHERE (o.STATUS IN ?3) " +
            "      AND o.REVISION IN (" +
            "      SELECT MAX(i.REVISION) AS 'REVISION' " +
            "      FROM HOUSEMAIDS_REVISIONS i " +
            "      WHERE i.LAST_MODIFICATION_DATE <=?1 " +
            "      AND o.ID = i.ID " +
            "      GROUP BY i.ID)) ", nativeQuery = true)
    List<Long> getIdsOfMaidsWhoWentWithClientFromAccommodation(Date atStartOfDay, Date atEndOfDay, List<String> inAccommodationStatuses);

    @Query(value = "SELECT ID FROM HOUSEMAIDS_REVISIONS " +
            "WHERE STATUS IN ?3  AND STATUS_MODIFIED = 1 " +
            "AND LAST_MODIFICATION_DATE >= ?1 AND LAST_MODIFICATION_DATE <= ?2 " +
            "and ID in (" +
            "      SELECT o.ID FROM HOUSEMAIDS_REVISIONS o WHERE (o.STATUS = 'WITH_CLIENT') " +
            "      AND o.REVISION IN (" +
            "      SELECT MAX(i.REVISION) AS 'REVISION' " +
            "      FROM HOUSEMAIDS_REVISIONS i " +
            "      WHERE i.LAST_MODIFICATION_DATE <=?1 " +
            "      AND o.ID = i.ID " +
            "      GROUP BY i.ID)) ", nativeQuery = true)
    List<Long> getIdsOfMaidsWhoComeFromClientToAccommodation(Date atStartOfDay, Date atEndOfDay, List<String> inAccommodationStatuses);

    @Query("select h from Housemaid h where h.landedInDubaiDate>=?1 and h.landedInDubaiDate>=?2 ")
    List<Housemaid> findByLandedInDubaiDateBetweenDates(Date begin, Date end);

    @Query("select distinct h from Housemaid h " +
            "inner join Attachment a " +
            "on a.ownerId = h.visaNewRequest.id and a.ownerType = 'NewRequest' and a.tag = 'insurancePolicy' " +
            "where (h.status not in ('EMPLOYEMENT_TERMINATED', 'VISA_UNSUCCESSFUL') or h.dateOfTermination > ?1) " +
            "and h.passportNumber not in (?2)")
    List<Housemaid> getActiveMaidsInERPButNotInFile(Date d, List<String> passports);

    @Query("select distinct h from Housemaid h  " +
            "where h.passportNumber in ?1 and " +
                "h.status in ('EMPLOYEMENT_TERMINATED', 'VISA_UNSUCCESSFUL') and " +
                "h.dateOfTermination <= ?2 and " +
                "not exists (select 1 from Housemaid h1 " +
                    "where h1.id <> h.id and h1.passportNumber = h.passportNumber and " +
                        "(h1.status not in ('EMPLOYEMENT_TERMINATED', 'VISA_UNSUCCESSFUL') or " +
                            "h1.dateOfTermination > ?2) )")
    List<Housemaid> getActiveMaidsInERPButNotInFile(List<String> passports, Date d);

    @Query("select count(h.id) > 0 from Housemaid h " +
            "inner join h.visaNewRequest v " +
            "left join Attachment a on a.ownerType = 'NewRequest' and a.ownerId = v.id and a.tag = 'medicalCertificate' " +
                "where h = ?1 and (v.completed = true or a is not null) and h.housemaidType = 'MAID_VISA'")
    boolean existsByMVMaidsWithMedicalCertificate(Housemaid h);
}
