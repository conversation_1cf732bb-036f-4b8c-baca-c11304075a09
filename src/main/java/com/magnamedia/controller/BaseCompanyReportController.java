package com.magnamedia.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.security.ViewScope;
import com.magnamedia.entity.*;
import com.magnamedia.entity.interfaces.BasePLVariableNode;
import com.magnamedia.entity.projection.PLComapanyProjection;
import com.magnamedia.entity.projection.PLNodeTransactionCSVProjection;
import com.magnamedia.entity.projection.PLNodeTransactionCSVProjectionWithRounding;
import com.magnamedia.entity.projection.PLNodeTransactionProjection;
import com.magnamedia.extra.*;
import com.magnamedia.helper.CsvHelper;
import com.magnamedia.helper.PaginationUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.PLNodeType;
import com.magnamedia.report.BaseReport;
import com.magnamedia.report.PNL_VS_SYS_Report;
import com.magnamedia.repository.*;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.List;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on Jul 20, 2020
 *         Jirra ACC-644
 */

public class BaseCompanyReportController<T extends BaseReportCompany> extends BaseRepositoryController<T> {

    @Autowired
    BaseCompanyReportRepository<T> repository;

    @Autowired
    private RevenueRepository revenueRepository;

    @Autowired
    private ExpenseRepository expenseRepository;

    @Autowired
    private ProjectionFactory projectionFactory;

    @Override
    public BaseRepository<T> getRepository() {
        return repository;
    }

    private ExecutorService executor = Executors.newFixedThreadPool(4);

    public BasePLNodeRepository getBaseNodeRepository() {
        return null;
    }

    public BasePLVariableBucketRepository getBasePLVariableBucketRepository() {
        return null;
    }

    @PreAuthorize("hasPermission('plcompanies','searchplcompanies')")
    @RequestMapping(value = "/searchplcompanies/page", method = RequestMethod.GET)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    @Transactional
    public ResponseEntity<?> searchPLCompanies(Pageable pageable, Sort sort,
                                               @RequestParam(name = "search", required = false) String queryString,
                                               @RequestParam(name = "isactive", required = false) Boolean isActive) {
        SelectQuery<BaseReportCompany> query = new SelectQuery(getEntityClass());
        if (queryString != null && !queryString.isEmpty())
            query.filterBy("name", "LIKE", "%" + queryString + "%");
        if (isActive != null)
            query.filterBy("company.isActive", "=", isActive);

        // Sorting
        if (sort != null) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("name", true, false);
        }

        //Jirra 402
        return new ResponseEntity<>(
                query.execute(pageable).map(
                        company ->
                                projectionFactory.createProjection(
                                        PLComapanyProjection.class, company)),
                HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('plcompanies','searchplcompanies')")
    @RequestMapping(value = "/searchplcompanies/list", method = RequestMethod.GET)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    @Transactional
    public ResponseEntity<?> searchPLCompaniesList(Sort sort,
                                                   @RequestParam(name = "search", required = false) String queryString,
                                                   @RequestParam(name = "isactive", required = false) Boolean isActive) {
        SelectQuery<BaseReportCompany> query = new SelectQuery(getEntityClass());
        if (queryString != null && !queryString.isEmpty())
            query.filterBy("name", "LIKE", "%" + queryString + "%");
        if (isActive != null)
            query.filterBy("company.isActive", "=", isActive);

        // Sorting
        if (sort != null) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("name", true, false);
        }

        //Jirra 402
        return new ResponseEntity<>(
                query.execute().stream().map(company ->
                        projectionFactory.createProjection(
                                PLComapanyProjection.class, company))
                        .collect(Collectors.toList()),
                HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('plcompanies','validatereports')")
    @RequestMapping(value = "/validatereports", method = RequestMethod.GET)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    @Transactional
    public ResponseEntity<?> validateReports() {

        Map<String, BigDecimal> variablesMap = new HashMap<>();

        SelectQuery<BasePLVariableBucket> query = new SelectQuery<>(getVariableBucketEntityClass());
        query.filterBy("PLVariable.parent.parent.PLCompany.company.isActive", "=", Boolean.TRUE);
        buildValidateReportsMap(variablesMap, query);

        query = new SelectQuery<>(getVariableBucketEntityClass());
        query.filterBy("PLVariable.parent.parent.parent.PLCompany.company.isActive", "=", Boolean.TRUE);
        buildValidateReportsMap(variablesMap, query);


        Map<String, List<PLVariableBucketEntity>> resultMap = new HashMap<>();
        for (String s : variablesMap.keySet()) {
            if (s.isEmpty() || Math.abs(variablesMap.get(s).floatValue()) == 1)
                continue;
            String[] ss = s.split("_");
            if (ss[0].equalsIgnoreCase("r")) {
                Revenue revenue = revenueRepository.findOne(Long.parseLong(ss[1]));
                List<BasePLVariableBucket> pLVariableBuckets1 =
                        getBasePLVariableBucketRepository().findByRevenue(revenue);
                if (!pLVariableBuckets1.isEmpty()) {
                    List<PLVariableBucketEntity> entities =
                            pLVariableBuckets1.stream()
                                    .filter(x -> x.getpLVariable().getParent().getParent().getPLCompany().getCompany().isIsActive())
                                    .map(x -> new PLVariableBucketEntity(x))
                                    .collect(Collectors.toList());
                    resultMap.put(revenue.getName(), entities);
                }
            } else if (ss[0].equalsIgnoreCase("e")) {
                Expense expense = expenseRepository.findOne(Long.parseLong(ss[1]));
                List<BasePLVariableBucket> pLVariableBuckets2 =
                        getBasePLVariableBucketRepository().findByExpense(expense);
                if (!pLVariableBuckets2.isEmpty()) {
                    List<PLVariableBucketEntity> entities =
                            pLVariableBuckets2.stream()
                                    .filter(x -> x.getpLVariable().getParent().getParent().getPLCompany().getCompany().isIsActive())
                                    .map(x -> new PLVariableBucketEntity(x))
                                    .collect(Collectors.toList());
                    resultMap.put(expense.getName(), entities);
                }
            }
        }

        if (resultMap.isEmpty())
            return new ResponseEntity<>(
                    "The SUM is equal 1 for all Revenues and Expenses.",
                    HttpStatus.OK);
        else
            return new ResponseEntity<>(resultMap,
                    HttpStatus.BAD_REQUEST);
    }

    private void buildValidateReportsMap(
            Map<String, BigDecimal> variablesMap,
            SelectQuery<BasePLVariableBucket> query) {

        List<BasePLVariableBucket> pLVariableBuckets;
        Integer pageIndex = 0;
        Page<BasePLVariableBucket> page;
        do {
            Pageable pageable = PageRequest.of(pageIndex++, 100);
            page = query.execute(pageable);
            pLVariableBuckets = page.getContent();

            for (BasePLVariableBucket pLVariableBucket : pLVariableBuckets) {
                String variableKey = getVariableKey(pLVariableBucket);
                BigDecimal variableValue = variablesMap.getOrDefault(variableKey, new BigDecimal(0));
                variablesMap.put(variableKey, variableValue.add(new BigDecimal(pLVariableBucket.getWieght())));
            }
        } while (page.hasNext());
    }

    private String getVariableKey(BasePLVariableBucket pLVariableBucket) {
        return (pLVariableBucket.getRevenue() != null ? "r_" + pLVariableBucket.getRevenue().getId()
                : (pLVariableBucket.getExpense() != null ? "e_" + pLVariableBucket.getExpense().getId() : ""));
    }

    @PreAuthorize("hasPermission('plcompanies','generatereport')")
    @RequestMapping(value = "/generatereport/{id}", method = RequestMethod.GET)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    @Transactional
    public ResponseEntity<?> generateReport(
            @PathVariable("id") T pLCompany,
            @RequestParam(required = false, value = "fromDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date fromDate,
            @RequestParam(required = false, value = "toDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date toDate,
            @RequestParam(value = "searchCriteria", defaultValue = "PNL_DATE") SearchCriteria searchCriteria) {

        pLCompany.calculateValue(fromDate, toDate, searchCriteria);
        return new ResponseEntity<>(pLCompany, HttpStatus.OK);
    }

    // Jirra ACC-281
    enum Format {
        HTML, EXCEL, PDF
    }

    // Jirra ACC-2912
    public enum SearchCriteria {
        PNL_DATE, ACTUAL_DATE
    }

    // Jirra ACC-281
    @PreAuthorize("hasPermission('plcompanies','downloadreport')")
    @RequestMapping(value = "/downloadreport/{id}/{format}", method = RequestMethod.GET)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    @Transactional
    public ResponseEntity<?> downloadReport(
            HttpServletResponse response,
            @PathVariable(value = "format", required = false) Format format,
            @PathVariable("id") T pLCompany,
            @RequestParam(required = false, value = "fromDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date fromDate,
            @RequestParam(required = false, value = "toDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date toDate,
            @RequestParam(required = false, value = "showFormula") boolean showFormula,
            @RequestParam(value = "withRounding", defaultValue = "false") boolean withRounding,
            @RequestParam(value = "searchCriteria", defaultValue = "PNL_DATE") SearchCriteria searchCriteria) throws Exception {
        if (format == null)
            format = Format.PDF;

        //Jirra ACC-2545
        if (fromDate != null && fromDate.before(new DateTime().minusYears(1).toDate()))
            throw new RuntimeException("the minimum value for 'From Date' is 1 year from now");

        pLCompany.calculateValue(fromDate, toDate, searchCriteria);

        //Jirra ACC-385
        int levels = Integer.parseInt(
                Setup.getParameter(
                        Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_PNL_Summary_Report_Levels));
        boolean isColored = Setup.getParameter(
                Setup.getCurrentModule(),
                AccountingModule.PARAMETER_PNL_Summary_Report_Colored)
                .equals("1");
        String baseUrl = Setup.getParameter(
                Setup.getCurrentModule(),
                AccountingModule.PARAMETER_BACKEND_BASE_URL);
        if (format == Format.HTML) {
            BaseReport report = getReport(pLCompany, fromDate, toDate, isColored, levels, baseUrl, format.toString(), showFormula, withRounding, searchCriteria);
            return new ResponseEntity<>(report.render(), HttpStatus.OK);
        }
        if (format == Format.PDF) {
            BaseReport report = getReport(pLCompany, fromDate, toDate, isColored, levels, baseUrl, format.toString(), showFormula, withRounding, searchCriteria);
            response.setContentType(getMimeType("pdf"));
            response.addHeader("Content-Disposition",
                    "attachment; filename=\"" + pLCompany.getName() + ".pdf\"");
            report.exportPdf(response.getOutputStream());
            response.flushBuffer();
        }

        if (format == Format.EXCEL) {
            generateExcelReportFile(response, pLCompany, fromDate, toDate, isColored, levels, showFormula, withRounding);
        }

        return null;
    }

    protected BaseReport getReport(BaseReportCompany pLCompany, Date fromDate, Date toDate, boolean isColored, int levels, String baseUrl, String format, boolean showFormula, boolean withRounding
            , SearchCriteria searchCriteria) throws Exception {
        return null;
    }

    // ACC-445
    @PreAuthorize("hasPermission('plcompanies','downloadreport')")
    @RequestMapping(value = "/downloadreportvs/{format}", method = RequestMethod.GET)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    @Transactional
    public ResponseEntity<?> downloadVsReport(
            HttpServletResponse response,
            @PathVariable(value = "format", required = false) Format format,
            @RequestParam(value = "indetail", required = false) boolean inDetail) throws Exception {

        if (format == null) {
            format = Format.PDF;
        }

        PNL_VS_SYS_Report vsReport = new PNL_VS_SYS_Report(inDetail);

        switch (format) {
            case HTML:
                return new ResponseEntity<>(vsReport.render(), HttpStatus.OK);
            case PDF:
                response.setContentType(getMimeType("pdf"));
                response.addHeader("Content-Disposition",
                        "attachment; filename=\"PnlVsSys.pdf\"");
                vsReport.exportPdf(response.getOutputStream());
                response.flushBuffer();
                break;

        }

        return null;
    }

    // Jirra ACC-281
    public void generateExcelReportFile(
            HttpServletResponse response,
            T pLCompany, Date fromDate, Date toDate,
            boolean isColored, Integer maxLevel, boolean showFormula, boolean withRounding)
            throws Exception {
    }

    // Jirra ACC-385
    public CellStyle createStyle(
            XSSFWorkbook workbook, DataFormat fmt, XSSFFont font,
            IndexedColors color, Short fillPattern) {
        CellStyle style = workbook.createCellStyle();
        if (fmt != null)
            style.setDataFormat(fmt.getFormat("@"));
        if (font != null)
            style.setFont(font);
        if (color != null)
            style.setFillForegroundColor(color.getIndex());
        if (fillPattern != null)
            style.setFillPattern(fillPattern);
        return style;
    }

    public CellStyle createHexStyle(
            XSSFWorkbook workbook, DataFormat fmt, XSSFFont font,
            String color, Short fillPattern) {
        CellStyle style = workbook.createCellStyle();
        if (fmt != null)
            style.setDataFormat(fmt.getFormat("@"));
        if (font != null)
            style.setFont(font);
        if (color != null && color.length() == 7)
            ((XSSFCellStyle) style).setFillForegroundColor(new XSSFColor(hex2Rgb(color)));
        if (fillPattern != null)
            style.setFillPattern(fillPattern);
        return style;
    }

    public Color hex2Rgb(String colorStr) {
        return new Color(
                Integer.valueOf(colorStr.substring(1, 3), 16),
                Integer.valueOf(colorStr.substring(3, 5), 16),
                Integer.valueOf(colorStr.substring(5, 7), 16));
    }

    public void buildSpacerRow(XSSFSheet spreadsheet, int rowId) {
        XSSFRow headLevelSpacerRow = spreadsheet.createRow(rowId);
        XSSFCell headLevelSpacerRowCell = headLevelSpacerRow.createCell(0);
        spreadsheet.addMergedRegion(
                new CellRangeAddress(rowId, rowId, 0, 3));
    }

    public void buildTableNameRow(
            XSSFWorkbook workbook,
            XSSFSheet spreadsheet,
            String name,
            DataFormat fmt,
            XSSFFont boldFont,
            int rowId) {

        CellStyle style = createStyle(
                workbook, fmt, boldFont,
                IndexedColors.GREY_25_PERCENT,
                CellStyle.SOLID_FOREGROUND);

        XSSFRow headLevelRow = spreadsheet.createRow(rowId);
        XSSFCell cell = headLevelRow.createCell(0);
        cell.setCellValue(name);
        cell.setCellStyle(style);
        XSSFCell cell1 = headLevelRow.createCell(1);
        cell1.setCellStyle(style);
        XSSFCell cell2 = headLevelRow.createCell(2);
        cell2.setCellStyle(style);
        XSSFCell cell3 = headLevelRow.createCell(3);
        cell3.setCellStyle(style);
        XSSFCell cell4 = headLevelRow.createCell(4);
        cell4.setCellStyle(style);
        XSSFCell cell5 = headLevelRow.createCell(5);
        cell5.setCellStyle(style);
        spreadsheet.addMergedRegion(
                new CellRangeAddress(rowId, rowId, 0, 3));
    }

    public void buildHeadTableRow(
            XSSFWorkbook workbook,
            XSSFSheet spreadsheet,
            String[] names,
            DataFormat fmt,
            XSSFFont boldFont,
            int rowId) {

        CellStyle style = createStyle(
                workbook, fmt, boldFont, null, null);

        XSSFRow headLevelRow = spreadsheet.createRow(rowId);
        int i = 0;
        for (String name : names) {
            XSSFCell cell = headLevelRow.createCell(i++);
            cell.setCellValue(name);
            cell.setCellStyle(style);
        }
    }

    // Jirra ACC-281
    public int buildNodeBlock(Date fromDate, Date toDate,
                              Map<String, Double> inputVatMap,
                              Map<String, Double> outputVatMap,
                              XSSFWorkbook workbook,
                              BasePLNode pLNode,
                              XSSFSheet spreadsheet,
                              int rowId,
                              Integer level,
                              Integer maxLevel,
                              boolean isSummary,
                              boolean isColored,
                              DataFormat fmt,
                              XSSFFont boldFont,
                              boolean showFormula,
                              boolean withRounding) throws Exception {

        DateTime now = new DateTime(fromDate);
        DateTime before1MonthDate = now.minusMonths(1).withDayOfMonth(1);
        DateTime before2MonthDate = now.minusMonths(2).withDayOfMonth(1);
        DateTime before3MonthDate = now.minusMonths(3).withDayOfMonth(1);

        if (level != null && level == maxLevel)
            buildVariableNodeBlock(fromDate, toDate, workbook, pLNode, spreadsheet, ++rowId, isSummary, isColored, fmt, showFormula, withRounding);
        else if (pLNode.getType().equals("PLVariableNode") || pLNode.getType().equals("AdhocVariableNode"))
            buildVariableNodeBlock(fromDate, toDate, workbook, pLNode, spreadsheet, ++rowId, isSummary, isColored, fmt, showFormula, withRounding);
        else {
            CellStyle style;
            //Jirra ACC-824
            CellStyle style2;
            if (isColored && (pLNode.getReportColor() != null) && (!pLNode.getReportColor().isEmpty())) {
                style = createHexStyle(
                        workbook, fmt, boldFont,
                        pLNode.getReportColor(),
                        CellStyle.SOLID_FOREGROUND);
                style2 = createHexStyle(
                        workbook, fmt, boldFont,
                        pLNode.getReportColor(),
                        CellStyle.SOLID_FOREGROUND);
            } else {
                style = createStyle(
                        workbook, fmt, boldFont,
                        null,
                        null);
                style2 = createStyle(
                        workbook, fmt, boldFont,
                        null,
                        null);
            }
            DataFormat format = workbook.createDataFormat();
            style2.setDataFormat(format.getFormat("#,##"));

            if (!getEntityClass().equals(AdhocCompany.class) || pLNode.getParent() != null) {
                XSSFRow LevelRow = spreadsheet.createRow(++rowId);
                XSSFCell cell = LevelRow.createCell(0);
                cell.setCellValue(pLNode.getName());
                cell.setCellStyle(style);
                XSSFCell cell1 = LevelRow.createCell(1);
                cell1.setCellStyle(style);
                XSSFCell cell2 = LevelRow.createCell(2);
                cell2.setCellStyle(style);
                XSSFCell cell3 = LevelRow.createCell(3);
                cell3.setCellStyle(style);
                spreadsheet.addMergedRegion(
                        new CellRangeAddress(rowId, rowId, 0, 3));
            }

            for (BasePLNode node : (List<BasePLNode>) pLNode.getSortedChildren()) {

                if (level != null) {
                    Integer level2 = level + 1;
                    rowId = buildNodeBlock(fromDate, toDate, inputVatMap, outputVatMap,
                            workbook, node, spreadsheet, rowId, level2, maxLevel, isSummary, isColored, fmt, boldFont, showFormula, withRounding);
                } else
                    rowId = buildNodeBlock(fromDate, toDate, inputVatMap, outputVatMap,
                            workbook, node, spreadsheet, rowId, null, maxLevel, isSummary, isColored, fmt, boldFont, showFormula, withRounding);
            }

            if (pLNode.getParent() == null) {
                if (!isSummary) {
                    //Jirra ACC-2500
                    if (pLNode.getpLNodeType() != null && pLNode.getpLNodeType().equals(PLNodeType.EXPENSES)) {
                        buildTotalInputVATCollectedRow(inputVatMap, spreadsheet, ++rowId, workbook, fmt, boldFont, withRounding);
                    }

                    if (pLNode.getpLNodeType() != null && pLNode.getpLNodeType().equals(PLNodeType.REVENUES)) {
                        buildTotalOutputVATCollectedRow(outputVatMap, spreadsheet, ++rowId, workbook, fmt, boldFont, withRounding);
                    }
                }
                //Jirra ACC-2681
                else {
                    if (pLNode.getpLNodeType() != null && pLNode.getpLNodeType().equals(PLNodeType.EXPENSES)) {
                        buildTotalInputVATCollectedRowForSummaryTable(inputVatMap, spreadsheet, ++rowId, workbook, fmt, withRounding);
                    }

                    if (pLNode.getpLNodeType() != null && pLNode.getpLNodeType().equals(PLNodeType.REVENUES)) {
                        buildTotalOutputVATCollectedRowForSummaryTable(outputVatMap, spreadsheet, ++rowId, workbook, fmt, withRounding);
                    }
                }
            }

            XSSFRow headLevelTotalRow = spreadsheet.createRow((++rowId));
            XSSFCell headLevelTotalRowCell1 = headLevelTotalRow.createCell(0);
            headLevelTotalRowCell1.setCellValue("Total " + pLNode.getName());
            headLevelTotalRowCell1.setCellStyle(style);

            Double value = pLNode.getValue();
            XSSFCell headLevelTotalRowCell2 = headLevelTotalRow.createCell(1);
            headLevelTotalRowCell2.setCellStyle(style);
            if (pLNode.getParent() == null) {
                if (pLNode.getpLNodeType().equals(PLNodeType.REVENUES)) {
                    value += outputVatMap.get("currentMonth");
                } else {
                    value += inputVatMap.get("currentMonth");
                }
            }

            headLevelTotalRowCell2.setCellValue(withRounding ? round(value / 1000) : round(value));

            //Jirra ACC-1389
            if (!isSummary && !getEntityClass().equals(AdhocCompany.class)) {
                Future<Double> before3MonthQueryValue = executor.submit(() -> pLNode.calculateValue(before3MonthDate.toDate(), before3MonthDate.dayOfMonth().withMaximumValue().toDate()));
                Future<Double> before2MonthQueryValue = executor.submit(() -> pLNode.calculateValue(before2MonthDate.toDate(), before2MonthDate.dayOfMonth().withMaximumValue().toDate()));
                Future<Double> before1MonthQueryValue = executor.submit(() -> pLNode.calculateValue(before1MonthDate.toDate(), before1MonthDate.dayOfMonth().withMaximumValue().toDate()));

                Double before3MonthValue = before3MonthQueryValue.get();
                Double before2MonthValue = before2MonthQueryValue.get();
                Double before1MonthValue = before1MonthQueryValue.get();

                if (pLNode.getParent() == null) {
                    if (pLNode.getpLNodeType().equals(PLNodeType.REVENUES)) {
                        before3MonthValue += outputVatMap.get("before3Month");
                        before2MonthValue += outputVatMap.get("before2Month");
                        before1MonthValue += outputVatMap.get("before1Month");
                    } else {
                        before3MonthValue += inputVatMap.get("before3Month");
                        before2MonthValue += inputVatMap.get("before2Month");
                        before1MonthValue += inputVatMap.get("before1Month");
                    }
                }

                XSSFCell Cell3 = headLevelTotalRow.createCell(2);
                Cell3.setCellValue(withRounding ? round(before1MonthValue / 1000) : round(before1MonthValue));
                Cell3.setCellStyle(style);

                XSSFCell Cell4 = headLevelTotalRow.createCell(3);
                Cell4.setCellValue(withRounding ? round(before2MonthValue / 1000) : round(before2MonthValue));
                Cell4.setCellStyle(style);

                XSSFCell Cell5 = headLevelTotalRow.createCell(4);
                Cell5.setCellValue(withRounding ? round(before3MonthValue / 1000) : round(before3MonthValue));
                Cell5.setCellStyle(style);

                XSSFCell Cell6 = headLevelTotalRow.createCell(5);
                Cell6.setCellValue(withRounding ? round(pLNode.getAverage() / 1000) : round(pLNode.getAverage()));
                Cell6.setCellStyle(style);

                XSSFCell Cell7 = headLevelTotalRow.createCell(6);
                Cell7.setCellStyle(style);
                if (pLNode.getParent() == null) {
                    if (pLNode.getpLNodeType().equals(PLNodeType.REVENUES)) {
                        Cell7.setCellValue(withRounding ? round((pLNode.getProfitAdjustment() - Math.abs(outputVatMap.get("currentMonth"))) / 1000) : pLNode.getProfitAdjustment() - Math.abs(outputVatMap.get("currentMonth")));
                    } else {
                        Cell7.setCellValue(withRounding ? round((pLNode.getProfitAdjustment() + Math.abs(inputVatMap.get("currentMonth"))) / 1000) : pLNode.getProfitAdjustment() + Math.abs(inputVatMap.get("currentMonth")));
                    }
                } else {
                    Cell7.setCellValue(withRounding ? round(pLNode.getProfitAdjustment() / 1000) : pLNode.getProfitAdjustment());
                }
            }

            if (!getEntityClass().equals(AdhocCompany.class)) {
                if (pLNode.getpLNodeType().equals(PLNodeType.EXPENSES)) {
                    XSSFCell Cell3 = headLevelTotalRow.createCell(isSummary ? 2 : 7);
                    Cell3.setCellValue("%" + String.format("%,.1f", (double) pLNode.getRelatedRatio() / 10));
                    Cell3.setCellStyle(style);
                } else {
                    XSSFCell Cell3 = headLevelTotalRow.createCell(isSummary ? 2 : 7);
                    Cell3.setCellValue("%" + String.format("%,.1f", (double) pLNode.getRatio() / 10));
                    Cell3.setCellStyle(style);
                }

                XSSFCell Cell3 = headLevelTotalRow.createCell(isSummary ? 3 : 8);
                Cell3.setCellStyle(style);
            } else {
                XSSFCell Cell3 = headLevelTotalRow.createCell(2);
                Cell3.setCellValue("%" + String.format("%,.1f", (double) (pLNode.getParent() == null ? pLNode.getRatio() / 10 : pLNode.getRatio())));
                Cell3.setCellStyle(style);
            }
        }
        return rowId;
    }

    // Jirra ACC-281
    public void buildVariableNodeBlock(Date fromDate, Date toDate,
                                       XSSFWorkbook workbook,
                                       BasePLNode pLNode,
                                       XSSFSheet spreadsheet,
                                       int rowId,
                                       boolean isSummary,
                                       boolean isColored,
                                       DataFormat fmt,
                                       boolean showFormula,
                                       boolean withRounding) {

        DateTime now = new DateTime(fromDate);
        DateTime before1MonthDate = now.minusMonths(1).withDayOfMonth(1);
        DateTime before2MonthDate = now.minusMonths(2).withDayOfMonth(1);
        DateTime before3MonthDate = now.minusMonths(3).withDayOfMonth(1);

        CellStyle style;
        CellStyle style2;
        if (isColored && (pLNode.getReportColor() != null) && (!pLNode.getReportColor().isEmpty())) {
            style = createHexStyle(
                    workbook, fmt, null, pLNode.getReportColor(),
                    CellStyle.SOLID_FOREGROUND);
            style2 = createHexStyle(
                    workbook, fmt, null, pLNode.getReportColor(),
                    CellStyle.SOLID_FOREGROUND);
        } else {
            style = createStyle(workbook, fmt, null, null, null);
            style2 = createStyle(workbook, fmt, null, null, null);
        }
        DataFormat format = workbook.createDataFormat();
        style2.setDataFormat(format.getFormat("#,##"));

        XSSFRow Row = spreadsheet.createRow(rowId);
        XSSFCell Cell1 = Row.createCell(0);
        Cell1.setCellValue(pLNode.getName());
        Cell1.setCellStyle(style);

        XSSFCell Cell2 = Row.createCell(1);
        Cell2.setCellValue(withRounding ? round(pLNode.getValue() / 1000) : round(pLNode.getValue()));
        Cell2.setCellStyle(style);

        //Jirra ACC-1389
        if (!isSummary && !getEntityClass().equals(AdhocCompany.class)) {
            Double before3MonthValue = pLNode.calculateValue(before3MonthDate.toDate(), before3MonthDate.dayOfMonth().withMaximumValue().toDate());
            Double before2MonthValue = pLNode.calculateValue(before2MonthDate.toDate(), before2MonthDate.dayOfMonth().withMaximumValue().toDate());
            Double before1MonthValue = pLNode.calculateValue(before1MonthDate.toDate(), before1MonthDate.dayOfMonth().withMaximumValue().toDate());

            XSSFCell Cell3 = Row.createCell(2);
            Cell3.setCellValue(withRounding ? round(before1MonthValue / 1000) : round(before1MonthValue));
            Cell3.setCellStyle(style);

            XSSFCell Cell4 = Row.createCell(3);
            Cell4.setCellValue(withRounding ? round(before2MonthValue / 1000) : round(before2MonthValue));
            Cell4.setCellStyle(style);

            XSSFCell Cell5 = Row.createCell(4);
            Cell5.setCellValue(withRounding ? round(before3MonthValue / 1000) : round(before3MonthValue));
            Cell5.setCellStyle(style);

            XSSFCell Cell6 = Row.createCell(5);
            Cell6.setCellValue(withRounding ? round(pLNode.getAverage() / 1000) : pLNode.getAverage());
            Cell6.setCellStyle(style);

            XSSFCell Cell7 = Row.createCell(6);
            Cell7.setCellValue(withRounding ? round(pLNode.getProfitAdjustment() / 1000) : pLNode.getProfitAdjustment());

            Cell7.setCellStyle(style);
        }

        if (!getEntityClass().equals(AdhocCompany.class)) {
            if (pLNode.getpLNodeType().equals(PLNodeType.EXPENSES)) {
                XSSFCell Cell3 = Row.createCell(isSummary ? 2 : 7);
                Cell3.setCellValue("%" + String.format("%,.1f", (double) pLNode.getRelatedRatio() / 10));
                Cell3.setCellStyle(style);
            } else {
                XSSFCell Cell3 = Row.createCell(isSummary ? 2 : 7);
                Cell3.setCellValue("%" + String.format("%,.1f", (double) pLNode.getRatio() / 10));
                Cell3.setCellStyle(style);
            }

            XSSFCell Cell4 = Row.createCell(isSummary ? 3 : 8);
            Cell4.setCellValue(isSummary ? "" : ((BasePLVariableNode) pLNode).getFormula());
            Cell4.setCellStyle(style);
        } else {
            XSSFCell Cell3 = Row.createCell(2);
            Cell3.setCellValue("%" + String.format("%,.1f", (double) pLNode.getRatio()));
            Cell3.setCellStyle(style);

            if (showFormula) {
                XSSFCell Cell4 = Row.createCell(3);
                Cell4.setCellValue(isSummary ? "" : ((BasePLVariableNode) pLNode).getFormula());
                Cell4.setCellStyle(style);
            }
        }
    }

    public void buildTotalProfitRow(
            T pLCompany,
            XSSFSheet spreadsheet,
            int rowId,
            XSSFWorkbook workbook,
            DataFormat fmt,
            XSSFFont boldFont,
            Map<String, Double> outputVatMap,
            Map<String, Double> inputVatMap,
            boolean withRounding) {

        CellStyle style = createStyle(
                workbook, fmt, boldFont,
                IndexedColors.LIGHT_YELLOW,
                CellStyle.SOLID_FOREGROUND);

        CellStyle style2 = createStyle(
                workbook, fmt, boldFont,
                IndexedColors.LIGHT_YELLOW,
                CellStyle.SOLID_FOREGROUND);
        DataFormat format = workbook.createDataFormat();
        style2.setDataFormat(format.getFormat("#,##"));

        XSSFRow Row = spreadsheet.createRow(rowId);
        XSSFCell Cell1 = Row.createCell(0);
        Cell1.setCellValue("Net Cash Profit");
        Cell1.setCellStyle(style);

        XSSFCell Cell2 = Row.createCell(1);
        Cell2.setCellValue(withRounding ?
                round(((pLCompany.getRevenuesValue() + outputVatMap.get("currentMonth")) - (pLCompany.getExpensesValue() + inputVatMap.get("currentMonth"))) / 1000)
                : (pLCompany.getRevenuesValue() + outputVatMap.get("currentMonth")) - (pLCompany.getExpensesValue() + inputVatMap.get("currentMonth")));
        Cell2.setCellStyle(style);

        XSSFCell Cell3 = Row.createCell(2);
        Cell3.setCellValue("%" + ((pLCompany.getRevenuesValue() != 0D) ? ((double) (1000 - pLCompany.getRelatedExpensesRatio())) / 10 : "0.0ratio"));
        Cell3.setCellStyle(style);

        XSSFCell Cell4 = Row.createCell(3);
        Cell4.setCellValue("");
        Cell4.setCellStyle(style);
    }

    public void buildAccrualProfitRow(
            T pLCompany,
            XSSFSheet spreadsheet,
            int rowId,
            XSSFWorkbook workbook,
            DataFormat fmt,
            XSSFFont boldFont,
            Map<String, Double> outputVatMap,
            Map<String, Double> inputVatMap,
            boolean withRounding) {

        CellStyle style = createStyle(
                workbook, fmt, boldFont,
                IndexedColors.LIGHT_YELLOW,
                CellStyle.SOLID_FOREGROUND);

        CellStyle style2 = createStyle(
                workbook, fmt, boldFont,
                IndexedColors.LIGHT_YELLOW,
                CellStyle.SOLID_FOREGROUND);
        DataFormat format = workbook.createDataFormat();
        style2.setDataFormat(format.getFormat("#,##"));

        XSSFRow Row = spreadsheet.createRow(rowId);
        XSSFCell Cell1 = Row.createCell(0);
        Cell1.setCellValue("Accrual Profit");
        Cell1.setCellStyle(style);

        XSSFCell Cell2 = Row.createCell(1);
        Double accrualProfit = pLCompany.getRevenuesValue() - pLCompany.getExpensesValue() + pLCompany.getProfitAdjustmentsValue() - Math.abs(outputVatMap.get("currentMonth")) + Math.abs(inputVatMap.get("currentMonth"));
        Cell2.setCellValue(withRounding ? round(accrualProfit / 1000) : accrualProfit);
        Cell2.setCellStyle(style);

        XSSFCell Cell3 = Row.createCell(2);
        Cell3.setCellValue("");
        Cell3.setCellStyle(style);

        XSSFCell Cell4 = Row.createCell(3);
        Cell4.setCellValue("");
        Cell4.setCellStyle(style);
    }

    public void buildTotalOutputVATCollectedRow(
            Map<String, Double> outputVatMap,
            XSSFSheet spreadsheet,
            int rowId,
            XSSFWorkbook workbook,
            DataFormat fmt,
            XSSFFont boldFont,
            boolean withRounding) {

        CellStyle style = createStyle(
                workbook, fmt, boldFont,
                IndexedColors.LIGHT_YELLOW,
                CellStyle.SOLID_FOREGROUND);

        CellStyle style2 = createStyle(
                workbook, fmt, boldFont,
                IndexedColors.LIGHT_YELLOW,
                CellStyle.SOLID_FOREGROUND);
        DataFormat format = workbook.createDataFormat();
        style2.setDataFormat(format.getFormat("#,##"));

        XSSFRow Row = spreadsheet.createRow(rowId);
        XSSFCell Cell1 = Row.createCell(0);
        Cell1.setCellValue("Output VAT Collected");
        Cell1.setCellStyle(style);

        Double value = withRounding ? round(Math.abs(outputVatMap.get("currentMonth") / 1000)) : Math.abs(outputVatMap.get("currentMonth"));

        XSSFCell Cell2 = Row.createCell(1);
        Cell2.setCellValue(value);
        Cell2.setCellStyle(style);

        XSSFCell Cell3 = Row.createCell(2);
        Cell3.setCellValue(withRounding ? round(Math.abs(outputVatMap.get("before1Month") / 1000)) : Math.abs(outputVatMap.get("before1Month")));
        Cell3.setCellStyle(style);

        XSSFCell Cell4 = Row.createCell(3);
        Cell4.setCellValue(withRounding ? round(Math.abs(outputVatMap.get("before2Month") / 1000)) : Math.abs(outputVatMap.get("before2Month")));
        Cell4.setCellStyle(style);

        XSSFCell Cell5 = Row.createCell(4);
        Cell5.setCellValue(withRounding ? round(Math.abs(outputVatMap.get("before3Month") / 1000)) : Math.abs(outputVatMap.get("before3Month")));
        Cell5.setCellStyle(style);

        XSSFCell Cell6 = Row.createCell(5);
        Cell6.setCellValue(0.0);
        Cell6.setCellStyle(style);

        XSSFCell Cell7 = Row.createCell(6);
        Cell7.setCellValue(-1 * value);
        Cell7.setCellStyle(style);

        XSSFCell Cell8 = Row.createCell(7);
        Cell8.setCellValue("");
        Cell8.setCellStyle(style);

        XSSFCell Cell9 = Row.createCell(8);
        Cell9.setCellValue("");
        Cell9.setCellStyle(style);
    }

    public void buildTotalOutputVATCollectedRowForSummaryTable(
            Map<String, Double> outputVatMap,
            XSSFSheet spreadsheet,
            int rowId,
            XSSFWorkbook workbook,
            DataFormat fmt,
            boolean withRounding) {
        CellStyle style = createStyle(workbook, fmt, null, null, null);
        XSSFRow Row = spreadsheet.createRow(rowId);
        XSSFCell Cell1 = Row.createCell(0);
        Cell1.setCellValue("Output VAT Collected");
        Cell1.setCellStyle(style);
        Double value = withRounding ? round(Math.abs(outputVatMap.get("currentMonth") / 1000)) : Math.abs(outputVatMap.get("currentMonth"));

        XSSFCell Cell2 = Row.createCell(1);
        Cell2.setCellValue(value);
        Cell2.setCellStyle(style);
    }

    public void buildTotalInputVATCollectedRow(
            Map<String, Double> inputVatMap,
            XSSFSheet spreadsheet,
            int rowId,
            XSSFWorkbook workbook,
            DataFormat fmt,
            XSSFFont boldFont,
            boolean withRounding) {
        CellStyle style = createStyle(
                workbook, fmt, boldFont,
                IndexedColors.LIGHT_YELLOW,
                CellStyle.SOLID_FOREGROUND);

        CellStyle style2 = createStyle(
                workbook, fmt, boldFont,
                IndexedColors.LIGHT_YELLOW,
                CellStyle.SOLID_FOREGROUND);
        DataFormat format = workbook.createDataFormat();
        style2.setDataFormat(format.getFormat("#,##"));

        XSSFRow Row = spreadsheet.createRow(rowId);
        XSSFCell Cell1 = Row.createCell(0);
        Cell1.setCellValue("Input VAT paid");
        Cell1.setCellStyle(style);

        Double value = withRounding ? round(Math.abs(inputVatMap.get("currentMonth") / 1000)) : Math.abs(inputVatMap.get("currentMonth"));
        XSSFCell Cell2 = Row.createCell(1);
        Cell2.setCellValue(value);
        Cell2.setCellStyle(style);

        XSSFCell Cell3 = Row.createCell(2);
        Cell3.setCellValue(withRounding ? round(Math.abs(inputVatMap.get("before1Month") / 1000)) : Math.abs(inputVatMap.get("before1Month")));
        Cell3.setCellStyle(style);

        XSSFCell Cell4 = Row.createCell(3);
        Cell4.setCellValue(withRounding ? round(Math.abs(inputVatMap.get("before2Month") / 1000)) : Math.abs(inputVatMap.get("before2Month")));
        Cell4.setCellStyle(style);

        XSSFCell Cell5 = Row.createCell(4);
        Cell5.setCellValue(withRounding ? round(Math.abs(inputVatMap.get("before3Month") / 1000)) : Math.abs(inputVatMap.get("before3Month")));
        Cell5.setCellStyle(style);

        XSSFCell Cell6 = Row.createCell(5);
        Cell6.setCellValue(0.0);
        Cell6.setCellStyle(style);

        XSSFCell Cell7 = Row.createCell(6);
        Cell7.setCellValue(value);
        Cell7.setCellStyle(style);

        XSSFCell Cell8 = Row.createCell(7);
        Cell8.setCellValue("");
        Cell8.setCellStyle(style);

        XSSFCell Cell9 = Row.createCell(8);
        Cell9.setCellValue("");
        Cell9.setCellStyle(style);
    }

    public void buildTotalInputVATCollectedRowForSummaryTable(
            Map<String, Double> inputVatMap,
            XSSFSheet spreadsheet,
            int rowId,
            XSSFWorkbook workbook,
            DataFormat fmt,
            boolean withRounding) {
        CellStyle style = createStyle(workbook, fmt, null, null, null);
        XSSFRow Row = spreadsheet.createRow(rowId);
        XSSFCell Cell1 = Row.createCell(0);
        Cell1.setCellValue("Input VAT paid");
        Cell1.setCellStyle(style);

        Double value = withRounding ? round(Math.abs(inputVatMap.get("currentMonth") / 1000)) : Math.abs(inputVatMap.get("currentMonth"));
        XSSFCell Cell2 = Row.createCell(1);
        Cell2.setCellValue(value);
        Cell2.setCellStyle(style);
    }

    // Jira ACC-1574
    @PreAuthorize("hasPermission('plcompanies','downloadreport')")
    @RequestMapping(value = "/showpagetransactionsbehindrow/{id}", method = RequestMethod.GET)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    @Transactional
    public ResponseEntity<?> showPageTransactionsBehindRow(
            Pageable pageable,
            Sort sort,
            @PathVariable(value = "id", required = false) Long nodeId,
            @RequestParam(required = false, value = "fromDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date fromDate,
            @RequestParam(required = false, value = "toDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date toDate,
            @RequestParam(value = "searchCriteria", defaultValue = "PNL_DATE") SearchCriteria searchCriteria) {
        BasePLNode node = null;

        if (nodeId != null)
            node = (BasePLNode) getBaseNodeRepository().findOne(nodeId);

        if (node == null)
            throw new RuntimeException("Node not found");

        List<TransactionAndDetailsResponse> transactionAndDetailsResponses = this.getTransactionAndDetailsBehindNode(node, sort, fromDate, toDate, searchCriteria);

        PageImpl s = (PageImpl) PaginationUtil.listToPage(pageable, transactionAndDetailsResponses).map(obj
                -> projectionFactory.createProjection(
                PLNodeTransactionProjection.class, obj));

        if (s != null) {
            TransactionPage transactionPage =
                    new TransactionPage(s.getContent(), pageable, s.getTotalElements(),
                            node.getpLNodeType().equals(PLNodeType.EXPENSES) ?
                                    transactionAndDetailsResponses.stream()
                                            .mapToDouble(TransactionAndDetailsResponse::getAmount).sum() :
                                    0,
                            0,
                            0,
                            node.getpLNodeType().equals(PLNodeType.EXPENSES) ?
                                    transactionAndDetailsResponses.stream()
                                            .mapToDouble(TransactionAndDetailsResponse::getVatAmount).sum()
                                    : 0);
            return new ResponseEntity<>(transactionPage, HttpStatus.OK);
        }
        return new ResponseEntity<>("Error when selecting transaction ", HttpStatus.BAD_REQUEST);
    }

    protected Class getVariableBucketEntityClass() {
        return null;
    }

    @PreAuthorize("hasPermission('plcompanies','exportTransactionsBehindNode')")
    @RequestMapping(value = "/exporttransactionsbehindrow/csv/{id}", method = RequestMethod.GET)
    public void exportTransactionsBehindRow(HttpServletResponse response,
                                            @PathVariable(value = "id", required = false) Long pLNodeId,
                                            Sort sort,
                                            @RequestParam(required = false, value = "fromDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date fromDate,
                                            @RequestParam(required = false, value = "toDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date toDate,
                                            @RequestParam(value = "withRounding", defaultValue = "false") Boolean withRounding,
                                            @RequestParam(value = "searchCriteria", defaultValue = "PNL_DATE") SearchCriteria searchCriteria) throws Exception {
        BasePLNode node = null;

        if (pLNodeId != null)
            node = (BasePLNode) getBaseNodeRepository().findOne(pLNodeId);

        if (node == null)
            throw new RuntimeException("Node not found");

        List<TransactionAndDetailsResponse> transactionAndDetailsResponses = this.getTransactionAndDetailsBehindNode(node, sort, fromDate, toDate, searchCriteria);

        String[] headers =
                {"Transaction ID", "Bucket From", "Revenue Name",
                        "Expense Name", "Bucket To", "Description",
                        "Amount (AED)", "VAT", "Average", "Profit Adjustment", "Date of Transaction", "Date of Creation"};

        String[] namesOrdered =
                {"id", "fromBucketName", "revenueName",
                        "expenseName", "toBucketName", "description",
                        "amount", "vatAmount", "averageAmount", "profitAdjustment", "pnlValueDate", "creationDate"};

        File excelFile;
        if (withRounding != null && withRounding) {
            excelFile = CsvHelper.generateCsv(transactionAndDetailsResponses, PLNodeTransactionCSVProjectionWithRounding.class, headers, namesOrdered,
                    "Transactions.csv");
        } else {
            excelFile = CsvHelper.generateCsv(transactionAndDetailsResponses, PLNodeTransactionCSVProjection.class, headers, namesOrdered,
                    "Transactions.csv");
        }
        
        InputStream is = null;
        
        try {
            is = new FileInputStream(excelFile);

            createDownloadResponse(response, "Transactions.csv", is);
        } finally {
            StreamsUtil.closeStream(is);
        }
    }

    private List<TransactionAndDetailsResponse> getTransactionAndDetailsBehindNode(BasePLNode node, Sort sort, Date fromDate, Date toDate, SearchCriteria searchCriteria) {
        List<Transaction> transactions = node.getTransactionsBehindNode(fromDate, toDate, searchCriteria);
        List<TransactionDetails> transactionDetails = node.getTransactionDetailsBehindNode(fromDate, toDate, searchCriteria);

        List<TransactionAndDetailsResponse> transactionAndDetailsResponses = new ArrayList();
        for (Transaction transaction : transactions) {
            transactionAndDetailsResponses.add(new TransactionAndDetailsResponse(transaction, null));
        }

        for (TransactionDetails transactionDetail : transactionDetails) {
            transactionAndDetailsResponses.add(new TransactionAndDetailsResponse(transactionDetail.getTransaction(), transactionDetail));
        }

        TransactionAndDetailsResponseComparator comparator = null;

        //Jirra ACC-2394
        if (sort == null) {
            Sort.Order order = new Sort.Order(Sort.Direction.DESC, "amount");
            sort = Sort.by(order);
        }

        for (Sort.Order order : sort) {
            comparator = new TransactionAndDetailsResponseComparator(order.getProperty(), order.getDirection().name());
        }
        if (comparator != null) {
            transactionAndDetailsResponses = transactionAndDetailsResponses.stream().sorted(comparator).collect(Collectors.toList());
        }

        return transactionAndDetailsResponses;
    }

    private Long round(Double value) {
        if (value < 0)
            return -Math.round(Math.abs(value));
        else return Math.round(value);
    }

    private double floor(Double value) {
        if (value < 0)
            return -Math.floor(Math.abs(value));
        else return Math.floor(value);
    }

    protected Map<String, Double> getInputVatMap(BaseReportCompany pLCompany, Date fromDate, Date toDate) {
        Map<String, Double> vatMap = new HashMap();

        DateTime now = new DateTime(fromDate);
        DateTime before1MonthDate = now.minusMonths(1).withDayOfMonth(1);
        DateTime before2MonthDate = now.minusMonths(2).withDayOfMonth(1);
        DateTime before3MonthDate = now.minusMonths(3).withDayOfMonth(1);

        try {
            Future<Double> currentMonthQueryValue = executor.submit(() -> pLCompany.calculateInputVATCollected(fromDate, toDate));
            Future<Double> before3MonthQueryValue = executor.submit(() -> pLCompany.calculateInputVATCollected(before3MonthDate.toDate(), before3MonthDate.dayOfMonth().withMaximumValue().toDate()));
            Future<Double> before2MonthQueryValue = executor.submit(() -> pLCompany.calculateInputVATCollected(before2MonthDate.toDate(), before2MonthDate.dayOfMonth().withMaximumValue().toDate()));
            Future<Double> before1MonthQueryValue = executor.submit(() -> pLCompany.calculateInputVATCollected(before1MonthDate.toDate(), before1MonthDate.dayOfMonth().withMaximumValue().toDate()));

            vatMap.put("currentMonth", currentMonthQueryValue.get());
            vatMap.put("before3Month", before3MonthQueryValue.get());
            vatMap.put("before2Month", before2MonthQueryValue.get());
            vatMap.put("before1Month", before1MonthQueryValue.get());
        } catch (Exception e) {
            throw new RuntimeException();
        }

        return vatMap;
    }

    protected Map<String, Double> getOutputVatMap(BaseReportCompany pLCompany, Date fromDate, Date toDate) {
        Map<String, Double> vatMap = new HashMap();

        DateTime now = new DateTime(fromDate);
        DateTime before1MonthDate = now.minusMonths(1).withDayOfMonth(1);
        DateTime before2MonthDate = now.minusMonths(2).withDayOfMonth(1);
        DateTime before3MonthDate = now.minusMonths(3).withDayOfMonth(1);

        try {
            Future<Double> currentMonthQueryValue = executor.submit(() -> pLCompany.calculateOutputVATCollected(fromDate, toDate));
            Future<Double> before3MonthQueryValue = executor.submit(() -> pLCompany.calculateOutputVATCollected(before3MonthDate.toDate(), before3MonthDate.dayOfMonth().withMaximumValue().toDate()));
            Future<Double> before2MonthQueryValue = executor.submit(() -> pLCompany.calculateOutputVATCollected(before2MonthDate.toDate(), before2MonthDate.dayOfMonth().withMaximumValue().toDate()));
            Future<Double> before1MonthQueryValue = executor.submit(() -> pLCompany.calculateOutputVATCollected(before1MonthDate.toDate(), before1MonthDate.dayOfMonth().withMaximumValue().toDate()));

            vatMap.put("currentMonth", currentMonthQueryValue.get());
            vatMap.put("before3Month", before3MonthQueryValue.get());
            vatMap.put("before2Month", before2MonthQueryValue.get());
            vatMap.put("before1Month", before1MonthQueryValue.get());
        } catch (Exception e) {
            throw new RuntimeException();
        }

        return vatMap;
    }

}
