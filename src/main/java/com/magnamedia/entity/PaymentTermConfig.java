package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.WordTemplate;
import com.magnamedia.core.serialize.IdLabelSerializer;

import javax.persistence.*;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Entity
//@Table(uniqueConstraints = {@UniqueConstraint(columnNames = {"CONTRACT_PROSPECT_TYPE_ID"})})
public class PaymentTermConfig extends AbstractPaymentTerm {

    @JsonIgnore
    @OneToMany(mappedBy = "paymentTermConfig", fetch = FetchType.LAZY)
    private List<PaymentTypeConfig> paymentTypeConfigs;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private WordTemplate paymentTermsTemplate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private WordTemplate taxInvoiceTemplate;

    @ManyToOne(fetch = FetchType.LAZY)
    private WordTemplate weeklyPaymentTermsTemplate;

    @ManyToOne(fetch = FetchType.LAZY)
    private WordTemplate weeklyTaxInvoiceTemplate;


    @Column(nullable = false)
    private boolean isDefault;

    @Column(nullable = false)
    private boolean showInSales;

    @Column
    @Label
    private String name;

    public boolean isDefault() { return isDefault; }

    public boolean isShowInSales() { return showInSales; }

    public String getName() { return name; }

    public List<PaymentTypeConfig> getPaymentTypeConfigs() {
        return paymentTypeConfigs;
    }

    public void setPaymentTypeConfigs(List<PaymentTypeConfig> paymentTypeConfigs) {
        this.paymentTypeConfigs = paymentTypeConfigs;
    }

    public WordTemplate getPaymentTermsTemplate() {
        return paymentTermsTemplate;
    }

    public void setPaymentTermsTemplate(WordTemplate paymentTermsTemplate) {
        this.paymentTermsTemplate = paymentTermsTemplate;
    }

    public WordTemplate getTaxInvoiceTemplate() {
        return taxInvoiceTemplate;
    }

    public void setTaxInvoiceTemplate(WordTemplate taxInvoiceTemplate) {
        this.taxInvoiceTemplate = taxInvoiceTemplate;
    }

    @Override
    List<AbstractPaymentTypeConfig> getPaymentTypeConfigs(String paymentTypeCode) {
        List<AbstractPaymentTypeConfig> paymentTypeConfigs = null;
        if (this.getPaymentTypeConfigs() != null) {
            paymentTypeConfigs =
                    this.getPaymentTypeConfigs().stream().filter(item -> item.getType().getCode().equals(paymentTypeCode))
                            .collect(Collectors.toList());
        }
        return paymentTypeConfigs != null && !paymentTypeConfigs.isEmpty() ? paymentTypeConfigs : null;
    }

    public WordTemplate getWeeklyPaymentTermsTemplate() {
        return weeklyPaymentTermsTemplate;
    }

    public void setWeeklyPaymentTermsTemplate(WordTemplate weeklyPaymentTermsTemplate) {
        this.weeklyPaymentTermsTemplate = weeklyPaymentTermsTemplate;
    }

    public WordTemplate getWeeklyTaxInvoiceTemplate() {
        return weeklyTaxInvoiceTemplate;
    }

    public void setWeeklyTaxInvoiceTemplate(WordTemplate weeklyTaxInvoiceTemplate) {
        this.weeklyTaxInvoiceTemplate = weeklyTaxInvoiceTemplate;
    }
}
