package com.magnamedia.helper;

import com.magnamedia.core.Setup;
import org.apache.commons.lang3.text.WordUtils;
import org.springframework.data.projection.ProjectionFactory;
import org.supercsv.cellprocessor.*;
import org.supercsv.cellprocessor.ift.CellProcessor;
import org.supercsv.io.CsvBeanReader;
import org.supercsv.io.CsvBeanWriter;
import org.supercsv.io.ICsvBeanReader;
import org.supercsv.io.ICsvBeanWriter;
import org.supercsv.prefs.CsvPreference;
import org.supercsv.util.CsvContext;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.*;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Sep 10, 2019
 *         Jirra ACC-837
 */
public class CsvHelper {

    public static <T, Y> File generateCsv(
            List<T> data,
            Class<Y> projectionClass,
            String[] headers,
            String[] columns,
            String fileName) throws IOException {
        return generateCsv(data, projectionClass, headers, columns, fileName, null);
    }

    public static <T, Y> File generateCsv(
            List<T> data,
            Class<Y> projectionClass,
            String[] headers,
            String[] columns,
            String fileName,
            String extension) throws IOException {

        File file = Paths.get(System.getProperty("java.io.tmpdir"),
                fileName + (extension == null ? ".csv" : extension))
                .toFile();
        FileOutputStream os = new FileOutputStream(file);
        os.write(0xef);
        os.write(0xbb);
        os.write(0xbf);

        try (ICsvBeanWriter beanWriter = new CsvBeanWriter(
                new OutputStreamWriter(os, "UTF-8"),
                CsvPreference.EXCEL_PREFERENCE)) {
            beanWriter.writeHeader(headers);
            for (Y c : project(data, projectionClass)) {
                beanWriter.write(c, columns);
            }
        }
        return file;
    }

    public static <T, Y> File generateCsv(
            List<T> data,
            Class<Y> projectionClass,
            String[] headers,
            String[] columns,
            String fileName,
            String extension,
            String[] fileHeaders) throws IOException {

        File file = Paths.get(System.getProperty("java.io.tmpdir"),
                        fileName + (extension == null ? ".csv" : extension))
                .toFile();
        FileOutputStream os = new FileOutputStream(file);
        os.write(0xef);
        os.write(0xbb);
        os.write(0xbf);

        try (ICsvBeanWriter beanWriter = new CsvBeanWriter(
                new OutputStreamWriter(os, "UTF-8"),
                CsvPreference.EXCEL_PREFERENCE)) {
            Arrays.stream(fileHeaders).forEach(s -> {
                try {
                    beanWriter.writeComment(s) ;
                } catch (IOException e) {
                    e.printStackTrace();
                }
            });
            beanWriter.writeHeader(headers);
            for (Y c : project(data, projectionClass)) {
                beanWriter.write(c, columns);
            }
        }
        return file;
    }

    public static <T, Y> File generateCsv(
            List<T> data,
            Class<Y> projectionClass,
            String[] columns,
            String fileName,
            String extension) throws IOException {

        File file = Paths.get(System.getProperty("java.io.tmpdir"),
                fileName + (extension == null ? ".csv" : extension))
                .toFile();
        FileOutputStream os = new FileOutputStream(file);
        os.write(0xef);
        os.write(0xbb);
        os.write(0xbf);

        try (ICsvBeanWriter beanWriter = new CsvBeanWriter(
                new OutputStreamWriter(os, "UTF-8"),
                CsvPreference.EXCEL_PREFERENCE)) {
            for (Y c : project(data, projectionClass)) {
                beanWriter.write(c, columns);
            }
        }
        return file;
    }

    public static <T, Y> List<Y> project(
            List<T> entities, Class<Y> projectionClass) {

        ProjectionFactory pf = Setup.getProjectionFactory();
        return entities.stream().map(
                (entity) ->
                {
                    return pf.createProjection(projectionClass, entity);
                })
                .collect(Collectors.toList());
    }

    public static <Y> List<Y> readCsv(InputStream is, Class<Y> projectionClass, String[] columns, boolean readHeader) throws Exception {
        List<Y> retList = new ArrayList();

        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(is, "UTF-8"));
        ICsvBeanReader beanReader = new CsvBeanReader(bufferedReader, CsvPreference.STANDARD_PREFERENCE);

        if (readHeader) {
            String[] headers = beanReader.getHeader(true);
            columns = new String[headers.length];
            int index = 0;
            for (String header : headers) {
                columns[index++] = WordUtils.capitalize(header.replaceAll("\\s", ""), ' ');
            }
        }

        Y data;

        while ((data = beanReader.read(projectionClass, columns, getProcessors(projectionClass, columns))) != null) {
            // process course
            retList.add(data);

        }

        return retList;
    }

    private static <Y> CellProcessor[] getProcessors(Class<Y> projectionClass, String[] columns) throws IntrospectionException {
        CellProcessor[] processors = new CellProcessor[columns.length];

        BeanInfo beanInfo = Introspector.getBeanInfo(projectionClass);
        int index = 0;
        for (String column : Arrays.asList(columns)) {
            PropertyDescriptor propertyDescriptor = null;
            for (PropertyDescriptor pD : beanInfo.getPropertyDescriptors()) {
                if (pD.getName().equalsIgnoreCase(column)) {
                    propertyDescriptor = pD;
                    break;
                }
            }
            if (propertyDescriptor != null) {
                processors[index] = mapClassTypeToCellProcessor(propertyDescriptor.getPropertyType());
            }

            index++;
        }
        return processors;
    }

    private static CellProcessor mapClassTypeToCellProcessor(Class classType) {
        if (classType.equals(String.class))
            return new Optional(new TrimmerCellProcessor());
        if (classType.equals(Integer.class))
            return new Optional(new TrimmerCellProcessor(new ParseInt()));
        if (classType.equals(Long.class))
            return new Optional(new TrimmerCellProcessor(new ParseLong()));
        if (classType.equals(Double.class))
            return new Optional(new TrimmerCellProcessor(new ParseDouble()));
        if (classType.equals(Date.class))
            return new Optional(new TrimmerCellProcessor(new ParseDate("yyyy-MM-dd")));
        if (classType.equals(Boolean.class))
            return new Optional(new TrimmerCellProcessor(new ParseBool()));

        return null;
    }

    public static int getCsvMailMaxLimit(String paramKey, int defaultValue) {
        try {
            return Integer.parseInt(
                    Setup.getParameter(Setup.getCurrentModule(), paramKey));
        } catch (Exception ignore) {
            return defaultValue;
        }
    }

    public static class TrimmerCellProcessor extends CellProcessorAdaptor {
        public TrimmerCellProcessor() {
            super();
        }

        public TrimmerCellProcessor(CellProcessor next) {
            // this constructor allows other processors to be chained after ParseDay
            super(next);
        }

        public Object execute(Object value, CsvContext context) {

            return next.execute(value.toString().trim(), context);

        }
    }
}
