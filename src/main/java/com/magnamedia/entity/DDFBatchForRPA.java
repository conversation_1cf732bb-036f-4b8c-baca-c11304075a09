package com.magnamedia.entity;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.module.type.DDFBatchStatus;
import com.magnamedia.repository.DirectDebitFileRepository;

import javax.persistence.*;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Sep 19, 2020
 *         Jirra ACC-2571
 */

@Entity
public class DDFBatchForRPA extends BaseEntity {

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private DDFBatchStatus status = DDFBatchStatus.NOT_SENT;
    
    @Column(columnDefinition = "boolean default true")
    private Boolean sendByRPA = Boolean.TRUE;

    @Column
    @Lob
    private String ids;
    
    @Transient
    private String references;

    public DDFBatchStatus getStatus() {
        return status;
    }

    public void setStatus(DDFBatchStatus status) {
        this.status = status;
    }
    
    public Boolean getSendByRPA() {
        return sendByRPA;
    }

    public void setSendByRPA(Boolean sendByRPA) {
        this.sendByRPA = sendByRPA;
    }
    
    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }

    public String getReferences() {
        
        this.references = "";  
        DirectDebitFileRepository directDebitFileRepository = Setup.getRepository(DirectDebitFileRepository.class);
        if (ids != null && !ids.isEmpty()){
            
            List<Long> idsLong = Arrays.stream(this.getIds().split(",")).map(x -> Long.parseLong(x.trim())).collect(Collectors.toList());
            List<DirectDebitFile> allDirectDebitFiles = directDebitFileRepository.findAll(idsLong);
            references = allDirectDebitFiles.stream().map(x -> x.getDdaRefNo()).collect(Collectors.toList()).toString();
        }
        return references;
    }
    
}
