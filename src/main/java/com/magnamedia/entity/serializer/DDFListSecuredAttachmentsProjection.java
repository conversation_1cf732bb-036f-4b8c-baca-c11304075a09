package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.entity.DirectDebitFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Nov 21, 2020
 *         Jirra ACC-2743
 */

public class DDFListSecuredAttachmentsProjection extends JsonSerializer<List<DirectDebitFile>> {

    @Override
    public void serialize(List<DirectDebitFile> values, JsonGenerator gen, SerializerProvider serializers)
            throws IOException {
        gen.writeStartArray();

        if (values != null) {
            for (DirectDebitFile value : values) {
                writeNode(value, gen);
            }
        }

        gen.writeEndArray();
    }

    private void writeNode(DirectDebitFile value, JsonGenerator gen) throws IOException {
        List<Attachment> attachments = value.getAttachments();
        value.setAttachments(value.getSecuredAttachments());

//        gen.writeStartObject();
        gen.writeObject(value);
//        gen.writeEndObject();

        value.setAttachments(attachments);
    }
}