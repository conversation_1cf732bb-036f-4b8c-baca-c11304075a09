package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 * Created by Ma<PERSON>.Masod on 2/27/2021.
 */
public enum DirectDebitMessagingWhenToStartSending implements LabelValueEnum {

    DIRECTLY_ON_CONTACT_CREATION("Directly on contract creation"),
    FIRST_OF_NEXT_MONTH_OF_CONTACT_CREATION("1st of next month of contract creation");

    private final String label;

    DirectDebitMessagingWhenToStartSending(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
