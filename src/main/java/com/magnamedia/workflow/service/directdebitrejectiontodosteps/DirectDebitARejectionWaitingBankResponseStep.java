package com.magnamedia.workflow.service.directdebitrejectiontodosteps;

import com.magnamedia.controller.DirectDebitController;
import com.magnamedia.core.Setup;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.ContractPayment;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DirectDebitRejectionToDoType;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.repository.DirectDebitFileRepository;
import com.magnamedia.repository.DirectDebitRejectionToDoRepository;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.service.ContractService;
import com.magnamedia.service.DirectDebitRejectionFlowService;
import com.magnamedia.service.DirectDebitSignatureService;
import com.magnamedia.workflow.service.DirectDebitRejectionToDoManualStep;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on 4-4-2020
 *         Jirra ACC-1595
 */
@Service
public class DirectDebitARejectionWaitingBankResponseStep
        extends DirectDebitRejectionToDoManualStep<DirectDebitRejectionToDo> {

    @Autowired
    private DirectDebitRepository directDebitRepository;

    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;

    @Autowired
    DirectDebitRejectionFlowService directDebitRejectionFlowService;

    @Autowired
    private DirectDebitRejectionToDoRepository directDebitRejectionToDoRepository;

    @Autowired
    private DirectDebitController directDebitController;

    @Autowired
    private DirectDebitSignatureService directDebitSignatureService;

    private static final Logger logger =
            Logger.getLogger(DirectDebitARejectionWaitingBankResponseStep.class.getName());

    public DirectDebitARejectionWaitingBankResponseStep() {
        this.setId(DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE.toString());
    }

    @Override
    public void onSave(DirectDebitRejectionToDo entity) {
    }

    @Override
    public void postSave(DirectDebitRejectionToDo entity) {
    }

    @Override
    public void onDone(DirectDebitRejectionToDo entity) { // this method will be called from BR when all dds rejected & from job that checks reminder after 24 hour
        super.onDone(entity);

        /*boolean createExpertTodo = false;
        String reasonToCall = "";
        String initialNotes = "";*/
        boolean scheduleForTermination = false;
        boolean dontSendDdMessage = false;
        boolean leadingRejectionFlow = false;

        Integer maxTrials =
                Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_MAX_TRIALS));

        DirectDebit lastRejected = entity.getLastDirectDebit();
        
        if (lastRejected != null) {
            ContractPaymentTerm contractPaymentTerm = lastRejected.getContractPaymentTerm();

            logger.log(Level.SEVERE, "DirectDebitARejectionWaitingBankResponseStep lastRejected: " + lastRejected.getId());

            if (lastRejected.getRejectCategory() != entity.getLastRejectCategory()) {
                logger.log(Level.SEVERE, "DirectDebitARejectionWaitingBankResponseStep make trials 0 because of change rejection category" +
                        " to : " + lastRejected.getRejectCategory() + " from : " + entity.getLastRejectCategory());
                entity.setTrials(0);
            }

            if (null != lastRejected.getRejectCategory()) switch (lastRejected.getRejectCategory()) {
                case Compliance:
                case Other:{
                    DirectDebit newDD = lastRejected.clone(DirectDebitStatus.PENDING);
                    directDebitRepository.save(newDD);

                    lastRejected.cloneChildDds(newDD, 0);
                    
                    addNewTask(entity, DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE.toString());
                    break;
                }
                
                case Signature:
                    Integer maxReSignTrials = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                            AccountingModule.PARAMETER_DD_MAX_RE_SIGN_TRIALS));
                    
                    logger.log(Level.SEVERE, "DirectDebitARejectionWaitingBankResponseStep maxReSignTrials: " + maxReSignTrials);
                    logger.log(Level.SEVERE, "DirectDebitARejectionWaitingBankResponseStep entity.getReSignTrials(): " + entity.getReSignTrials());
                    //ACC-4715
                    if (directDebitRejectionFlowService.flowStoppedAfterIncreaseReSignTrials(entity)) return;

                    if (entity.getReSignTrials() > maxReSignTrials) {
                        scheduleForTermination = true;    
                    } else if (entity.getReSignTrials() <= maxReSignTrials) {
                        logger.log(Level.SEVERE, "DirectDebitARejectionWaitingBankResponseStep lastRejected id: {0}", lastRejected.getId());

                        // same condition of (Are there other DDs approved in this contract?)
                        if (directDebitRejectionFlowService.shouldGenerateNewDdUsingOldSignatures(
                                entity.getReSignTrials(), contractPaymentTerm)) {

                            // generate new dd from current contract payment term
                            List<ContractPayment> contractPayment = new ArrayList<>();
                            for (ContractPayment payment : lastRejected.getContractPayments()) {
                                payment.setDirectDebit(null);
                                contractPayment.add(payment);
                            }
                            
                            List<DirectDebit> directDebits = directDebitController.generateDD(contractPayment,
                                    null, lastRejected.getContractPaymentTerm(), true, false,
                                    true, false, false, false, false);
                            
                            for (DirectDebit dd : directDebits) {
                                directDebitRejectionFlowService.mergePendingDataEntryDDsIntoOneToDo(dd);
                            }
                            
                            for (DirectDebit dd : directDebits) {
                                dd = directDebitRepository.findOne(dd.getId());
                                dd.setImageForDD(lastRejected.getImageForDD());
                                dd.setDirectDebitRejectionToDo(entity);
                                directDebitRepository.save(directDebits);
                            }
                            
                            dontSendDdMessage = true;
                            addNewTask(entity, DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE.toString());
                            
                        } else { // not using same signature
                            DirectDebit newDD = lastRejected.clone(DirectDebitStatus.IN_COMPLETE);
                            newDD.setNonCompletedInfo(true);
                            newDD.setConfirmedBankInfo(false);
                            newDD.setDirectDebitRejectionToDo(entity);
                            newDD.setAttachments(new ArrayList<>());
                            directDebitRepository.save(newDD);
                            
                            entity.setReminder(0);
                            entity.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
                            
                            logger.log(Level.SEVERE, "DirectDebitARejectionWaitingBankResponseStep before add new task");
                            // ACC-2860
                            leadingRejectionFlow = !directDebitRejectionFlowService.existOtherWaitingClientSignatureFlow(
                                    contractPaymentTerm.getContract(), Arrays.asList(lastRejected.getId()));
                            
                            addNewTask(entity, DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE.toString());
                            
                            /*if (entity.getReSignTrials() == maxReSignTrials) {
                                createExpertTodo = leadingRejectionFlow;
                                reasonToCall = "Client signature is rejected from bank side for " + maxReSignTrials + " time";
                            }*/
                        }
                    }
                    break;
                
                /*case Account:
                case EID:
                    lastRejected.setMStatus(DirectDebitStatus.PENDING_DATA_ENTRY);
                    lastRejected.setManualDdfFile(null);
                    lastRejected.setConfirmedBankInfo(false);
                    directDebitRejectionFlowService.sendDDFsBackToAccountant(lastRejected);
                    
                    dontSendDdMessage = true;
                    addNewTask(entity, DirectDebitRejectionToDoType.WAITING_ACCOUNTANT_ACTION.toString());
                    break;*/

                case Account:
                case EID:
                case Invalid_Account:
                case Authorization:{
                    // make the rejection flow waits for client to provide new info
                    leadingRejectionFlow = !directDebitRejectionFlowService.existOtherWaitingClientSignatureFlow(
                            contractPaymentTerm.getContract(), Arrays.asList(lastRejected.getId()));
                    addNewTask(entity, DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE.toString());
                    
                    entity.setTrials(entity.getTrials() + 1);
                    entity.setReminder(0);
                    
                    /*if (entity.getTrials() == (maxTrials - 1) && leadingRejectionFlow) {
                        createExpertTodo = true;
                        reasonToCall = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_AUTH_REJECT_EXPERT_TODO_REASON_TO_CALL);
                        initialNotes = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_AUTH_REJECT_EXPERT_TODO_INITIAL_NOTES);
                    }*/
                    if (entity.getTrials() > maxTrials) {
                        scheduleForTermination = true;
                    }
                    DirectDebit newDD = lastRejected.clone(DirectDebitStatus.IN_COMPLETE);
                    newDD.setNonCompletedInfo(true);
                    newDD.setConfirmedBankInfo(false);
                    newDD.setDirectDebitRejectionToDo(entity);
                    newDD.setAttachments(new ArrayList<>());
                    directDebitRepository.save(newDD);
                    
                    entity.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
                    break;
                }
                
                /*case Invalid_Account:{
                    DirectDebit newDD = lastRejected.clone(DirectDebitStatus.IN_COMPLETE);
                    newDD.setNonCompletedInfo(true);
                    newDD.setConfirmedBankInfo(false);
                    newDD.setDirectDebitRejectionToDo(entity);
                    newDD.setAttachments(new ArrayList<>());
                    directDebitRepository.save(newDD);
                    
                    entity.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
                    entity.setTrials(entity.getTrials() + 1);
                    entity.setReminder(0);
                    if (entity.getTrials() > maxTrials) {
                        scheduleForTermination = true;
                    }
                    
                    logger.log(Level.SEVERE, "DirectDebitARejectionWaitingBankResponseStep before add new task");
                    // make the rejection flow waits for client resign
                    addNewTask(entity, DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE.toString());
                    
                    leadingRejectionFlow = !directDebitRejectionFlowService.existOtherWaitingClientSignatureFlow(
                            contractPaymentTerm.getContract(), Arrays.asList(lastRejected.getId()));
                    break;
                }*/
                default:
                    break;
            }

            entity.setDontSendDdMessage(dontSendDdMessage);
            entity.setLeadingRejectionFlow(leadingRejectionFlow);

            if (scheduleForTermination) {
                Contract contract = lastRejected.getContractPaymentTerm().getContract();
                
                if (contract.isTerminateContractDueRejection() || !entity.isDdAddedByOecFlow()) {
                    logger.info("contract.terminateContractDueRejection: " + contract.isTerminateContractDueRejection() +
                            "; entity.isDdAddedByOecFlow: " + entity.isDdAddedByOecFlow());

                    entity.setContractScheduleDateOfTermination(
                            directDebitRejectionFlowService.setContractForTermination(
                                    lastRejected.getContractPaymentTerm(),
                                    "direct_debit_rejection_type_a_maxsignaturetrials_reached",
                                    entity));
                    entity.setLeadingRejectionFlow(true);
                }
                entity.setCompleted(true);
                entity.setStopped(true);
            }

            /*if (createExpertTodo) {
                entity.setVoiceResolverTodoId(directDebitRejectionFlowService.createExpertTodo(lastRejected, reasonToCall, initialNotes));
                entity.setLastTrialDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
            }*/

            entity.setLastRejectCategory(lastRejected.getRejectCategory());
            directDebitRejectionToDoRepository.save(entity);
        }
    }

    @Override
    public void postDone(DirectDebitRejectionToDo entity) {
    }
}
