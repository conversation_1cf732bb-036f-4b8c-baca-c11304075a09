package com.magnamedia.helper;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.entity.workflow.ExpensePayment;

/**
 * <PERSON> (Feb 21, 2021)
 */
public class AttachmentHelper {
    public static Attachment getRequestAttachment(BaseEntity request, String tag) {
        for (Attachment a : request.getAttachments()) {
            Attachment attachment = Setup.getRepository(AttachementRepository.class).findOne(a.getId());
            if (attachment.getTag().equals(tag))
                return attachment;
        }
        return null;
    }
}
