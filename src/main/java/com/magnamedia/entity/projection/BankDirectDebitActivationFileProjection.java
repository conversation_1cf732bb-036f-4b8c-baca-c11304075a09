package com.magnamedia.entity.projection;

import java.util.Date;
import org.springframework.beans.factory.annotation.Value;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Dec 17, 2018
 * Jirra ACC-330
 */
public interface BankDirectDebitActivationFileProjection {
    
    public Long getId();
    
    public String getBankName();

    public Date getDate();
    
    @Value("#{target.getAttachments().size()>0?target.getAttachments().get(0).getName():null}")
    public String getFileName();
    
    @Value("#{target.getAttachments().size()>0?target.getAttachments().get(0).getUuid():null}")
    public String getUuid();

    //Jirra ACC-1400
    Boolean getFileParsed();
}
