/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.entity.projection;

import java.util.Map;
import org.springframework.beans.factory.annotation.Value;

/**
 *
 * <AUTHOR>
 */
public interface Housemaidspayrollpage {
    Long getId();
    
    Double getAmount();
    
    @Value("#{{id:target.getHousemaid().getId(), name:target.getHousemaid().getLabel(), status:target.getHousemaid().getStatus()}}")
    Map<?, ?> getHousemaid();
}
