package com.magnamedia.entity.dto.salesbinder;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <PERSON> (Jan 25, 2021)
 */
public class SupplierDto {
    private String name;
    @JsonProperty("id")
    private String supplierId;

    @JsonProperty("url")
    private String webSite;

    @JsonProperty("office_phone")
    private String phoneNumber;

    @JsonProperty("office_email")
    private String email;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId;
    }

    public String getWebSite() {
        return webSite;
    }

    public void setWebSite(String webSite) {
        this.webSite = webSite;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}
