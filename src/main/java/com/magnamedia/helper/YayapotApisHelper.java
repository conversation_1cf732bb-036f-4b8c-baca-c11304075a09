/*
package com.magnamedia.helper;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.module.AccountingModule;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

*/
/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Apr 15, 2018
 * Jirra ACC-1092
 *//*

@Component
public class YayapotApisHelper {
    
    private static final Logger logger = Logger.getLogger(
            BaseRepositoryController.class.getName());
    
    public String getRequest(String uri) {
        HttpURLConnection con = null;
        
        try {
            URL url = new URL(uri);
            String userName = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.YAYA_USER);
            String pwd = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.YAYA_PASSWORD);
            
            con = (HttpURLConnection) url.openConnection();
            con.setRequestMethod("GET");
            con.setRequestProperty("Content-Type", "application/json");
            con.setRequestProperty("Cache-Control", "no-cache");
            con.setDoOutput(true);
            String encoded = Base64.getEncoder().encodeToString(
                    (userName + ":" + pwd).getBytes(StandardCharsets.UTF_8));
            con.setRequestProperty("Authorization", "Basic " + encoded);
            
            String response = getResponsebody(con);
            
            return response;
        } catch (Exception ex) {
            logger.log(Level.SEVERE, null, ex);    
            return "Error";
        } finally {
            if (con != null) {
                con.disconnect();
            }
        }
    }
    public String postRequest(String uri, String jsonString, Integer timeOut){
        HttpURLConnection con = null;
        
        try {
            URL url = new URL(uri.toString());
            String userName = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.YAYA_USER);
            String pwd = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.YAYA_PASSWORD);
            
            con = (HttpURLConnection) url.openConnection();
            if(timeOut!=null && timeOut>0)
                con.setConnectTimeout(timeOut);
            con.setRequestMethod("POST");
            con.setRequestProperty("Content-Type", "application/json");
            con.setRequestProperty("Cache-Control", "no-cache");
            con.setDoOutput(true);
            String encoded = Base64.getEncoder().encodeToString(
                    (userName + ":" + pwd).getBytes(StandardCharsets.UTF_8));
            con.setRequestProperty("Authorization", "Basic " + encoded);

            BufferedWriter out
                    = new BufferedWriter(new OutputStreamWriter(con.getOutputStream()));
            out.write(jsonString);
            out.close();
            return getResponsebody(con);
        } catch (Exception ex) {
            Logger.getLogger(AccountingModule.class.getName()).log(Level.SEVERE, null, ex);
            return "Error";
        } finally {
            if (con != null) {
                con.disconnect();
            }
        }
    }
    
    public String postRequest(String uri, JSONObject data) {
        HttpURLConnection con = null;
        
        try {
            URL url = new URL(uri.toString());
            String userName = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.YAYA_USER);
            String pwd = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.YAYA_PASSWORD);
            
            con = (HttpURLConnection) url.openConnection();
            con.setRequestMethod("POST");
            con.setRequestProperty("Content-Type", "application/json");
            con.setRequestProperty("Cache-Control", "no-cache");
            con.setDoOutput(true);
            String encoded = Base64.getEncoder().encodeToString(
                    (userName + ":" + pwd).getBytes(StandardCharsets.UTF_8));
            con.setRequestProperty("Authorization", "Basic " + encoded);

            String jsonString = data.toString();

            BufferedWriter out
                    = new BufferedWriter(new OutputStreamWriter(con.getOutputStream()));
           */
/* jsonString = "{\"count\":3,\"id\":10378,\"sfId\":\"f9755d6e-dcb6-4d44-8048-33d5b1385889\",\"botIds\":[\"****************\"]}";*//*

            out.write(jsonString);
            out.close();
            return getResponsebody(con);
        } catch (Exception ex) {
            DebugHelper.sendExceptionMail(Setup.getParameter(Setup.getModule("clientmgmt"), AccountingModule.ClientModule_PARAMETER_WARNING_EMAILS), ex, "exception in yaya bot Apis Helper", false);
            Logger.getLogger(YayapotApisHelper.class.getName()).log(Level.SEVERE, null, ex);
            return "Error";
        } finally {
            if (con != null) {
                con.disconnect();
            }
        }
    }
    
    private String getResponsebody(HttpURLConnection con){
        try{
            StringBuilder sb = new StringBuilder();
            int HttpResult = con.getResponseCode();
            if (HttpResult == HttpURLConnection.HTTP_OK) {
                BufferedReader br = new BufferedReader(
                        new InputStreamReader(con.getInputStream(), "utf-8"));
                String line = null;
                while ((line = br.readLine()) != null) {
                    sb.append(line + "\n");
                }
                br.close();
                return sb.toString();
            } else {
                return "Error";
            }
        }
        catch(Exception ex){
            return "Error";
        }
    }
    
}
*/
