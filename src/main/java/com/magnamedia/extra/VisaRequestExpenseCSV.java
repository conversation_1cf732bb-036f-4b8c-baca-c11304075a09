package com.magnamedia.extra;


import com.magnamedia.module.type.PaymentType;

import java.text.DecimalFormat;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class VisaRequestExpenseCSV {

    private Long visaRequestExpenseID;
    private String visaExpenseType;
    private String name;
    private Date creationDate;
    private EmployeeType employeeType;
    private String contractType;
    private String housemaidName;
    private PaymentType paymentType;
    private String description;
    private Double amount;
    private String fromBucketName;
    private String expenseName;
    private String referenceNumber;


    public VisaRequestExpenseCSV(
            Long id, String visaExpenseType, Date creationDate, String housemaidName,
            String description, String fromBucketName, String expenseName, PaymentType paymentType,
            Double amount, String referenceNumber, EmployeeType employeeType, String contractType) {

        setVisaRequestExpenseID(id);
        setVisaExpenseType(visaExpenseType);
        setCreationDate(creationDate);
        setName(getVisaExpenseType().replace("RequestExpense", "") + "_" + getVisaRequestExpenseID());
        setHousemaidName(housemaidName);
        setPaymentType(paymentType);
        setDescription(description);
        setFromBucketName(fromBucketName);
        setExpenseName(expenseName);
        setAmount((double) Math.round(amount * 100) / 100);
        setReferenceNumber(referenceNumber);
        setEmployeeType(employeeType);
        setContractType(contractType);
    }

    public Long getVisaRequestExpenseID() {
        return visaRequestExpenseID;
    }

    public void setVisaRequestExpenseID(Long visaRequestExpenseID) {
        this.visaRequestExpenseID = visaRequestExpenseID;
    }

    public String getVisaExpenseType() {
        return visaExpenseType;
    }

    public void setVisaExpenseType(String visaExpenseType) { this.visaExpenseType = visaExpenseType; }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public EmployeeType getEmployeeType() {
        return employeeType;
    }

    public void setEmployeeType(EmployeeType employeeType) {
        this.employeeType = employeeType;
    }

    public String getHouseMaidName() {
        return housemaidName;
    }

    public void setHousemaidName(String housemaidName) {
        this.housemaidName = housemaidName;
    }

    public PaymentType getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(PaymentType paymentType) {
        this.paymentType = paymentType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getAmount() {
        DecimalFormat decimalFormat = new DecimalFormat("############.##");
        return decimalFormat.format(amount);
    }

    public String getContractType() { return contractType; }

    public void setContractType(String contractType) { this.contractType = contractType; }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public String getFromBucketName() {
        return fromBucketName;
    }

    public void setFromBucketName(String fromBucketName) {
        this.fromBucketName = fromBucketName;
    }

    public String getExpenseName() {
        return expenseName;
    }

    public void setExpenseName(String expenseName) {
        this.expenseName = expenseName;
    }

    public String getReferenceNumber() { return referenceNumber; }

    public String getReferenceNumberCSV() { return getReferenceNumber() != null ? "'" + getReferenceNumber() : ""; }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }
}