package com.magnamedia.workflow.service.directdebitrejectiontodosteps;

import com.magnamedia.controller.DirectDebitController;
import com.magnamedia.core.Setup;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.extra.DDUtils;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DirectDebitMethod;
import com.magnamedia.module.type.DirectDebitRejectionToDoType;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.module.type.SwitchingType;
import com.magnamedia.repository.DirectDebitFileRepository;
import com.magnamedia.repository.DirectDebitRejectionToDoRepository;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.service.DirectDebitRejectionFlowService;
import com.magnamedia.service.DirectDebitSignatureService;
import com.magnamedia.service.SwitchingBankAccountService;
import com.magnamedia.service.SwitchingNationalityService;
import com.magnamedia.workflow.service.DirectDebitRejectionToDoManualStep;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on 4-4-2020
 *         Jirra ACC-1595
 */
@Service
public class DirectDebitBCaseDRejectionWaitingBankResponseStep
        extends DirectDebitRejectionToDoManualStep<DirectDebitRejectionToDo> {

    @Autowired
    private DirectDebitRepository directDebitRepository;

    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;

    @Autowired
    private DirectDebitRejectionToDoRepository directDebitRejectionToDoRepository;

    @Autowired
    DirectDebitRejectionFlowService directDebitRejectionFlowService;

    @Autowired
    private DirectDebitController directDebitCtrl;

    @Autowired
    private DirectDebitSignatureService directDebitSignatureService;

    private static final Logger logger =
            Logger.getLogger(DirectDebitBCaseDRejectionWaitingBankResponseStep.class.getName());

    public DirectDebitBCaseDRejectionWaitingBankResponseStep() {
        this.setId(DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B_CASE_D.toString());
    }

    @Override
    public void onSave(DirectDebitRejectionToDo entity) {
    }

    @Override
    public void postSave(DirectDebitRejectionToDo entity) {
    }

    @Override
    public void onDone(DirectDebitRejectionToDo entity) { // this method will be called from BR when all dds rejected & from job that checks reminder after 24 hour
        super.onDone(entity);

        DirectDebit lastRejected = entity.getLastDirectDebit();

        DirectDebitRejectionFlowService directDebitRejectionFlowService =
                Setup.getApplicationContext().getBean(DirectDebitRejectionFlowService.class);

        if (lastRejected != null) {
            boolean dontSendDdMessage = false;
            boolean leadingRejectionFlow = false;

            // check if all rejected again then do the type a work flow else do the type b normal work flow
            boolean allManualRejected = directDebitRejectionFlowService.allManualFilesRejected(lastRejected.getDirectDebitFiles());
            boolean allAutoRejected = directDebitRejectionFlowService.allAutoFilesRejected(lastRejected.getDirectDebitFiles());

            DirectDebitConfiguration ddConfiguration = lastRejected.getDdConfiguration();
            logger.info("directDebitConfiguration IsIncludeManuals: " + ddConfiguration.isIncludeManualInDDBFlow());

            if ((allAutoRejected && allManualRejected)
                    || (allAutoRejected && !lastRejected.isGenerateManualDDFsFromConfig())) {
                
                /*boolean createExpertTodo = false;
                String reasonToCall = "";
                String initialNotes = "";*/
                boolean scheduleForTermination = false;

                Integer maxTrials =
                        Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_MAX_TRIALS));

                ContractPaymentTerm contractPaymentTerm = lastRejected.getContractPaymentTerm();
                SwitchingNationalityService switchingNationalityService = Setup.getApplicationContext().getBean(SwitchingNationalityService.class);
                SwitchingBankAccountService switchingBankAccountService = Setup.getApplicationContext().getBean(SwitchingBankAccountService.class);

                SwitchingType switchingType = switchingNationalityService.relatesToSwitching(lastRejected.getId(), false) ?
                        SwitchingType.NATIONALITY :
                        switchingBankAccountService.isClientSwitchingBankAccount(lastRejected) ? SwitchingType.BANK_ACCOUNT : null;

                if (switchingType != null) {
                    try {
                        handleSwitchingRejection(lastRejected, entity, switchingType);
                        return;
                    } catch (Exception e) {
                        e.printStackTrace();
                        throw new RuntimeException(e.getMessage());
                    }
                }

                if (lastRejected.getRejectCategory() != entity.getLastRejectCategory()) {
                    entity.setTrials(0);
                }

                if (null != lastRejected.getRejectCategory()) switch (lastRejected.getRejectCategory()) {
                    case Compliance:
                    case Other:{
                        DirectDebit newDD = lastRejected.clone(DirectDebitStatus.PENDING);
                        newDD.setGenerateManualDDFs(lastRejected.isGenerateManualDDFsFromConfig() && ddConfiguration.isIncludeManualInDDBFlow());
                        directDebitRepository.save(newDD);

                        lastRejected.cloneChildDds(newDD, 0);

                        addNewTask(entity, DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B_CASE_D.toString());    
                        break;
                    }
                    
                    case Signature:
                        Integer maxReSignTrials = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                                AccountingModule.PARAMETER_DD_MAX_RE_SIGN_TRIALS));
                        logger.log(Level.SEVERE, "DirectDebitBCaseDRejectionWaitingBankResponseStep maxReSignTrials: " + maxReSignTrials);
                        logger.log(Level.SEVERE, "DirectDebitBCaseDRejectionWaitingBankResponseStep entity.getReSignTrials(): " + entity.getReSignTrials());
                        //ACC-4715
                        if (directDebitRejectionFlowService.flowStoppedAfterIncreaseReSignTrials(entity)) return;

                        if (entity.getReSignTrials() > maxReSignTrials) {
                            scheduleForTermination = true;        
                        } else if (entity.getReSignTrials() == maxReSignTrials) {    
                            DirectDebit newDD = lastRejected.clone(DirectDebitStatus.IN_COMPLETE);
                            newDD.setNonCompletedInfo(true);
                            newDD.setConfirmedBankInfo(false);
                            newDD.setDirectDebitRejectionToDo(entity);
                            newDD.setAttachments(new ArrayList<>());
                            newDD.setGenerateManualDDFs(lastRejected.isGenerateManualDDFsFromConfig() && ddConfiguration.isIncludeManualInDDBFlow());

                            directDebitRepository.save(newDD);

                            entity.setReminder(0);
                            entity.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
                            
                            // ACC-2860
                            leadingRejectionFlow = !directDebitRejectionFlowService.existOtherWaitingClientSignatureFlow(
                                    contractPaymentTerm.getContract(), Arrays.asList(lastRejected.getId()));

                            /*createExpertTodo = leadingRejectionFlow;
                            reasonToCall = "Client signature is rejected from bank side for " + maxReSignTrials + " time";*/
                            
                            // make the rejection flow waits for client resign
                            addNewTask(entity, DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_CASE_D.toString());

                        } else if (entity.getReSignTrials() < maxReSignTrials) {
                            if (directDebitRejectionFlowService.shouldGenerateNewDdUsingOldSignatures(
                                    entity.getReSignTrials(), contractPaymentTerm)) {

                                // generate new dd from current contract payment term
                                List<ContractPayment> contractPayments = new ArrayList<>();

                                for (ContractPayment payment : lastRejected.getContractPayments()) {
                                    payment.setDirectDebit(null);
                                    payment.setGenerateManualDDFs(lastRejected.isGenerateManualDDFsFromConfig() && ddConfiguration.isIncludeManualInDDBFlow());
                                    contractPayments.add(payment);
                                }
                                
                                List<DirectDebit> directDebits = Setup.getApplicationContext().getBean(DirectDebitController.class)
                                        .generateDD(contractPayments, null, lastRejected.getContractPaymentTerm(), true, false,
                                        true, false, false, false, false);

                                for (DirectDebit dd : directDebits) {
                                    dd = directDebitRepository.findOne(dd.getId());
                                    dd.setImageForDD(lastRejected.getImageForDD());
                                    dd.setDirectDebitRejectionToDo(entity);
                                    directDebitRejectionFlowService.mergePendingDataEntryDDsIntoOneToDo(dd);
                                }
                                
                                directDebitRepository.save(directDebits);
                                
                                dontSendDdMessage = true;
                                addNewTask(entity, DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B_CASE_D.toString());
                                
                            } else { // not using same signature
                                DirectDebit newDD = lastRejected.clone(DirectDebitStatus.IN_COMPLETE);
                                newDD.setNonCompletedInfo(true);
                                newDD.setConfirmedBankInfo(false);
                                newDD.setDirectDebitRejectionToDo(entity);
                                newDD.setAttachments(new ArrayList<>());
                                newDD.setGenerateManualDDFs(lastRejected.isGenerateManualDDFsFromConfig() && ddConfiguration.isIncludeManualInDDBFlow());
                                
                                directDebitRepository.save(newDD);
                                
                                entity.setReminder(0);
                                entity.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
                                
                                // ACC-2860
                                leadingRejectionFlow = !directDebitRejectionFlowService.existOtherWaitingClientSignatureFlow(
                                        contractPaymentTerm.getContract(), Arrays.asList(lastRejected.getId()));
                                
                                addNewTask(entity, DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_CASE_D.toString());
                            }
                        }
                        break;
                        /*case Account:
                        case EID:
                            if (lastRejected.isGenerateManualDDFsFromConfig() && ddConfiguration.isIncludeManualInDDBFlow())
                                lastRejected.setMStatus(DirectDebitStatus.PENDING_DATA_ENTRY);
                            else
                                lastRejected.setMStatus(DirectDebitStatus.REJECTED);

                            lastRejected.setStatus(DirectDebitStatus.PENDING_DATA_ENTRY);
                            lastRejected.setAutoDdfFile(null);
                            lastRejected.setManualDdfFile(null);
                            lastRejected.setConfirmedBankInfo(false);

                            directDebitRejectionFlowService.sendDDFsBackToAccountant(lastRejected);

                            dontSendDdMessage = true;

                            addNewTask(entity, DirectDebitRejectionToDoType.WAITING_ACCOUNTANT_ACTION_B_CASE_D.toString());
                            break;*/
                    case Account:
                    case EID:
                    case Authorization:
                    case Invalid_Account:{
                        // make the rejection flow waits for client to provide new info
                        addNewTask(entity, DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_CASE_D.toString());
                        leadingRejectionFlow = !directDebitRejectionFlowService.existOtherWaitingClientSignatureFlow(
                            contractPaymentTerm.getContract(), Arrays.asList(lastRejected.getId()));
                        entity.setTrials(entity.getTrials() + 1);
                        entity.setReminder(0);
                        
                        /*if (entity.getTrials() == (maxTrials - 1)) {
                            //createExpertTodo = true;
                            reasonToCall = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_AUTH_REJECT_EXPERT_TODO_REASON_TO_CALL);
                            initialNotes = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_AUTH_REJECT_EXPERT_TODO_INITIAL_NOTES);
                        }*/
                        if (entity.getTrials() > maxTrials) {
                            scheduleForTermination = true;
                        }
                        DirectDebit newDD = lastRejected.clone(DirectDebitStatus.IN_COMPLETE);
                        newDD.setNonCompletedInfo(true);
                        newDD.setConfirmedBankInfo(false);
                        newDD.setDirectDebitRejectionToDo(entity);
                        newDD.setAttachments(new ArrayList<>());
                        newDD.setGenerateManualDDFs(lastRejected.isGenerateManualDDFsFromConfig() && ddConfiguration.isIncludeManualInDDBFlow());
                        directDebitRepository.save(newDD);
                        
                        entity.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
                        break;
                    }
                    
                    /*case Invalid_Account:{
                        DirectDebit newDD = lastRejected.clone(DirectDebitStatus.IN_COMPLETE);
                        newDD.setNonCompletedInfo(true);
                        newDD.setConfirmedBankInfo(false);
                        newDD.setDirectDebitRejectionToDo(entity);
                        newDD.setAttachments(new ArrayList<>());
                        newDD.setGenerateManualDDFs(lastRejected.isGenerateManualDDFsFromConfig() && ddConfiguration.isIncludeManualInDDBFlow());
                        directDebitRepository.save(newDD);
                        
                        entity.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
                        entity.setTrials(entity.getTrials() + 1);
                        entity.setReminder(0);
                        
                        if (entity.getTrials() > maxTrials) {
                            scheduleForTermination = true;
                        }
                        logger.log(Level.SEVERE, "DirectDebitARejectionWaitingBankResponseStep before add new task");
                        
                        // make the rejection flow waits for client resign
                        addNewTask(entity, DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_CASE_D.toString());

                        leadingRejectionFlow = !directDebitRejectionFlowService.existOtherWaitingClientSignatureFlow(
                                contractPaymentTerm.getContract(), Arrays.asList(lastRejected.getId()));
                        break;
                    }*/
                    
                    default:
                        break;
                }

                if (scheduleForTermination) {
                    Contract contract = lastRejected.getContractPaymentTerm().getContract();
                    
                    if (contract.isTerminateContractDueRejection() || !entity.isDdAddedByOecFlow()) {
                        logger.info("contract.terminateContractDueRejection: " + contract.isTerminateContractDueRejection() +
                                "; entity.isDdAddedByOecFlow: " + entity.isDdAddedByOecFlow());

                        entity.setContractScheduleDateOfTermination(
                                directDebitRejectionFlowService
                                        .setContractForTermination(
                                                lastRejected.getContractPaymentTerm(),
                                                "direct_debit_rejection_type_b_maxsignaturetrialsb_reached",
                                                entity));
                        entity.setLeadingRejectionFlow(true);
                    }
                    entity.setCompleted(true);
                    entity.setStopped(true);
                }

                /*if (createExpertTodo) {
                    entity.setVoiceResolverTodoId(directDebitRejectionFlowService.createExpertTodo(lastRejected, reasonToCall, initialNotes));
                    entity.setLastTrialDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
                }*/
            } else { // one of manual or auto gets confirmed so return to b normal flow

                if (lastRejected.getStatus() == DirectDebitStatus.CONFIRMED && allManualRejected
                && lastRejected.isGenerateManualDDFs()
                && lastRejected.getDdConfiguration().isIncludeManualInDDBFlow()) {
                    List<DirectDebitFile> manualDDs = lastRejected.getDirectDebitFiles().stream()
                            .filter(ddf -> ddf.getDdMethod() == DirectDebitMethod.MANUAL &&
                                    ddf.getTrialNumber() == entity.getManualDDBTrialsPatch())
                            .limit(Math.min(
                                    lastRejected.getDdConfiguration().getNumberOfGeneratedDDs(),
                                    Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                                            AccountingModule.PARAMETER_NUMBER_OF_DIRECT_DEBIT_SIGNATURES))))
                            .collect(Collectors.toList());

                    for (DirectDebitFile manualDD : manualDDs) {
                        directDebitFileRepository.save(manualDD.clone(entity.getManualDDBTrialsPatch()));
                    }

                    entity.setManualDDBTrials(entity.getManualDDBTrials() + 1);
                    entity.setManualDDBTrialsPatch(entity.getManualDDBTrialsPatch() + 1);
                    addNewTask(entity, DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B.toString());

                } else if (allAutoRejected && lastRejected.getMStatus() == DirectDebitStatus.CONFIRMED) {

                    List<DirectDebitFile> autoDDs = lastRejected.getDirectDebitFiles().stream()
                            .filter(ddf -> ddf.getDdMethod() == DirectDebitMethod.AUTOMATIC &&
                                    ddf.getTrialNumber() == entity.getAutoDDBTrialsPatch())
                            .limit(Math.min(
                                    lastRejected.getDdConfiguration().getNumberOfGeneratedDDs(),
                                    Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                                            AccountingModule.PARAMETER_NUMBER_OF_DIRECT_DEBIT_SIGNATURES))))
                            .collect(Collectors.toList());

                    for (DirectDebitFile autoDD : autoDDs) {
                        directDebitFileRepository.save(autoDD.clone(entity.getAutoDDBTrialsPatch() + 1));
                    }

                    entity.setAutoDDBTrials(entity.getAutoDDBTrials() + 1);
                    entity.setAutoDDBTrialsPatch(entity.getAutoDDBTrialsPatch() + 1);

                    addNewTask(entity, DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B.toString());

                }

            }
            // save last reject reason and update the flow with new values
            entity.setDontSendDdMessage(dontSendDdMessage);
            entity.setLeadingRejectionFlow(leadingRejectionFlow);
            entity.setLastRejectCategory(lastRejected.getRejectCategory());
            directDebitRejectionToDoRepository.save(entity);
        }
    }

    private void handleSwitchingRejection(DirectDebit lastRejected, DirectDebitRejectionToDo entity, SwitchingType switchingType) {
        ContractPaymentTerm contractPaymentTerm = lastRejected.getContractPaymentTerm();
        boolean dontSendDdMessage = false;
        boolean leadingRejectionFlow = false;

        /*boolean createExpertTodo = false;
        String reasonToCall = "";
        String initialNotes = "";*/
        boolean scheduleForTermination = false;

        Integer maxTrials =
                Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_MAX_TRIALS));

        if (lastRejected.getRejectCategory() != entity.getLastRejectCategory()) {
            entity.setTrials(0);
        }

        DirectDebitRejectionToDoType startStep = null;
        DirectDebit newDD;

        if (lastRejected.getRejectCategory() != null) switch(lastRejected.getRejectCategory()) {
            case Compliance:
            case Other:
                newDD = directDebitRejectionFlowService.getSwitchingNewDD(switchingType, lastRejected, true);
                if (newDD.getImageForDD() == null) newDD.setImageForDD(lastRejected.getImageForDD());
                newDD.setDirectDebitRejectionToDo(entity);
                directDebitRepository.save(newDD);

                startStep = DDUtils.getDDRejectionToDoNextStep(DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B_CASE_D, newDD.getCategory());
                break;

            case Signature:
                Integer maxReSignTrials = Integer.parseInt(
                        Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_MAX_RE_SIGN_TRIALS));

                logger.log(Level.SEVERE, "maxReSignTrials: " + maxReSignTrials);
                logger.log(Level.SEVERE, "entity.getReSignTrials(): " + entity.getReSignTrials());

                if (directDebitRejectionFlowService.flowStoppedAfterIncreaseReSignTrials(entity)) return; //ACC-4715

                if (entity.getReSignTrials() > maxReSignTrials) {
                    scheduleForTermination = true;
                } else if (entity.getReSignTrials() == maxReSignTrials) {

                    newDD = directDebitRejectionFlowService.getSwitchingNewDD(switchingType, lastRejected, false);

                    newDD.setStatus(DirectDebitStatus.IN_COMPLETE);
                    newDD.setMStatus(DirectDebitStatus.IN_COMPLETE);
                    newDD.setNonCompletedInfo(true);
                    newDD.setConfirmedBankInfo(false);
                    newDD.setDirectDebitRejectionToDo(entity);
                    newDD.setAttachments(new ArrayList<>());
                    if (newDD.getImageForDD() == null) newDD.setImageForDD(lastRejected.getImageForDD());
                    directDebitRepository.save(newDD);

                    entity.setReminder(0);
                    entity.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());

                    // ACC-2860
                    leadingRejectionFlow = !directDebitRejectionFlowService.existOtherWaitingClientSignatureFlow(
                            contractPaymentTerm.getContract(), Arrays.asList(lastRejected.getId()));

                    //createExpertTodo = leadingRejectionFlow;
                    //reasonToCall = "Client signature is rejected from bank side for " + maxReSignTrials + " time";

                    // make the rejection flow waits for client resign
                    startStep = DDUtils.getDDRejectionToDoNextStep(DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE, newDD.getCategory());

                } else if (entity.getReSignTrials() < maxReSignTrials) {
                    Map<String, Object> signatureType = directDebitSignatureService
                            .getLastSignatureType(contractPaymentTerm, false, false);

                    if ((Boolean) signatureType.get("useApprovedSignature") || (Boolean) signatureType.get("useNonRejectedSignature")) {

                        newDD = directDebitRejectionFlowService.getSwitchingNewDD(switchingType, lastRejected, true);
                        directDebitRejectionFlowService.mergePendingDataEntryDDsIntoOneToDo(newDD);

                        if (newDD.getImageForDD() == null) newDD.setImageForDD(lastRejected.getImageForDD());
                        newDD.setDirectDebitRejectionToDo(entity);
                        directDebitRepository.save(newDD);

                        dontSendDdMessage = true;
                        startStep = DDUtils.getDDRejectionToDoNextStep(DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B_CASE_D, newDD.getCategory());

                    } else { // not using same signature
                        newDD = directDebitRejectionFlowService.getSwitchingNewDD(switchingType, lastRejected, false);

                        newDD.setStatus(DirectDebitStatus.IN_COMPLETE);
                        newDD.setMStatus(DirectDebitStatus.IN_COMPLETE);
                        newDD.setNonCompletedInfo(true);
                        newDD.setConfirmedBankInfo(false);
                        newDD.setDirectDebitRejectionToDo(entity);
                        newDD.setAttachments(new ArrayList<>());
                        if (newDD.getImageForDD() == null) newDD.setImageForDD(lastRejected.getImageForDD());
                        directDebitRepository.save(newDD);

                        entity.setReminder(0);
                        entity.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());

                        // ACC-2860
                        leadingRejectionFlow = !directDebitRejectionFlowService.existOtherWaitingClientSignatureFlow(
                                contractPaymentTerm.getContract(), Arrays.asList(lastRejected.getId()));

                        startStep = DDUtils.getDDRejectionToDoNextStep(DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE, newDD.getCategory());
                    }
                }
                break;

            /*case Account:
            case EID:
                lastRejected.setMStatus(DirectDebitStatus.REJECTED);
                lastRejected.setStatus(DirectDebitStatus.REJECTED);

                newDD = directDebitRejectionFlowService.getSwitchingNewDD(switchingType, lastRejected, true);
                newDD.setDirectDebitRejectionToDo(entity);
                newDD.setMStatus(DirectDebitStatus.PENDING_DATA_ENTRY);
                newDD.setStatus(DirectDebitStatus.PENDING_DATA_ENTRY);
                newDD.setAutoDdfFile(null);
                newDD.setManualDdfFile(null);
                newDD.setConfirmedBankInfo(false);
                if (newDD.getImageForDD() == null) newDD.setImageForDD(lastRejected.getImageForDD());

                newDD = directDebitRepository.save(newDD);
                directDebitRejectionFlowService.sendDDFsBackToAccountant(newDD, false);

                dontSendDdMessage = true;
                startStep = DDUtils.getDDRejectionToDoNextStep(DirectDebitRejectionToDoType.WAITING_ACCOUNTANT_ACTION, newDD.getCategory());
                break;*/

            case EID:
            case Account:
            case Invalid_Account:
            case Authorization:
                entity.setTrials(entity.getTrials() + 1);
                entity.setReminder(0);
                entity.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());

                /*if (entity.getTrials() == (maxTrials - 1)) {
                    createExpertTodo = true;
                    reasonToCall = Setup.getParameter(Setup.getCurrentModule(),
                            AccountingModule.PARAMETER_DD_AUTH_REJECT_EXPERT_TODO_REASON_TO_CALL);
                    initialNotes = Setup.getParameter(Setup.getCurrentModule(),
                            AccountingModule.PARAMETER_DD_AUTH_REJECT_EXPERT_TODO_INITIAL_NOTES);
                }*/

                if (entity.getTrials() > maxTrials) scheduleForTermination = true;

                newDD = directDebitRejectionFlowService.getSwitchingNewDD(switchingType, lastRejected, false);
                newDD.setStatus(DirectDebitStatus.IN_COMPLETE);
                newDD.setMStatus(DirectDebitStatus.IN_COMPLETE);
                newDD.setNonCompletedInfo(true);
                newDD.setConfirmedBankInfo(false);
                newDD.setDirectDebitRejectionToDo(entity);
                newDD.setAttachments(new ArrayList<>());
                if (newDD.getImageForDD() == null) newDD.setImageForDD(lastRejected.getImageForDD());

                directDebitRepository.save(newDD);

                leadingRejectionFlow = !directDebitRejectionFlowService.existOtherWaitingClientSignatureFlow(
                        contractPaymentTerm.getContract(), Arrays.asList(lastRejected.getId()));
                startStep = DDUtils.getDDRejectionToDoNextStep(DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE, newDD.getCategory());
                break;

            /*case Invalid_Account:
                newDD = directDebitRejectionFlowService.getSwitchingNewDD(switchingType, lastRejected, false);

                newDD.setStatus(DirectDebitStatus.IN_COMPLETE);
                newDD.setMStatus(DirectDebitStatus.IN_COMPLETE);
                newDD.setNonCompletedInfo(true);
                newDD.setConfirmedBankInfo(false);
                newDD.setDirectDebitRejectionToDo(entity);
                newDD.setAttachments(new ArrayList<>());
                if (newDD.getImageForDD() == null) newDD.setImageForDD(lastRejected.getImageForDD());
                directDebitRepository.save(newDD);

                entity.setTrials(entity.getTrials() + 1);
                entity.setReminder(0);
                entity.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());

                if (entity.getTrials() > maxTrials) scheduleForTermination = true;

                logger.log(Level.SEVERE, "DirectDebitARejectionWaitingBankResponseStep before add new task");
                // make the rejection flow waits for client resign
                startStep = DDUtils.getDDRejectionToDoNextStep(DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE, newDD.getCategory());

                leadingRejectionFlow = !directDebitRejectionFlowService.existOtherWaitingClientSignatureFlow(
                        contractPaymentTerm.getContract(), Arrays.asList(lastRejected.getId()));
                break;*/
        }

        if (startStep != null) addNewTask(entity, startStep.toString());

        entity.setDontSendDdMessage(dontSendDdMessage);
        entity.setLeadingRejectionFlow(leadingRejectionFlow);

        if (scheduleForTermination) {
            Contract contract = lastRejected.getContractPaymentTerm().getContract();
            
            if (contract.isTerminateContractDueRejection() || !entity.isDdAddedByOecFlow()) {
                logger.info("contract.terminateContractDueRejection: " + contract.isTerminateContractDueRejection() +
                        "; entity.isDdAddedByOecFlow: " + entity.isDdAddedByOecFlow());

                entity.setContractScheduleDateOfTermination(
                        directDebitRejectionFlowService
                                .setContractForTermination(
                                        lastRejected.getContractPaymentTerm(),
                                        "direct_debit_rejection_type_b_maxsignaturetrialsb_reached",
                                        entity));
                entity.setLeadingRejectionFlow(true);
            }
            entity.setCompleted(true);
            entity.setStopped(true);
        }

        /*if (createExpertTodo) {
            entity.setVoiceResolverTodoId(directDebitRejectionFlowService.createExpertTodo(lastRejected, reasonToCall, initialNotes));
            entity.setLastTrialDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
        }*/

        entity.setLastRejectCategory(lastRejected.getRejectCategory());
        directDebitRejectionToDoRepository.save(entity);
    }

    @Override
    public void postDone(DirectDebitRejectionToDo entity) {
    }
}
