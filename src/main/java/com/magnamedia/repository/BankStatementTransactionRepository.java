package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.BankStatementTransaction;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created At Apr 18, 2020
 *
 **/


@Repository
public interface BankStatementTransactionRepository extends BaseRepository<BankStatementTransaction> {

    Long countByFileIdAndResolved(long fileId, boolean resolved);

    boolean existsByUniqueId(String uniqueId);

    @Query("select t.uniqueId from BankStatementTransaction t where t.uniqueId in ?1")
    List<String> findUniqueIdByUniqueIdIn(List<String> uniqueId);

    List<BankStatementTransaction> findByFileId(Long fileId);

    List<BankStatementTransaction> findByPayment(Payment payment);

    BankStatementTransaction findFirstByClientRefundToDo(ClientRefundToDo clientRefundToDo);

}
