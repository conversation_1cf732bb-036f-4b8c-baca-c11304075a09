package com.magnamedia.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.workflow.WorkflowController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.WordTemplate;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.WordTemplateRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.DirectDebitCancelationToDo;
import com.magnamedia.extra.PaymentHelper;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PdfHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.*;
import com.magnamedia.workflow.entity.projection.DirectDebitCancelationToDoProjection;
import com.magnamedia.workflow.entity.projection.DirectDebitCancelationToDoResponseProjection;
import com.magnamedia.workflow.service.DirectDebitCancelationToDoFlow;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.poi.xwpf.usermodel.*;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Paths;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Nov 20, 2019 Jirra ACC-1134
 */
@RestController
@RequestMapping("/directdebitcancelationtodos")
public class DirectDebitCancelationToDoController
        extends WorkflowController<DirectDebitCancelationToDo, DirectDebitCancelationToDoFlow> {

    private static final String prefix = "MMM ";
    public static final Integer NUMBER_OF_CELLS_FOR_DDA_REF = 23;
    public static final String DD_CANCELLATION_FORM = "DD_CANCELLATION_FORM";
    @Autowired
    private DirectDebitCancelationToDoRepository directDebitCancelationToDoRepository;
    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;
    @Autowired
    private DirectDebitRepository directDebitRepository;
    //ACC-1974
    @Autowired
    WordTemplateRepository templateRepository;
    @Autowired
    WordTemplateService wordTemplateService;
    @Autowired
    private ProjectionFactory projectionFactory;
    @Autowired
    private BouncingFlowService bouncingFlowService;
    @Autowired
    private DirectDebitService directDebitService;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private DirectDebitCancellationService directDebitCancellationService;

    // ACC-1134 revised flow
    @Override
    public ResponseEntity<?> createEntity(DirectDebitCancelationToDo directDebitCancelationToDo) {
        DirectDebitFileCancellationTrigger cancellationTrigger = directDebitCancelationToDo.getDirectDebitFile().getCancellationTrigger();
        if (cancellationTrigger == null) {
            cancellationTrigger = DirectDebitFileCancellationTrigger.AUTOMATIC;
        }

        DirectDebitFile directDebitFile = directDebitFileRepository.findOne(directDebitCancelationToDo.getDirectDebitFile().getId());

        directDebitFile.setCancellationTrigger(cancellationTrigger);

        if (Arrays.asList(DirectDebitStatus.CANCELED, DirectDebitStatus.PENDING_FOR_CANCELLATION).contains(directDebitFile.getDdStatus())) {
            logger.log(Level.SEVERE, "DD status = " + directDebitFile.getDdStatus() + " -> do nothing");
            return okResponse();
        }

        directDebitCancelationToDo.setDirectDebit(directDebitFile.getDirectDebit());
        List<DirectDebitCancelationToDo> alreadyExistsDDCancelationToDos = 
                directDebitCancelationToDoRepository.findByDirectDebitFileAndCompletedFalseAndStoppedFalse(directDebitFile);
        
        if (!alreadyExistsDDCancelationToDos.isEmpty()) {
            logger.log(Level.SEVERE, "a cancellation to do for this direct debit file already exists");

            if (!BooleanUtils.toBoolean(directDebitCancelationToDo.getHidden())) {
                for (DirectDebitCancelationToDo ddCToDo : alreadyExistsDDCancelationToDos) {
                    ddCToDo.setHidden(false);
                    getRepository().save(ddCToDo);
                }
            }

            return new ResponseEntity("a cancellation to do for this direct debit file already exists", HttpStatus.INTERNAL_SERVER_ERROR);
        }

        ResponseEntity response = new ResponseEntity(HttpStatus.OK);
        boolean toBeCancelledDirectly = false;

        if (directDebitFile.getDdStatus().equals(DirectDebitStatus.REJECTED) ||
                directDebitFile.getDdStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY) ||
                directDebitFile.getDdStatus().equals(DirectDebitStatus.IN_COMPLETE)) {
            
            toBeCancelledDirectly = true;
        }

        if (directDebitFile.getDdStatus().equals(DirectDebitStatus.PENDING)) {
            if (directDebitFile.getStatus().equals(DirectDebitFileStatus.SENT)) {
                response = super.createEntity(directDebitCancelationToDo);
                
            } else if (directDebitFile.getStatus().equals(DirectDebitFileStatus.NOT_SENT)) {
                logger.log(Level.SEVERE, "ddf status: NOT_SENT -> cancel it directly");
                toBeCancelledDirectly = true;
            }
        }

        if (directDebitFile.getDdStatus().equals(DirectDebitStatus.CONFIRMED)) {
            response = super.createEntity(directDebitCancelationToDo);
        }

        if (directDebitFile.getDdStatus().equals(DirectDebitStatus.EXPIRED)) {
            if (directDebitFile.isDoNotCancelExpiredDirectly())
                response = super.createEntity(directDebitCancelationToDo);
            else
                toBeCancelledDirectly = true;
        }

        //Jirra ACC-3198
        if (toBeCancelledDirectly) {
            if (cancellationTrigger.equals(DirectDebitFileCancellationTrigger.AUTOMATIC)) {
                List<DDFBatchForRPA> ddfBatchForRPAs = Setup.getRepository(DDFBatchForRPARepository.class).findByIdsContainsAndStatusNotIn(directDebitFile.getId().toString(), Arrays.asList(DDFBatchStatus.SENT_TO_BANK, DDFBatchStatus.ERROR_OCCURRED_SENT_TO_BANK));
                for (DDFBatchForRPA ddfBatchForRPA : ddfBatchForRPAs) {
                    if (!ddfBatchForRPA.getStatus().equals(DDFBatchStatus.NOT_SENT)) {
                        sendCancellationFailureEmail(directDebitFile.getId(), directDebitFile.getApplicationId());
                        return response;
                    }
                }
            }

            directDebitFile.setDdStatus(DirectDebitStatus.CANCELED);
            directDebitFile.setCancellationDate(new Date());
        }

        directDebitFile.setCancellationReason(directDebitCancelationToDo.getReason());
        directDebitFile.setRequestedToCancelDate(new Date());

        if (CurrentRequest.getUser() != null && CurrentRequest.getUser().getId() != null) {
            directDebitFile.setWhoRequestedToCancellDD(CurrentRequest.getUser().getId());
        }

        directDebitFile.setIgnoreDDRejectionFlow(directDebitCancelationToDo.getIgnoreDDRejectionFlow());

        directDebitFileRepository.save(directDebitFile);

        return response;
    }

    private void sendCancellationFailureEmail(
            Long ddfId, String ddfAppId) {

        logger.info("sendCancellationFailureEmail for DD#" + ddfId + ", app id#" + ddfAppId);

        Map<String, String> parameters = new HashMap();
        parameters.put("dd_id", ddfAppId);

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff("dd_cancellation_failed_due_rpa_process",
                        parameters, Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_CANCELLATION_FAILED_DUE_RPA_PROCESS_EMAIL),
                        "ERP Failed to cancel DD (@dd_id@)".replace("(@dd_id@)", ddfAppId));
    }

    // Jirra ACC-1335
    @PreAuthorize("hasPermission('directdebitcancelationtodos','generate-to-dos-confirmed-dds')")
    @RequestMapping(value = "/generate-to-dos-confirmed-dds", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity generateToDosForConfirmedDds() {
        // get confirmed direct debits related to (cancelled, expired) MaidCC Contracts
        SelectQuery<DirectDebit> query = new SelectQuery(DirectDebit.class);
        query.filterBy("status", "=", DirectDebitStatus.CONFIRMED);
        query.filterBy("contractPaymentTerm.contract.status", "in", Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED));
        query.filterBy("contractPaymentTerm.contract.contractProspectType.code",
                "IN", Arrays.asList(PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE), PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE)));

        List<DirectDebit> directDebits = query.execute();

        if (directDebits == null) {
            return new ResponseEntity(HttpStatus.NO_CONTENT);
        }

        logger.log(Level.SEVERE, prefix + "dd list size: " + directDebits.size());
        for (DirectDebit directDebit : directDebits) {
            if (directDebit.getContractPaymentTerm().getContract().getDateOfTermination() == null) {
                continue;
            }
            
            Double clientBalance = 0D;
            if (directDebit.getContractPaymentTerm().getContract().getContractProspectType().getCode().equals(AccountingModule.MAID_CC_PROSPECT_TYPE)) {
                clientBalance = Setup.getApplicationContext().getBean(ContractService.class)
                        .getCorrectedBalance(
                                directDebit.getContractPaymentTerm().getContract().getId(), 
                                DateUtil.formatDateDashed(directDebit.getContractPaymentTerm().getContract().getDateOfTermination()));
                logger.log(Level.SEVERE, prefix + "client balance: " + clientBalance);
            }
            
            if (clientBalance >= 1D) {
                continue;
            }
            
            logger.log(Level.SEVERE, prefix + "generating dd cancellation for dd with id -> " + directDebit.getId());
            //Jirra ACC-1587
//            DirectDebitCancelationToDo directDebitCancelationToDo = new DirectDebitCancelationToDo();
//            directDebitCancelationToDo.setDirectDebit(directDebit);
//            this.createEntity(directDebitCancelationToDo);
            
            directDebitCancellationService.cancelWholeDD(directDebit, null);
            logger.log(Level.SEVERE, prefix + "dd cancellation todo generated");
        }

        return new ResponseEntity(HttpStatus.OK);
    }

    //ACC-1972
    @PreAuthorize("hasPermission('directdebitcancelationtodos','getAll')")
    @RequestMapping(value = "/getAll", method = RequestMethod.GET)
    public ResponseEntity getAll(Pageable pageable) {

        SelectQuery<DirectDebitCancelationToDo> query = new SelectQuery<>(DirectDebitCancelationToDo.class);

        query.filterBy("completed", "=", false);
        query.filterBy("stopped", "=", false);
        query.filterBy("taskName", "=", "Direct Debit Cancelation Prepare");
        query.filterBy("directDebitFile.ddStatus", "in", Arrays.asList(DirectDebitStatus.CONFIRMED,
                DirectDebitStatus.EXPIRED, DirectDebitStatus.PENDING_FOR_CANCELLATION));
        query.filterBy("hidden", "=", false);
        query.filterBy(CurrentRequest.getSearchFilter());

        // ACC-8863
        query.sortBy("directDebitFile.directDebit.category", false);

        PageImpl page = (PageImpl) query.execute(pageable)
                .map(obj
                        -> projectionFactory.createProjection(
                        DirectDebitCancelationToDoResponseProjection.class, obj));

        return new ResponseEntity<>(page, HttpStatus.OK);
    }

    //ACC-1972
    @Transactional
    @PreAuthorize("hasPermission('directdebitcancelationtodos','sentToBank')")
    @RequestMapping(value = "/sentToBank", method = RequestMethod.POST)
    public void sentToBank(@RequestBody List<Long> directDebitCancelationToDos) throws IOException {
        DirectDebitCancelationToDoRepository repos = Setup.getRepository(DirectDebitCancelationToDoRepository.class);

        List<DirectDebitCancelationToDo> byIdIn = repos.findByIdIn(directDebitCancelationToDos);

        boolean hasEmptyDDRefNo = byIdIn.stream().anyMatch(ddc -> (ddc.getDirectDebitFile().getDdaRefNo() == null
                || ddc.getDirectDebitFile().getDdaRefNo().equalsIgnoreCase("")));

        if (hasEmptyDDRefNo) {
            throw new RuntimeException("Please fill the DD reference number for all selected DDs");
        }

        for (DirectDebitCancelationToDo ddCancelationToDo : byIdIn) {
            this.completeTask(ddCancelationToDo, "Direct Debit Cancelation Prepare");
        }

    }

    //ACC-1972
    @Transactional
    @PreAuthorize("hasPermission('directdebitcancelationtodos','allSentToBank')")
    @RequestMapping(value = "/allSentToBank", method = RequestMethod.POST)
    public void allSentToBank() throws IOException {
        SelectQuery<DirectDebitCancelationToDo> query = new SelectQuery<>(DirectDebitCancelationToDo.class);

        query.filterBy("completed", "=", false);
        query.filterBy("stopped", "=", false);
        query.filterBy("taskName", "=", "Direct Debit Cancelation Prepare");
        query.filterBy("directDebitFile.ddStatus", "in", Arrays.asList(DirectDebitStatus.CONFIRMED, DirectDebitStatus.EXPIRED));
        query.filterBy("hidden", "=", false);

        query.filterBy(CurrentRequest.getSearchFilter());
        List<DirectDebitCancelationToDo> byIdIn = query.execute();

        boolean hasEmptyDDRefNo = byIdIn.stream().anyMatch(ddc -> (ddc.getDirectDebitFile().getDdaRefNo() == null
                || ddc.getDirectDebitFile().getDdaRefNo().equalsIgnoreCase("")));

        if (hasEmptyDDRefNo) {
            throw new RuntimeException("Please fill the DD reference number for all selected DDs");
        }

        for (DirectDebitCancelationToDo id : byIdIn) {
            this.completeTask(id, "Direct Debit Cancelation Prepare");
        }

    }

    //ACC-1972
    //generate PDF for Page
    @PreAuthorize("hasPermission('directdebitcancelationtodos','getDirectDebitCancellationTempFile')")
    @RequestMapping(value = "/getDirectDebitCancellationTempFile")
    public void getDirectDebitCancellationTempFileForPage(
            HttpServletResponse response,
            @RequestBody List<Long> ids,
            @RequestParam(value = "date", required = false) @org.springframework.format.annotation.DateTimeFormat(pattern = "yyyy-MM-dd") Date terminationDate) throws IOException, Exception {
        HashMap<String, InputStream> inputStreamMap = new HashMap<>();

        List<DirectDebitFile> byIdIn = directDebitFileRepository.findByIdIn(ids);

        boolean hasEmptyDDRefNo = byIdIn.stream().anyMatch(ddc -> (ddc.getDdaRefNo() == null
                || ddc.getDdaRefNo().equalsIgnoreCase("")));

        if (hasEmptyDDRefNo) {
            throw new RuntimeException("Please fill the DD reference number for all selected DDs");
        }

        for (DirectDebitFile directDebitFile : byIdIn) {
            if (directDebitFile == null) {
                throw new Exception("Direct Debit record not found");
            }
            Contract contract = directDebitFile.getDirectDebit().getContractPaymentTerm().getContract();
            LocalDate cancellationDate
                    = (terminationDate != null
                    ? new LocalDate(terminationDate)
                    : LocalDate.now());

            InputStream ddCancellationInputStream = generateDDCancellationDocument(contract, directDebitFile, cancellationDate);
            String fileName = "Direct_Debit_Cancellation_Form_DD_no_" + directDebitFile.getId() + ".pdf";
            inputStreamMap.put(fileName, ddCancellationInputStream);

        }
        File zipFile = Paths.get(System.getProperty("java.io.tmpdir"),
                new Date().getTime() + ".rar")
                .toFile();

        String zipFileName = zipFile.getPath();
        FileOutputStream fos = new FileOutputStream(zipFileName);
        ZipOutputStream zos = new ZipOutputStream(fos);

        inputStreamMap.forEach((name, file) -> {
            try {
                zos.putNextEntry(new ZipEntry(name + ".pdf"));
                int bufferLength = 2048;
                byte buffer[] = new byte[bufferLength];
                int length;
                while ((length = file.read(buffer)) >= 0) {
                    zos.write(buffer, 0, length);
                }
                zos.closeEntry();
            } catch (IOException ex) {
                Logger.getLogger(DirectDebitCancelationToDoController.class.getName()).log(Level.SEVERE, null, ex);
            }
        });
        zos.close();
        createDownloadResponse(response,
                "dds.rar",
                new FileInputStream(zipFile));
    }

    //generate PDF for ALL entry
    @PreAuthorize("hasPermission('directdebitcancelationtodos','getAllDirectDebitCancellationTempFile')")
    @RequestMapping(value = "/getAllDirectDebitCancellationTempFile")
    public void getAllDirectDebitCancellationTempFile(
            HttpServletResponse response,
            @RequestParam(value = "date", required = false) @org.springframework.format.annotation.DateTimeFormat(pattern = "yyyy-MM-dd") Date terminationDate) throws IOException, Exception {

        SelectQuery<DirectDebitCancelationToDo> query = new SelectQuery<>(DirectDebitCancelationToDo.class);

        query.filterBy("completed", "=", false);
        query.filterBy("stopped", "=", false);
        query.filterBy("taskName", "=", "Direct Debit Cancelation Prepare");
        query.filterBy("directDebitFile.ddStatus", "in", Arrays.asList(DirectDebitStatus.CONFIRMED, DirectDebitStatus.EXPIRED));
        query.filterBy("hidden", "=", false);
        query.filterBy(CurrentRequest.getSearchFilter());
        List<DirectDebitCancelationToDo> execute = query.execute();

        HashMap<String, InputStream> inputStreamMap = new HashMap<>();
        for (DirectDebitCancelationToDo id : execute) {
            DirectDebitFile directDebitFile = id.getDirectDebitFile();
            if (directDebitFile == null) {
                throw new Exception("Direct Debit record with id=" + id + " not found");
            }
            Contract contract = directDebitFile.getDirectDebit().getContractPaymentTerm().getContract();
            LocalDate cancellationDate
                    = (terminationDate != null
                    ? new LocalDate(terminationDate)
                    : LocalDate.now());

            InputStream ddCancellationInputStream = generateDDCancellationDocument(contract, directDebitFile, cancellationDate);
            String fileName = "Direct_Debit_Cancellation_Form_DD_no_" + directDebitFile.getId() + ".pdf";
            inputStreamMap.put(fileName, ddCancellationInputStream);

        }
        File zipFile = Paths.get(System.getProperty("java.io.tmpdir"),
                new Date().getTime() + ".rar")
                .toFile();

        String zipFileName = zipFile.getPath();
        FileOutputStream fos = new FileOutputStream(zipFileName);
        ZipOutputStream zos = new ZipOutputStream(fos);

        inputStreamMap.forEach((name, file) -> {
            try {
                zos.putNextEntry(new ZipEntry(name + ".pdf"));
                int bufferLength = 2048;
                byte buffer[] = new byte[bufferLength];
                int length;
                while ((length = file.read(buffer)) >= 0) {
                    zos.write(buffer, 0, length);
                }
                zos.closeEntry();
            } catch (IOException ex) {
                Logger.getLogger(DirectDebitCancelationToDoController.class.getName()).log(Level.SEVERE, null, ex);
            }
        });
        zos.close();
        createDownloadResponse(response,
                "dds.rar",
                new FileInputStream(zipFile));
    }

    //ACC-1972
    //generate attachment
    @RequestMapping(value = "/generateDDCancellation/{id}")
    public ResponseEntity<?> getDirectDebitCancellation(
            @PathVariable(name = "id") Long directDebitId,
            @RequestParam(value = "date", required = false) @org.springframework.format.annotation.DateTimeFormat(pattern = "yyyy-MM-dd") Date terminationDate) 
            throws IOException, Exception {

        DirectDebitFile directDebitFile = directDebitFileRepository.findOne(directDebitId);

        if (directDebitFile == null) {
            throw new Exception("Direct Debit record not found");
        }

        boolean hasEmptyDDRefNo = directDebitFile.getDdaRefNo() == null || directDebitFile.getDdaRefNo().equalsIgnoreCase("");

        if (hasEmptyDDRefNo) {
            throw new RuntimeException("Please fill the DD reference number for all selected DDs");
        }

        Contract contract = directDebitFile.getDirectDebit().getContractPaymentTerm().getContract();

        LocalDate cancellationDate
                = (terminationDate != null
                ? new LocalDate(terminationDate)
                : LocalDate.now());

        InputStream ddCancellationInputStream = null;
        try {
            ddCancellationInputStream = generateDDCancellationDocument(contract, directDebitFile, cancellationDate);

            String fileName = "Direct_Debit_Cancellation_Form_DD_no_" + directDebitFile.getId() + ".pdf";
            Attachment temp = Storage.storeTemporary(fileName, ddCancellationInputStream, DD_CANCELLATION_FORM, Boolean.TRUE);
            directDebitFile.addAttachment(temp);
            directDebitFile = directDebitFileRepository.save(directDebitFile);
            return new ResponseEntity<>(directDebitFile.getAttachment(DD_CANCELLATION_FORM), HttpStatus.OK);
        } finally {
            StreamsUtil.closeStream(ddCancellationInputStream);
        }
    }

    //generate attachment for page (pdf of images)
    @PreAuthorize("hasPermission('directdebitcancelationtodos','generateDDCancellation')")
    @RequestMapping(value = "/generateDDCancellation")
    public void getDirectDebitCancellationForPage(
            HttpServletResponse response,
            @RequestBody List<Long> ids,
            @RequestParam(value = "date", required = false) @org.springframework.format.annotation.DateTimeFormat(pattern = "yyyy-MM-dd") Date terminationDate) 
            throws IOException, Exception {
        
        HashMap<Long, Attachment> result = new HashMap<>();
        List<Long> attchmentsIds = new ArrayList<>();
        for (Long id : ids) {
            DirectDebitFile directDebitFile = directDebitFileRepository.findOne(id);
            if (directDebitFile == null) {
                throw new Exception("Direct Debit record not found");
            }

            Contract contract = directDebitFile.getDirectDebit().getContractPaymentTerm().getContract();

            LocalDate cancellationDate
                    = (terminationDate != null
                    ? new LocalDate(terminationDate)
                    : LocalDate.now());

            InputStream ddCancellationInputStream = null;
            try {
                ddCancellationInputStream = generateDDCancellationDocument(contract, directDebitFile, cancellationDate);

                String fileName = "Direct_Debit_Cancellation_Form_DD_no_" + directDebitFile.getId() + ".pdf";
                Attachment temp = Storage.storeTemporary(fileName, ddCancellationInputStream, DD_CANCELLATION_FORM, Boolean.TRUE);
                directDebitFile.addAttachment(temp);
                directDebitFile = directDebitFileRepository.save(directDebitFile);
                attchmentsIds.add(directDebitFile.getAttachment(DD_CANCELLATION_FORM).getId());
                result.put(directDebitFile.getId(), directDebitFile.getAttachment(DD_CANCELLATION_FORM));
            } finally {
                StreamsUtil.closeStream(ddCancellationInputStream);
            }
        }

        getPdfOfImagesForPage(response, attchmentsIds);
    }

    //generate attachment for all (pdf of images)
    @PreAuthorize("hasPermission('directdebitcancelationtodos','getAllDirectDebitCancellation')")
    @RequestMapping(value = "/getAllDirectDebitCancellation")
    public void getAllDirectDebitCancellation(
            HttpServletResponse response,
            @RequestParam(value = "date", required = false) @org.springframework.format.annotation.DateTimeFormat(pattern = "yyyy-MM-dd") Date terminationDate) 
            throws IOException, Exception {
        
        HashMap<Long, Attachment> result = new HashMap<>();
        List<Long> attchmentsIds = new ArrayList<>();

        SelectQuery<DirectDebitCancelationToDo> query = new SelectQuery<>(DirectDebitCancelationToDo.class);

        query.filterBy("completed", "=", false);
        query.filterBy("stopped", "=", false);
        query.filterBy("taskName", "=", "Direct Debit Cancelation Prepare");
        query.filterBy("directDebitFile.ddStatus", "in", Arrays.asList(DirectDebitStatus.CONFIRMED, DirectDebitStatus.EXPIRED));
        query.filterBy("hidden", "=", false);
        query.filterBy(CurrentRequest.getSearchFilter());
        List<DirectDebitCancelationToDo> execute = query.execute();


        for (DirectDebitCancelationToDo directDebitCancelationToDo : execute) {
            DirectDebitFile directDebitFile = directDebitCancelationToDo.getDirectDebitFile();
            if (directDebitFile == null) {
                throw new Exception("Direct Debit record not found");
            }

            Contract contract = directDebitFile.getDirectDebit().getContractPaymentTerm().getContract();

            LocalDate cancellationDate
                    = (terminationDate != null
                    ? new LocalDate(terminationDate)
                    : LocalDate.now());

            InputStream ddCancellationInputStream = null;
            try {
                ddCancellationInputStream = generateDDCancellationDocument(contract, directDebitFile, cancellationDate);

                String fileName = "Direct_Debit_Cancellation_Form_DD_no_" + directDebitFile.getId() + ".pdf";
                Attachment temp = Storage.storeTemporary(fileName, ddCancellationInputStream, DD_CANCELLATION_FORM, Boolean.TRUE);
                directDebitFile.addAttachment(temp);
                directDebitFile = directDebitFileRepository.save(directDebitFile);
                attchmentsIds.add(directDebitFile.getAttachment(DD_CANCELLATION_FORM).getId());
                result.put(directDebitFile.getId(), directDebitFile.getAttachment(DD_CANCELLATION_FORM));
            } finally {
                StreamsUtil.closeStream(ddCancellationInputStream);
            }
        }

        getPdfOfImagesForPage(response, attchmentsIds);

    }


    //generate PDF
    @PreAuthorize("hasPermission('directdebitcancelationtodos','getDirectDebitCancellationTempFile')")
    @RequestMapping(value = "/getDirectDebitCancellationTempFile/{id}")
    public void getDirectDebitCancellationTempFile(
            HttpServletResponse response,
            @PathVariable(name = "id") Long directDebitId,
            @RequestParam(value = "date", required = false) @org.springframework.format.annotation.DateTimeFormat(pattern = "yyyy-MM-dd") Date terminationDate) throws IOException, Exception {

        DirectDebitFile directDebitFile = directDebitFileRepository.findOne(directDebitId);
        if (directDebitFile == null) {
            throw new Exception("Direct Debit record not found");
        }

        boolean hasEmptyDDRefNo = directDebitFile.getDdaRefNo() == null || directDebitFile.getDdaRefNo().equalsIgnoreCase("");

        if (hasEmptyDDRefNo) {
            throw new RuntimeException("Please fill the DD reference number for all selected DDs");
        }

        Contract contract = directDebitFile.getDirectDebit().getContractPaymentTerm().getContract();

        LocalDate cancellationDate
                = (terminationDate != null
                ? new LocalDate(terminationDate)
                : LocalDate.now());

        InputStream ddCancellationInputStream = null;
        
        try {
            ddCancellationInputStream = generateDDCancellationDocument(contract, directDebitFile, cancellationDate);

            String fileName = "Direct_Debit_Cancellation_Form_DD_no_" + directDebitFile.getId() + ".pdf";
            createDownloadResponse(response, fileName, "pdf", ddCancellationInputStream);
        } finally {
            StreamsUtil.closeStream(ddCancellationInputStream);
        }
    }


    //generate image
    @PreAuthorize("hasPermission('directdebitcancelationtodos','getpdfofimages')")
    @RequestMapping(value = "/getpdfofimages/{attachmentId}", method = RequestMethod.GET, produces = "application/MediaType.APPLICATION_OCTET_STREAM_VALUE")
    public ResponseEntity<?> getPdfOfImages(
            @PathVariable("attachmentId") Long attachmentId) throws IOException {
        Attachment attachment = Storage.getAttchment(attachmentId);
        if (attachment == null) {
            return new ResponseEntity<>("Attachment not exist", HttpStatus.INTERNAL_SERVER_ERROR);
        }
        if (!attachment.getExtension().toLowerCase().equals("pdf")) {
            return new ResponseEntity<>("Attachment type is not pdf", HttpStatus.INTERNAL_SERVER_ERROR);
        }
        ByteArrayOutputStream byteArrayOutputStream = PdfHelper.convertToImagesPdf(Storage.getStream(attachment));
        byte[] contents = byteArrayOutputStream.toByteArray();

        //byte[] contents = PdfHelper.getPdfPages(attachment,"all");

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/pdf");
        headers.add("Content-Disposition", "attachment; filename=" + attachment.getName());
        headers.setCacheControl("must-revalidate, post-check=0, pre-check=0");
        ResponseEntity<byte[]> response = new ResponseEntity<>(contents, headers, HttpStatus.OK);
        return response;
    }

    //generate image for page
    @NoPermission
    @RequestMapping(value = "/getpdfofimages", method = RequestMethod.GET, produces = "application/MediaType.APPLICATION_OCTET_STREAM_VALUE")
    public void getPdfOfImagesForPage(
            HttpServletResponse response,
            @RequestBody List<Long> ids) throws IOException, Exception {
        
        HashMap<String, byte[]> contents = new HashMap<>();
        for (long id : ids) {
            Attachment attachment = Storage.getAttchment(id);
            if (attachment == null) {
                throw new Exception("Attachment not exist");
            }
            if (!attachment.getExtension().toLowerCase().equals("pdf")) {
                throw new Exception("Attachment type is not pdf");
            }
            ByteArrayOutputStream byteArrayOutputStream = PdfHelper.convertToImagesPdf(Storage.getStream(attachment));
            byte[] content = byteArrayOutputStream.toByteArray();
            //byte[] content = PdfHelper.getPdfPages(attachment,"all");
            contents.put(attachment.getName() + attachment.getId(), content);
        }

        File zipFile = Paths.get(System.getProperty("java.io.tmpdir"),
                new Date().getTime() + ".rar")
                .toFile();

        String zipFileName = zipFile.getPath();
        FileOutputStream fos = new FileOutputStream(zipFileName);
        ZipOutputStream zos = new ZipOutputStream(fos);

        contents.forEach((name, file) -> {
            try {
                zos.putNextEntry(new ZipEntry(name + ".pdf"));
                zos.write(file);
                zos.closeEntry();
            } catch (IOException ex) {
                Logger.getLogger(DirectDebitCancelationToDoController.class.getName()).log(Level.SEVERE, null, ex);
            }
        });
        zos.close();
        createDownloadResponse(response,
                "dds.rar",
                new FileInputStream(zipFile));
    }

    //ACC-1972
    @Transactional
    @PreAuthorize("hasPermission('directdebitcancelationtodos','SentToBankList')")
    @RequestMapping(value = "/SentToBankList/{stepName}", method = RequestMethod.POST)
    public void DDsSentToBankList(@RequestBody List<Long> directDebitCancelationToDos,
                                  DirectDebitCancellationToDoReason reason,
                                  @PathVariable("stepName") String stepName) throws IOException {
        
        DirectDebitCancelationToDoRepository repos = Setup.getRepository(DirectDebitCancelationToDoRepository.class);
        for (Long id : directDebitCancelationToDos) {
            DirectDebitCancelationToDo findOne = repos.findOne(id);
            if (findOne == null) continue;
            DirectDebitCancelationToDo object = new DirectDebitCancelationToDo();
            object.setId(id);

            this.completeTask(object, new ObjectMapper().createObjectNode(), stepName);
        }
    }

    public InputStream generateDDCancellationDocument(Contract contract, DirectDebitFile directDebitFile, LocalDate cancellationDate) throws IOException {
        String applicationId
                = (directDebitFile.getApplicationId() != null
                ? directDebitFile.getApplicationId()
                : "");

        String WORD_TEMPLATE_CODE_DD_CANCELLATION = "contract_direct_debit_cancellation";
        WordTemplate template = templateRepository.findByCodeIgnoreCase(WORD_TEMPLATE_CODE_DD_CANCELLATION);

        XWPFDocument document = new XWPFDocument(Storage.getStream(template.getAttachment("template")));

        DateTimeFormatter dateFormat = DateTimeFormat.forPattern("yyyy-MM-dd");
        String cancellationDateStr = dateFormat.print(cancellationDate);

        XWPFTable fromToTable = document.getTables().get(0);
//        addValueToCell(
//                fromToTable.getRow(1).getCell(0),
//                "Al Mustaqeem Domestic Workers Services",
//                -2,
//                ParagraphAlignment.CENTER);
        addValueToCell(
                fromToTable.getRow(1).getCell(1),
                contract.getClient().getName(),
                -2,
                ParagraphAlignment.CENTER);
        addValueToCell(
                fromToTable.getRow(2).getCell(1),
                cancellationDateStr,
                -1,
                ParagraphAlignment.CENTER);

        XWPFTable detailsTable = document.getTables().get(3);
        String dda_ref_no = directDebitFile.getDdaRefNo();
        if (dda_ref_no != null) {
            for (int i = 0; i < dda_ref_no.length() && i <= NUMBER_OF_CELLS_FOR_DDA_REF; i++) {
                detailsTable.getRow(0).getCell(i + 1).setText(String.valueOf(dda_ref_no.charAt(i)));
            }
        }
        addValueToCell(
                detailsTable.getRow(1).getCell(1),
                "Domestic Workers Services",
                -1,
                ParagraphAlignment.CENTER);
        addValueToCell(
                detailsTable.getRow(2).getCell(1),
                applicationId,
                -1,
                ParagraphAlignment.CENTER);
        addValueToCell(
                detailsTable.getRow(3).getCell(1),
                "P99 – Unspecific Reason",
                -1,
                ParagraphAlignment.CENTER);

        XWPFTable signatureTable = document.getTables().get(4);
        addValueToCell(
                signatureTable.getRow(0).getCell(0),
                contract.getClient().getName() + ", " + cancellationDateStr,
                -2,
                ParagraphAlignment.LEFT);
        addValueToCell(
                signatureTable.getRow(0).getCell(1),
                cancellationDateStr,
                -2,
                ParagraphAlignment.CENTER);

        XWPFTable officialUseTable = document.getTables().get(5);
//        List<String> identificationCodeAutomatic = Arrays.asList("9", "6", "9", "0", "0", "0", "0", "2", "2");
//        List<String> identificationCodeManual = Arrays.asList("8", "6", "9", "0", "0", "0", "0", "2", "5");

        DDFExportingConfigRepository ddfExportingConfigRepository = Setup.getRepository(DDFExportingConfigRepository.class);
        DDFExportingConfig ddfAutomaticExportingConfig = ddfExportingConfigRepository.findFirstByName("Automatic");
        DDFExportingConfig ddfManualExportingConfig = ddfExportingConfigRepository.findFirstByName("Manual");

        String autoOic = ddfAutomaticExportingConfig.getOic();
        String manualOic = ddfManualExportingConfig.getOic();

        char[] chars = autoOic.toCharArray();
        char[] chars1 = manualOic.toCharArray();

        List<String> identificationCodeAutomatic = new ArrayList<>();
        List<String> identificationCodeManual = new ArrayList<>();

        for (int i = 0; i < chars.length; i++) {
            identificationCodeAutomatic.add(String.valueOf(chars[i]));
        }

        for (int i = 0; i < chars1.length; i++) {
            identificationCodeManual.add(String.valueOf(chars1[i]));
        }

        if (directDebitFile.getDdMethod() == DirectDebitMethod.AUTOMATIC) {
            for (int i = 0; i < identificationCodeAutomatic.size(); i++) {
                if (officialUseTable.getRow(0).getCell(i + 1) != null) {
                    officialUseTable.getRow(0).getCell(i + 1).setText(identificationCodeAutomatic.get(i));
                } else {
                    throw new RuntimeException("template cells number < auto oic code length");
                }
            }
        } else if (directDebitFile.getDdMethod() == DirectDebitMethod.MANUAL) {

            for (int i = 0; i < identificationCodeManual.size(); i++) {
                if (officialUseTable.getRow(0).getCell(i + 1) != null) {
                    officialUseTable.getRow(0).getCell(i + 1).setText(identificationCodeManual.get(i));
                } else {
                    throw new RuntimeException("template cells number < manual oic code length");
                }
            }
        }
        addValueToCell(
                officialUseTable.getRow(1).getCell(1),
                "AL MUSTAQEEM DOMESTIC WORKERS SERVICES",
                -1,
                ParagraphAlignment.LEFT);

        XWPFTable stampTable = document.getTables().get(6);
        addValueToCell(
                stampTable.getRow(0).getCell(0),
                cancellationDateStr,
                2,
                ParagraphAlignment.CENTER);

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        document.write(out);
        
        InputStream inputStream = null;
        InputStream stampInputStream = null;
        InputStream in = null;
        
        try {
            inputStream = new ByteArrayInputStream(out.toByteArray());
            String STAMP_PATH = "tadbeer-stamp.png";
            Resource resource = new ClassPathResource(STAMP_PATH);
            stampInputStream = resource.getInputStream();

            in = wordTemplateService.generateDocument(
                    inputStream,
                    getDirectDebitCancellationParameters(stampInputStream)
            );

            return in;
        } finally {
            StreamsUtil.closeStream(inputStream);
            StreamsUtil.closeStream(stampInputStream);
        }
    }

    private void addValueToCell(XWPFTableCell cell, String value, int appendAtPosition, ParagraphAlignment alignment) {
        if (appendAtPosition == -1) {
            while (cell.getParagraphs().size() > 0) {
                cell.removeParagraph(0);
            }

            addParagraph(cell, value, alignment);
        } else if (appendAtPosition == -2) {
            addParagraph(cell, value, alignment);
        } else if (appendAtPosition >= 0) {
            insertParagraph(cell, value, alignment, appendAtPosition);
        }
    }

    public XWPFParagraph addParagraph(XWPFTableCell cell, String value, ParagraphAlignment alignment) {
        XWPFParagraph paragraph = cell.addParagraph();
        paragraph.setAlignment(alignment);
        paragraph.setVerticalAlignment(TextAlignment.AUTO);
        paragraph.createRun().setText(value);
        paragraph.setSpacingAfter(0);
        paragraph.setSpacingBefore(0);

        return paragraph;
    }

    public void insertParagraph(XWPFTableCell cell, String value, ParagraphAlignment alignment, int index) {
        XWPFParagraph paragraph = addParagraph(cell, value, alignment);
        if (index >= 0) {
            cell.getParagraphs().add(index, paragraph);
            cell.getCTTc().getPList().add(index, paragraph.getCTP());
            cell.removeParagraph(cell.getParagraphs().size() - 1);
        }
    }

    private Map<String, Object> getDirectDebitCancellationParameters(InputStream signature) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("signature", signature != null ? new WordImage(signature, 120, 120) : "");

        return parameters;
    }

    public void cancelAllContractDDs(Contract contract) {
        List<DirectDebit> directDebits = directDebitRepository.findByContractPaymentTerm_Contract(contract);
        
        if (directDebits != null && !directDebits.isEmpty()) {
            for (DirectDebit dd : directDebits) {
                logger.log(Level.SEVERE, prefix + "cancelling dd with id -> " + dd.getId());
                
                if(!directDebitService.ddHasAllPastNonMonthlyPayment(dd)){
                    directDebitCancellationService.cancelWholeDD(dd, DirectDebitCancellationToDoReason.CONTRACT_CANCELLATION);
                }
            }
        }
    }

    @Transactional
    public void createToDoIfValid(Contract contract, boolean checkRequiredUnfitToWorkPayments, List<Long> notToCheckRequiredVisaPayment_ForBoucning,
                                  DirectDebitCancellationToDoReason reason) {
        if (contract.getScheduledDateOfTermination() == null || contract.getIsScheduledForTermination() == null || !contract.getIsScheduledForTermination()) {
            logger.info("Contract#" + contract.getId() + "not Scheduled for Termination");
            return;
        }

        // Jirra ACC-1633
        // if this contract is maid visa
        // and there is at least one required payment for unfit to work
        // stop cancelling
        if (bouncingFlowService.doWeWantMoneyFromClient(contract, checkRequiredUnfitToWorkPayments, notToCheckRequiredVisaPayment_ForBoucning)) {
            logger.info("Contract#" + contract.getId() + " still needs money from Client -> do nothing");
            return;
        }

        Calendar icCalendar = Calendar.getInstance();
        icCalendar.setTime(contract.getScheduledDateOfTermination());
        Calendar todayCalendar = Calendar.getInstance();

        // (Contract Active and Scheduled Date within this month and (not before NOW) and ((MaidCC and client not paying through cheque) OR (MAID VISA)))
        // OR ((Contract Canceled or Expired) and (MAID VISA))
        if ((contract.getStatus().equals(ContractStatus.ACTIVE) &&
                icCalendar.get(Calendar.MONTH) == todayCalendar.get(Calendar.MONTH) &&
                icCalendar.get(Calendar.YEAR) == todayCalendar.get(Calendar.YEAR) &&
                (!icCalendar.getTime().before(new DateTime().withTimeAtStartOfDay().toDate())) &&
                ((contract.isMaidCc()  && !contract.getClient().isPayingThroughCheques(contract)) ||
                        contract.isMaidVisa())) || // ACC-6102
                (Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED).contains(contract.getStatus()) &&
                        contract.isMaidVisa())) {
            logger.log(Level.SEVERE, prefix + "contract is valid");
            List<DirectDebit> dds = directDebitRepository.findByContractPaymentTerm_Contract(contract);
            logger.log(Level.SEVERE, prefix + "dds size: " + dds.size());
            if (dds != null) {
                DirectDebitService ddService = Setup.getApplicationContext().getBean(DirectDebitService.class);
                for (DirectDebit dd : dds) {
                    logger.log(Level.SEVERE, prefix + "cancel direct debit with id: " + dd.getId());
                    // ACC-3844
                     if(!ddService.ddHasAllPastNonMonthlyPayment(dd)){
                         //Jirra ACC-1587
                         directDebitCancellationService.cancelWholeDD(dd, reason);
                     }
                    
                    logger.log(Level.SEVERE, prefix + "done with id: " + dd.getId());
                }
            }
        }
    }

    //Jirra ACC-2739
    @Transactional
    public void showHiddenTodo(Long toDoID) {
        logger.log(Level.SEVERE, prefix + "showing ddCToDo with id: " + toDoID);
        DirectDebitCancelationToDo toDo = getRepository().findOne(toDoID);
        if (toDo == null) {
            logger.log(Level.SEVERE, prefix + "ToDo Not Fount");
            return;
        }

        toDo.setHidden(false);
        getRepository().save(toDo);
        logger.log(Level.SEVERE, prefix + "done for id: " + toDo.getId());
    }

    @Override
    public BaseRepository<DirectDebitCancelationToDo> getRepository() {
        return directDebitCancelationToDoRepository;
    }

    @Override
    protected SelectFilter filter(
            SelectFilter filter, String search, List<String> joins, Map<String, String> joinType) {
        filter = new SelectFilter("directDebit.contractPaymentTerm.contract.client.name", "LIKE", "%" + search + "%");
        return filter;
    }

    @Override
    protected Class<?> getProjectionClass() {
        return DirectDebitCancelationToDoProjection.class;
    }
}
