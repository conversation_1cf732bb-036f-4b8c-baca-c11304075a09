package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.PaymentTypeDescription;
import com.magnamedia.repository.PaymentTypeDescriptionRepository;
import com.magnamedia.service.PaymentTypeDescriptionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@RestController
@RequestMapping("/paymentTypeDescription")
public class PaymentTypeDescriptionController extends BaseRepositoryController<PaymentTypeDescription> {

    @Autowired
    private PaymentTypeDescriptionRepository paymentTypeDescriptionRepository;
    @Autowired
    private PaymentTypeDescriptionService paymentTypeDescriptionService;

    @Override
    public BaseRepository<PaymentTypeDescription> getRepository() {
        return paymentTypeDescriptionRepository;
    }

    @PreAuthorize("hasPermission('paymentTypeDescription','initializePaymentTypeDescription')")
    @PostMapping("/initializePaymentTypeDescription")
    public ResponseEntity<?> initializePaymentTypeDescription(MultipartFile file) throws IOException {

        paymentTypeDescriptionService.initializePaymentTypeDescription(file);

        return ResponseEntity.ok("Done");
    }
}
