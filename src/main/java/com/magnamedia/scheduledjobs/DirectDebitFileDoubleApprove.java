package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.service.MessagingService;
import org.joda.time.LocalDateTime;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

public class DirectDebitFileDoubleApprove implements MagnamediaJob {
    private DirectDebitRepository directDebitRepository;

    @Override
    public void run(Map<String, ?> map) {
        logger.log(Level.INFO, "DirectDebitValidationJob  : start" );
        directDebitRepository = Setup.getRepository(DirectDebitRepository.class);
        verifyDuplicateApproveDDF (); //ACC-3575
        logger.log(Level.INFO, "DirectDebitValidationJob  : end" );
    }

    public void verifyDuplicateApproveDDF (){
        logger.log(Level.INFO, "DirectDebitValidationJob VerifyDuplicateApproveDDF start");
        String ddfStatusChangeDate = new LocalDateTime().minusDays(1).toString("yyyy-MM-dd HH:mm:ss");
        List<String> contractsIds = directDebitRepository.findDuplicateApproveDDF(ddfStatusChangeDate);

        if (contractsIds != null && contractsIds.size() > 0) {
            logger.log(Level.INFO, "DirectDebitValidationJob VerifyDuplicateApproveDDF contractsIds size " + contractsIds.size());
            String contract_ids = "";

            for (String contractId : contractsIds) {
                contract_ids += contractId + " <br/>";
            }
            HashMap<String, String> parameters = new HashMap<>();
            parameters.put("contract_ids", contract_ids);
            Setup.getApplicationContext()
                    .getBean(MessagingService.class)
                    .sendEmailToOfficeStaff("direct_debit_multiple_confirmed_file",
                            parameters, Setup.getParameter(Setup.getCurrentModule(),
                                    AccountingModule.PARAMETER_DD_MULTI_CONFIRMED_EMAILS),
                            "Alert - master DDs has multiple Automatic/Manual DDs confirmed");
        }
        logger.log(Level.INFO, "DirectDebitValidationJob VerifyDuplicateApproveDDF end");
    }

}
