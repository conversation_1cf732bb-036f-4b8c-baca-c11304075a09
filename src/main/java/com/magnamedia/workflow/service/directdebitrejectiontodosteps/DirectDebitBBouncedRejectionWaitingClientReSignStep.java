package com.magnamedia.workflow.service.directdebitrejectiontodosteps;

import com.magnamedia.controller.DirectDebitController;
import com.magnamedia.core.Setup;
import com.magnamedia.entity.ContractPayment;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.module.type.DirectDebitRejectCategory;
import com.magnamedia.module.type.DirectDebitRejectionToDoType;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.repository.DirectDebitRejectionToDoRepository;
import com.magnamedia.workflow.service.DirectDebitRejectionToDoManualStep;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on 5-3-2020
 *         Jirra ACC-1777
 */
@Service
public class DirectDebitBBouncedRejectionWaitingClientReSignStep
        extends DirectDebitRejectionToDoManualStep<DirectDebitRejectionToDo> {

    private static final Logger logger =
            Logger.getLogger(DirectDebitBBouncedRejectionWaitingClientReSignStep.class.getName());

    @Autowired
    private DirectDebitRejectionToDoRepository directDebitRejectionToDoRepository;

    public DirectDebitBBouncedRejectionWaitingClientReSignStep() {
        this.setId(DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_BOUNCED.toString());
    }

    @Override
    public void onSave(DirectDebitRejectionToDo entity) {
    }

    @Override
    public void postSave(DirectDebitRejectionToDo entity) {
    }

    @Override
    public void onDone(DirectDebitRejectionToDo entity) {
        super.onDone(entity);

        logger.log(Level.SEVERE, "DirectDebitBBouncedRejectionWaitingClientReSignStep on done starts ");

        DirectDebit directDebit = entity.getLastDirectDebit();
        if (directDebit != null) {

            if (!directDebit.getStatus().equals(DirectDebitStatus.IN_COMPLETE)) {
                logger.log(Level.SEVERE, " client provided new Info");
                entity.setReminder(0);
                entity.setLeadingRejectionFlowForced(Boolean.FALSE);
                addNewTask(entity, DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B_BOUNCED.toString());
                directDebitRejectionToDoRepository.save(entity);
            } else if (entity.getLastRejectCategory().equals(DirectDebitRejectCategory.Authorization)) {
                logger.log(Level.SEVERE, " client didn't provide new Info");
                //DirectDebitFileRepository directDebitFileRepository = Setup.getRepository(DirectDebitFileRepository.class);

                //List<Object> signatures = new ArrayList();
                //List<DirectDebitFile> directDebitFiles = directDebitFileRepository.findByDirectDebitAndForBouncingPaymentOrderByIdDesc(directDebit, true);


//                int requiredSignatures = 5;
//
//                if (directDebitFiles != null) {
//                    for (DirectDebitFile ddf : directDebitFiles) {
//                        Attachment signature = ddf.getAttachment(DirectDebitFile.FILE_TAG_DD_SIGNATURE);
//                        if (signature != null) {
//                            signatures.add(signature);
//                        }
//
//                        if (signatures.size() == requiredSignatures) break;
//                    }
//                }

                DirectDebitController directDebitController = Setup.getApplicationContext().getBean(DirectDebitController.class);

                List<ContractPayment> contractPayments = new ArrayList();
                for (ContractPayment payment : directDebit.getContractPayments()) {
                    contractPayments.add(payment);
                }

                directDebitController.generateDD(contractPayments,
                        null, directDebit.getContractPaymentTerm(), true, false,
                        true, false, true, false, false);

                entity.setReminder(0);
                entity.setLeadingRejectionFlowForced(Boolean.FALSE);
                addNewTask(entity, DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B_BOUNCED.toString());
                directDebitRejectionToDoRepository.save(entity);
            }
        } else {
            throw new RuntimeException("directDebit can't be null");
        }
    }

    @Override
    public void postDone(DirectDebitRejectionToDo entity) {
    }
}
