package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.*;
import com.magnamedia.extra.VatInfoWrapper;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ExpectedWireTransferStatus;
import com.magnamedia.module.type.VatType;
import com.magnamedia.module.type.WireTransferTempPaymentRelatesTo;
import com.magnamedia.repository.*;
import com.magnamedia.service.CalculateDiscountsWithVatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;

import static com.magnamedia.module.AccountingModule.PICKLIST_TRANSACTION_LICENSE;
import static com.magnamedia.module.AccountingModule.PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM;

/**
 * <AUTHOR> masod <<EMAIL>>
 *         Created on Sep 22, 2020
 *         ACC-2570
 */

@RestController
@RequestMapping("/wireTransferTempPayment")
public class WireTransferTempPaymentController extends BaseRepositoryController<WireTransferTempPayment> {

    @Autowired
    private WireTransferTempPaymentRepository wireTransferTempPaymentRepository;
    @Autowired
    private WireTransferTempTransactionRepository wireTransferTempTransactionRepository;
    @Autowired
    private TransactionsController transactionsController;
    @Autowired
    private TransactionRepository transactionRepository;
    @Autowired
    private ExpectedWireTransferRepository expectedWireTransferRepository;
    @Autowired
    private BucketRepository bucketRepository;
    @Autowired
    private RevenueRepository revenueRepository;
    @Autowired
    private ContractRepository contractRepository;
    @Autowired
    private InterModuleConnector moduleConnector;

    @Override
    public BaseRepository<WireTransferTempPayment> getRepository() {
        return wireTransferTempPaymentRepository;
    }

    @PreAuthorize("hasPermission('wireTransferTempPayment','getPayments')")
    @RequestMapping(value = "/getPayments/{relatesTo}/{relatedEntityId}", method = RequestMethod.GET)
    @Transactional
    public ResponseEntity getPayments(
            @PathVariable("relatesTo") WireTransferTempPaymentRelatesTo relatesTo,
            @PathVariable("relatedEntityId") Long relatedEntityId) {
        SelectQuery<WireTransferTempPayment> selectQuery = new SelectQuery(WireTransferTempPayment.class);
        selectQuery.filterBy("relatesTo", "=", relatesTo);
        selectQuery.filterBy("relatedEntityId", "=", relatedEntityId);

        return new ResponseEntity(selectQuery.execute(), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('wireTransferTempPayment','savePayments')")
    @RequestMapping(value = "/savePayments", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity savePayments(@RequestBody List<WireTransferTempPayment> tempPayments) throws Exception {

        //delete old temp payments and transactions
        if (tempPayments != null && !tempPayments.isEmpty()) {
            WireTransferTempPayment tempPayment = tempPayments.get(0);
            SelectQuery<WireTransferTempPayment> selectQuery = new SelectQuery(WireTransferTempPayment.class);
            selectQuery.filterBy("relatesTo", "=", tempPayment.getRelatesTo());
            selectQuery.filterBy("relatedEntityId", "=", tempPayment.getRelatedEntityId());
            List<WireTransferTempPayment> wireTransferTempPayments = selectQuery.execute();
            if (wireTransferTempPayments != null && !wireTransferTempPayments.isEmpty()) {
                SelectQuery<WireTransferTempTransaction> selectQuery2 = new SelectQuery(WireTransferTempTransaction.class);
                selectQuery2.filterBy("wireTransferPayment", "IN", wireTransferTempPayments);
                List<WireTransferTempTransaction> wireTransferTempTransactions = selectQuery2.execute();
                if (wireTransferTempTransactions != null && !wireTransferTempTransactions.isEmpty())
                    wireTransferTempTransactionRepository.delete(wireTransferTempTransactions);
                wireTransferTempPaymentRepository.delete(wireTransferTempPayments);
            }
        }

        List<WireTransferTempPayment> persistedPayments = new ArrayList();
        Transaction transaction = null;

        for (WireTransferTempPayment tempPayment : tempPayments) {
            logger.log(Level.SEVERE, "Payment Amount: " + tempPayment.getAmountOfPayment());

            if (tempPayment.getRelatesTo().equals(WireTransferTempPaymentRelatesTo.UNKNOWN_WIRE_TRANSFER)) {
                transaction = transactionRepository.findOne(tempPayment.getRelatedEntityId());

            }

            if (tempPayment.getRelatesTo().equals(WireTransferTempPaymentRelatesTo.EXPECTED_WIRE_TRANSFER)) {
                transaction = transactionRepository.findOne(tempPayment.getRelatedEntityId());
            }

            if (transaction != null) {
                tempPayment.setDateOfPayment(transaction.getDate());
            }

            persistedPayments.add(wireTransferTempPaymentRepository.save(tempPayment));
        }

        // check the amounts if match the main transaction

        Double sumOfTempTransactions = tempPayments.stream().mapToDouble(tempPayment -> tempPayment.getAmountOfPayment()).sum();

        logger.log(Level.SEVERE, "Transaction Amount: " + transaction.getAmount());
        logger.log(Level.SEVERE, "Sum of the amounts: " + sumOfTempTransactions);

        if (!sumOfTempTransactions.equals(transaction.getAmount())) {
            throw new RuntimeException("Sum of the amounts('" + sumOfTempTransactions + "') doesn't match the main Wire Transfer Amount('" + transaction.getAmount() + "')");
        }

        for (WireTransferTempPayment tempPayment : persistedPayments) {
            WireTransferTempTransaction tempTransaction = createRelatedTransaction(tempPayment);
            wireTransferTempTransactionRepository.save(tempTransaction);
        }
        return new ResponseEntity("Done", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('wireTransferTempPayment','confirmTransactions')")
    @RequestMapping(value = "/confirmTransactions", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity confirmTransactions(@RequestBody List<Long> tempTransactionsIDs) throws Exception {

        if (tempTransactionsIDs == null || tempTransactionsIDs.isEmpty()) {
            throw new RuntimeException("no transactions to confirm Found");
        }

        List<WireTransferTempTransaction> tempTransactions = wireTransferTempTransactionRepository.findAll(tempTransactionsIDs);

        if (tempTransactions == null || tempTransactions.isEmpty()) {
            throw new RuntimeException("no transactions to confirm Found");
        }

        if (tempTransactions.stream().anyMatch(tempTransaction -> tempTransaction.isConfirmed())) {
            throw new RuntimeException("transactions already confirmed");
        }

        ExpectedWireTransfer expectedWireTransfer = tempTransactions.get(0).getWireTransferPayment().getExpectedWireTransfer();
        if (expectedWireTransfer != null) {
            expectedWireTransfer.setStatus(ExpectedWireTransferStatus.Matched);
            expectedWireTransferRepository.save(expectedWireTransfer);
        }
 /*       for (WireTransferTempTransaction tempTransaction : tempTransactions) {
            WireTransferTempPayment tempPayment = tempTransaction.getWireTransferPayment();

            Long generatedPaymentId = createPayment(tempTransaction.getWireTransferPayment());
            tempPayment.setGeneratedPaymentId(generatedPaymentId);
            tempPayment = wireTransferTempPaymentRepository.save(tempPayment);
            tempTransaction.setWireTransferPayment(tempPayment);
        }*/

        updateRelatedEntity(tempTransactions.get(0));

        if (tempTransactions.size() > 1) {
            for (Integer index = 1; index < tempTransactions.size(); index++) {
                WireTransferTempTransaction tempTransaction = tempTransactions.get(index);
//                tempTransaction.setGeneratedTransactionId(createTransaction(tempTransaction));
            }
        }

        for (WireTransferTempTransaction tempTransaction : tempTransactions) {
            tempTransaction.setConfirmed(true);
            wireTransferTempTransactionRepository.save(tempTransaction);
        }

        return new ResponseEntity("Done", HttpStatus.OK);
    }

    public WireTransferTempTransaction createRelatedTransaction(WireTransferTempPayment tempPayment) {
        WireTransferTempTransaction tempTransaction = new WireTransferTempTransaction();
        tempTransaction.setWireTransferPayment(tempPayment);
        tempTransaction.setAmount(tempPayment.getAmountOfPayment());
        tempTransaction.setTransactionDate(tempPayment.getDateOfPayment());
        tempTransaction.setPnlValueDate(tempPayment.getDateOfPayment());

        //Jirra ACC-2695
        Contract contract = contractRepository.findOne(tempPayment.getContract().getId());
        if (tempPayment.isVatPaidByClient()) {
            ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
            tempTransaction.setVatType(tempPayment.getVatType());


            VatInfoWrapper v = new VatInfoWrapper();
            v.setIncludeWorkerSalary(tempPayment.isIncludeWorkerSalary());
            v.setAmountOfPayment(tempPayment.getAmountOfPayment());
            v.setProspectTypeId(contract.getContractProspectType().getId());
            v.setTypeOfPaymentId(tempPayment.getTypeOfPayment().getId());
            v.setWorkerSalary(contract.getWorkerSalaryNew());

            // ACC-2695
            Double vatAmount = Setup.getApplicationContext()
                    .getBean(CalculateDiscountsWithVatService.class)
                    .calculateVat(v, cpt.getContract());

            logger.log(Level.SEVERE, "response: " + vatAmount);

            tempTransaction.setVatAmount(vatAmount);
        }

        if (tempPayment.getTypeOfPayment().getId().equals(getItem("TypeOfPayment", "monthly_payment").getId())) {
            tempTransaction.setToBucket(bucketRepository.findByCode("BC 10"));

            if (tempPayment.isVatPaidByClient()) {
                tempTransaction.setVatType(VatType.OUT);
            }

            tempTransaction.setLicense(PicklistHelper.getItemNoException(PICKLIST_TRANSACTION_LICENSE, PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM));

            String description = "wire transfer received ";
            if (contract != null && contract.getClient() != null && contract.getClient().getName() != null) {
                description += contract.getClient().getName() + " /";
            }

            if (contract != null && contract.getId() != null) {
                description += " C.ID: " + contract.getId() + " /";
            }

            tempTransaction.setDescription(description);

            if (contract != null && contract.getContractProspectType().getCode().equals(PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE))) {
                tempTransaction.setRevenue(revenueRepository.findByCode("FTR 02"));
            }

            if (contract != null && contract.getContractProspectType().getCode().equals(PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE))) {
                tempTransaction.setRevenue(revenueRepository.findByCode("MVR 02"));
            }

        } else {
            tempTransaction.setRevenue(tempPayment.getRevenue());
            tempTransaction.setToBucket(tempPayment.getToBucket());
            tempTransaction.setLicense(PicklistHelper.getItemNoException(PICKLIST_TRANSACTION_LICENSE, PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM));
            tempTransaction.setDescription(tempPayment.getDescription());
        }

        return tempTransaction;
    }

    public void updateRelatedEntity(WireTransferTempTransaction tempTransaction) {
        Transaction transaction = null;

        if (tempTransaction.getWireTransferPayment().getRelatesTo().equals(WireTransferTempPaymentRelatesTo.UNKNOWN_WIRE_TRANSFER)) {
            transaction = transactionRepository.findOne(tempTransaction.getWireTransferPayment().getRelatedEntityId());
        }

        if (tempTransaction.getWireTransferPayment().getRelatesTo().equals(WireTransferTempPaymentRelatesTo.EXPECTED_WIRE_TRANSFER)) {
            transaction = transactionRepository.findOne(tempTransaction.getWireTransferPayment().getRelatedEntityId());
        }

        transactionsController.updateEntity(transaction);
    }
}
