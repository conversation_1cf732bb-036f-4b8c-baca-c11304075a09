package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.*;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.service.DirectDebitStatusChangeService;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Mar 6, 2019
 *         Jirra ACC-475
 *         this BR should alaways be AfterUpdate update because it's related to DirectDebitFileCancelationToDoBusinessRule
 * 
 */

//ask osamah
@BusinessRule(moduleCode = "", entity = DirectDebitFile.class,
        events = {BusinessEvent.AfterCreate, BusinessEvent.AfterUpdate},
        fields = {"id", "status", "ddStatus", "ddMethod", "directDebit.id", "isFromAccountantAction", "ignoreDDRejectionFlow"})
public class DirectDebitFileBusinessRule implements BusinessAction<DirectDebitFile> {

    private static final Logger logger = Logger.getLogger(DirectDebitFileBusinessRule.class.getName());

    @Override
    public boolean validate(DirectDebitFile entity, BusinessEvent event) {

        logger.log(Level.SEVERE, "DirectDebitFileBusinessRule Validation.");
        if (entity.getIsFromAccountantAction() != null && entity.getIsFromAccountantAction()) {
            logger.log(Level.SEVERE, "DirectDebitFileBusinessRule dont wake it's from dd entry by accountant");
            return false;
        }

        if (entity.getIgnoreDDRejectionFlow() != null && entity.getIgnoreDDRejectionFlow()) {
            logger.log(Level.SEVERE, "DirectDebitFileBusinessRule dont wake it's from Replace Payment BR");
            return false;
        }

        DirectDebitRepository directDebitRepository =
                Setup.getRepository(DirectDebitRepository.class);
        DirectDebit dd =
                directDebitRepository.findOne(entity.getDirectDebit().getId());

        HistorySelectQuery<DirectDebitFile> historyQuery = new HistorySelectQuery<>(DirectDebitFile.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.filterByChanged("ddStatus");
        historyQuery.sortBy("lastModificationDate", false, true);
        historyQuery.setLimit(1);

        List<DirectDebitFile> oldDirectDebitFiles = historyQuery.execute();
        DirectDebitFile oldDirectDebitFile = null;

        logger.log(Level.SEVERE, "DirectDebitFileBusinessRule execute dd id:" + entity.getDirectDebit().getId());
        logger.log(Level.SEVERE, "DirectDebitFileBusinessRule execute dd category:" + entity.getDirectDebit().getCategory());
        logger.log(Level.SEVERE, "DirectDebitFileBusinessRule execute ddf id:" + entity.getId());
        logger.log(Level.SEVERE, "DirectDebitFileBusinessRule execute ddf status:" + entity.getDdStatus());
        logger.log(Level.SEVERE, "DirectDebitFileBusinessRule execute ddf ddMethod:" + entity.getDdMethod().getValue());


        if (!oldDirectDebitFiles.isEmpty()) {
            oldDirectDebitFile = oldDirectDebitFiles.get(0);
        }

        logger.log(Level.SEVERE, "DirectDebitFileBusinessRule dd.id: " + dd.getId());
        logger.log(Level.SEVERE, "DirectDebitFileBusinessRule dd.getStatus: " + dd.getStatus());
        logger.log(Level.SEVERE, "DirectDebitFileBusinessRule dd.getMStatus: " + dd.getMStatus());
        logger.log(Level.SEVERE, "DirectDebitFileBusinessRule entity.id: " + entity.getId());
        logger.log(Level.SEVERE, "DirectDebitFileBusinessRule entity.getStatus: " + entity.getStatus());
        logger.log(Level.SEVERE, "DirectDebitFileBusinessRule entity.getDdStatus: " + entity.getDdStatus());
        Contract contract = dd.getContractPaymentTerm().getContract();
        if (contract.isCancelledWithinFirstXDays()) {
            logger.log(Level.SEVERE, "Contract is Cancelled within First X Days");
            return false;
        }

        //Jirra ACC-2792
//        if (ddStatusOfDDfChanged && entity.getDdStatus().equals(DirectDebitStatus.CANCELED)) {
//            if (entity.getDdMethod().equals(DirectDebitMethod.AUTOMATIC) && dd != null && dd.getAutoDdfFile() != null && dd.getAutoDdfFile().getId() != null &&
//                    dd.getAutoDdfFile().getId().equals(entity.getId())) {
//                logger.log(Level.SEVERE, "DirectDebitFileBusinessRule Cancelled DDF & it's the main Auto File" + entity.getDdStatus());
//                return false;
//            }
//
//            if (entity.getDdMethod().equals(DirectDebitMethod.MANUAL) && dd != null && dd.getManualDdfFile() != null && dd.getManualDdfFile().getId() != null &&
//                    dd.getManualDdfFile().getId().equals(entity.getId())) {
//                logger.log(Level.SEVERE, "DirectDebitFileBusinessRule Cancelled DDF & it's the main Manual File" + entity.getDdStatus());
//                return false;
//            }
//        }

        boolean ddStatusOfDDfChanged = (oldDirectDebitFile == null && entity.getDdStatus() != null
                        && entity.getDdStatus().equals(DirectDebitStatus.CANCELED))
                        || (oldDirectDebitFile != null && oldDirectDebitFile.getDdStatus() != null && entity.getDdStatus() != null
                        && (!oldDirectDebitFile.getDdStatus().equals(DirectDebitStatus.CANCELED)
                        && entity.getDdStatus().equals(DirectDebitStatus.CANCELED)));


        return ddStatusOfDDfChanged && dd.getStatus() != null && ((entity.getDdMethod() == DirectDebitMethod.AUTOMATIC && dd.getStatus().equals(DirectDebitStatus.PENDING))
                || (entity.getDdMethod() == DirectDebitMethod.MANUAL && dd.getMStatus().equals(DirectDebitStatus.PENDING)));
    }

    @Override
    public Map<String, Object> execute(DirectDebitFile entity, BusinessEvent even) {
        logger.log(Level.SEVERE, "DirectDebitFileBusinessRule execute.");

        return Setup.getApplicationContext()
                .getBean(DirectDebitStatusChangeService.class)
                .ddFileHandelRejectedCanceled(entity);
    }
}
