package com.magnamedia.helper;

import com.magnamedia.entity.Client;
import org.apache.commons.lang3.text.WordUtils;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public final class SmsFormatter {

    private static final List<String> compositeNames = Arrays.asList("abdul","abdoul","abdel","abd","abed","abu");

    private static final String pattern="EEEE, MMM dd, yyyy";

    private static final SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);

    public static String formatClientName(Client client) {

        if(client==null || client.getName()==null)
            return "";
        
        if (client.getName() == null || client.getName().trim().equals(""))
            return client.getName();

        String title="";

        if(client.getTitle() != null){
             title=client.getTitle().getName()+". ";
        }

        return  "Dear "+title+formatName(client.getName())+",";
    }

    public static String clientGreeting(){
        return "Thank you for using our services.";
    }

    public static String formatName(String name){
        
        if(name == null)
            return "";

        String fullname=name.toLowerCase();


        //remove prefixes and trim the string
        final String regex = "^(mr|mr.|mr-|mrs|mrs.|mrs-|ms|ms.|ms-)\\b";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(fullname);
        fullname=matcher.replaceAll("");
        fullname = fullname.replaceFirst("[\\-\\+\\.\\^:,]","").trim();


        //get the first name
        int firstSpace = fullname.indexOf(" ");

        if(firstSpace > -1){

            String firstName=fullname.substring(0, firstSpace);
            if(!compositeNames.contains(firstName))
                return WordUtils.capitalizeFully(firstName);


            // abd abed abdul abdel
            int secondSpace=fullname.indexOf(" ",firstSpace+1);
            if(secondSpace > -1 ){

                firstName=fullname.substring(0, secondSpace);

                if (firstName.endsWith(" el") || firstName.endsWith(" al")){
                    int thirdSpace = fullname.indexOf(" ",secondSpace+1);
                    if(thirdSpace > -1 ){
                        firstName=fullname.substring(0, thirdSpace);
                        return WordUtils.capitalizeFully(firstName);
                    }else {
                        return WordUtils.capitalizeFully(fullname);
                    }
                }

                return WordUtils.capitalizeFully(firstName);
            }
        }

        return WordUtils.capitalizeFully(fullname);
    }
}