package com.magnamedia.extra;

import com.magnamedia.entity.*;
import com.magnamedia.module.type.TransactionEntityType;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Mar 18, 2019
 *         Jirra ACC-462
 */
public class AddDissmissVisaExpense {

    private Long visaRequestExpenseID;
    private String visaRequestExpenseType;
    private Transaction transaction;
    private TransactionEntityType transactionType;
    private OfficeStaff officeStaff;
    private Housemaid housemaid;

    public AddDissmissVisaExpense() {
    }

    public AddDissmissVisaExpense(Long visaRequestExpenseID, String visaRequestExpenseType) {
        this.visaRequestExpenseID = visaRequestExpenseID;
        this.visaRequestExpenseType = visaRequestExpenseType;
    }

    public AddDissmissVisaExpense(Long visaRequestExpenseID, String visaRequestExpenseType, Transaction transaction) {
        this.visaRequestExpenseID = visaRequestExpenseID;
        this.visaRequestExpenseType = visaRequestExpenseType;
        this.transaction = transaction;
    }

    public Long getVisaRequestExpenseID() {
        return visaRequestExpenseID;
    }

    public void setVisaRequestExpenseID(Long visaRequestExpenseID) {
        this.visaRequestExpenseID = visaRequestExpenseID;
    }

    public String getVisaRequestExpenseType() {
        return visaRequestExpenseType;
    }

    public void setVisaRequestExpenseType(String visaRequestExpenseType) {
        this.visaRequestExpenseType = visaRequestExpenseType;
    }

    public Transaction getTransaction() {
        return transaction;
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
    }

    public TransactionEntityType getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(TransactionEntityType transactionType) {
        if (transactionType != null) {
            this.transactionType = transactionType;
            this.transaction.setTransactionType(transactionType);
        }
    }

    public OfficeStaff getOfficeStaff() {
        return officeStaff;
    }

    public void setOfficeStaff(OfficeStaff officeStaff) {
        if (officeStaff != null) {
            this.officeStaff = officeStaff;
            List<OfficeStaffTransaction> officeStaffsTransaction = new ArrayList();
            OfficeStaffTransaction officeStaffTransaction = new OfficeStaffTransaction();
            officeStaffTransaction.setOfficeStaff(officeStaff);
            officeStaffsTransaction.add(officeStaffTransaction);
            this.transaction.setOfficeStaffs(officeStaffsTransaction);
        }
    }

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {

        if (housemaid != null) {
            this.housemaid = housemaid;
            List<HousemaidTransaction> housemaidsTransactions = new ArrayList();
            HousemaidTransaction housemaidTransaction = new HousemaidTransaction();
            housemaidTransaction.setHousemaid(housemaid);
            housemaidTransaction.setAmount(transaction.getAmount());
            housemaidsTransactions.add(housemaidTransaction);
            this.transaction.setHousemaids(housemaidsTransactions);
        }
    }
}
