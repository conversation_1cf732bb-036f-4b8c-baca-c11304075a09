package com.magnamedia.entity.projection;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Jun 26, 2019
 * Jirra ACC-675
 */
public class ProjectFilter{

    private String field;
    private String operation;
    private String value;
    private Boolean and;
    private ProjectFilter left;
    private ProjectFilter right;
    private String fieldType;
    private String fieldValue;

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getFieldType() {
        return fieldType;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getFieldValue() {
        return fieldValue;
    }

    public void setFieldValue(String fieldValue) {
        this.fieldValue = fieldValue;
    }

    public Boolean getAnd() {
        return and;
    }

    public void setAnd(Boolean and) {
        this.and = and;
    }

    public ProjectFilter getLeft() {
        return left;
    }

    public void setLeft(ProjectFilter left) {
        this.left = left;
    }

    public ProjectFilter getRight() {
        return right;
    }

    public void setRight(ProjectFilter right) {
        this.right = right;
    }


}
