package com.magnamedia.extra;

import com.magnamedia.module.type.ExpensePaymentMethod;
import org.springframework.beans.factory.annotation.Value;

public interface SupplierCSVProjection {

    Long getId();

    String getName();

    ExpensePaymentMethod getPaymentMethod();

    @Value("#{target.getPaymentMethod() != null && target.getPaymentMethod().getValue().equals(\"CREDIT_CARD\") && " +
            "target.getNameInFinancialStatement() != null ? target.getNameInFinancialStatement() : ''}")
    String getFinancialName();

    @Value("#{target.getPaymentMethod() != null && target.getPaymentMethod().getValue().equals(\"BANK_TRANSFER\") ? " +
            "((target.getInternational() != null && target.getInternational()) ? 'International' : 'Local') : ''}")
    String getInternationalAsString();

    @Value("#{target.getPaymentMethod() != null && target.getPaymentMethod().getValue().equals(\"BANK_TRANSFER\") && " +
            "target.getIban() != null ? target.getIban() : ''}")
    String getIbanForSupplier();

    @Value("#{target.getPaymentMethod() != null && target.getPaymentMethod().getValue().equals(\"BANK_TRANSFER\") && " +
            "target.getAccountNumber() != null ? target.getAccountNumber() : ''}")
    String getAccountNumberForSupplier();

    @Value("#{target.getPaymentMethod() != null && target.getPaymentMethod().getValue().equals(\"BANK_TRANSFER\") && " +
            "target.getInternational() != null && target.getInternational() != null && " +
            "target.getInternational() && target.getSwift() != null ? target.getSwift() : ''}")
    String getSwiftForSupplier();

    @Value("#{target.getVatRegistered() != null && target.getVatRegistered() ? 'Yes' : 'No'}")
    String getVatRegisteredAsString();
}