package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.ContractPaymentType;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;


@Repository
public interface ContractPaymentTypeRepository extends BaseRepository<ContractPaymentType> {

    @Query("select count(t.id) > 0 " +
            "from PicklistItem pit join pit.tags t " +
            "where pit.id = ?1 and t.name = 'NO_VAT'")
    boolean paymentHasNoVat(Long id);

    @Query(nativeQuery = true, value =
            "SELECT y.AMOUNT FROM CONTRACTPAYMENTTYPES y INNER JOIN CONTRACTPAYMENTTERMS t ON y.CONTRACT_PAYMENT_TERM_ID = t.ID " +
            "WHERE t.CONTRACT_ID = ?1 AND t.IS_ACTIVE = 1 AND y.TYPE_ID = 1")
    Double findWpsAmountWithVatByContractId(Long contractId);

    ContractPaymentType findByIdAndPostponedDdGeneratedFalse(Long id);
}
