/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.extra;

import static com.magnamedia.extra.TicketMatchingLibrary.logger;
import com.magnamedia.module.type.OperationType;
import java.util.Arrays;
import java.util.List;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
public class StatementsCSVParsingLibrary {
    //Formats for transaction date
    public static final List<DateTimeFormatter> FORMATTERS1
            = Arrays.asList(
                    DateTimeFormat.forPattern("dd/MMM/yy"),
                    DateTimeFormat.forPattern("dd-MMM-yy"),
                    DateTimeFormat.forPattern("dd.MMM.yy"),
                    DateTimeFormat.forPattern("yyyy MMM dd"),
                    DateTimeFormat.forPattern("dd MMM yyyy")
            );

    //Formats for opetation date
    public static final List<DateTimeFormatter> FORMATTERS2
            = Arrays.asList(
                    DateTimeFormat.forPattern("dd-MM-yyyy"),
                    DateTimeFormat.forPattern("dd.MM.yyyy"),
                    DateTimeFormat.forPattern("dd/MM/yyyy"));
    
final static Logger logger = LoggerFactory.getLogger(TicketMatchingLibrary.class);

    public static StatementCand parsLine(StatementCand line) {
        //Transaction Date
        for (DateTimeFormatter formatter : FORMATTERS1) {
            try {
                line.setTransactionDate(formatter.parseLocalDate(line.getColumn1()).toDate());
                debug("Transaction Date" + line.getTransactionDate());
            } catch (IllegalArgumentException e) {
                // Go on to the next format
            }
        }
        if (line.getTransactionDate() == null) {
            line.setIsValid(Boolean.FALSE);
            debug("Faild with transaction date "+line.getColumn1());
        }

        //Debit & Credit
//        try {
//            String amount = line.getColumn3().replace(" ", "").replace(",", "").replace("AED", "");
//            if (Double.valueOf(amount) < 0) {
//                line.setDebit(Math.abs(Double.valueOf(amount)));
//            } else {
//                line.setCredit(Double.valueOf(amount));
//            }
//        } catch (Exception e) {
//            line.setIsValid(Boolean.FALSE);
//            debug("Failed with Debit or Credit "+line.getColumn3());
//        }
        //Debit
        try {
            String amount = line.getColumn3().replace(" ", "").replace(",", "").replace("AED", "");
            if(!amount.equals("")&&amount!=null)
                line.setDebit(Math.abs(Double.valueOf(amount)));
            
        } catch (Exception e) {
            line.setIsValid(Boolean.FALSE);
            debug("Failed with Debit "+line.getColumn3());
        }
        
        //credit
        try {
            String amount = line.getColumn4().replace(" ", "").replace(",", "").replace("AED", "");
            if(!amount.equals("")&&amount!=null)
                line.setCredit(Math.abs(Double.valueOf(amount)));
            
        } catch (Exception e) {
            line.setIsValid(Boolean.FALSE);
            debug("Failed with Credit "+line.getColumn4());
        }

        //Balance
        try {
            String balance = line.getColumn5().replace(" ", "").replace(",", "").replace("AED", "");
            line.setBalance(Double.valueOf(balance));
        } catch (Exception e) {
            line.setIsValid(Boolean.FALSE);
            debug("Faild with Balance "+line.getColumn5());
        }

        //Description
        // Operation Type
        try {
            line.setOpertaionType(line.getColumn2().toUpperCase().contains("PURCHASE") ? OperationType.PURCHASE : OperationType.REFUNDS);

        } catch (Exception e) {
            line.setIsValid(Boolean.FALSE);
            debug("Failed with operation type "+line.getColumn2());
        }

        //Operation date
        String[] dateTokens = line.getColumn2().split(" ");
        try {
            for (String temp : dateTokens) {
                for (DateTimeFormatter formatter : FORMATTERS2) {
                    try {
                        line.setOperationDate(formatter.parseLocalDate(temp).toDate());
                        break;
                    } catch (IllegalArgumentException e) {
                        // Go on to the next format
                    }
                }
            }
        } catch (Exception e) {
            line.setIsValid(Boolean.FALSE);
            debug("Failed with operation Date "+line.getColumn2());
        }

        //Company name
        String[] commaSplitted = line.getColumn2().split(",");
        String[] spaceSplitted = line.getColumn2().split(" ");
        try {
            try {

                String[] companyValues = commaSplitted[1].split(" ");
                if (companyValues[0].length() == 3) {
                    line.setCompany(companyValues[1]);
                } else {
                    line.setCompany(companyValues[0].substring(3));
                }
            } catch (Exception e) {
                String[] tokens = line.getColumn2().split(" ");
                for (int i = 0; i < tokens.length; i++) {
                    for (DateTimeFormatter formatter : FORMATTERS2) {
                        try {
                            formatter.parseLocalDate(tokens[i]);
                            line.setCompany(tokens[i + 1]);

                            break;
                        } catch (IllegalArgumentException ex) {
                            // Go on to the next format
                        }
                    }
                }
            }
        } catch (Exception e) {
            line.setIsValid(Boolean.FALSE);
            debug("Failed with Company name "+line.getColumn2());
        }
        //Booking confirmation number
        
        try {
            String beforeColon = commaSplitted[1].split(":")[0];
            String[] s = beforeColon.split(" ");
            if (s.length > 3 && !s[1].toUpperCase().contains("THAI") && s[2].length() == 6 && StringLibrary.checkIfUpperCase(s[2])) {
                line.setBookingConfirmationCode(s[2]);
            } else if (s.length > 3 && s[1].toUpperCase().contains("THAI")) {
                line.setBookingConfirmationCode(s[3]);
            } else if (s.length > 2 && s[0].toUpperCase().contains("QATAR") && s[1].contains("0000")) {
                int position = s[2].indexOf("0000" + 4);
                line.setBookingConfirmationCode(s[2].substring(position));
            } else if (beforeColon.toUpperCase().contains("QATAR") && beforeColon.contains("0000")) {
                int position = beforeColon.indexOf("0000" + 4);
                if (beforeColon.contains(" ")) {
                    line.setBookingConfirmationCode(beforeColon.substring(position).split(" ")[0]);
                } else {
                    line.setBookingConfirmationCode(beforeColon.substring(position));
                }
            } else if (s[2].toUpperCase().contains("TRIPSTA")) {
                line.setBookingConfirmationCode(beforeColon.substring(beforeColon.indexOf("-")).split(" ")[0].replace("-", ""));
            } else {
                for (int i = 0; i < spaceSplitted.length; i++) {
                    if (spaceSplitted[i].toUpperCase().startsWith("NO")) {
                        line.setBookingConfirmationCode(spaceSplitted[i + 1]);
                    }
                }
            }
        } catch (Exception e) {
            line.setIsValid(Boolean.FALSE);
            debug("Failed with Booking Confirmation Code "+line.getColumn2());
        }
        line.setDescription(line.getColumn2());
        return line;
    }

    public static void debug(String s){
        System.out.println(s);
    }
}
