package com.magnamedia.entity.projection;

import com.magnamedia.entity.ContractPaymentConfirmationToDo;
import com.magnamedia.module.type.PaymentMethod;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;

public interface ContractPaymentConfirmationSearchProjectionCsv {
    @Value("#{target.getClientName()}")
    String getClient();

    @Value("#{target.getHousemaidName()}")
    String getHousemaid();

    Long getContractId();

    String getContractType();

    @Value("#{target.getNationalityName()}")
    String getNationality();

    @Value("#{(new org.joda.time.LocalDateTime(target.getCreationDate()).toString('yyyy-MM-dd HH:mm:ss'))}")
    String getDateChangedToReceived();

    @Value("#{target.getTotalAmount()}")
    Double getAmount();

    String getTransferReference();

    Date getExpectedDate();

    PaymentMethod getPaymentMethod();

    @Value("#{target.getTypesOfPaymentsLabels()}")
    String getPaymentType();

    ContractPaymentConfirmationToDo.Source getSource();

    String getDescription();

    String getAuthorizationCode();

    @Value("#{target.isActive()}")
    Boolean getIsActive();
}
