package com.magnamedia.extra;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.magnamedia.entity.Supplier;
import com.magnamedia.module.type.ExpensePaymentMethod;

/**
 * Created by Mamon.Masod on 4/19/2021.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class SupplierCsvDTO {
    private String supplierName;
    private String supplierLocation;
    private String supplierPhoneNumber;
    private String supplierWebSite;
    private String supplierEmail;
    private Boolean vatRegistered;
    private String paymentMethod;
    private String mobileNumber;
    private String localAccountName;
    private String localAccountNumber;
    private String localIban;
    private String internationalAccountName;
    private String internationalAccountNumber;
    private String internationalIban;
    private String internationalSwiftCode;
    private String internationalAddress;
    private String nameInBankSOA;

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSupplierLocation() {
        return supplierLocation;
    }

    public void setSupplierLocation(String supplierLocation) {
        this.supplierLocation = supplierLocation;
    }

    public String getSupplierPhoneNumber() {
        return supplierPhoneNumber;
    }

    public void setSupplierPhoneNumber(String supplierPhoneNumber) {
        this.supplierPhoneNumber = supplierPhoneNumber;
    }

    public String getSupplierWebSite() {
        return supplierWebSite;
    }

    public void setSupplierWebSite(String supplierWebSite) {
        this.supplierWebSite = supplierWebSite;
    }

    public String getSupplierEmail() {
        return supplierEmail;
    }

    public void setSupplierEmail(String supplierEmail) {
        this.supplierEmail = supplierEmail;
    }

    public Boolean getVatRegistered() {
        return vatRegistered;
    }

    public void setVatRegistered(Boolean vatRegistered) {
        this.vatRegistered = vatRegistered;
    }

    public ExpensePaymentMethod getPaymentMethodAsEnum() {
        if (paymentMethod == null) return null;

        return ExpensePaymentMethod.valueOf(paymentMethod);
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getLocalAccountName() {
        return localAccountName;
    }

    public void setLocalAccountName(String localAccountName) {
        this.localAccountName = localAccountName;
    }

    public String getLocalAccountNumber() {
        return localAccountNumber;
    }

    public void setLocalAccountNumber(String localAccountNumber) {
        this.localAccountNumber = localAccountNumber;
    }

    public String getLocalIban() {
        return localIban;
    }

    public void setLocalIban(String localIban) {
        this.localIban = localIban;
    }

    public String getInternationalAccountName() {
        return internationalAccountName;
    }

    public void setInternationalAccountName(String internationalAccountName) {
        this.internationalAccountName = internationalAccountName;
    }

    public String getInternationalAccountNumber() {
        return internationalAccountNumber;
    }

    public void setInternationalAccountNumber(String internationalAccountNumber) {
        this.internationalAccountNumber = internationalAccountNumber;
    }

    public String getInternationalIban() {
        return internationalIban;
    }

    public void setInternationalIban(String internationalIban) {
        this.internationalIban = internationalIban;
    }

    public String getInternationalSwiftCode() {
        return internationalSwiftCode;
    }

    public void setInternationalSwiftCode(String internationalSwiftCode) {
        this.internationalSwiftCode = internationalSwiftCode;
    }

    public String getInternationalAddress() {
        return internationalAddress;
    }

    public void setInternationalAddress(String internationalAddress) {
        this.internationalAddress = internationalAddress;
    }

    public String getNameInBankSOA() {
        return nameInBankSOA;
    }

    public void setNameInBankSOA(String nameInBankSOA) {
        this.nameInBankSOA = nameInBankSOA;
    }

    public void fillSupplierProps(Supplier supplier) {
        supplier.setName(this.getSupplierName());
        supplier.setLocation(this.getSupplierLocation());
        supplier.setPhoneNumber(this.getSupplierPhoneNumber());
        supplier.setWebSite(this.getSupplierWebSite());
        supplier.setEmail(this.getSupplierEmail());
        supplier.setVatRegistered(this.getVatRegistered());
        supplier.setPaymentMethod(this.getPaymentMethodAsEnum());
        supplier.setMobileNumber(this.getMobileNumber());

        if (!StringUtils.isEmpty(this.getLocalAccountName())) {
            supplier.setInternational(Boolean.FALSE);
            supplier.setAccountName(this.getLocalAccountName());
            supplier.setAccountNumber(!StringUtils.isEmpty(this.getLocalAccountNumber()) ? this.getLocalAccountNumber().replace("'", "") : null);
            supplier.setIban(this.getLocalIban());
        } else {
            if (!StringUtils.isEmpty(this.getInternationalAccountName())) {
                supplier.setInternational(Boolean.TRUE);
            }

            supplier.setAccountName(this.getInternationalAccountName());
            supplier.setAccountNumber(!StringUtils.isEmpty(this.getInternationalAccountNumber()) ? this.getInternationalAccountNumber().replace("'", "") : null);
            supplier.setIban(this.getInternationalIban());
        }

        supplier.setSwift(this.getInternationalSwiftCode());
        supplier.setAddress(this.getInternationalAddress());
        supplier.setNameInFinancialStatement(this.getNameInBankSOA());
    }
}
