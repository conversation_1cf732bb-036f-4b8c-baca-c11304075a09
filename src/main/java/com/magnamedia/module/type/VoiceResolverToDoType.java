package com.magnamedia.module.type;

import com.fasterxml.jackson.annotation.JsonValue;

public enum VoiceResolverToDoType {
    TALK_TO_CLIENT("Talk To Client"), 
    CLIENT_WANTS_TO_RENEW("Client Wants To Renew"), 
    GENERATE_DD("Generate DD"), 
    RENEWAL_NUDGE("RENEWAL_NUDGE"); 

    private final String label;

    private VoiceResolverToDoType(String label) {
        this.label = label;
    }

    @JsonValue
    public String getLabel() {
        return label;
    }
}
