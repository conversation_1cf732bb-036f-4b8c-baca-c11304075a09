package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on May 10, 2020
 *         Jirra ACC-1870
 */
public enum PaymentMatchingRecordStatus implements LabelValueEnum {
    MATCHED("Matched"),
    NOT_MATCHED("Not Matched"),
    CONFIRMED("Confirmed"),
    FAILED("Failed"),
    IN_PROGRESS("in Progress");

    private final String label;

    PaymentMatchingRecordStatus(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}