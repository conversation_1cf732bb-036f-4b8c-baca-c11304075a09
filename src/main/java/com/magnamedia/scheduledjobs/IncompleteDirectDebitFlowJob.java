package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.FlowProcessorEntity;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.repository.FlowProcessorEntityRepository;
import com.magnamedia.service.FlowProcessorService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

// ACC-6804
public class IncompleteDirectDebitFlowJob implements MagnamediaJob {
    private static final Logger logger =
            Logger.getLogger(IncompleteDirectDebitFlowJob.class.getName());

    private final FlowProcessorService flowProcessorService;
    private final FlowProcessorEntityRepository flowProcessorEntityRepository;

    public IncompleteDirectDebitFlowJob() {
        flowProcessorEntityRepository = Setup.getRepository(FlowProcessorEntityRepository.class);
        flowProcessorService = Setup.getApplicationContext()
                .getBean(FlowProcessorService.class);
    }

    @Override
    public void run(Map<String, ?> map) {

        sendMessages();
    }

    public void sendMessages() {

        Page<FlowProcessorEntity> p;
        Long lastId = -1L;
        do {
            p = flowProcessorEntityRepository
                    .findRunningIncompleteFlowMissingBankInfo(
                            lastId, FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_BANK_INFO, PageRequest.of(0, 200));
            for (FlowProcessorEntity f : p.getContent()) {
                try {
                    logger.info("cpt id:" + f.getContractPaymentTerm().getId() + "; flow id: " + f.getId());
                    flowProcessorService.processFlowSubEventConfig(f);

                } catch (Exception e) {
                    logger.log(Level.SEVERE, "error " + e.getMessage());
                    e.printStackTrace();
                }
            }

            if (!p.getContent().isEmpty()) lastId = p.getContent().get(p.getContent().size() - 1).getId();
        } while (!p.getContent().isEmpty());
    }
}
