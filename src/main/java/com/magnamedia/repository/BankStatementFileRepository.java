package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.BankStatementFile;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created At Apr 18, 2020
 *
 **/


@Repository
public interface BankStatementFileRepository extends BaseRepository<BankStatementFile> {

    public List<BankStatementFile> findByCreationDateBetween(Date fromDate, Date toDate);

}
