package com.magnamedia.service;

import com.magnamedia.controller.ContractPaymentTermController;
import com.magnamedia.controller.DirectDebitController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.extra.DDUtils;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.workflow.service.directdebitrejectiontodosteps.DirectDebitBCaseDRejectionWaitingBankResponseStep;
import com.magnamedia.workflow.service.directdebitrejectiontodosteps.DirectDebitBRejectionWaitingBankResponseStep;
import org.joda.time.DateTime;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Apr 23, 2020
 *         Jirra ACC-1611
 */
@Service
public class DirectDebitRejectionFlowService {

    @Autowired
    private DirectDebitRepository directDebitRepository;
    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;
    @Autowired
    private DirectDebitRejectionToDoRepository directDebitRejectionToDoRepository;
    @Autowired
    private FlowProcessorEntityRepository flowProcessorEntityRepository;

    @Autowired
    private DirectDebitController directDebitController;
    @Autowired
    private ContractPaymentTermController cptController;
    @Autowired
    private InterModuleConnector moduleConnector;
    @Autowired
    private BouncingFlowService bouncingFlowService;
    @Autowired
    private CollectionFlowLogService collectionFlowLogService;
    @Autowired
    private DirectDebitSignatureService directDebitSignatureService;
    @Autowired
    private DirectDebitGenerationPlanService directDebitGenerationPlanService;
    @Autowired
    private DirectDebitService directDebitService;

    private static final Logger logger =
            Logger.getLogger(DirectDebitRejectionFlowService.class.getName());

    public void startRejectionFlowUponDDStatusChange(DirectDebit directDebit) {
        logger.log(Level.SEVERE, "DirectDebitFileAcceptedBusinessRule startRejectionFlow for: " + directDebit.getId());

        DirectDebitRejectionToDo directDebitRejectionToDo = directDebitService.getDirectDebitToDo(directDebit);
        
        if (directDebitRejectionToDo != null && !directDebitRejectionToDo.isStopped() && !directDebitRejectionToDo.isCompleted()) {
            List<String> currentTasks = directDebitRejectionToDo.getCurrentTasks();
            if (!currentTasks.isEmpty()) {
                DirectDebitRejectionToDoType step = DirectDebitRejectionToDoType.valueOf(currentTasks.get(currentTasks.size() - 1));
                switch (step) {
                    case WAITING_BANK_RESPONSE_B: {
                        Setup.getApplicationContext().getBean(DirectDebitBRejectionWaitingBankResponseStep.class)
                                .onDone(directDebitRejectionToDo);
                        break;
                    }
                    case WAITING_BANK_RESPONSE_B_CASE_D: {
                        Setup.getApplicationContext().getBean(DirectDebitBCaseDRejectionWaitingBankResponseStep.class)
                                .onDone(directDebitRejectionToDo);
                        break;
                    }
                }
            }
        }
    }
    
    @Transactional
    public DirectDebitRejectionToDo startFlow(DirectDebit directDebit) {
        //ACC-4715
        if (directDebit.getContractPaymentTerm().getContract().isPayingViaCreditCard()) {
            Setup.getApplicationContext().getBean(ClientPayingViaCreditCardService.class)
                .startNewFlow(directDebit.getContractPaymentTerm(),
                    FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card,
                    FlowSubEventConfig.FlowSubEventName.DD_Rejection,
                    directDebit,null);

            return null;
        }

        return directDebit.getCategory() == DirectDebitCategory.A ?
                startTypeAFlow(directDebit) : startTypeBFlow(directDebit);
    }

    private DirectDebitRejectionToDo startTypeAFlow(DirectDebit directDebit) {

        logger.log(Level.SEVERE, "DirectDebitRejectionFlowService startTypeAFlow");
        logger.log(Level.SEVERE, "DirectDebitRejectionFlowService directDebit.getRejectCategory(): " + directDebit.getRejectCategory());

        DirectDebitRejectionToDoType startStep = null;
        int trials = 0;
        int reSingTrials = 0;
        int reminder = 0;

        /*boolean createExpertTodo = false;
        String reasonToCall = "";
        String initialNotes = "";*/
        boolean scheduleForTermination = false;
        boolean dontSendDDMessage = false;
        boolean leadingRejectionFlow = false;

        Integer maxTrials =
                Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_MAX_TRIALS));

        List<DirectDebit> newGeneratedDirectDebits = new ArrayList<>();

        if (null != directDebit.getRejectCategory()) switch (directDebit.getRejectCategory()) {
            case Compliance:
            case Other:
                DirectDebit newDD = directDebit.clone(DirectDebitStatus.PENDING);
                directDebitRepository.save(newDD);
                newGeneratedDirectDebits.add(newDD);

                directDebit.cloneChildDds(newDD, 0);

                startStep = DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE;
                break;
            
            case Signature:

                logger.log(Level.SEVERE, "startTypeAFlow dd id: {0}", directDebit.getId());

                reSingTrials += 1;
                Integer maxReSignTrials =
                        Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_MAX_RE_SIGN_TRIALS));
                logger.log(Level.SEVERE, "DirectDebitARejectionWaitingBankResponseStep maxReSignTrials: " + maxReSignTrials);
                
                if (maxReSignTrials == 0) {
                    scheduleForTermination = true;
                } /*else if (maxReSignTrials == 1) {
                    createExpertTodo = true;
                    reasonToCall = "Client signature is rejected from bank side for " + maxReSignTrials + " time";
                }*/
                
                if (!shouldGenerateNewDdUsingOldSignatures(reSingTrials, directDebit.getContractPaymentTerm())) {
                    startStep = DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE;
                    
                    leadingRejectionFlow = !existOtherWaitingClientSignatureFlow(
                            directDebit.getContractPaymentTerm().getContract(), Arrays.asList(directDebit.getId()));
                } else {
                    // client has old approved dds so we can use it's signatures
                    // generate new dd from current contract payment term
                    
                    List<ContractPayment> contractPayment = new ArrayList<>();
                    for (ContractPayment payment : directDebit.getContractPayments()) {
                        payment.setDirectDebit(null);
                        contractPayment.add(payment);
                    }
                    
                    newGeneratedDirectDebits = directDebitController.generateDD(contractPayment,
                            null, directDebit.getContractPaymentTerm(), true, false,
                            true, false, false, false, false);
                    
                    for (DirectDebit dd : newGeneratedDirectDebits) {
                        mergePendingDataEntryDDsIntoOneToDo(dd);
                    }
                    
                    dontSendDDMessage = true;
                    startStep = DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE;
                }
                break;
            
            /*case Account:
            case EID:
                directDebit.setMStatus(DirectDebitStatus.PENDING_DATA_ENTRY);
                directDebit.setConfirmedBankInfo(false);
                sendDDFsBackToAccountant(directDebit);
                startStep = DirectDebitRejectionToDoType.WAITING_ACCOUNTANT_ACTION;
                dontSendDDMessage = true;
                break;*/
            case Account:
            case EID:
            case Authorization:
            case Invalid_Account:
                startStep = DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE;
                leadingRejectionFlow = !existOtherWaitingClientSignatureFlow(
                    directDebit.getContractPaymentTerm().getContract(), Arrays.asList(directDebit.getId()));
                trials++;
                reminder = 0;
                
                /*if (trials == (maxTrials - 1)) {
                    //createExpertTodo = true;
                    reasonToCall = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_AUTH_REJECT_EXPERT_TODO_REASON_TO_CALL);
                    initialNotes = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_AUTH_REJECT_EXPERT_TODO_INITIAL_NOTES);
                }*/
                if (trials > maxTrials) {
                    scheduleForTermination = true;
                }
                break;
            
            /*case Invalid_Account:
                startStep = DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE;
                leadingRejectionFlow = !existOtherWaitingClientSignatureFlow(
                        directDebit.getContractPaymentTerm().getContract(), Arrays.asList(directDebit.getId()));
                trials++;
                reminder = 0;
                if (trials > maxTrials) {
                    scheduleForTermination = true;
                }
                break;*/
            default:
                break;
        }

        if (startStep == null) {
            logger.log(Level.SEVERE, "DirectDebitRejectionFlowService startRejectionFlow A unknown step when trying to create rejection todo: " + directDebit.getId());
            return null;
        }

        DirectDebitRejectionToDo directDebitRejectionToDo = new DirectDebitRejectionToDo(startStep.toString());
        directDebitRejectionToDo.setDdCategory(directDebit.getCategory());
        directDebitRejectionToDo.setDontSendDdMessage(dontSendDDMessage);
        directDebitRejectionToDo.setLeadingRejectionFlow(leadingRejectionFlow);
        directDebitRejectionToDo.setDdAddedByOecFlow(directDebit.isAddedByOecFlow());
        directDebitRejectionToDo.setLastRejectCategory(directDebit.getRejectCategory());
        directDebitRejectionToDo.setTrials(trials);
        directDebitRejectionToDo.setReSignTrials(reSingTrials);
        directDebitRejectionToDo.setReminder(reminder);
        directDebitRejectionToDo.setLastDirectDebit(directDebit);
        
        if (startStep == DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE) {
            directDebitRejectionToDo.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
        }

        if (scheduleForTermination) {
            Contract contract = directDebit.getContractPaymentTerm().getContract();
            
            if (contract.isTerminateContractDueRejection() || !directDebitRejectionToDo.isDdAddedByOecFlow()) {
                logger.info("contract.terminateContractDueRejection: " + contract.isTerminateContractDueRejection() +
                        "; entity.isDdAddedByOecFlow: " + directDebitRejectionToDo.isDdAddedByOecFlow());

                directDebitRejectionToDo.setContractScheduleDateOfTermination(
                        setContractForTermination(directDebit.getContractPaymentTerm(),
                                "direct_debit_rejection_type_a_maxbankinfotrials_reached",
                                directDebitRejectionToDo));
                directDebitRejectionToDo.setLeadingRejectionFlow(true);
            }

            directDebitRejectionToDo.setStopped(true);
        }
        
        /*if (createExpertTodo) {
            directDebitRejectionToDo.setVoiceResolverTodoId(createExpertTodo(directDebit, reasonToCall, initialNotes));
            directDebitRejectionToDo.setLastTrialDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
        }*/

        logger.log(Level.SEVERE, "DirectDebitRejectionFlowService startRejectionFlow A, directDebitRejectionToDo id: " + directDebitRejectionToDo.getId());
        directDebitRejectionToDoRepository.save(directDebitRejectionToDo);

        logger.log(Level.SEVERE, "DirectDebitRejectionFlowService startRejectionFlow A, directDebitRejectionToDo id: " + directDebitRejectionToDo.getId());
        logger.log(Level.SEVERE, "DirectDebitRejectionFlowService startRejectionFlow A, directDebit id: " + directDebit.getId());
        
        directDebit = directDebitRepository.findOne(directDebit.getId());
        directDebit.setDirectDebitRejectionToDo(directDebitRejectionToDo);
        // put the reject_todo id  on the new generated dds
        
        for (DirectDebit d : newGeneratedDirectDebits) {
            d = directDebitRepository.findOne(d.getId());
            d.setImageForDD(directDebit.getImageForDD());
            d.setDirectDebitRejectionToDo(directDebitRejectionToDo);
            d.setIsSigned(false);
            directDebitRepository.save(d);
        }
        
        directDebitRepository.save(directDebit);
        logger.log(Level.SEVERE, "DirectDebitRejectionFlowService startRejectionFlow A, after save directDebit id: " + directDebit.getId());

        try {
            logger.log(Level.SEVERE, "DirectDebitRejectionFlowService startRejectionFlow A, after save directDebit getDirectDebitRejectionToDo: " + directDebit.getDirectDebitRejectionToDo().getId());
        } catch (Exception e) {

        }
        logger.log(Level.SEVERE, "DirectDebitRejectionFlowService startRejectionFlow A, startStep: " + startStep);
        
        if (startStep == DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE) {
            DirectDebit newDD = directDebit.clone(DirectDebitStatus.IN_COMPLETE);
            newDD.setNonCompletedInfo(true);
            newDD.setConfirmedBankInfo(false);
            newDD.setImageForDD(directDebit.getImageForDD());
            newDD.setDirectDebitRejectionToDo(directDebitRejectionToDo);
            newDD.setAttachments(new ArrayList());
            directDebitRepository.save(newDD);
        }

        return directDebitRejectionToDo;
    }

    private DirectDebitRejectionToDo startTypeBFlow(DirectDebit directDebit) {
        DirectDebitRejectionToDoType startStep = null;
        int manualDDBTrials = 0;
        int manualDDBTrialsPatch = 0;
        int autoDDBTrials = 0;
        int autoDDBTrialsPatch = 0;
        logger.log(Level.INFO, "directDebit id : {0}", directDebit.getId());

        boolean allManualRejected = this.allManualFilesRejected(directDebit.getDirectDebitFiles());
        boolean allAutoRejected = this.allAutoFilesRejected(directDebit.getDirectDebitFiles());

        if (directDebit.getStatus() == DirectDebitStatus.CONFIRMED && allManualRejected
                && directDebit.isGenerateManualDDFs()
                && directDebit.getDdConfiguration().isIncludeManualInDDBFlow()) {

            List<DirectDebitFile> manualDDs = directDebit.getDirectDebitFiles().stream()
                    .filter(ddf -> ddf.getDdMethod() == DirectDebitMethod.MANUAL
                            && ddf.getTrialNumber() == 0)
                    .limit(Math.min(
                            directDebit.getDdConfiguration().getNumberOfGeneratedDDs(),
                            Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                                    AccountingModule.PARAMETER_NUMBER_OF_DIRECT_DEBIT_SIGNATURES))))
                    .collect(Collectors.toList());
            logger.log(Level.INFO, "manualDDs size : {0}", manualDDs.size());

            for (DirectDebitFile manualDD : manualDDs) {
                directDebitFileRepository.save(manualDD.clone(1));
            }

            manualDDBTrials += 1;
            manualDDBTrialsPatch += 1;
            startStep = DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B;

            DirectDebitRejectionToDo directDebitRejectionToDo = new DirectDebitRejectionToDo(startStep.toString());
            directDebitRejectionToDo.setDdCategory(directDebit.getCategory());
            directDebitRejectionToDo.setLastRejectCategory(directDebit.getRejectCategory());
            directDebitRejectionToDo.setManualDDBTrials(manualDDBTrials);
            directDebitRejectionToDo.setManualDDBTrialsPatch(manualDDBTrialsPatch);
            directDebitRejectionToDo.setLastDirectDebit(directDebit);
            directDebitRejectionToDo.setDdAddedByOecFlow(directDebit.isAddedByOecFlow());

            directDebitRejectionToDoRepository.save(directDebitRejectionToDo);
            directDebit = directDebitRepository.findOne(directDebit.getId());
            directDebit.setDirectDebitRejectionToDo(directDebitRejectionToDo);
            directDebitRepository.save(directDebit);

            return directDebitRejectionToDo;

        } else if (allAutoRejected && directDebit.getMStatus() == DirectDebitStatus.CONFIRMED) {
            List<DirectDebitFile> autoDDs = directDebit.getDirectDebitFiles().stream()
                    .filter(ddf -> ddf.getDdMethod() == DirectDebitMethod.AUTOMATIC &&
                            ddf.getTrialNumber() == 0)
                    .limit(Math.min(
                            directDebit.getDdConfiguration().getNumberOfGeneratedDDs(),
                            Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                                    AccountingModule.PARAMETER_NUMBER_OF_DIRECT_DEBIT_SIGNATURES))))
                    .collect(Collectors.toList());
            logger.log(Level.INFO, "autoDDs size : {0}", autoDDs.size());

            for (DirectDebitFile autoDD : autoDDs) {
                directDebitFileRepository.save(autoDD.clone(1));
            }
            autoDDBTrials += 1;
            autoDDBTrialsPatch += 1;
            startStep = DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B;

            DirectDebitRejectionToDo directDebitRejectionToDo = new DirectDebitRejectionToDo(startStep.toString());
            directDebitRejectionToDo.setDdCategory(directDebit.getCategory());
            directDebitRejectionToDo.setLastRejectCategory(directDebit.getRejectCategory());
            directDebitRejectionToDo.setAutoDDBTrials(autoDDBTrials);
            directDebitRejectionToDo.setAutoDDBTrialsPatch(autoDDBTrialsPatch);
            directDebitRejectionToDo.setLastDirectDebit(directDebit);
            directDebitRejectionToDo.setDdAddedByOecFlow(directDebit.isAddedByOecFlow());

            directDebitRejectionToDoRepository.save(directDebitRejectionToDo);
            directDebit = directDebitRepository.findOne(directDebit.getId());
            directDebit.setDirectDebitRejectionToDo(directDebitRejectionToDo);
            directDebitRepository.save(directDebit);

            return directDebitRejectionToDo;

        } else if ((allAutoRejected && allManualRejected)
                || (allAutoRejected && !directDebit.isGenerateManualDDFsFromConfig())){
            logger.log(Level.INFO, "allAutoRejected : {0}; allManualRejected : {1}; directDebit.isGenerateManualDDFsFromConfig() : {2}"
                    , new Object[]{allAutoRejected, allManualRejected,directDebit.isGenerateManualDDFsFromConfig() });

            return startTypeBCaseDFlow(directDebit);
        }
        return null;
    }

    private DirectDebitRejectionToDo startTypeBCaseDFlow(DirectDebit directDebit) {
        SwitchingNationalityService switchingNationalityService = Setup.getApplicationContext().getBean(SwitchingNationalityService.class);
        SwitchingBankAccountService switchingBankAccountService = Setup.getApplicationContext().getBean(SwitchingBankAccountService.class);

        SwitchingType switchingType = switchingNationalityService.relatesToSwitching(directDebit.getId(), false) ?
                SwitchingType.NATIONALITY :
                switchingBankAccountService.isClientSwitchingBankAccount(directDebit) ? SwitchingType.BANK_ACCOUNT : null;

        if (switchingType != null) {
            logger.log(Level.SEVERE, "directDebit id: " + directDebit.getId());
            try {
                return startTypeBCaseDFlowSwitching(directDebit, switchingType);
            } catch (Exception e) {
                e.printStackTrace();
                throw new RuntimeException(e.getMessage());
            }
        }

        DirectDebitRejectionToDoType startStep = null;
        logger.log(Level.SEVERE, "directDebit id: " + directDebit.getId());
        logger.log(Level.SEVERE, "directDebit reject cat: " + directDebit.getRejectCategory());

        int trials = 0;
        int reSingTrials = 0;
        int reminder = 0;

        /*boolean createExpertTodo = false;
        String reasonToCall = "";
        String initialNotes = "";*/
        boolean scheduleForTermination = false;
        boolean dontSendDDMessage = false;
        boolean leadingRejectionFlow = false;
        Integer maxTrials = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_DD_MAX_TRIALS));
        
        DirectDebitConfiguration ddConfiguration = directDebit.getDdConfiguration();
        List<DirectDebit> newGeneratedDirectDebits = new ArrayList<>();

        if (null != directDebit.getRejectCategory()) switch (directDebit.getRejectCategory()) {
            case Compliance:
            case Other:
                DirectDebit newDD = directDebit.clone(DirectDebitStatus.PENDING);
                newDD.setGenerateManualDDFs(directDebit.isGenerateManualDDFsFromConfig() && ddConfiguration.isIncludeManualInDDBFlow());
                directDebitRepository.save(newDD);
                
                newGeneratedDirectDebits.add(newDD);

                directDebit.cloneChildDds(newDD, 1);
                startStep = DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B_CASE_D;
                break;
            
            case Signature:
                logger.log(Level.SEVERE, "startTypeBCaseDFlow dd id: {0}", directDebit.getId());

                reSingTrials = 1;
                Integer maxReSignTrials = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_DD_MAX_RE_SIGN_TRIALS));
                
                logger.log(Level.SEVERE, "startTypeBCaseDFlow maxReSignTrials: " + maxReSignTrials);
                Boolean leading = !existOtherWaitingClientSignatureFlow(
                        directDebit.getContractPaymentTerm().getContract(), Arrays.asList(directDebit.getId()));
                
                if (maxReSignTrials == 0) {
                    scheduleForTermination = true;
                } /*else if (maxReSignTrials == 1 && leading) {
                     createExpertTodo = true;
                     reasonToCall = "Client signature is rejected from bank side for " + maxReSignTrials + " time";
                }*/
                if (!shouldGenerateNewDdUsingOldSignatures(reSingTrials, directDebit.getContractPaymentTerm())) {
                    startStep = DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_CASE_D;    
                    leadingRejectionFlow = leading;
                } else { // client has old approved dds so we can use it's signatures
                    startStep = DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B_CASE_D;
                    dontSendDDMessage = true;
                    
                    List<ContractPayment> contractPayment = new ArrayList<>();
                    for (ContractPayment payment : directDebit.getContractPayments()) {
                        payment.setDirectDebit(null);
                        payment.setGenerateManualDDFs(directDebit.isGenerateManualDDFsFromConfig() && ddConfiguration.isIncludeManualInDDBFlow());
                        contractPayment.add(payment);
                    }
                    
                    newGeneratedDirectDebits = directDebitController.generateDD(contractPayment,
                            null, directDebit.getContractPaymentTerm(), true, false,
                            true, false, false, false, false);
                    
                    for (DirectDebit dd : newGeneratedDirectDebits) {
                        mergePendingDataEntryDDsIntoOneToDo(dd);
                    }
                }   
                break;
            
            /*case Account:
            case EID:
                if (directDebit.isGenerateManualDDFsFromConfig() && ddConfiguration.isIncludeManualInDDBFlow())
                    directDebit.setMStatus(DirectDebitStatus.PENDING_DATA_ENTRY);
                else
                    directDebit.setMStatus(DirectDebitStatus.REJECTED);
                
                directDebit.setStatus(DirectDebitStatus.PENDING_DATA_ENTRY);
                directDebit.setAutoDdfFile(null);
                directDebit.setManualDdfFile(null);
                directDebit.setConfirmedBankInfo(false);
                
                sendDDFsBackToAccountant(directDebit);
                startStep = DirectDebitRejectionToDoType.WAITING_ACCOUNTANT_ACTION_B_CASE_D;
                dontSendDDMessage = true;
                break;*/
            case Account:
            case EID:
            case Authorization:
            case Invalid_Account:
                startStep = DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_CASE_D;
                leadingRejectionFlow = !existOtherWaitingClientSignatureFlow(
                    directDebit.getContractPaymentTerm().getContract(), Arrays.asList(directDebit.getId()));
                trials++;
                reminder = 0;
                /*if (trials == (maxTrials - 1)) {
                    createExpertTodo = true;
                    reasonToCall = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_AUTH_REJECT_EXPERT_TODO_REASON_TO_CALL);
                    initialNotes = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_AUTH_REJECT_EXPERT_TODO_INITIAL_NOTES);
                }*/
                if (trials > maxTrials) {
                    scheduleForTermination = true;
                }
                break;
            
            /*case Invalid_Account:
                startStep = DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_CASE_D;
                leadingRejectionFlow = !existOtherWaitingClientSignatureFlow(
                        directDebit.getContractPaymentTerm().getContract(), Arrays.asList(directDebit.getId()));
                trials++;
                reminder = 0;
                
                if (trials > maxTrials) {
                    scheduleForTermination = true;
                }
                break;*/
            
            default:
                break;
        }

        if (startStep == null) {
            logger.log(Level.SEVERE, "DirectDebitRejectionFlowService startRejectionFlow B case D unknown step when trying to create rejection todo: " + directDebit.getId());
            return null;
        }

        DirectDebitRejectionToDo directDebitRejectionToDo = new DirectDebitRejectionToDo(startStep.toString());
        directDebitRejectionToDo.setDdCategory(directDebit.getCategory());

        directDebitRejectionToDo.setDontSendDdMessage(dontSendDDMessage);
        directDebitRejectionToDo.setLeadingRejectionFlow(leadingRejectionFlow);
        directDebitRejectionToDo.setDdAddedByOecFlow(directDebit.isAddedByOecFlow());
        directDebitRejectionToDo.setLastRejectCategory(directDebit.getRejectCategory());
        directDebitRejectionToDo.setTrials(trials);
        directDebitRejectionToDo.setReminder(reminder);
        directDebitRejectionToDo.setReSignTrials(reSingTrials);
        directDebitRejectionToDo.setLastDirectDebit(directDebit);
        
        if (startStep == DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_CASE_D) {
            directDebitRejectionToDo.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
        }
        
        if (scheduleForTermination) {
            Contract contract = directDebit.getContractPaymentTerm().getContract();
            
            if (contract.isTerminateContractDueRejection() || !directDebitRejectionToDo.isDdAddedByOecFlow()) {
                logger.info("contract.terminateContractDueRejection: " + contract.isTerminateContractDueRejection() +
                        "; entity.isDdAddedByOecFlow: " + directDebitRejectionToDo.isDdAddedByOecFlow());

                directDebitRejectionToDo.setContractScheduleDateOfTermination(
                        setContractForTermination(directDebit.getContractPaymentTerm(),
                                "direct_debit_rejection_type_a_maxbankinfotrials_reached",
                                directDebitRejectionToDo));
                directDebitRejectionToDo.setLeadingRejectionFlow(true);
            }

            directDebitRejectionToDo.setStopped(true);
        }
        
        /*if (createExpertTodo) {
            directDebitRejectionToDo.setVoiceResolverTodoId(createExpertTodo(directDebit, reasonToCall, initialNotes));
            directDebitRejectionToDo.setLastTrialDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
        }*/
        directDebitRejectionToDoRepository.save(directDebitRejectionToDo);

        for (DirectDebit d : newGeneratedDirectDebits) {
            d = directDebitRepository.findOne(d.getId());
            d.setImageForDD(directDebit.getImageForDD());
            d.setDirectDebitRejectionToDo(directDebitRejectionToDo);
            d.setIsSigned(false);
            directDebitRepository.save(d);
        }
        
        directDebit = directDebitRepository.findOne(directDebit.getId());
        directDebit.setDirectDebitRejectionToDo(directDebitRejectionToDo);
        directDebitRepository.save(directDebit);

        if (startStep == DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_CASE_D) {
            DirectDebit newDD = directDebit.clone(DirectDebitStatus.IN_COMPLETE);
            newDD.setConfirmedBankInfo(false);
            newDD.setNonCompletedInfo(true);
            newDD.setImageForDD(directDebit.getImageForDD());
            newDD.setDirectDebitRejectionToDo(directDebitRejectionToDo);
            newDD.setAttachments(new ArrayList<>());
            newDD.setGenerateManualDDFs(directDebit.isGenerateManualDDFsFromConfig() && ddConfiguration.isIncludeManualInDDBFlow());
            directDebitRepository.save(newDD);
        }

        return directDebitRejectionToDo;
    }

    private DirectDebitRejectionToDo startTypeBCaseDFlowSwitching(DirectDebit directDebit, SwitchingType switchingType) {
        DirectDebitRejectionToDoType startStep = null;

        logger.log(Level.SEVERE, "DirectDebitRejectionFlowService startTypeBCaseDFlowSwitching directDebit id: " + directDebit.getId());
        logger.log(Level.SEVERE, "DirectDebitRejectionFlowService startTypeBCaseDFlowSwitching directDebit reject cat: " + directDebit.getRejectCategory());

        int trials = 0;
        int reSingTrials = 0;
        int reminder = 0;

        /*boolean createExpertTodo = false;
        String reasonToCall = "";
        String initialNotes = "";*/
        boolean scheduleForTermination = false;
        boolean dontSendDDMessage = false;
        boolean leadingRejectionFlow = false;
        Integer maxTrials = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), 
                AccountingModule.PARAMETER_DD_MAX_TRIALS));
        List<DirectDebit> newGeneratedDirectDebits = new ArrayList<>();

        if (null != directDebit.getRejectCategory()) switch (directDebit.getRejectCategory()) {
            case Compliance:
            case Other:{
                DirectDebit newDD = getSwitchingNewDD(switchingType, directDebit, true);
                newGeneratedDirectDebits.add(newDD);
                startStep = DDUtils.getDDRejectionToDoNextStep(DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B_CASE_D, newDD.getCategory());
                break;
            }
            
            case Signature:

                logger.log(Level.SEVERE, "startTypeBCaseDFlow id: {0}", directDebit.getId());

                Boolean leading = !existOtherWaitingClientSignatureFlow(
                        directDebit.getContractPaymentTerm().getContract(), Arrays.asList(directDebit.getId()));
                reSingTrials = 1;
                Integer maxReSignTrials = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), 
                        AccountingModule.PARAMETER_DD_MAX_RE_SIGN_TRIALS));
                logger.log(Level.SEVERE, "startTypeBCaseDFlow switching maxReSignTrials: " + maxReSignTrials);
                
                if (maxReSignTrials == 0) {
                    scheduleForTermination = true;
                } /*else if (maxReSignTrials == 1 && leading) {
                    createExpertTodo = true;
                    reasonToCall = "Client signature is rejected from bank side for " + maxReSignTrials + " time";
                }*/
                
                if (!shouldGenerateNewDdUsingOldSignatures(reSingTrials, directDebit.getContractPaymentTerm())) {
                    startStep = DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE;
                    leadingRejectionFlow = leading;    
                } else { // client has old approved dds so we can use it's signatures
                    dontSendDDMessage = true;
                    
                    DirectDebit newDD = getSwitchingNewDD(switchingType, directDebit, true);
                    mergePendingDataEntryDDsIntoOneToDo(newDD);
                    newGeneratedDirectDebits.add(newDD);
                    
                    startStep = DDUtils.getDDRejectionToDoNextStep(DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B_CASE_D, newDD.getCategory());
                }   
                break;
            
            /*
            case Account:
            case EID:{
                // make master dd rejected since we didn't reject it from BR
                directDebit = directDebitRepository.findOne(directDebit.getId());
                directDebit.setMStatus(DirectDebitStatus.REJECTED);
                directDebit.setStatus(DirectDebitStatus.REJECTED);
                directDebit = directDebitRepository.save(directDebit);
                
                DirectDebit newDD = getSwitchingNewDD(switchingType, directDebit, true);
                newDD.setMStatus(DirectDebitStatus.PENDING_DATA_ENTRY);
                newDD.setStatus(DirectDebitStatus.PENDING_DATA_ENTRY);
                newDD.setAutoDdfFile(null);
                newDD.setManualDdfFile(null);
                newDD.setConfirmedBankInfo(false);
                newDD = directDebitRepository.save(newDD);
                
                newGeneratedDirectDebits.add(newDD);
                
                sendDDFsBackToAccountant(newDD, false);
                startStep = DDUtils.getDDRejectionToDoNextStep(DirectDebitRejectionToDoType.WAITING_ACCOUNTANT_ACTION, newDD.getCategory());
                dontSendDDMessage = true;
                break;
            }*/
            case Account:
            case EID:
            case Authorization:
            case Invalid_Account:
                startStep = DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE;
                leadingRejectionFlow = !existOtherWaitingClientSignatureFlow(
                    directDebit.getContractPaymentTerm().getContract(), Arrays.asList(directDebit.getId()));
                trials++;
                reminder = 0;
                
                /*if (trials == (maxTrials - 1)) {
                    createExpertTodo = true;
                    reasonToCall = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_AUTH_REJECT_EXPERT_TODO_REASON_TO_CALL);
                    initialNotes = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_AUTH_REJECT_EXPERT_TODO_INITIAL_NOTES);
                }*/
                if (trials > maxTrials) {
                    scheduleForTermination = true;
                }
                break;
            
            /*case Invalid_Account:
                startStep = DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE;
                leadingRejectionFlow = !existOtherWaitingClientSignatureFlow(
                        directDebit.getContractPaymentTerm().getContract(), Arrays.asList(directDebit.getId()));
                trials++;
                reminder = 0;
                
                if (trials > maxTrials) {
                    scheduleForTermination = true;
                }
                break;*/
            
            default:
                break;
        }

        if (startStep == null) {
            logger.log(Level.SEVERE, "DirectDebitRejectionFlowService startRejectionFlow B case D switching unknown step "
                    + "when trying to create rejection todo: " + directDebit.getId());
            return null;
        }

        if (startStep == DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE) {
            DirectDebit newDD = getSwitchingNewDD(switchingType, directDebit, false);
            newDD.setStatus(DirectDebitStatus.IN_COMPLETE);
            newDD.setMStatus(DirectDebitStatus.IN_COMPLETE);
            newDD.setNonCompletedInfo(true);
            newDD.setConfirmedBankInfo(false);
            newDD.setAttachments(new ArrayList());
            newGeneratedDirectDebits.add(directDebitRepository.save(newDD));

            startStep = DDUtils.getDDRejectionToDoNextStep(
                    DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE, newDD.getCategory());
        }

        DirectDebitRejectionToDo directDebitRejectionToDo = new DirectDebitRejectionToDo(startStep.toString());
        directDebitRejectionToDo.setDdCategory(directDebit.getCategory());
        directDebitRejectionToDo.setDontSendDdMessage(dontSendDDMessage);
        directDebitRejectionToDo.setLeadingRejectionFlow(leadingRejectionFlow);
        directDebitRejectionToDo.setDdAddedByOecFlow(directDebit.isAddedByOecFlow());
        directDebitRejectionToDo.setLastRejectCategory(directDebit.getRejectCategory());
        directDebitRejectionToDo.setTrials(trials);
        directDebitRejectionToDo.setReminder(reminder);
        directDebitRejectionToDo.setReSignTrials(reSingTrials);
        directDebitRejectionToDo.setLastDirectDebit(directDebit);
        directDebitRejectionToDo.setDdCategory(DirectDebitCategory.A); // turn it to a since we are generating dda in this method
        
        if (startStep == DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE) {
            directDebitRejectionToDo.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
        }

        if (scheduleForTermination) {
            Contract contract = directDebit.getContractPaymentTerm().getContract();
            if (contract.isTerminateContractDueRejection() || !directDebitRejectionToDo.isDdAddedByOecFlow()) {
                logger.info("contract.terminateContractDueRejection: " + contract.isTerminateContractDueRejection() +
                        "; entity.isDdAddedByOecFlow: " + directDebitRejectionToDo.isDdAddedByOecFlow());

                directDebitRejectionToDo.setContractScheduleDateOfTermination(
                        setContractForTermination(directDebit.getContractPaymentTerm(),
                                "direct_debit_rejection_type_a_maxbankinfotrials_reached",
                                directDebitRejectionToDo));
                directDebitRejectionToDo.setLeadingRejectionFlow(true);
            }

            directDebitRejectionToDo.setStopped(true);
        }

        /*if (createExpertTodo) {
            directDebitRejectionToDo.setVoiceResolverTodoId(createExpertTodo(directDebit, reasonToCall, initialNotes));
            directDebitRejectionToDo.setLastTrialDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
        }*/

        directDebitRejectionToDoRepository.save(directDebitRejectionToDo);

        for (DirectDebit d : newGeneratedDirectDebits) {
            d = directDebitRepository.findOne(d.getId());
            d.setDirectDebitRejectionToDo(directDebitRejectionToDo);
            d.setIsSigned(false);
            d.setImageForDD(directDebit.getImageForDD());
            directDebitRepository.save(d);
        }

        directDebit = directDebitRepository.findOne(directDebit.getId());
        directDebit.setDirectDebitRejectionToDo(directDebitRejectionToDo);
        directDebitRepository.save(directDebit);

        return directDebitRejectionToDo;

    }

    @Transactional
    public void startTypeBBouncedFlow(DirectDebit directDebit) {
        SelectQuery<Payment> query = new SelectQuery<>(Payment.class);
        query.filterBy("directDebitId", "=", directDebit.getId());
        query.filterBy("replaced", "=", false);
        query.filterBy("status", "=", PaymentStatus.BOUNCED);
        query.filterBy("bouncedFlowPausedForReplacement", "=", true);
        query.sortBy("lastModificationDate", false);
        query.setLimit(1);

        List<Payment> payments = query.execute();

        if (!payments.isEmpty()) {
            logger.log(Level.SEVERE, "DirectDebitBBouncedRejectionWaitingBankResponseStep directDebitRejectionToDo is on direct debit that has paused payment for bouncing flow");
            logger.log(Level.SEVERE, "DirectDebitBBouncedRejectionWaitingBankResponseStep directDebit id:" + directDebit.getId());
            logger.log(Level.SEVERE, "DirectDebitBBouncedRejectionWaitingBankResponseStep paused payment id:" + payments.get(0).getId());

            createRejectionTodoWithPauseStepForBouncing(directDebit);
            return;
        }

        DirectDebitRejectionToDoType startStep = null;
        int trials = 0;
        int reSingTrials = 0;
        int reminder = 0;

        /*boolean createExpertTodo = false;
        String reasonToCall = "";
        String initialNotes = "";*/
        boolean scheduleForTermination = false;
        boolean dontSendDDMessage = false;
        boolean leadingRejectionFlow = false;
        
        Integer maxTrials = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), 
                AccountingModule.PARAMETER_DD_MAX_TRIALS));

        logger.log(Level.SEVERE, "DirectDebitFileBusinessRule payments startTypeBBouncedFlow dd id:" + directDebit.getId());
        logger.log(Level.SEVERE, "DirectDebitFileBusinessRule payments startTypeBBouncedFlow dd getBouncingRejectCategory:" + directDebit.getBouncingRejectCategory());

        List<DirectDebitSignature> signatures;
        
        if (null != directDebit.getBouncingRejectCategory()) switch (directDebit.getBouncingRejectCategory()) {
            case Compliance:
            case Other:
                Map<String, Object> signatureType = directDebitSignatureService
                        .getLastSignatureType(directDebit.getContractPaymentTerm(), true, false);
                
                signatures = (List<DirectDebitSignature>) signatureType.get("currentSignatures");
                logger.log(Level.SEVERE, "DirectDebitFileBusinessRule payments startTypeBBouncedFlow approvedDDFSignature:"
                        + (signatures != null ? signatures.size() : 0));
                try {
                    Map<String, Object> map = new HashMap<>();
                    map.put("signatures", signatures);
                    bouncingFlowService.createManualDDsForBouncedFlow(directDebit, map);
//                bouncingFlowService.increaseDDPaymentsTrials(directDebit);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RuntimeException(e.getMessage());
                }
                startStep = DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B_BOUNCED;
                break;
            
            case Signature:

                logger.log(Level.SEVERE, "payments startTypeBBouncedFlow id: {0}", directDebit.getId());

                Boolean leading = !existOtherWaitingClientSignatureFlow(
                        directDebit.getContractPaymentTerm().getContract(), Arrays.asList(directDebit.getId()));

                reSingTrials += 1;
                Integer maxReSignTrials =
                        Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                                AccountingModule.PARAMETER_DD_MAX_RE_SIGN_TRIALS));
                logger.log(Level.SEVERE, "startTypeBBouncedFlow maxReSignTrials: " + maxReSignTrials);
                
                if (maxReSignTrials == 0) {
                    scheduleForTermination = true;
                } /*else if (maxReSignTrials == 1 && leading) {
                    createExpertTodo = true;
                    reasonToCall = "Client signature is rejected from bank side for " + maxReSignTrials + " time";
                }*/
                Map<String, Object> map = new HashMap<>();
                if (!shouldGenerateNewDdUsingOldSignatures(reSingTrials, directDebit.getContractPaymentTerm(), map)) {
                    startStep = DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_BOUNCED;    
                    leadingRejectionFlow = leading;
                } else { // client has old approved dds so we can use it's signatures
                    startStep = DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B_BOUNCED;
                    dontSendDDMessage = true;
                    

                    logger.log(Level.SEVERE, "payments startTypeBBouncedFlow create manual dd");
                    try {
                        bouncingFlowService.createManualDDsForBouncedFlow(directDebit, map);
//                    bouncingFlowService.increaseDDPaymentsTrials(directDebit);
                        mergePendingDataEntryDDsIntoOneToDo(directDebit);
                    } catch (Exception e) {
                        e.printStackTrace();
                        throw new RuntimeException(e.getMessage());
                    }
                    
                }   
                break;
            
            /*case Account:
            case EID:
                directDebit.setMStatus(DirectDebitStatus.PENDING_DATA_ENTRY);
                directDebit.setManualDdfFile(null);
                directDebit.setConfirmedBankInfo(false);
                
                List<DirectDebitFile> manualsForBouncing = directDebit.getDirectDebitFiles()
                        .stream()
                        .filter(ddf -> ddf.getDdMethod() == DirectDebitMethod.MANUAL && ddf.getDdStatus().equals(DirectDebitStatus.REJECTED)
                                && ddf.getForBouncingPayment() != null && ddf.getForBouncingPayment())
                        .collect(Collectors.toList());
                
                sendDDFsBackToAccountant(manualsForBouncing);
                dontSendDDMessage = true;
                startStep = DirectDebitRejectionToDoType.WAITING_ACCOUNTANT_ACTION_B_BOUNCED;
                break;*/
            case Account:
            case EID:
            case Authorization:
            case Invalid_Account:
                startStep = DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_BOUNCED;
                leadingRejectionFlow = !existOtherWaitingClientSignatureFlow(
                    directDebit.getContractPaymentTerm().getContract(), Arrays.asList(directDebit.getId()));
                trials++;
                reminder = 0;
                
                /*if (trials == (maxTrials - 1)) {
                    createExpertTodo = true;
                    reasonToCall = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_AUTH_REJECT_EXPERT_TODO_REASON_TO_CALL);
                    initialNotes = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_AUTH_REJECT_EXPERT_TODO_INITIAL_NOTES);
                }*/
                
                if (trials > maxTrials) {
                    scheduleForTermination = true;
                }
                break;
            
            /*case Invalid_Account:
                startStep = DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_BOUNCED;
                leadingRejectionFlow = !existOtherWaitingClientSignatureFlow(
                        directDebit.getContractPaymentTerm().getContract(), Arrays.asList(directDebit.getId()));
                trials++;
                reminder = 0;
                
                if (trials > maxTrials) {
                    scheduleForTermination = true;
                }
                break;*/
            
            default:
                break;
        }

        if (startStep == null) {
            logger.log(Level.SEVERE, "DirectDebitRejectionFlowService startRejectionFlow B bounced unknown step when trying to create rejection todo: " + directDebit.getId());
            return;
        }

        DirectDebitRejectionToDo directDebitRejectionToDo = new DirectDebitRejectionToDo(startStep.toString());
        directDebitRejectionToDo.setDdCategory(directDebit.getCategory());
        directDebitRejectionToDo.setDontSendDdMessage(dontSendDDMessage);
        directDebitRejectionToDo.setLeadingRejectionFlow(leadingRejectionFlow);
        directDebitRejectionToDo.setDdAddedByOecFlow(directDebit.isAddedByOecFlow());
        directDebitRejectionToDo.setLastRejectCategory(directDebit.getBouncingRejectCategory());
        directDebitRejectionToDo.setReminder(reminder);
        directDebitRejectionToDo.setTrials(trials);
        directDebitRejectionToDo.setReSignTrials(reSingTrials);
        directDebitRejectionToDo.setLastDirectDebit(directDebit);
        
        if (startStep == DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_BOUNCED) {
            directDebitRejectionToDo.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
        }

        if (scheduleForTermination) {
            Contract contract = directDebit.getContractPaymentTerm().getContract();
            
            if (contract.isTerminateContractDueRejection() || !directDebitRejectionToDo.isDdAddedByOecFlow()) {
                logger.info("contract.terminateContractDueRejection: " + contract.isTerminateContractDueRejection() +
                        "; entity.isDdAddedByOecFlow: " + directDebitRejectionToDo.isDdAddedByOecFlow());

                directDebitRejectionToDo.setContractScheduleDateOfTermination(
                        setContractForTermination(directDebit.getContractPaymentTerm(),
                                "direct_debit_rejection_type_a_maxbankinfotrials_reached",
                                directDebitRejectionToDo));
                directDebitRejectionToDo.setLeadingRejectionFlow(true);
            }

            directDebitRejectionToDo.setStopped(true);
        }
        /*if (createExpertTodo) {
            directDebitRejectionToDo.setVoiceResolverTodoId(createExpertTodo(directDebit, reasonToCall, initialNotes));
            directDebitRejectionToDo.setLastTrialDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
        }*/

        directDebitRejectionToDoRepository.save(directDebitRejectionToDo);

        logger.log(Level.SEVERE, "DirectDebitRejectionFlowService directDebit directDebitRejectionToDo after save: " + directDebit.getId());
        if (startStep == DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_BOUNCED) {
            bouncingFlowService.createManualDDsForBouncedFlow(directDebit, new HashMap<>());
//            bouncingFlowService.increaseDDPaymentsTrials(directDebit);
            logger.log(Level.SEVERE, "DirectDebitFileBusinessRule payments isNextStepClientSignature createManualDDs");
        }

        logger.log(Level.SEVERE, "DirectDebitRejectionFlowService directDebitRejectionToDo after save: " + directDebitRejectionToDo.getId());
        directDebit.setDirectDebitBouncingRejectionToDo(directDebitRejectionToDo);

        if (directDebit.getDirectDebitBouncingRejectionToDo() != null) {
            logger.log(Level.SEVERE, "DirectDebitRejectionFlowService directDebit directDebitRejectionToDo after save: " + directDebit.getDirectDebitBouncingRejectionToDo().getId());
        }

        directDebitRepository.save(directDebit);
        logger.log(Level.SEVERE, "DirectDebitRejectionFlowService directDebitRejectionToDo before return: " + directDebitRejectionToDo.getId());
    }

    private void createRejectionTodoWithPauseStepForBouncing(DirectDebit directDebit) {
        DirectDebitRejectionToDo directDebitRejectionToDo = new DirectDebitRejectionToDo(DirectDebitRejectionToDoType.WAITING_FLOW_PAUSE_B_BOUNCED.toString());
        directDebitRejectionToDo.setDdCategory(directDebit.getCategory());
        directDebitRejectionToDo.setLastRejectCategory(directDebit.getBouncingRejectCategory());
        directDebitRejectionToDo.setDdAddedByOecFlow(directDebit.isAddedByOecFlow());

        directDebitRejectionToDoRepository.save(directDebitRejectionToDo);

        directDebit.setDirectDebitBouncingRejectionToDo(directDebitRejectionToDo);
        directDebitRepository.save(directDebit);
    }

    public boolean allAutoFilesRejected(List<DirectDebitFile> directDebitFiles) {
        return allFilesRejected(directDebitFiles, Arrays.asList(DirectDebitMethod.AUTOMATIC));
    }

    public boolean allManualFilesRejected(List<DirectDebitFile> directDebitFiles) {
        return allFilesRejected(directDebitFiles, Arrays.asList(DirectDebitMethod.MANUAL));
    }

    public boolean allFilesRejected(List<DirectDebitFile> directDebitFiles, List<DirectDebitMethod> directDebitMethods) {
        if (directDebitFiles == null || directDebitFiles.isEmpty()) return false;

        boolean allRejected = directDebitFiles.stream().anyMatch(ddf -> directDebitMethods.contains(ddf.getDdMethod()) &&
                ddf.getDdStatus().equals(DirectDebitStatus.REJECTED));

        if (allRejected) {
            DirectDebitCancelationToDoRepository directDebitCancelationToDoRepository = Setup.getRepository(DirectDebitCancelationToDoRepository.class);
            for (DirectDebitFile ddf : directDebitFiles)
                if (directDebitMethods.contains(ddf.getDdMethod()) &&
                        !Arrays.asList(DirectDebitStatus.REJECTED,
                                        DirectDebitStatus.CANCELED,
                                        DirectDebitStatus.PENDING_FOR_CANCELLATION)
                                .contains(ddf.getDdStatus()) &&
                        // check ddf has active cancellationTodo
                        !directDebitCancelationToDoRepository
                                .existsByDirectDebitFileAndCompletedFalseAndStoppedFalse(ddf)) {
                    allRejected = false;
                    break;
                }
        }

        return allRejected;
    }

    public void sendDDFsBackToAccountant(DirectDebit directDebit) {
        if (directDebit == null) return;

        sendDDFsBackToAccountant(directDebit, true);
    }

    public void sendDDFsBackToAccountant(DirectDebit directDebit, boolean generateNewApplicationID) {
        if (directDebit == null) return;

        List<DirectDebitFile> directDebitFiles = directDebit.getDirectDebitFiles() != null ?
                directDebit.getDirectDebitFiles().stream().filter(ddf ->
                        ddf.getDirectDebit().getCategory().equals(DirectDebitCategory.A)
                                || ddf.getDdMethod().equals(DirectDebitMethod.AUTOMATIC)
                                || directDebit.getDdConfiguration().isIncludeManualInDDBFlow())
                        .collect(Collectors.toList()) : null;

        sendDDFsBackToAccountant(directDebitFiles, generateNewApplicationID);
    }

    public void sendDDFsBackToAccountant(List<DirectDebitFile> ddfList) {
        sendDDFsBackToAccountant(ddfList, true);
    }

    public void sendDDFsBackToAccountant(List<DirectDebitFile> ddfList, boolean generateNewApplicationID) {
        if (ddfList == null) return;

        AttachementRepository attachementRepository = Setup.getRepository(AttachementRepository.class);
        int serial = 0;
        for (DirectDebitFile directDebitFile : ddfList) {
            directDebitFile.setStatus(DirectDebitFileStatus.NOT_SENT);
            directDebitFile.setDdStatus(DirectDebitStatus.PENDING_DATA_ENTRY);
            directDebitFile.setConfirmedBankInfo(false);
            directDebitFile.setNeedAccountantReConfirmation(true);

            //Jirra ACC-2724
            if (generateNewApplicationID) {
                directDebitFile.generateNewApplicationIdForExistingDDF(serial);
            }

            directDebitFileRepository.save(directDebitFile);
            directDebitSignatureService.updateSignatureStatus(directDebitFile,
                                            DirectDebitSignatureStatus.UNUSED);
            logger.log(Level.SEVERE, "deleting FILE_TAG_DD_ACTIVATION for: " + directDebitFile.getId());
            Attachment attachment = directDebitFile.getAttachment(DirectDebitFile.FILE_TAG_DD_ACTIVATION);
            if (attachment != null) {
                attachementRepository.delete(attachment);
            }

            serial++;
        }
    }

    /*public Integer createExpertTodo(DirectDebit rejectedDD, String reasonToCall, String initialNotes) {
        Contract contract = rejectedDD.getContractPaymentTerm().getContract();

        if (contract != null && contract.getId() != null) {
            Integer voiceResolverTodoId;
            Map body = new HashMap();
            body.put("reasonToCall", reasonToCall);
            body.put("initialNote", initialNotes);
            body.put("type", VoiceResolverToDoReason.REJECTED_DD_FORMS.toString());

            logger.info("contract ID: " + contract.getId());
            logger.info("reasonToCall: " + reasonToCall);
            logger.info("initialNote: " + initialNotes);

            Map voiceResolverTodo = moduleConnector.postJson("/clientmgmt/voiceResolverToDo/createexperttodo/" + contract.getId(), body, Map.class);
            if (voiceResolverTodo != null && !voiceResolverTodo.isEmpty() && voiceResolverTodo.containsKey("id")) {
                voiceResolverTodoId = (Integer) voiceResolverTodo.get("id");
                return voiceResolverTodoId;
            }
        } else {
            logger.info("contract or it's ID is NULL");
        }

        return null;
    }*/

    public DirectDebit getSwitchingNewDD(SwitchingType switchingType, DirectDebit oldDD, boolean useSignatures) {
        try {
            DirectDebit newDD;
            switch (switchingType) {
                case NATIONALITY: {
                    newDD = cptController.createDirectDebitForSwitchingNationalityRejectionFlow(oldDD, useSignatures);
                    break;
                }
                case BANK_ACCOUNT: {
                    newDD = cptController.createDirectDebitForSwitchingBankAccountRejectionFlow(oldDD);
                    break;
                }
                default:
                    newDD = null;
            }

            return newDD;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        }
    }

    public void stopAllRejectionFlows(ContractPaymentTerm cpt, Long todoId) {
        directDebitRejectionToDoRepository.findActiveByContract(cpt.getContract(), todoId)
                .forEach(t -> {
                    t.setStopped(true);
                    t.setLeadingRejectionFlow(false);
                    directDebitRejectionToDoRepository.save(t);
                });
    }

    public boolean existOtherWaitingClientSignatureFlow(Contract contract, List<Long> ddIDs) {
        ddIDs = (ddIDs == null || ddIDs.isEmpty() ? Arrays.asList(-1L) : ddIDs);
        logger.info("existOtherWaitingClientSignatureFlow Contract#: " + contract.getId() + ": DDs: " + Arrays.toString(ddIDs.toArray()));
        
        Optional<Long> currentTodoId = directDebitRepository.findAll(ddIDs)
                .stream().map(d -> d.getDirectDebitBouncingRejectionToDo() != null ? 
                        d.getDirectDebitBouncingRejectionToDo() :
                        d.getDirectDebitRejectionToDo())
                .filter(t -> t != null)
                .map(DirectDebitRejectionToDo::getId)
                .findFirst();
        
        logger.info("currentTodo : " + (currentTodoId.isPresent() ? currentTodoId.get() : null));
        
        List<DirectDebit> rejectedDDs = directDebitRepository.findByContractPaymentTermContractAndHasRejectionToDoAndIdNotInAndStatusNotIn(
                contract, ddIDs, Arrays.asList(DirectDebitStatus.EXPIRED, DirectDebitStatus.CANCELED));
        
        logger.info("existOtherWaitingClientSignatureFlow - considered DDs: " + rejectedDDs.size());
        
        return doesClientHavePendingDesignerToDo(contract) ||
                rejectedDDs.stream()
                        .map(d -> d.getDirectDebitBouncingRejectionToDo() != null ? 
                                d.getDirectDebitBouncingRejectionToDo() :
                                d.getDirectDebitRejectionToDo())
                        .anyMatch(t -> {
                            boolean matched = t != null && !t.isStopped() && !t.isCompleted() && isWaitingClientSignature(t);
                            boolean notCurrentToDo = t != null && (!currentTodoId.isPresent() || !currentTodoId.get().equals(t.getId()));

                            logger.info("existOtherWaitingClientSignatureFlow: " + t.getId() + 
                                    "; matched: " + matched + 
                                    "; currentTodo : " + (currentTodoId.isPresent() ? currentTodoId.get() : null) +
                                    "; notCurrentToDo: " + notCurrentToDo);

                            return matched && notCurrentToDo;
                        });
    }

    public boolean isWaitingClientSignature(DirectDebitRejectionToDo rejectionToDo) {
        List<String> currentTasks = rejectionToDo.getCurrentTasks();
        if (currentTasks == null || currentTasks.isEmpty()) return false;

        String currentTask = currentTasks.get(currentTasks.size() - 1);
        if (currentTask.replaceAll("\\s+", "").isEmpty()) return false;

        logger.info("Current Task:" + currentTask + ", ");

        DirectDebitRejectionToDoType step = DirectDebitRejectionToDoType.valueOf(currentTask);

        return Arrays.asList(DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE,
                DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_CASE_D,
                DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_BOUNCED).contains(step);
    }

    public void mergePendingDataEntryDDsIntoOneToDo(DirectDebit directDebit) {
        directDebit = directDebitRepository.findOne(directDebit.getId());

        if (!directDebit.getStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY) &&
                !directDebit.getMStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY)) return;

        List<Long> otherPendingDataEntryDDsBankInfoGroups = directDebitFileRepository.getPendingDDFsByCPTAndDDIdNotInGroupByDDBankInfoGroup(
                directDebit.getContractPaymentTerm(), Arrays.asList(directDebit.getId()));

        if (otherPendingDataEntryDDsBankInfoGroups == null || otherPendingDataEntryDDsBankInfoGroups.isEmpty()) return;

        Long bankInfoGroup = otherPendingDataEntryDDsBankInfoGroups.get(0);
        directDebit.setDdBankInfoGroup(bankInfoGroup);
        directDebitRepository.save(directDebit);
    }

    public boolean doesClientHavePendingDesignerToDo(Contract contract) {
        logger.info("Contract ID: " + contract.getId());
        Map response = moduleConnector.get("visa/GraphicDesignerWorkFlow/get-digitalize-contract-signature?contractId=" +
                contract.getId(), Map.class);
        if (response == null) return false;

        return response.containsKey("status") && response.get("status").toString().equals("PENDING");
    }

    // ACC-4715
    public boolean flowStoppedAfterIncreaseReSignTrials(DirectDebitRejectionToDo ddRejectionToDo) {
        Integer maxReSignTrials = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_REJECTION_FLOW_START_PAYING_VIA_CC_TRIAL));
        ddRejectionToDo.setReSignTrials(ddRejectionToDo.getReSignTrials() + 1);
        if (ddRejectionToDo.getReSignTrials() < maxReSignTrials) return false;

        DirectDebit dd = ddRejectionToDo.getLastDirectDebit();

        Setup.getApplicationContext().getBean(ClientPayingViaCreditCardService.class).startNewFlow(
                dd.getContractPaymentTerm(),
                FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card,
                dd.getCategory().equals(DirectDebitCategory.A) ?
                        FlowSubEventConfig.FlowSubEventName.INITIAL_FLOW_FOR_DDA :
                        FlowSubEventConfig.FlowSubEventName.INITIAL_FLOW_FOR_DDB,
                null, null);

        ddRejectionToDo.setDontSendDdMessage(true);
        return true;
    }
    //ACC-4577
    @Transactional
    public void validateStopDdRejectionFlowAfterPaymentPdc(Long paymentId) {
        logger.log(Level.SEVERE, "Payment id : {0}", paymentId);
        Payment payment = Setup.getRepository(PaymentRepository.class).findOne(paymentId);
        Contract contract = Setup.getRepository(ContractRepository.class).findOne(payment.getContract().getId());
        ContractPaymentTerm contractPaymentTerm = contract.getActiveContractPaymentTerm();
        
        DateTime lastReceived = Setup.getApplicationContext().getBean(PaymentService.class)
                .getLastReceivedMonthlyPaymentDate(contract);
        if(lastReceived != null)
            lastReceived = lastReceived.dayOfMonth().withMaximumValue();
        DateTime currentPaymentDate = new DateTime(payment.getDateOfPayment());
        DateTime paidEndDate = lastReceived != null && currentPaymentDate.isBefore(lastReceived) ?
                lastReceived : currentPaymentDate;

        logger.log(Level.SEVERE,  "contract id: {0}; lastReceived: {1}; paidEndDate: {2}",
                new Object[]{ contract.getId(), lastReceived, paidEndDate });

        //ACC-4742
        // if logic applied before -> just update generation plans
        if (directDebitGenerationPlanService.updatePlansAfterMonthlyPaymentReceived(
                contractPaymentTerm, paidEndDate)) return;

        List<DirectDebit> ddsToCanceled = stopRejectionFlowAfterPaymentPdc(contractPaymentTerm, payment);

        if (ddsToCanceled.isEmpty()) return;

        logger.log(Level.SEVERE, "ddsToCanceled size : {0}", ddsToCanceled.size());
        List<DirectDebit> ddbRejectedList = ddsToCanceled.stream()
                .filter(dd -> dd.getMStatus().equals(DirectDebitStatus.REJECTED) ||
                        dd.getStatus().equals(DirectDebitStatus.REJECTED))
                .collect(Collectors.toList());
        List<DirectDebit> ddListToGenerate = ddbRejectedList.isEmpty() ?
                ddsToCanceled : ddbRejectedList;

        //ACC-4941
        if (ddsToCanceled.stream().noneMatch(dd ->
                (dd.getStatus().equals(DirectDebitStatus.PENDING) ||
                        dd.getMStatus().equals(DirectDebitStatus.PENDING)) &&
                        dd.getDirectDebitFiles().stream().anyMatch(f ->
                                f.getStatus().equals(DirectDebitFileStatus.SENT)))) {

            directDebitGenerationPlanService.generatePlansAfterCancelMonthlyPaymentDd(
                    contractPaymentTerm, paidEndDate,
                    ddListToGenerate.stream().filter(dd ->
                            new DateTime(dd.getStartDate()).isAfter(paidEndDate) ||
                                    new DateTime(dd.getExpiryDate()).isAfter(paidEndDate))
                            .collect(Collectors.toList()));
        }
        
        ddsToCanceled =  ddsToCanceled.stream().filter(dd -> (
                !dd.getStatus().equals(DirectDebitStatus.PENDING) &&
                        !dd.getMStatus().equals(DirectDebitStatus.PENDING)) ||
                        ((dd.getStatus().equals(DirectDebitStatus.PENDING) ||
                                dd.getMStatus().equals(DirectDebitStatus.PENDING)) &&
                                dd.getDirectDebitFiles().stream().noneMatch(f ->
                                        f.getStatus().equals(DirectDebitFileStatus.SENT))))
                .collect(Collectors.toList());
        
        ddsToCanceled.forEach(dd -> {
                logger.log(Level.INFO, "cancel dd  id : {0}", dd.getId());
            Setup.getApplicationContext().getBean(DirectDebitCancellationService.class)
                        .cancelWholeDD(dd, DirectDebitCancellationToDoReason.CONTRACT_ADJUSTED_END_DATE_UPDATED);});
    }

    //ACC-4577
    @Transactional
    public List<DirectDebit> stopRejectionFlowAfterPaymentPdc(ContractPaymentTerm contractPaymentTerm, Payment payment) {
        logger.log(Level.INFO, "ContractPaymentTerm id : {0}", contractPaymentTerm.getId());
        List<DirectDebit> dds = directDebitRepository.findDDBByContractPaymentTermAndStatus(contractPaymentTerm.getId());

        //ACC-4941
        List<DirectDebit>  ddsToCanceled = dds.stream()
                .filter(directDebit -> new DateTime(payment.getDateOfPayment()).isAfter(
                                new DateTime(directDebit.getStartDate()).minusHours(1)) &&
                        new DateTime(payment.getDateOfPayment()).isBefore(
                                new DateTime(directDebit.getExpiryDate()).plusHours(1)))
                .collect(Collectors.toList());
        if (ddsToCanceled.isEmpty()) return new ArrayList<>();
        
        DirectDebit directdebit = ddsToCanceled.get(0);
        logger.log(Level.INFO, "dd id : {0}", directdebit.getId());
        ddsToCanceled = dds.stream()
            .filter(directDebit -> new DateTime(directDebit.getStartDate()).plusHours(1).isAfter(
                new DateTime(payment.getDateOfPayment()).minusHours(1)) ||
                new DateTime(directDebit.getExpiryDate()).isAfter(
                    new DateTime(payment.getDateOfPayment())))
            .collect(Collectors.toList());

        //ACC-4941
        // validating incomplete DD flow -> if after cash -> no incomplete flow running
        if (directdebit.getDirectDebitRejectionToDo() == null &&
                flowProcessorEntityRepository.existsByFlowEventConfig_NameAndContractPaymentTerm_ContractAndStoppedFalseAndCompletedFalse(
                        FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED, contractPaymentTerm.getContract()))
            return new ArrayList<>();

        // validating rejected DD flow
        if (directdebit.getDirectDebitRejectionToDo() != null) {
            logger.log(Level.INFO, "DirectDebtRejectionToDo id : {0}", directdebit.getDirectDebitRejectionToDo().getId());
            ddsToCanceled.addAll(directdebit.getDirectDebitRejectionToDo().getDirectDebits().stream()
                    .filter(dd -> !dds.contains(dd) && !dd.getStatus().equals(DirectDebitStatus.CONFIRMED)
                        && !dd.getMStatus().equals(DirectDebitStatus.CONFIRMED)).collect(Collectors.toList()));

            directdebit.getDirectDebitRejectionToDo().setStopped(true);
            directDebitRejectionToDoRepository.save(directdebit.getDirectDebitRejectionToDo());

            stopAllRejectionFlows(directdebit.getContractPaymentTerm(), directdebit.getDirectDebitRejectionToDo().getId());
        }

        return ddsToCanceled.stream().distinct().collect(Collectors.toList());
    }

    public boolean shouldGenerateNewDdUsingOldSignatures(
            int trial, ContractPaymentTerm cpt) {

        return shouldGenerateNewDdUsingOldSignatures(trial, cpt, new HashMap<>());
    }

    public boolean shouldGenerateNewDdUsingOldSignatures(
            int trial, ContractPaymentTerm cpt, Map<String, Object> map) {

        int paperModeThreshold = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_SIGNING_PAPER_MODE_THRESHOLD_TRIAL));

        if (trial >= paperModeThreshold) return false;

        Map<String, Object> signatureType = directDebitSignatureService
                .getLastSignatureType(cpt, true, false);

        if (!(Boolean) signatureType.get("useApprovedSignature")
                && !(Boolean) signatureType.get("useNonRejectedSignature")) return false;

        map.put("signatures", signatureType.get("currentSignatures"));
        return true;
    }

    public boolean todoExpired(DirectDebitRejectionToDo t) {
        DirectDebit d = t.getLastDirectDebit();
        if (d == null) return true;

        PaymentRepository paymentRepository = Setup.getRepository(PaymentRepository.class);

        List<DirectDebitStatus> canceledStatuses = Arrays.asList(
                DirectDebitStatus.EXPIRED, DirectDebitStatus.CANCELED);
        ContractPaymentTerm cpt = d.getContractPaymentTerm();
        Contract contract = d.getContractPaymentTerm().getContract();

        boolean contractTerminated = !cpt.isActive() || contract.getDateOfTermination() != null ||
                contract.getScheduledDateOfTermination() != null;
        boolean mStop = canceledStatuses.contains(d.getMStatus()) || d.getMStatus().equals(DirectDebitStatus.CONFIRMED);
        boolean ddbBouncedStop = d.getMStatus().equals(DirectDebitStatus.CONFIRMED) && d.getDirectDebitBouncingRejectionToDo() != null;
        boolean aStop = canceledStatuses.contains(d.getStatus()) ||
                (d.getStatus().equals(DirectDebitStatus.CONFIRMED) &&
                        d.getDirectDebitBouncingRejectionToDo() == null);
        boolean ddaReceived = d.getCategory().equals(DirectDebitCategory.A) &&
                d.getContractPayments().stream().allMatch(p -> paymentRepository.existsByStatusAndContractAndDateOfPaymentAndTypeOfPayment(
                        PaymentStatus.RECEIVED, d.getContractPaymentTerm().getContract(), p.getDate(), p.getPaymentType()));

        logger.info("contractTerminated: " + contractTerminated +
                "; mStop: " + mStop + "; ddbBouncedStop: " + ddbBouncedStop +
                "; aStop: " + aStop + "; ddaReceived: " + ddaReceived);

        // close rejection to-do if the contract is not active anymore or the CPT is not active or
        // the direct debit is rejected or expired or cancelled
        // or if it's related to DDB and there is a newer DDB cover the same period (not rejected or cancelled or expired)
        boolean todoExpired =  contractTerminated || ddaReceived ||
                (d.getCategory().equals(DirectDebitCategory.A) && mStop) ||
                (d.getCategory().equals(DirectDebitCategory.B) && aStop) ||
                (d.getCategory().equals(DirectDebitCategory.B) && ddbBouncedStop) ||
                (d.getCategory().equals(DirectDebitCategory.B) && d.getDirectDebitBouncingRejectionToDo() == null &&
                        directDebitRepository.existOverLappingDDBs(cpt, d.getStartDate(), d.getExpiryDate()));
        logger.info("todoExpired: " + todoExpired);

        return todoExpired;
    }

    // ACC-7082
    public Date setContractForTermination(ContractPaymentTerm cpt, String terminationReason, DirectDebitRejectionToDo todo) {
        stopAllRejectionFlows(cpt, todo.getId());

        return Setup.getApplicationContext()
                .getBean(ContractService.class)
                .setContractForTermination(cpt.getContract(), terminationReason);
    }

    public boolean existsRunningDirectDebitRejectionFlow(Contract c) {

        return directDebitRejectionToDoRepository.existsActiveTodoByCategory(c.getId(), null);
    }
}