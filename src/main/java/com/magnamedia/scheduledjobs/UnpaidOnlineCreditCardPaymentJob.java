package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.FlowProcessorEntity;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowProgressPeriod;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.repository.FlowEventConfigRepository;
import com.magnamedia.repository.FlowProcessorEntityRepository;
import com.magnamedia.service.FlowProcessorService;
import org.joda.time.DateTime;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class UnpaidOnlineCreditCardPaymentJob implements MagnamediaJob {

    private static final Logger logger = Logger.getLogger(UnpaidOnlineCreditCardPaymentJob.class.getName());

    private FlowProcessorService flowProcessorService;
    private FlowEventConfig flowEventConfig;
    private FlowProcessorEntityRepository flowProcessorEntityRepository;

    @Override
    public void run(Map<String, ?> map) {
        logger.log(Level.INFO, "Job started");

        flowProcessorEntityRepository = Setup.getRepository(FlowProcessorEntityRepository.class);
        flowProcessorService = Setup.getApplicationContext().getBean(FlowProcessorService.class);
        flowEventConfig = Setup.getRepository(FlowEventConfigRepository.class)
                .findByName(FlowEventConfig.FlowEventName.ONLINE_CREDIT_CARD_PAYMENT_REMINDERS);
        if (flowEventConfig == null) return;
        logger.log(Level.INFO, "flowEventConfig id: {0}", flowEventConfig.getId());

        sendUnpaidOnlineCreditCardPaymentMessages();
        logger.log(Level.INFO, "Job finished");

    }

    public void sendUnpaidOnlineCreditCardPaymentMessages() {
        logger.log(Level.INFO, "sendUnpaidOnlineCreditCardPaymentMessages runs");

        List <FlowProcessorEntity> flowProcessorEntities = flowProcessorEntityRepository
                .findByFlowEventConfigAndStoppedFalseAndCompletedFalse(flowEventConfig);

        logger.log(Level.INFO, "flowProcessorEntities size: {0}", flowProcessorEntities.size());

        for (FlowProcessorEntity flowProcessorEntity : flowProcessorEntities) {
            logger.log(Level.INFO, "processing entity id: {0}", flowProcessorEntity.getId());
            try {
                logger.info("contractPaymentTerms id: " + flowProcessorEntity.getContractPaymentTerm().getId());

                if (shouldDelayTerminationMessageBeforePaidEndDate(flowProcessorEntity)) {
                    logger.info("Flow Stopped Or Delay sending the termination message of the Online Reminder flow required -> exiting");
                    continue;
                }

                flowProcessorService.processFlowSubEventConfig(flowProcessorEntity);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public boolean shouldDelayTerminationMessageBeforePaidEndDate(FlowProcessorEntity flow) throws ParseException {

        logger.info("Flow Trials: " + flow.getTrials() + "; Flow reminders: " + flow.getReminders());

        // 1-Check Stop Flow
        if (flowProcessorService.validateFlowStopping(flow)) return true;

        // 2-Validation and Check
        if (!flow.getCurrentSubEvent().getName()
                .equals(FlowSubEventConfig.FlowSubEventName.PAYMENT_REMINDER_FOR_REQUIRED_PAYMENTS)) return false;

        if (!flow.getCurrentSubEvent().isTerminateContractOnMaxReminders() ||
                flow.getReminders() < flow.getCurrentSubEvent().getMaxReminders() - 1) return false;

        DateTime paidEndDate = new DateTime(flow.getContractPaymentTerm().getContract().getPaidEndDate());

        FlowProgressPeriod flowProgressPeriod = flow.getCurrentSubEvent().getProgressPeriods()
                .stream()
                .filter(progress ->
                        flow.getCurrentSubEvent().getMaxTrials() == progress.getTrials() &&
                                flow.getCurrentSubEvent().getMaxReminders() == progress.getReminders())
                .findFirst()
                .orElse(null);

        logger.info("paidEndDate: " + paidEndDate.toString("yyyy-MM-dd HH:mm:ss") +
                ", Hours Period: " + flowProgressPeriod.getPeriodInHours());

        DateTime lastExecutionDate = new DateTime(paidEndDate)
                .withHourOfDay(9)
                .withMinuteOfHour(0)
                .withSecondOfMinute(0)
                .minusHours(flowProgressPeriod.getPeriodInHours());

        Date nextTrialSendDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                .parse(String.valueOf(flow.getAdditionalValue("lastTrialSendDate")));
        if (nextTrialSendDate != null) {
            nextTrialSendDate = new DateTime(nextTrialSendDate)
                    .plusHours(flowProgressPeriod.getPeriodInHours()).toDate();
        }

        // Check If the new "LastExecutionDate" is in the past && Allowed to send the message, the process should be completed.
        if (new DateTime().isAfter(lastExecutionDate) &&
                (nextTrialSendDate == null || new Date().getTime() >= nextTrialSendDate.getTime())) {

            flow.setLastExecutionDate(lastExecutionDate.toDate());
            return false;
        }

        // In order to skip updating the "LastExecutionDate" every time the job runs
        // Update only when necessary
        if (lastExecutionDate.toString("yyyy-MM-dd")
                .equals(new DateTime(flow.getLastExecutionDate()).toString("yyyy-MM-dd"))) return true;

        // 3-Delay Sending Termination Message
        flow.setLastExecutionDate(lastExecutionDate.toDate());
        flowProcessorEntityRepository.save(flow);
        return true;
    }
}