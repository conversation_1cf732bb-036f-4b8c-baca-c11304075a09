package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

public enum VoiceResolverToDoReason implements LabelValueEnum {

    PAYMENT_EXPIRY_OLD_CC("Payment Expiry Old CC"),
    REQUEST_FOR_CANELLATION("Request for Cancellation"),
    PAYMENT_EXPIRY_INDEFINITE_AGREEMENT("Payment Expiry Indefinite Agreement"),
    COMPLAINT("Complain<PERSON>"),
    CLIENT_3DAYS_WITHOUT_MAID("Client 3 Days Without Maid"),
    PAYMENT_EXPIRY("Payment Expiry") ,
    REJECTED_DD_FORMS("Rejected DD Forms"),
    UNDO_CANCELLATION("Undo Cancellation"),
    CLIENT_REQUESTS_A_CALL("Client Requests Us to Call Him"),
    CALL_BLOCKED_CLIENT("Call Blocked Client"),
    BOUNCED_PAYMENT("Bounced Payment"),
    PAYMENT_REMINDER("Payment Reminder"),
    INCOMPLETE_DD("Incomplete_DD"),
    REJECTED_MAID_PHOTO_BY_ICA("<PERSON> <PERSON>'s photo is rejected by ICA");

    private final String label;

    VoiceResolverToDoReason(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}