package com.magnamedia.repository;

import com.magnamedia.core.entity.Template;
import com.magnamedia.core.entity.template.ChannelSpecificSetting;
import com.magnamedia.core.repository.BaseRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AccTemplateRepository extends BaseRepository<Template> {
    boolean existsByName(String k);

    // ACC-5498
    @Query("select distinct s from TemplateChannelParameter p " +
            "join p.setting s " +
            "where s.type = 'SMS' and s.trailingSentence is null and " +
            "p.name in ('pay_using_different_bank_account_sms', 'bounced_payment_sms_link')")
    List<ChannelSpecificSetting> findTemplatesAcc5498();

    // ACC-5498
    @Query("select distinct t from Template t " +
            "where t.creatorModule = ?1 and t.status.code = 'active' and t.newModel = 1 " +
                "and not exists (select 1 from TemplateAllowedParameter p where p.template = t)")
    List<Template> getTemplateForGenerateGenderExpressions(Long id);
}